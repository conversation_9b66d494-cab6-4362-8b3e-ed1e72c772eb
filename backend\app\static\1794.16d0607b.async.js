(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1794],{47046:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"}},51042:function(e,t,n){"use strict";var r=n(1413),s=n(67294),a=n(42110),o=n(91146),i=function(e,t){return s.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:a.Z}))},c=s.forwardRef(i);t.Z=c},14079:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var r=n(1413),s=n(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 161H699.2c-49.1 0-97.1 14.1-138.4 40.7L512 233l-48.8-31.3A255.2 255.2 0 00324.8 161H96c-17.7 0-32 14.3-32 32v568c0 17.7 14.3 32 32 32h228.8c49.1 0 97.1 14.1 138.4 40.7l44.4 28.6c1.3.8 2.8 1.3 4.3 1.3s3-.4 4.3-1.3l44.4-28.6C602 807.1 650.1 793 699.2 793H928c17.7 0 32-14.3 32-32V193c0-17.7-14.3-32-32-32zM324.8 721H136V233h188.8c35.4 0 69.8 10.1 99.5 29.2l48.8 31.3 6.9 4.5v462c-47.6-25.6-100.8-39-155.2-39zm563.2 0H699.2c-54.4 0-107.6 13.4-155.2 39V298l6.9-4.5 48.8-31.3c29.7-19.1 64.1-29.2 99.5-29.2H888v488zM396.9 361H211.1c-3.9 0-7.1 3.4-7.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c.1-4.1-3.1-7.5-7-7.5zm223.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c0-4.1-3.2-7.5-7.1-7.5H627.1c-3.9 0-7.1 3.4-7.1 7.5zM396.9 501H211.1c-3.9 0-7.1 3.4-7.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c.1-4.1-3.1-7.5-7-7.5zm416 0H627.1c-3.9 0-7.1 3.4-7.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c.1-4.1-3.1-7.5-7-7.5z"}}]},name:"read",theme:"outlined"},o=n(91146),i=function(e,t){return s.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:a}))};var c=s.forwardRef(i)},9502:function(e,t,n){"use strict";n.d(t,{Z:function(){return Z}});var r=n(87462),s=n(93967),a=n.n(s),o=n(67294),i=n(71471);const c=o.createContext(null);var l=({children:e})=>{const{prefixCls:t}=o.useContext(c);return o.createElement("div",{className:a()(`${t}-group-title`)},e&&o.createElement(i.Z.Text,null,e))},u=n(29245),d=n(51398),f=function(e,t){return o.createElement(d.Z,(0,r.Z)({},e,{ref:t,icon:u.Z}))};var m=o.forwardRef(f),p=n(83062),h=n(85418),g=n(64217);const y=e=>{e.stopPropagation()};var v=e=>{const{prefixCls:t,info:n,className:s,direction:c,onClick:l,active:u,menu:d,...f}=e,v=(0,g.Z)(f,{aria:!0,data:!0,attr:!0}),{disabled:b}=n,[w,$]=o.useState(!1),[E,x]=o.useState(!1),R=a()(s,`${t}-item`,{[`${t}-item-active`]:u&&!b},{[`${t}-item-disabled`]:b});return o.createElement(p.Z,{title:n.label,open:w&&E,onOpenChange:x,placement:"rtl"===c?"left":"right"},o.createElement("li",(0,r.Z)({},v,{className:R,onClick:()=>{!b&&l&&l(n)}}),n.icon&&o.createElement("div",{className:`${t}-icon`},n.icon),o.createElement(i.Z.Text,{className:`${t}-label`,ellipsis:{onEllipsis:$}},n.label),d&&!b&&o.createElement(h.Z,{menu:d,placement:"rtl"===c?"bottomLeft":"bottomRight",trigger:["click"],disabled:b,onOpenChange:e=>{e&&x(!e)}},o.createElement(m,{onClick:y,disabled:b,className:`${t}-menu-icon`}))))},b=n(21770),w=n(21450),$=n(36158);const E="__ungrouped";var x=(e,t=[])=>{const[n,r,s]=o.useMemo((()=>{if(!e)return[!1,void 0,void 0];let t={sort:void 0,title:void 0};return"object"==typeof e&&(t={...t,...e}),[!0,t.sort,t.title]}),[e]);return o.useMemo((()=>{if(!n){return[[{name:E,data:t,title:void 0}],n]}const e=t.reduce(((e,t)=>{const n=t.group||E;return e[n]||(e[n]=[]),e[n].push(t),e}),{});return[(r?Object.keys(e).sort(r):Object.keys(e)).map((t=>({name:t===E?void 0:t,title:s,data:e[t]}))),n]}),[t,e])},R=n(11568),S=n(83262),H=n(43495);var k=(0,H.I$)("Conversations",(e=>(e=>{const{componentCls:t}=e;return{[t]:{display:"flex",flexDirection:"column",gap:e.paddingXXS,overflowY:"auto",padding:e.paddingSM,[`&${t}-rtl`]:{direction:"rtl"},[`& ${t}-list`]:{display:"flex",gap:e.paddingXXS,flexDirection:"column",[`& ${t}-item`]:{paddingInlineStart:e.paddingXL},[`& ${t}-label`]:{color:e.colorTextDescription}},[`& ${t}-item`]:{display:"flex",height:e.controlHeightLG,minHeight:e.controlHeightLG,gap:e.paddingXS,padding:`0 ${(0,R.bf)(e.paddingXS)}`,alignItems:"center",borderRadius:e.borderRadiusLG,cursor:"pointer",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,"&:hover":{backgroundColor:e.colorBgTextHover},"&-active":{backgroundColor:e.colorBgTextHover,[`& ${t}-label, ${t}-menu-icon`]:{color:e.colorText}},"&-disabled":{cursor:"not-allowed",[`& ${t}-label`]:{color:e.colorTextDisabled}},"&:hover, &-active":{[`& ${t}-menu-icon`]:{opacity:1}}},[`& ${t}-label`]:{flex:1,color:e.colorText},[`& ${t}-menu-icon`]:{opacity:0,fontSize:e.fontSizeXL},[`& ${t}-group-title`]:{display:"flex",alignItems:"center",height:e.controlHeightLG,minHeight:e.controlHeightLG,padding:`0 ${(0,R.bf)(e.paddingXS)}`}}}})((0,S.IX)(e,{}))),(()=>({})));var Z=e=>{const{prefixCls:t,rootClassName:n,items:s,activeKey:i,defaultActiveKey:u,onActiveChange:d,menu:f,styles:m={},classNames:p={},groupable:h,className:y,style:E,...R}=e,S=(0,g.Z)(R,{attr:!0,aria:!0,data:!0}),[H,Z]=(0,b.Z)(u,{value:i}),[C,T]=x(h,s),{getPrefixCls:N,direction:q}=(0,$.Z)(),M=N("conversations",t),L=(0,w.Z)("conversations"),[z,U,X]=k(M),A=a()(M,L.className,y,n,U,X,{[`${M}-rtl`]:"rtl"===q}),I=e=>{Z(e.key),d&&d(e.key)};return z(o.createElement("ul",(0,r.Z)({},S,{style:{...L.style,...E},className:A}),C.map(((e,t)=>{const n=e.data.map(((e,t)=>o.createElement(v,{key:e.key||`key-${t}`,info:e,prefixCls:M,direction:q,className:a()(p.item,L.classNames.item),style:{...L.styles.item,...m.item},menu:"function"==typeof f?f(e):f,active:H===e.key,onClick:I})));return T?o.createElement("li",{key:e.name||`key-${t}`},o.createElement(c.Provider,{value:{prefixCls:M}},e.title?.(e.name,{components:{GroupTitle:l}})||o.createElement(l,{key:e.name},e.name)),o.createElement("ul",{className:`${M}-list`},n)):n}))))}},93461:function(e,t,n){"use strict";n.d(t,{Z:function(){return d}});var r=n(67294);const s=e=>""!==(e??"").trim();var a=function(e){const{readableStream:t,transformStream:n}=e;if(!(t instanceof ReadableStream))throw new Error("The options.readableStream must be an instance of ReadableStream.");const r=new TextDecoderStream,a=n?t.pipeThrough(r).pipeThrough(n):t.pipeThrough(r).pipeThrough(function(){let e="";return new TransformStream({transform(t,n){e+=t;const r=e.split("\n\n");r.slice(0,-1).forEach((e=>{s(e)&&n.enqueue(e)})),e=r[r.length-1]},flush(t){s(e)&&t.enqueue(e)}})}()).pipeThrough(new TransformStream({transform(e,t){const n=e.split("\n").reduce(((e,t)=>{const n=t.indexOf(":");if(-1===n)throw new Error('The key-value separator ":" is not found in the sse line chunk!');const r=t.slice(0,n);if(!s(r))return e;const a=t.slice(n+1);return{...e,[r]:a}}),{});0!==Object.keys(n).length&&t.enqueue(n)}}));return a[Symbol.asyncIterator]=async function*(){const e=this.getReader();for(;;){const{done:t,value:n}=await e.read();if(t)break;n&&(yield n)}},a};var o=async(e,t={})=>{const{fetch:n=globalThis.fetch,middlewares:r={},...s}=t;if("function"!=typeof n)throw new Error("The options.fetch must be a typeof fetch function!");let a=[e,s];if("function"==typeof r.onRequest){a=await r.onRequest(...a)}let o=await n(...a);if("function"==typeof r.onResponse){const e=await r.onResponse(o);if(!(e instanceof Response))throw new Error("The options.onResponse must return a Response instance!");o=e}if(!o.ok)throw new Error(`Fetch failed with status ${o.status}`);if(!o.body)throw new Error("The response body is empty.");return o};class i{baseURL;model;defaultHeaders;customOptions;static instanceBuffer=new Map;constructor(e){const{baseURL:t,model:n,dangerouslyApiKey:r,...s}=e;this.baseURL=e.baseURL,this.model=e.model,this.defaultHeaders={"Content-Type":"application/json",...e.dangerouslyApiKey&&{Authorization:e.dangerouslyApiKey}},this.customOptions=s}static init(e){if(!e.baseURL||"string"!=typeof e.baseURL)throw new Error("The baseURL is not valid!");const t=e.fetch||e.baseURL;return i.instanceBuffer.has(t)||i.instanceBuffer.set(t,new i(e)),i.instanceBuffer.get(t)}create=async(e,t,n)=>{const r={method:"POST",body:JSON.stringify({model:this.model,...e}),headers:this.defaultHeaders};try{const e=await o(this.baseURL,{fetch:this.customOptions.fetch,...r});if(n)return void await this.customResponseHandler(e,t,n);const s=e.headers.get("content-type")||"";switch(s.split(";")[0].trim()){case"text/event-stream":await this.sseResponseHandler(e,t);break;case"application/json":await this.jsonResponseHandler(e,t);break;default:throw new Error(`The response content-type: ${s} is not support!`)}}catch(e){const n=e instanceof Error?e:new Error("Unknown error!");throw t?.onError?.(n),n}};customResponseHandler=async(e,t,n)=>{const r=[];for await(const s of a({readableStream:e.body,transformStream:n}))r.push(s),t?.onUpdate?.(s);t?.onSuccess?.(r)};sseResponseHandler=async(e,t)=>{const n=[];for await(const r of a({readableStream:e.body}))n.push(r),t?.onUpdate?.(r);t?.onSuccess?.(n)};jsonResponseHandler=async(e,t)=>{const n=await e.json();t?.onUpdate?.(n),t?.onSuccess?.([n])}}var c=i.init;let l=0;class u{config;requestingMap={};constructor(e){this.config=e}finishRequest(e){delete this.requestingMap[e]}request=(e,t)=>{const{request:n}=this.config,{onUpdate:r,onSuccess:s,onError:a}=t,o=l;l+=1,this.requestingMap[o]=!0,n?.(e,{onUpdate:e=>{this.requestingMap[o]&&r(e)},onSuccess:e=>{this.requestingMap[o]&&(s(e),this.finishRequest(o))},onError:e=>{this.requestingMap[o]&&(a(e),this.finishRequest(o))}})};isRequesting(){return Object.keys(this.requestingMap).length>0}}function d(e){const{request:t,...n}=e;return r.useMemo((()=>[new u({request:t||c({baseURL:n.baseURL,model:n.model,dangerouslyApiKey:n.dangerouslyApiKey}).create,...n})]),[])}},34114:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var r=n(56790),s=n(67294);function a(e){const{defaultMessages:t,agent:n,requestFallback:a,requestPlaceholder:o,parser:i}=e,c=s.useRef(0),[l,u,d]=function(e){const[,t]=s.useState(0),n=s.useRef("function"==typeof e?e():e),r=s.useCallback((e=>{n.current="function"==typeof e?e(n.current):e,t((e=>e+1))}),[]),a=s.useCallback((()=>n.current),[]);return[n.current,r,a]}((()=>(t||[]).map(((e,t)=>({id:`default_${t}`,status:"local",...e}))))),f=(e,t)=>{const n={id:`msg_${c.current}`,message:e,status:t};return c.current+=1,n},m=s.useMemo((()=>{const e=[];return l.forEach((t=>{const n=i?i(t.message):t.message,r=(s=n,Array.isArray(s)?s:[s]);var s;r.forEach(((n,s)=>{let a=t.id;r.length>1&&(a=`${a}_${s}`),e.push({id:a,message:n,status:t.status})}))})),e}),[l]),p=e=>e.filter((e=>"loading"!==e.status&&"error"!==e.status)).map((e=>e.message)),h=()=>p(d());return{onRequest:(0,r.zX)((e=>{if(!n)throw new Error("The agent parameter is required when using the onRequest method in an agent generated by useXAgent.");let t=null;u((n=>{let r=[...n,f(e,"local")];if(o){let n;n="function"==typeof o?o(e,{messages:p(r)}):o;const s=f(n,"loading");t=s.id,r=[...r,s]}return r}));let r=null;const s=(e,n)=>{let s=d().find((e=>e.id===r));return s?u((t=>t.map((t=>t.id===r?{...t,message:e,status:n}:t)))):(s=f(e,n),u((e=>[...e.filter((e=>e.id!==t)),s])),r=s.id),s};n.request({message:e,messages:h()},{onUpdate:e=>{s(e,"loading")},onSuccess:e=>{s(e,"success")},onError:async n=>{if(a){let s;s="function"==typeof a?await a(e,{error:n,messages:h()}):a,u((e=>[...e.filter((e=>e.id!==t&&e.id!==r)),f(s,"error")]))}else u((e=>e.filter((e=>e.id!==t&&e.id!==r))))}})})),messages:l,parsedMessages:m,setMessages:u}}},78205:function(e,t,n){"use strict";n.d(t,{Z:function(){return g}});var r=n(71471),s=n(86250),a=n(93967),o=n.n(a),i=n(67294),c=n(21450),l=n(36158),u=n(83262),d=n(43495);const f=e=>{const{componentCls:t,calc:n}=e,r=n(e.fontSizeHeading3).mul(e.lineHeightHeading3).equal(),s=n(e.fontSize).mul(e.lineHeight).equal();return{[t]:{gap:e.padding,[`${t}-icon`]:{height:n(r).add(s).add(e.paddingXXS).equal(),display:"flex",img:{height:"100%"}},[`${t}-content-wrapper`]:{gap:e.paddingXS,flex:"auto",minWidth:0,[`${t}-title-wrapper`]:{gap:e.paddingXS},[`${t}-title`]:{margin:0},[`${t}-extra`]:{marginInlineStart:"auto"}}}}},m=e=>{const{componentCls:t}=e;return{[t]:{"&-filled":{paddingInline:e.padding,paddingBlock:e.paddingSM,background:e.colorFillContent,borderRadius:e.borderRadiusLG},"&-borderless":{[`${t}-title`]:{fontSize:e.fontSizeHeading3,lineHeight:e.lineHeightHeading3}}}}};var p=(0,d.I$)("Welcome",(e=>{const t=(0,u.IX)(e,{});return[f(t),m(t)]}),(()=>({})));function h(e,t){const{prefixCls:n,rootClassName:a,className:u,style:d,variant:f="filled",classNames:m={},styles:h={},icon:g,title:y,description:v,extra:b}=e,{direction:w,getPrefixCls:$}=(0,l.Z)(),E=$("welcome",n),x=(0,c.Z)("welcome"),[R,S,H]=p(E),k=i.useMemo((()=>{if(!g)return null;let e=g;return"string"==typeof g&&g.startsWith("http")&&(e=i.createElement("img",{src:g,alt:"icon"})),i.createElement("div",{className:o()(`${E}-icon`,x.classNames.icon,m.icon),style:h.icon},e)}),[g]),Z=i.useMemo((()=>y?i.createElement(r.Z.Title,{level:4,className:o()(`${E}-title`,x.classNames.title,m.title),style:h.title},y):null),[y]),C=i.useMemo((()=>b?i.createElement("div",{className:o()(`${E}-extra`,x.classNames.extra,m.extra),style:h.extra},b):null),[b]);return R(i.createElement(s.Z,{ref:t,className:o()(E,x.className,u,a,S,H,`${E}-${f}`,{[`${E}-rtl`]:"rtl"===w}),style:d},k,i.createElement(s.Z,{vertical:!0,className:`${E}-content-wrapper`},b?i.createElement(s.Z,{align:"flex-start",className:`${E}-title-wrapper`},Z,C):Z,v&&i.createElement(r.Z.Text,{className:o()(`${E}-description`,x.classNames.description,m.description),style:h.description},v))))}var g=i.forwardRef(h)},64599:function(e,t,n){var r=n(96263);e.exports=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=r(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var s=0,a=function(){};return{s:a,n:function(){return s>=e.length?{done:!0}:{done:!1,value:e[s++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){c=!0,o=e},f:function(){try{i||null==n.return||n.return()}finally{if(c)throw o}}}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);