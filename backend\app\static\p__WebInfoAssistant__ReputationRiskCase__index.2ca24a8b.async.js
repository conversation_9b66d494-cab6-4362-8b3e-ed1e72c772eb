"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1983],{92287:function(e,n){n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"}},34804:function(e,n,t){var r=t(1413),s=t(67294),c=t(66023),o=t(91146),a=function(e,n){return s.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:c.Z}))},i=s.forwardRef(a);n.Z=i},64029:function(e,n,t){var r=t(1413),s=t(67294),c=t(92287),o=t(91146),a=function(e,n){return s.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:c.Z}))},i=s.forwardRef(a);n.Z=i},78653:function(e,n,t){t.r(n),t.d(n,{default:function(){return he}});var r=t(97857),s=t.n(r),c=t(15009),o=t.n(c),a=t(64599),i=t.n(a),l=t(19632),u=t.n(l),d=t(99289),p=t.n(d),f=t(5574),x=t.n(f),m=t(84901),v=t(11488),g=t(93461),h=t(34114),k=t(78205),b=t(78919),y=t(4628),Z=t(9502),j=t(37864),w=t(42075),_=t(71471),S=t(83622),C=t(2453),P=t(17788),D=t(74330),F=t(86250),R=t(66309),z=t(83062),T=t(55102),M=t(85265),Y=t(67294),E=t(10048),N=t(10981),H=t(78404),I=t(1832),q=t(14079),L=t(66513),A=t(93045),O=t(71255),B=t(51042),W=t(82061),J=t(64029),G=t(34804),K=t(47389),X=t(87784),U=t(25820),$=t(75750),Q=t(12906),V=t(85175),ee=t(43471),ne=t(27484),te=t.n(ne),re=(0,t(28846).kc)((function(e){var n=e.token;return{reference:{background:"".concat(n.colorBgLayout,"80"),minWidth:"400px",maxWidth:"720px",width:"50%"},layout:{width:"100%",minWidth:"800px",borderRadius:n.borderRadius,display:"flex",background:n.colorBgContainer,fontFamily:"AlibabaPuHuiTi, ".concat(n.fontFamily,", sans-serif"),".ant-prompts":{color:n.colorText},border:"3px solid",borderImage:"linear-gradient(45deg, ".concat(n.colorPrimary,", ").concat(n.colorSplit,") 1")},menu:{height:"100%",display:"flex",flexDirection:"column"},conversations:{padding:"0 12px",flex:1,overflowY:"auto"},chat:{height:"100%",width:"50%",maxWidth:"700px",margin:"0 auto",boxSizing:"border-box",display:"flex",flexDirection:"column",padding:n.paddingLG,gap:"16px"},messages:{flex:1},placeholder:{paddingTop:"32px"},sender:{boxShadow:n.boxShadow},logo:{display:"flex",height:"72px",alignItems:"center",justifyContent:"start",padding:"0 24px",boxSizing:"border-box",img:{width:"24px",height:"24px",display:"inline-block"},span:{display:"inline-block",margin:"0 8px",fontWeight:"bold",color:n.colorText,fontSize:"16px"}},addBtn:{background:"#1677ff0f",border:"1px solid #1677ff34",width:"calc(100% - 24px)",margin:"0 12px 24px 12px"},wiseAnswer:{display:"block",padding:"12px",background:"#e6f7ff",borderRadius:"8px",borderLeft:"4px solid #1890ff",margin:"8px 0"}}})),se=t(93933),ce=t(13973),oe=t(85893);function ae(e){return e+"-"+Date.now()}var ie=function(e,n){return(0,oe.jsxs)(w.Z,{align:"start",children:[e,(0,oe.jsx)("span",{children:n})]})},le=[{key:"1",label:ie((0,oe.jsx)(I.Z,{style:{color:"#FF4D4F"}}),"热门合规主题"),description:"您想了解哪些合规问题？",children:[{key:"1-1",description:"如何确保金融产品合规销售？"},{key:"1-2",description:"反洗钱相关规定有哪些？"},{key:"1-3",description:"客户信息保护要求有哪些？"}]},{key:"2",label:ie((0,oe.jsx)(q.Z,{style:{color:"#1890FF"}}),"合规风控"),description:"了解金融业务合规要求",children:[{key:"2-1",icon:(0,oe.jsx)(L.Z,{}),description:"业务操作合规指南"},{key:"2-2",icon:(0,oe.jsx)(A.Z,{}),description:"风险控制要点解析"},{key:"2-3",icon:(0,oe.jsx)(O.Z,{}),description:"合规案例分析"}]},{key:"3",label:ie((0,oe.jsx)(q.Z,{style:{color:"#1890FF"}}),"监管政策"),description:"了解最新监管要求",children:[{key:"3-1",icon:(0,oe.jsx)(L.Z,{}),description:"最新监管政策解读"},{key:"3-2",icon:(0,oe.jsx)(A.Z,{}),description:"合规检查重点提示"},{key:"3-3",icon:(0,oe.jsx)(O.Z,{}),description:"违规处罚案例警示"}]}],ue=[{key:"historyConversation",description:"历史对话",icon:(0,oe.jsx)(q.Z,{style:{color:"#FF4D4F"}})},{key:"newConversation",description:"新建对话",icon:(0,oe.jsx)(B.Z,{style:{color:"#1890FF"}})},{key:"clearConversation",description:"清空对话",icon:(0,oe.jsx)(W.Z,{style:{color:"#1890FF"}})}],de=(0,N.bG)(),pe=(0,H.kH)(),fe=(0,E.Z)({html:!0,breaks:!0}),xe=function(e){return(0,oe.jsx)(_.Z,{style:{marginBottom:0},children:(0,oe.jsx)("div",{dangerouslySetInnerHTML:{__html:fe.render(e)}})})},me="complianceQA",ve=function(e){var n=e.content,t=(0,Y.useState)(!1),r=x()(t,2),s=r[0],c=r[1],o=function(e){var n=[];return{processedContent:e.replace(/<think>([\s\S]*?)<\/think>/g,(function(e,t){return n.push(t.trim()),""})).trim(),thinkBlocks:n}}(n),a=o.processedContent,i=o.thinkBlocks;return(0,oe.jsxs)("div",{style:{position:"relative"},children:[xe(a),i.length>0&&(0,oe.jsxs)("div",{style:{marginTop:8},children:[(0,oe.jsx)(S.ZP,{type:"text",size:"small",icon:s?(0,oe.jsx)(J.Z,{}):(0,oe.jsx)(G.Z,{}),onClick:function(){return c(!s)},style:{color:"#666",fontSize:12,padding:0,height:"auto",background:"transparent"},children:(0,oe.jsxs)("span",{style:{marginLeft:4},children:[s?"收起":"展开","思考过程 (",i.length,")"]})}),(0,oe.jsx)("div",{style:{maxHeight:s?"1000px":"0",overflow:"hidden",transition:"max-height 0.3s ease-out",background:"#f9f9f9",borderRadius:8,marginTop:4},children:i.map((function(e,n){return(0,oe.jsx)("pre",{style:{whiteSpace:"pre-wrap",margin:0,padding:12,fontFamily:"monospace",fontSize:12},children:e},n)}))})]})]})},ge=function(e){return(0,oe.jsx)(ve,{content:e})},he=function(){var e,n=re().styles,t=(0,Y.useState)(window.innerHeight),r=x()(t,1)[0],c=Y.useRef(),a=Y.useState(!1),l=x()(a,2),d=l[0],f=l[1],_=Y.useState(""),E=x()(_,2),H=E[0],I=E[1],q=Y.useState([]),L=x()(q,2),A=L[0],O=L[1],J=Y.useState(),G=x()(J,2),ne=G[0],ie=G[1],fe=(0,Y.useState)(!1),xe=x()(fe,2),ve=xe[0],he=xe[1],ke=(0,Y.useState)(!1),be=x()(ke,2),ye=be[0],Ze=be[1],je=(0,Y.useState)(!1),we=x()(je,2),_e=we[0],Se=we[1],Ce=(0,Y.useState)(""),Pe=x()(Ce,2),De=Pe[0],Fe=Pe[1],Re=(0,Y.useState)(""),ze=x()(Re,2),Te=ze[0],Me=ze[1],Ye=(0,Y.useState)([]),Ee=x()(Ye,2),Ne=Ee[0],He=Ee[1],Ie=(0,Y.useRef)(null),qe=(0,Y.useState)(!1),Le=x()(qe,2),Ae=Le[0],Oe=Le[1],Be=(0,Y.useState)(""),We=x()(Be,2),Je=We[0],Ge=We[1],Ke=function(e){Ge(e),Oe(!0)},Xe=function(e){var n=Ne.find((function(n){return n.message_id===e}));n&&navigator.clipboard.writeText(n.content).then((function(){C.ZP.success("复制成功")})).catch((function(){C.ZP.error("复制失败")}))},Ue=(0,g.Z)({request:(e=p()(o()().mark((function e(n,t){var r,s,a,l,d,p,f,x,m,g,h,k,b,y,Z,j,w,_,S,P,D,F,R,z,T,M,Y,E,H,I,q,L,A,O,B,W,J;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.messages,s=n.message,a=t.onSuccess,l=t.onUpdate,d=t.onError,e.prev=2,!ye){e.next=6;break}return C.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 6:if(Ze(!0),f=(0,N.bW)(),x=s?s.id:ae(c.current),s||a({content:"出现了异常:",role:"assistant",id:x,references:[],collected:!1,query:[]}),m={conversation_id:c.current||"",message_id:x,meta_data:{},extra:{},role:s?s.role:"user",content:s?s.content:"",app_info:me,user_id:null==de?void 0:de.id,user_name:null==de?void 0:de.name,references:[],token_count:null,price:null,collected:!1,created_at:te()().format("YYYY-MM-DD HH:mm:ss")},He((function(e){var n=[].concat(u()(e),[m]);return console.log("更新后的消息列表:",n),n})),c.current){e.next=15;break}throw C.ZP.error("No active conversation selected"),new Error("No active conversation selected");case 15:return console.log("activeKey===>",c.current),g={conversation_id:c.current,app_info:me,user_id:null==de?void 0:de.id,user_name:null==de?void 0:de.name,extra:{},messages:r},h={id:ae(c.current),role:"assistant",content:"",references:[],collected:!1,query:[]},k=!1,b="",y=[],l(h),e.next=24,fetch("/api/app/chat/system_app_completions",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(f)},body:JSON.stringify(g)});case 24:if((Z=e.sent).ok){e.next=27;break}throw new Error("HTTP 错误！状态码：".concat(Z.status));case 27:if(j=null===(p=Z.body)||void 0===p?void 0:p.getReader()){e.next=30;break}throw new Error("当前浏览器不支持 ReadableStream。");case 30:w=new TextDecoder("utf-8"),_={conversation_id:c.current||"",message_id:h.id,meta_data:{},extra:{},role:"assistant",content:"",app_info:me,user_id:null==de?void 0:de.id,user_name:null==de?void 0:de.name,references:[],token_count:null,price:null,collected:!1,created_at:te()().format("YYYY-MM-DD HH:mm:ss")};case 32:if(k){e.next=103;break}return e.next=35,j.read();case 35:S=e.sent,P=S.value,S.done&&(k=!0),b+=w.decode(P,{stream:!0}),D=b.split("\n\n"),b=D.pop()||"",F=i()(D),e.prev=43,F.s();case 45:if((R=F.n()).done){e.next=93;break}if(""!==(z=R.value).trim()){e.next=49;break}return e.abrupt("continue",91);case 49:T=z.split("\n"),M=null,Y=null,E=i()(T);try{for(E.s();!(H=E.n()).done;)(I=H.value).startsWith("event: ")?M=I.substring(7).trim():I.startsWith("data: ")&&(Y=I.substring(6))}catch(e){E.e(e)}finally{E.f()}if(!Y){e.next=91;break}e.t0=M,e.next="answer"===e.t0?58:"moduleStatus"===e.t0?70:"appStreamResponse"===e.t0?72:"flowResponses"===e.t0?74:"end"===e.t0?76:"error"===e.t0?78:91;break;case 58:if("[DONE]"===Y){e.next=69;break}e.prev=59,L=JSON.parse(Y),(A=(null===(q=L.choices[0])||void 0===q||null===(q=q.delta)||void 0===q?void 0:q.content)||"")&&(h.content+=A,l(h)),e.next=69;break;case 65:return e.prev=65,e.t1=e.catch(59),console.error("Error parsing answer data:",e.t1),e.abrupt("return",a({content:"出现了异常:"+Y,role:"assistant",id:ae(c.current),references:[],query:[],collected:!1}));case 69:return e.abrupt("break",91);case 70:try{O=JSON.parse(Y),console.log("模块状态：",O)}catch(e){console.error("Error parsing moduleStatus data:",e)}return e.abrupt("break",91);case 72:try{B=JSON.parse(Y),console.log("appStreamData===>",B),y=(0,v.n)(B),h.references=y}catch(e){console.error("Error parsing appStreamResponse data:",e)}return e.abrupt("break",91);case 74:try{console.log("flowResponsesData",Y)}catch(e){console.error("Error parsing flowResponses data:",e)}return e.abrupt("break",91);case 76:return k=!0,e.abrupt("break",91);case 78:e.prev=78,k=!0,W=JSON.parse(Y),h.content=W.message,_.role="assistant",l(h),e.next=90;break;case 86:throw e.prev=86,e.t2=e.catch(78),console.error("Error event received:",e.t2),e.t2;case 90:return e.abrupt("break",91);case 91:e.next=45;break;case 93:e.next=98;break;case 95:e.prev=95,e.t3=e.catch(43),F.e(e.t3);case 98:return e.prev=98,F.f(),e.finish(98);case 101:e.next=32;break;case 103:if(console.info(h),a(h),!h.content||""===h.content.trim()){e.next=112;break}return _.content=h.content,_.references=y,e.next=110,(0,se.tn)(_);case 110:(J=e.sent).success?(_.message_id=J.data.message_id,console.log("创建消息成功，返回数据:",J.data),He((function(e){var n=[].concat(u()(e),[J.data]);return console.log("更新后的消息列表:",n),n}))):C.ZP.error("消息上报失败");case 112:e.next=119;break;case 114:e.prev=114,e.t4=e.catch(2),console.log("error===>",e.t4),a({content:"出现了，系统正在处理其他对话。请稍后重试",role:"assistant",id:ae(c.current),references:[],collected:!1,query:[]}),d(e.t4 instanceof Error?e.t4:new Error("Unknown error"));case 119:return e.prev=119,Ze(!1),e.finish(119);case 122:case"end":return e.stop()}}),e,null,[[2,114,119,122],[43,95,98,101],[59,65],[78,86]])}))),function(n,t){return e.apply(this,arguments)})}),$e=x()(Ue,1)[0],Qe=(0,h.Z)({agent:$e}),Ve=Qe.onRequest,en=Qe.messages,nn=Qe.setMessages,tn=function(e){ie(e),console.log("activeKey 设置",e),c.current=e},rn=function(e){var n=e.filter((function(e){return e.pinned})).sort((function(e,n){return new Date(n.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return s()(s()({},e),{},{key:e.id||"",label:e.conversation_name||e.id,group:"置顶"})})),t=e.filter((function(e){return!e.pinned})).sort((function(e,n){return new Date(n.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return s()(s()({},e),{},{key:e.id||"",label:e.conversation_name||"",group:"对话"})}));O([].concat(u()(n),u()(t)))},sn=function(e){return 0===e.length?null:e.reduce((function(e,n){return new Date(n.active_at)>new Date(e.active_at)?n:e}))},cn=function(){var e=p()(o()().mark((function e(n){var t,r,s;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,Ze(!0),console.info("获取对话信息",n),t=te()().format("YYYY-MM-DD HH:mm:ss"),e.next=6,(0,se.$o)(n,{conversation_name:null,active_at:t,pinned_at:null,pinned:null});case 6:null!=(r=e.sent)&&r.messages?(console.info("设置对话信息",r.messages),s=r.messages.map((function(e){return{id:e.id||e.message_id,message:{id:e.id||e.message_id,content:e.content,role:e.role,references:e.references||[],collected:e.collected||!1},status:"assistant"===e.role?"success":"local",meta:e.meta||{avatar:"assistant"===e.role?(null==pe?void 0:pe.logo)||"/static/logo.png":(null==de?void 0:de.avatar)||"/avatar/default.jpeg"}}})),He(r.messages),nn(s),tn(n)):C.ZP.error("获取对话信息失败"),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),console.error("切换对话时出错：",e.t0);case 13:return e.prev=13,Ze(!1),ie(n),e.finish(13);case 17:case"end":return e.stop()}}),e,null,[[0,10,13,17]])})));return function(n){return e.apply(this,arguments)}}(),on=function(){var e=p()(o()().mark((function e(){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(c.current){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,(0,se.Db)(c.current);case 4:e.sent.success?(He([]),nn([]),Ie.current&&Ie.current.updateReferenceList([])):C.ZP.error("清空对话失败");case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),an=function(){var e=p()(o()().mark((function e(){var n,t,r,s;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!ye){e.next=3;break}return C.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(!(n=(0,N.bG)())){e.next=19;break}return e.prev=5,t=(new Date).toLocaleString(),r="对话-".concat(t),e.next=10,(0,se.Xw)({user_id:n.id,user_name:n.name,conversation_name:r,app_info:me});case 10:s=e.sent,rn([].concat(u()(A),[{key:s.id||"",id:s.id||"",label:s.conversation_name||"",conversation_name:s.conversation_name||"",active_at:s.active_at||"",pinned_at:s.pinned_at,pinned:s.pinned||!1,messages:[]}])),tn(s.id||""),on(),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(5),console.error("创建新对话时出错：",e.t0);case 19:case"end":return e.stop()}}),e,null,[[5,16]])})));return function(){return e.apply(this,arguments)}}(),ln=function(){var e=p()(o()().mark((function e(n){var t,r,c,a,i;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=A.find((function(e){return e.key===n}))){e.next=3;break}return e.abrupt("return");case 3:return r=t.pinned,c=!r,e.prev=6,a=te()().format("YYYY-MM-DD HH:mm:ss"),e.next=10,(0,se.X1)(n,{conversation_name:null,active_at:null,pinned:c,pinned_at:a});case 10:i=A.map((function(e){return e.key===n?s()(s()({},e),{},{pinned:c}):e})),rn(i),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(6),console.error("更新置顶状态时出错：",e.t0);case 17:case"end":return e.stop()}}),e,null,[[6,14]])})));return function(n){return e.apply(this,arguments)}}(),un=function(){var e=p()(o()().mark((function e(n){var t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,se.SJ)(n);case 3:t=A.filter((function(e){return e.key!==n})),rn(t),c.current===n&&t.length>0&&cn(t[0].key||""),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除对话时出错：",e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(n){return e.apply(this,arguments)}}(),dn=function(){var e=p()(o()().mark((function e(n,t){var r,c;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,A.find((function(e){return e.key===n}))){e.next=4;break}return e.abrupt("return");case 4:return r={conversation_name:t,active_at:null,pinned_at:null,pinned:null},e.next=7,(0,se.X1)(n,r);case 7:null!=(c=e.sent)&&c.success?O((function(e){return e.map((function(e){return e.key===n?s()(s()({},e),{},{label:t}):e}))})):C.ZP.error("更新对话标题失败"),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("更新对话标题时出错：",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(n,t){return e.apply(this,arguments)}}(),pn=function(){var e=p()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!ye){e.next=3;break}return C.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:return e.next=5,cn(n);case 5:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}();(0,Y.useEffect)((function(){var e=function(){var e=p()(o()().mark((function e(){var n,t,r;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(n=(0,N.bG)())){e.next=30;break}return e.prev=2,Ze(!0),e.next=6,(0,se.Mw)({user_id:n.id,app_info:me});case 6:if(!(t=e.sent).success||!Array.isArray(t.data)){e.next=22;break}if(0!==t.data.length){e.next=13;break}return e.next=11,an();case 11:e.next=22;break;case 13:if(r=sn(t.data),rn(t.data),!r){e.next=20;break}return e.next=18,cn(r.id);case 18:e.next=22;break;case 20:return e.next=22,cn(t.data[0].id||"");case 22:e.next=27;break;case 24:e.prev=24,e.t0=e.catch(2),console.error("初始化对话时出错：",e.t0);case 27:return e.prev=27,Ze(!1),e.finish(27);case 30:case"end":return e.stop()}}),e,null,[[2,24,27,30]])})));return function(){return e.apply(this,arguments)}}();e()}),[me]);var fn=function(){var e=p()(o()().mark((function e(n){var t,r,s,c;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("重新生成消息:",n),e.prev=1,t=Ne.findIndex((function(e){return e.message_id===n})),console.log("currentIndex===>",t),-1!==t){e.next=7;break}return C.ZP.error("未找到指定消息"),e.abrupt("return");case 7:return r=Ne[t],s=Ne.slice(t),console.log("将要删除的消息:",s),e.next=12,(0,se.qP)(s.map((function(e){return e.message_id})));case 12:e.sent.success||C.ZP.error("删除消息失败"),He((function(e){return e.slice(0,t)})),nn((function(e){return e.slice(0,t)})),"assistant"===r.role?(c=Ne.slice(0,t).reverse().find((function(e){return"user"===e.role})))&&Ve({id:n,role:"user",content:c.content,references:[],query:[],collected:!1}):Ve({id:n,role:"user",content:r.content,references:[],query:[],collected:!1}),C.ZP.success("正在重新生成回复..."),e.next=24;break;case 20:e.prev=20,e.t0=e.catch(1),console.error("重新生成消息时出错：",e.t0),C.ZP.error("重新生成消息失败");case 24:case"end":return e.stop()}}),e,null,[[1,20]])})));return function(n){return e.apply(this,arguments)}}(),xn=function(){var e=p()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:P.Z.confirm({title:"确认删除",content:"确定要删除这条消息吗？删除后不可恢复。",okText:"确认",cancelText:"取消",onOk:function(){return p()(o()().mark((function e(){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("delete messageId===>",n),e.next=4,(0,se.$Z)(n);case 4:e.sent.success?(He((function(e){return e.filter((function(e){return e.message_id!==n}))})),console.log("delete currentConversationMessages===>",Ne),nn((function(e){return e.filter((function(e){return e.message.id!==n}))})),C.ZP.success("消息及相关引用已删除")):C.ZP.error("删除消息失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除消息时出错：",e.t0),C.ZP.error("删除消息失败");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})))()}});case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),mn=function(){var e=p()(o()().mark((function e(n,t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("收藏状态切换:",n,t),e.next=3,(0,se.bk)({message_id:n,collected:!t});case 3:e.sent.success?(C.ZP.success(t?"取消收藏成功":"收藏成功"),nn((function(e){return e.map((function(e){return e.id===n?s()(s()({},e),{},{message:s()(s()({},e.message),{},{collected:!t})}):e}))})),He((function(e){return e.map((function(e){return e.message_id===n?s()(s()({},e),{},{collected:!t}):e}))}))):C.ZP.error(t?"取消收藏失败":"收藏失败");case 5:case"end":return e.stop()}}),e)})));return function(n,t){return e.apply(this,arguments)}}(),vn=function(){var e=p()(o()().mark((function e(n){var t,r,s;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!ye){e.next=3;break}return C.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(t=n.data,r=t.key,s=t.description,"historyConversation"!==r){e.next=8;break}he(!0),e.next=19;break;case 8:if("newConversation"!==r){e.next=13;break}return e.next=11,an();case 11:e.next=19;break;case 13:if("clearConversation"!==r){e.next=18;break}return e.next=16,on();case 16:e.next=19;break;case 18:Ve({id:ae(c.current),role:"user",content:s,references:[],collected:!1,query:[]});case 19:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),gn=(0,oe.jsxs)(w.Z,{direction:"vertical",size:16,className:n.placeholder,children:[(0,oe.jsx)(k.Z,{variant:"borderless",icon:(0,oe.jsx)("img",{src:(null==pe?void 0:pe.logo)||"/static/logo.png",alt:"logo"}),title:"你好，我是声誉风险案例助手.",description:"基于智能知识库和监管规则，为您提供专业的金融合规咨询和指导服务"}),(0,oe.jsx)(b.Z,{title:"您想了解哪些合规问题？",items:le,styles:{list:{width:"100%"},item:{backgroundImage:"linear-gradient(137deg, #e5f4ff 0%, #efe7ff 100%)",border:0,flex:1}},onItemClick:vn})]}),hn=en.length>0?en.map((function(e){var n=e.id,t=e.message,r=e.status;return{key:c.current+"_"+n,loadingRender:function(){return(0,oe.jsxs)(w.Z,{children:[(0,oe.jsx)(D.Z,{size:"small"}),"模型思考中..."]})},loading:"loading"===r&&t.content.length<1,content:t.content,shape:"local"===r?"corner":"round",variant:"local"===r?"filled":"borderless",rols:t.role,messageRender:ge,avatar:"local"===r?{src:(null==de?void 0:de.avatar)||"/avatar/default.jpeg"}:{src:(null==pe?void 0:pe.logo)||"/static/logo.png"},placement:"local"!==r?"start":"end",footer:"local"!==r?(0,oe.jsxs)(F.Z,{children:[t.references.length>0&&(0,oe.jsxs)(R.Z,{bordered:!1,color:"blue",onClick:function(){return e=t.id,console.log("filterMessageReference===>",e),void(Ie.current&&(Ie.current.getFilterMessageId()===e?Ie.current.clearFilter():Ie.current.filterByMessageId(e)));var e},children:["引用:",t.references.length]}),(0,oe.jsx)(S.ZP,{size:"small",type:"text",icon:(0,oe.jsx)(W.Z,{style:{color:"#ccc"}}),onClick:function(){return xn(t.id)}}),(0,oe.jsx)(S.ZP,{size:"small",type:"text",icon:t.collected?(0,oe.jsx)(U.Z,{style:{color:"#FFD700"}}):(0,oe.jsx)($.Z,{style:{color:"#ccc"}}),onClick:function(){return mn(t.id,t.collected)}}),(0,oe.jsx)(S.ZP,{size:"small",type:"text",icon:(0,oe.jsx)(Q.Z,{style:{color:"#ccc"}}),onClick:function(){return Ke(t.id)}}),(0,oe.jsx)(S.ZP,{size:"small",type:"text",icon:(0,oe.jsx)(V.Z,{style:{color:"#ccc"}}),onClick:function(){return Xe(t.id)}})]}):(0,oe.jsxs)(F.Z,{children:[(0,oe.jsx)(S.ZP,{size:"small",type:"text",icon:(0,oe.jsx)(ee.Z,{style:{color:"#ccc"}}),onClick:function(){return fn(t.id)}}),(0,oe.jsx)(S.ZP,{size:"small",type:"text",icon:(0,oe.jsx)(W.Z,{style:{color:"#ccc"}}),onClick:function(){return xn(t.id)}}),(0,oe.jsx)(S.ZP,{size:"small",type:"text",icon:t.collected?(0,oe.jsx)(U.Z,{style:{color:"#FFD700"}}):(0,oe.jsx)($.Z,{style:{color:"#ccc"}}),onClick:function(){return mn(t.id,t.collected)}}),(0,oe.jsx)(S.ZP,{size:"small",type:"text",icon:(0,oe.jsx)(Q.Z,{style:{color:"#ccc"}}),onClick:function(){return Ke(t.id)}}),(0,oe.jsx)(S.ZP,{size:"small",type:"text",icon:(0,oe.jsx)(V.Z,{style:{color:"#ccc"}}),onClick:function(){return Xe(t.id)}})]})}})):[{content:gn,variant:"borderless"}],kn=(0,oe.jsx)(y.Z.Header,{title:"Attachments",open:d,onOpenChange:f,styles:{content:{padding:0}}}),bn=(0,oe.jsxs)("div",{className:n.logo,children:[(0,oe.jsx)("span",{children:"对话记录"}),(0,oe.jsx)(z.Z,{title:"新对话",children:(0,oe.jsx)(S.ZP,{type:"text",icon:(0,oe.jsx)(B.Z,{}),onClick:an,style:{fontSize:"16px"}})})]}),yn=(0,oe.jsx)(P.Z,{title:"修改对话标题",open:_e,onOk:function(){Te&&De.trim()&&(dn(Te,De.trim()),Se(!1))},onCancel:function(){Se(!1),Fe(""),Me("")},children:(0,oe.jsx)(T.Z,{value:De,onChange:function(e){return Fe(e.target.value)},placeholder:"请输入新的对话标题"})}),Zn=(0,oe.jsx)(M.Z,{title:"历史对话",placement:"right",width:400,onClose:function(){return he(!1)},open:ve,children:(0,oe.jsxs)("div",{className:n.menu,children:[bn,(0,oe.jsx)(Z.Z,{items:A,activeKey:ne,onActiveChange:pn,menu:function(e){return{items:[{label:"重命名",key:"edit",icon:(0,oe.jsx)(K.Z,{})},{label:"置顶",key:"pin",icon:(0,oe.jsx)(X.Z,{})},{label:"删除",key:"delete",icon:(0,oe.jsx)(W.Z,{}),danger:!0}],onClick:function(n){switch(console.log("menuInfo","Click ".concat(e.key," - ").concat(n.key)),n.key){case"edit":Me(e.key),Fe(e.label),Se(!0);break;case"pin":ln(e.key);break;case"delete":if(ye)return void C.ZP.error("系统正在处理其他对话。请稍😊");un(e.key)}}}},groupable:!0})]})});return(0,Y.useEffect)((function(){console.log("currentConversationMessages 更新了:",Ne)}),[Ne]),(0,oe.jsxs)("div",{className:n.layout,style:{height:r-56},children:[(0,oe.jsxs)("div",{className:n.chat,children:[(0,oe.jsx)(j.Z.List,{items:hn,className:n.messages}),(0,oe.jsx)(b.Z,{items:ue,onItemClick:vn}),(0,oe.jsx)(y.Z,{value:H,header:kn,onSubmit:function(e){console.log("nextContent===>",e),e&&(Ve({id:ae(c.current),role:"user",content:e,references:[],collected:!1,query:[]}),I(""))},onChange:I,loading:$e.isRequesting(),className:n.sender})]}),(0,oe.jsx)("div",{className:n.reference,children:(0,oe.jsx)(m.Z,{ref:Ie,messages:Ne})}),yn,Zn,(0,oe.jsx)(ce.Z,{visible:Ae,messageId:Je,conversationId:ne,appInfo:me,onClose:function(){return Oe(!1)}})]})}}}]);