"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7948],{49185:function(e,n,t){t.d(n,{D9:function(){return l},IV:function(){return p},eV:function(){return x},ws:function(){return u}});var r=t(15009),a=t.n(r),s=t(99289),i=t.n(s),o=t(78158);function l(e){return c.apply(this,arguments)}function c(){return(c=i()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/knowledge_base",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function u(e){return d.apply(this,arguments)}function d(){return(d=i()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/my_knowledge_bases",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function p(e){return f.apply(this,arguments)}function f(){return(f=i()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/knowledge_bases/".concat(n),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function x(e){return h.apply(this,arguments)}function h(){return(h=i()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/knowledge_bases/".concat(n),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},14921:function(e,n,t){t.r(n),t.d(n,{default:function(){return Ue}});var r=t(97857),a=t.n(r),s=t(15009),i=t.n(s),o=t(99289),l=t.n(o),c=t(5574),u=t.n(c),d=t(97131),p=t(88372),f=t(26412),x=t(4393),h=t(67294),m=t(34994),g=t(5966),v=t(90672),y=t(64317),j=t(52688),b=t(71471),k=t(2453),w=t(96074),Z=t(2487),C=t(24444),S=(0,C.kc)((function(e){var n=e.token;return{baseView:{maxWidth:"1000px",margin:"0 auto",backgroundColor:n.colorBgContainer},section:{"> h4":{marginBottom:"24px"}},formSection:{padding:"24px 0",".ant-form-item:last-child":{marginBottom:0}},settingsList:{".ant-list-item":{padding:"16px 0",borderBottom:"1px solid ".concat(n.colorBorderSecondary),"&:last-child":{borderBottom:"none"}},".ant-list-item-meta":{marginBottom:0}},settingTitle:{fontSize:"16px",fontWeight:500,color:n.colorTextHeading},settingDesc:{color:n.colorTextSecondary}}})),P=t(58258),I=t(85893),_=b.Z.Title,T=function(e){var n=S().styles,t=(0,h.useState)([]),r=u()(t,2),s=r[0],o=r[1];console.log(e),(0,h.useEffect)((function(){var e=function(){var e=l()(i()().mark((function e(){var n,t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,P.DY)();case 3:(n=e.sent).success&&(t=n.data.map((function(e){return{label:e.name,value:e.embedding_name,id:e.id}})),o(t.map((function(e){return a()(a()({},e),{},{id:Number(e.id)})})))),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),k.ZP.error("获取向量模型列表失败");case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);var c=function(){var n=l()(i()().mark((function n(t){var r,o,l;return i()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,r=s.find((function(e){return e.value===t.embedding_model})),o=a()(a()({},t),{},{embedding_model_id:null==r?void 0:r.id.toString()}),n.next=5,(0,P.CH)(e.id,o);case 5:(l=n.sent).success?k.ZP.success("更新知识库信息成功"):k.ZP.error(l.message||"更新知识库信息失败"),n.next=13;break;case 9:n.prev=9,n.t0=n.catch(0),console.error("更新知识库出错:",n.t0),k.ZP.error("更新知识库信息失败");case 13:case"end":return n.stop()}}),n,null,[[0,9]])})));return function(e){return n.apply(this,arguments)}}();return(0,I.jsx)("div",{className:n.baseView,children:(0,I.jsxs)("div",{className:n.section,children:[(0,I.jsx)(_,{level:4,children:"知识库基础信息"}),(0,I.jsxs)(m.A,{layout:"vertical",onFinish:c,submitter:{searchConfig:{submitText:"更新知识库信息"},render:function(e,n){return n[1]}},initialValues:{name:e.name,description:e.description,embedding_model:e.embedding_model,embedding_model_id:e.embedding_model_id,reRank_model:e.reRank_model,reRank_model_id:e.reRank_model_id,basic_index:e.basic_index,graph_index:e.graph_index,semantic_index:e.semantic_index},hideRequiredMark:!0,children:[(0,I.jsxs)("div",{className:n.formSection,children:[(0,I.jsx)(g.Z,{width:"xl",name:"name",label:"知识库名称",rules:[{required:!0,message:"请输入知识库名称!"}]}),(0,I.jsx)(v.Z,{width:"xl",name:"description",label:"知识库描述",rules:[{required:!0,message:"请输入知识库描述!"}],placeholder:"请输入知识库描述"})]}),(0,I.jsx)(w.Z,{}),(0,I.jsxs)("div",{className:n.formSection,children:[(0,I.jsx)(_,{level:4,children:"基础模型"}),(0,I.jsx)(y.Z,{width:"xl",name:"embedding_model",label:"选择向量模型，知识库构建时会使用，构建后会自动使用，但是不建议修改。",options:s,rules:[{required:!0,message:"请选择向量模型!"}],tooltip:"用于文本向量化的预训练模型",placeholder:"请选择向量模型",fieldProps:{loading:0===s.length,disabled:!0},readonly:!0,disabled:!0})]}),(0,I.jsx)(w.Z,{}),(0,I.jsxs)("div",{className:n.formSection,children:[(0,I.jsx)(_,{level:4,children:"索引设置"}),(0,I.jsx)(Z.Z,{className:n.settingsList,itemLayout:"horizontal",dataSource:[{title:"基础索引",description:"构建基础的文本检索索引，支持关键词匹配和语义相似度检索",fieldName:"basic_index"},{title:"Graph索引",description:"构建知识图谱索引，支持实体关系分析和知识推理",fieldName:"graph_index"},{title:"语义索引",description:"构建语义索引，支持语义相似度检索",fieldName:"semantic_index"}],renderItem:function(e){return(0,I.jsx)(Z.Z.Item,{actions:[(0,I.jsx)(j.Z,{name:e.fieldName,noStyle:!0},e.fieldName)],children:(0,I.jsx)(Z.Z.Item.Meta,{title:(0,I.jsx)("span",{className:n.settingTitle,children:e.title}),description:(0,I.jsx)("span",{className:n.settingDesc,children:e.description})})})}})]})]})]})})},R=t(55102),B=function(e){var n=S().styles,t=e.value,r=e.onChange,a=["",""];return t&&(a=t.split("-")),(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)(R.Z,{className:n.area_code,value:a[0],onChange:function(e){r&&r("".concat(e.target.value,"-").concat(a[1]))}}),(0,I.jsx)(R.Z,{className:n.phone_number,onChange:function(e){r&&r("".concat(a[0],"-").concat(e.target.value))},value:a[1]})]})},M=t(72269),N=function(){var e,n=[{title:"账户密码",description:"其他用户的消息将以站内信的形式通知",actions:[e=(0,I.jsx)(M.Z,{checkedChildren:"开",unCheckedChildren:"关",defaultChecked:!0})]},{title:"系统消息",description:"系统消息将以站内信的形式通知",actions:[e]},{title:"待办任务",description:"待办任务将以站内信的形式通知",actions:[e]}];return(0,I.jsx)(h.Fragment,{children:(0,I.jsx)(Z.Z,{itemLayout:"horizontal",dataSource:n,renderItem:function(e){return(0,I.jsx)(Z.Z.Item,{actions:e.actions,children:(0,I.jsx)(Z.Z.Item.Meta,{title:e.title,description:e.description})})}})})},E={strong:(0,I.jsx)("span",{className:"strong",children:"强"}),medium:(0,I.jsx)("span",{className:"medium",children:"中"}),weak:(0,I.jsx)("span",{className:"weak",children:"弱 Weak"})},L=function(){var e=[{title:"账户密码",description:(0,I.jsxs)(I.Fragment,{children:["当前密码强度：",E.strong]}),actions:[(0,I.jsx)("a",{children:"修改"},"Modify")]},{title:"密保手机",description:"已绑定手机：138****8293",actions:[(0,I.jsx)("a",{children:"修改"},"Modify")]},{title:"密保问题",description:"未设置密保问题，密保问题可有效保护账户安全",actions:[(0,I.jsx)("a",{children:"设置"},"Set")]},{title:"备用邮箱",description:"已绑定邮箱：ant***sign.com",actions:[(0,I.jsx)("a",{children:"修改"},"Modify")]},{title:"MFA 设备",description:"未绑定 MFA 设备，绑定后，可以进行二次确认",actions:[(0,I.jsx)("a",{children:"绑定"},"bind")]}];return(0,I.jsx)(I.Fragment,{children:(0,I.jsx)(Z.Z,{itemLayout:"horizontal",dataSource:e,renderItem:function(e){return(0,I.jsx)(Z.Z.Item,{actions:e.actions,children:(0,I.jsx)(Z.Z.Item.Meta,{title:e.title,description:e.description})})}})})},F=t(19632),D=t.n(F),O=t(9783),z=t.n(O),A=t(38703),V=t(13457),q=t(17788),W=t(11550),H=t(33914),G=t(51042),K=t(34041),J=t(83622),X=W.Z.Dragger,U=K.default.Option,Y=[{id:"default",name:"默认解析器"},{id:"annual",name:"企业年报解析器"},{id:"law",name:"法律法规解析器"},{id:"manual",name:"使用手册解析器"},{id:"Personalization",name:"自定义解析器"}],$=[{value:"semantic",label:"语义切片"},{value:"fixed",label:"固定长度切片"},{value:"sliding",label:"滑动窗口切片"},{value:"token",label:"Token切片"}],Q=function(e){var n=e.knowledgeId,t=(0,h.useState)([]),r=u()(t,2),s=r[0],o=r[1],c=(0,h.useState)([]),d=u()(c,2),p=d[0],f=d[1],x=(0,h.useState)({}),g=u()(x,2),v=g[0],y=g[1],j=(0,h.useState)(!1),b=u()(j,2),w=b[0],Z=b[1],C=(0,h.useState)(0),S=u()(C,2),_=S[0],T=S[1],B=(0,h.useState)([]),N=u()(B,2),E=N[0],L=N[1],F=(0,h.useState)(0),O=u()(F,2),Q=O[0],ee=O[1],ne=(0,h.useState)(0),te=u()(ne,2),re=te[0],ae=te[1],se=(0,h.useState)([]),ie=u()(se,2),oe=ie[0],le=ie[1],ce=n,ue=(0,h.useState)(""),de=u()(ue,2),pe=de[0],fe=de[1],xe=(0,h.useState)("default"),he=u()(xe,2),me=he[0],ge=he[1],ve=(0,h.useState)(!1),ye=u()(ve,2),je=ye[0],be=ye[1],ke=(0,h.useState)(!1),we=u()(ke,2),Ze=we[0],Ce=we[1],Se=(0,h.useState)(""),Pe=u()(Se,2),Ie=Pe[0],_e=Pe[1],Te=(0,h.useState)("semantic"),Re=u()(Te,2),Be=Re[0],Me=Re[1],Ne=(0,h.useState)(500),Ee=u()(Ne,2),Le=Ee[0],Fe=Ee[1],De=(0,h.useState)(50),Oe=u()(De,2),ze=Oe[0],Ae=Oe[1],Ve={name:"file",multiple:!0,progress:{strokeColor:{"0%":"#108ee9","100%":"#87d068"},size:3,showInfo:!0},maxCount:50,fileList:s,beforeUpload:function(e){return[".pdf",".docx",".csv",".xls",".xlsx",".json",".pptx",".html",".md",".jpg",".png",".wav",".mp3"].some((function(n){return e.name.toLowerCase().endsWith(n)}))?(y((function(n){return a()(a()({},n),{},z()({},e.name,0))})),!1):(k.ZP.error("只支持 PDF、CSV、Excel、JSON、DOCX、PPTX、HTML、Markdown、JPG、PNG、WAV、MP3 格式的文件"),W.Z.LIST_IGNORE)},onChange:function(e){o(e.fileList);var n=e.file.status;"uploading"===n?e.file.percent&&y((function(n){return a()(a()({},n),{},z()({},e.file.name,e.file.percent||0))})):"done"===n?y((function(n){return a()(a()({},n),{},z()({},e.file.name,100))})):"error"===n&&k.ZP.error("".concat(e.file.name," 上传失败")),e.fileList.length>50&&k.ZP.warning("最多只能上传50个文件"),console.log(e.file,e.fileList)},onDrop:function(e){console.log("Dropped files",e.dataTransfer.files)},itemRender:function(e,n){return(0,I.jsxs)("div",{style:{marginBottom:8},children:[e,v[n.name]>0&&v[n.name]<100&&(0,I.jsx)(A.Z,{percent:Math.round(v[n.name]),size:"small",status:"error"===n.status?"exception":"active",style:{marginTop:4}})]})},showUploadList:{showDownloadIcon:!1,showRemoveIcon:!0}},qe=function(){var e=l()(i()().mark((function e(){var n,t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,P.E)(ce,"",[],!0);case 3:n=e.sent,console.log("🚀 ~ fetchCategories ~ response:",n),t=n.data||[],r=t.filter((function(e){return"folder"===e.data_type})),f(r),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(0),k.ZP.error("获取分类失败"),console.error("获取分类失败:",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,10]])})));return function(){return e.apply(this,arguments)}}();(0,h.useEffect)((function(){qe()}),[]);var We=function(){var e=l()(i()().mark((function e(t,r,s,l,c,u,d){var p,f;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,y((function(e){return a()(a()({},e),{},z()({},t.name,10))})),p=setInterval((function(){y((function(e){return e[t.name]&&e[t.name]<90?a()(a()({},e),{},z()({},t.name,Math.min(e[t.name]+Math.floor(15*Math.random())+1,90))):e}))}),500),e.prev=4,e.next=7,(0,P.bV)(n,r,[t],s,l,"",c,u,d);case 7:if(f=e.sent,clearInterval(p),!f.detail){e.next=11;break}throw new Error(f.detail);case 11:return y((function(e){return a()(a()({},e),{},z()({},t.name,100))})),o((function(e){return e.filter((function(e){return!(e.originFileObj&&e.originFileObj.name===t.name)}))})),ee((function(e){return e+1})),e.abrupt("return",!0);case 17:return e.prev=17,e.t0=e.catch(4),clearInterval(p),k.ZP.error("".concat(t.name," 上传失败: ").concat((null===e.t0||void 0===e.t0?void 0:e.t0.message)||"未知错误")),ae((function(e){return e+1})),le((function(n){return[].concat(D()(n),[{file:t,reason:(null===e.t0||void 0===e.t0?void 0:e.t0.message)||"未知错误"}])})),e.abrupt("return",!1);case 24:return e.prev=24,T((function(e){return e-1})),e.finish(24);case 27:e.next=33;break;case 29:return e.prev=29,e.t1=e.catch(0),console.error("Upload error:",e.t1),e.abrupt("return",!1);case 33:case"end":return e.stop()}}),e,null,[[0,29],[4,17,24,27]])})));return function(n,t,r,a,s,i,o){return e.apply(this,arguments)}}();(0,h.useEffect)((function(){if(E.length>0&&_<2&&w){var e=E[0],n=E.slice(1);L(n),T((function(e){return e+1})),We(e,je?1:0,pe,me,Be,Le,ze)}0===E.length&&0===_&&w&&(Z(!1),0===re&&Q>0?k.ZP.success("所有文件上传成功"):re>0&&k.ZP.warning("上传完成，成功: ".concat(Q," 失败: ").concat(re)),fe(""),ge("default"),be(!1),Me("semantic"),Fe(500),Ae(50),y({}),ee(0),ae(0))}),[E,_,w,je,pe,me,Q,re]);var He=function(){var e=l()(i()().mark((function e(){var n,t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,0!==s.length){e.next=4;break}return k.ZP.error("请选择要上传的文件"),e.abrupt("return");case 4:console.log("🚀 ~ onFinish ~ fileList:",s),n=s.map((function(e){return e.originFileObj})),t={},n.forEach((function(e){e&&e.name&&(t[e.name]=0)})),y(t),console.log("开始上传文件，总数:",n.length),Z(!0),T(0),ee(0),ae(0),le([]),L(n),e.next=23;break;case 18:e.prev=18,e.t0=e.catch(0),Z(!1),k.ZP.error((null===e.t0||void 0===e.t0?void 0:e.t0.message)||"创建数据集失败"),console.error("创建数据集错误:",e.t0);case 23:case"end":return e.stop()}}),e,null,[[0,18]])})));return function(){return e.apply(this,arguments)}}(),Ge=function(){Ce(!1),_e("")},Ke=function(){var e=l()(i()().mark((function e(){var t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,"add",e.next=4,(0,P.l$)("add",Ie,n,"","");case 4:(t=e.sent).detail?k.ZP.error(t.detail):(k.ZP.success("分类创建成功"),qe(),Ge()),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("创建分类失败:",e.t0),k.ZP.error("创建分类失败，请重试");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(){return e.apply(this,arguments)}}();return(0,I.jsxs)("div",{children:[(0,I.jsxs)(m.A,{style:{margin:"auto",marginTop:8,marginBottom:24,maxWidth:600,paddingBottom:24},name:"basic",layout:"vertical",initialValues:{name:"新建数据集",description:"这是一个新的数据集，用于..."},onFinish:He,submitter:{searchConfig:{submitText:"上传"},submitButtonProps:{loading:w,disabled:w||0===s.length}},children:[(0,I.jsxs)(X,a()(a()({},Ve),{},{style:{margin:"auto",marginTop:8,marginBottom:24,maxWidth:600,paddingBottom:24},children:[(0,I.jsx)("p",{className:"ant-upload-drag-icon",children:(0,I.jsx)(H.Z,{})}),(0,I.jsx)("p",{className:"ant-upload-text",children:"点击或拖拽文件到此区域上传"}),(0,I.jsx)("p",{className:"ant-upload-hint",children:"支持单个或批量上传。严格禁止上传公司数据或其他禁止的文件。支持数据格式：PDF、CSV、Excel、JSON、DOCX、PPTX、HTML、Markdown、JPG、PNG、WAV、MP3，最多上传50个文件。"})]})),(0,I.jsxs)(m.A.Group,{style:{display:"flex",gap:32},labelCol:{span:8},wrapperCol:{span:16},children:[(0,I.jsx)(m.A.Item,{label:"分类",children:(0,I.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,I.jsx)(K.default,{defaultValue:"",placeholder:"请选择分类",value:pe,style:{width:200,marginRight:8},onChange:function(e){console.log("选择的分类: ".concat(e)),fe(e)},children:p.map((function(e){return(0,I.jsx)(U,{value:e.id,children:e.name},e.id)}))}),(0,I.jsx)(J.ZP,{type:"primary",icon:(0,I.jsx)(G.Z,{}),onClick:function(){Ce(!0)}})]})}),(0,I.jsx)(m.A.Item,{label:"解析器类型",children:(0,I.jsx)(K.default,{value:me,defaultValue:"default",style:{width:150},onChange:function(e){console.log("选择的解析器类型: ".concat(e)),ge(e)},children:Y.map((function(e){return(0,I.jsx)(U,{value:e.id,children:e.name},e.id)}))})}),(0,I.jsx)(m.A.Item,{label:"启用视觉识别",children:(0,I.jsx)(M.Z,{checked:je,onChange:function(e){console.log("OCR 开关: ".concat(e)),be(e)}})})]}),"Personalization"===me&&(0,I.jsxs)(m.A.Group,{style:{display:"flex",gap:32},labelCol:{span:8},wrapperCol:{span:16},children:[(0,I.jsx)(m.A.Item,{label:"切片方式",children:(0,I.jsx)(K.default,{value:Be,style:{width:150},onChange:function(e){console.log("选择的切片方式: ".concat(e)),Me(e)},children:$.map((function(e){return(0,I.jsx)(U,{value:e.value,children:e.label},e.value)}))})}),(0,I.jsx)(m.A.Item,{label:"切块大小",children:(0,I.jsx)(V.Z,{value:Le,onChange:function(e){var n=e||1;console.log("切块大小: ".concat(n)),Fe(n)},placeholder:"字符数或token数",style:{width:150},min:1,max:9999,precision:0})}),(0,I.jsx)(m.A.Item,{label:"重叠大小",children:(0,I.jsx)(V.Z,{value:ze,onChange:function(e){var n=e||0;console.log("重叠大小: ".concat(n)),Ae(n)},placeholder:"仅滑动窗口使用",style:{width:150},min:1,max:9999,precision:0})})]}),(0,I.jsxs)(q.Z,{title:"创建分类",visible:Ze,onCancel:Ge,footer:null,style:{marginTop:80},width:400,children:[(0,I.jsx)(R.Z,{value:Ie,placeholder:"请输入分类名称",onChange:function(e){return _e(e.target.value)}}),(0,I.jsx)(J.ZP,{type:"primary",onClick:Ke,style:{marginTop:"10px"},children:"确定"})]})]}),oe.length>0&&(0,I.jsxs)("div",{style:{marginTop:24},children:[(0,I.jsx)("b",{children:"上传失败的文件："}),(0,I.jsx)("ul",{children:oe.map((function(e){return(0,I.jsxs)("li",{style:{marginBottom:8},children:[(0,I.jsx)("span",{children:e.file.name}),(0,I.jsx)("span",{style:{color:"red",marginLeft:8},children:e.reason}),(0,I.jsx)(J.ZP,{size:"small",style:{marginLeft:16},onClick:function(){return n=e.file,L([n]),Z(!0),void le((function(e){return e.filter((function(e){return e.file.name!==n.name}))}));var n},disabled:w,children:"单独重新上传"})]},e.file.name)}))}),(0,I.jsx)(J.ZP,{type:"primary",onClick:function(){L(oe.map((function(e){return e.file}))),le([]),Z(!0)},disabled:w,children:"重新上传全部失败文件"})]})]})},ee=t(50136),ne=t(85418),te=t(96974),re=(W.Z.Dragger,function(e){e.knowledgeId;var n=h.useState([]),t=u()(n,2),r=(t[0],t[1],(0,h.useState)(null)),a=u()(r,2),s=(a[0],a[1]),o=(0,h.useState)(!1),c=u()(o,2),d=c[0],p=c[1],f=(0,h.useState)(!1),x=u()(f,2),m=x[0],g=x[1],v=(0,h.useState)(null),y=u()(v,2),j=y[0],b=y[1],w=(0,h.useState)(""),Z=u()(w,2),C=Z[0],S=Z[1],_=(0,h.useState)([]),T=u()(_,2),B=T[0],M=T[1],N=(0,h.useState)(!1),E=u()(N,2),L=E[0],F=E[1],O=(0,h.useState)(""),z=u()(O,2),A=z[0],V=z[1],W=(0,h.useState)(""),H=u()(W,2),G=H[0],K=H[1],J=(0,te.TH)(),X=new URLSearchParams(J.search).get("id");console.log("🚀 ~ FileOperations ~ id:",X),(0,h.useEffect)((function(){X&&K(X)}),[X]),(0,h.useEffect)((function(){var e=function(){var e=l()(i()().mark((function e(){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(G){e.next=3;break}return console.log("knowledgeIdParams 为空，跳过接口调用"),e.abrupt("return");case 3:return e.prev=3,console.log("🚀 ~ fetchTags ~ knowledgeIdParams:",G),e.next=7,(0,P.Bp)(G);case 7:n=e.sent,console.log("🚀 ~ fetchTags ~ response:",n),n&&Array.isArray(n.data.tags)?M(n.data.tags):(console.warn("未找到标签数据，使用空数组"),M([])),e.next=16;break;case 12:e.prev=12,e.t0=e.catch(3),console.error("获取标签时出错:",e.t0),M([]);case 16:case"end":return e.stop()}}),e,null,[[3,12]])})));return function(){return e.apply(this,arguments)}}();e()}),[G]);var U=function(){var e=l()(i()().mark((function e(){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(""!==C.trim()){e.next=3;break}return k.ZP.error("标签名不能为空"),e.abrupt("return");case 3:return n=B.map((function(e){return e===j?C:e})),e.next=6,(0,P.IV)(G,n);case 6:M(n),p(!1),k.ZP.success("标签重命名成功");case 9:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Y=function(){var e=l()(i()().mark((function e(){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=B.filter((function(e){return e!==j})),e.next=3,(0,P.IV)(G,n);case 3:M(n),g(!1),k.ZP.success("标签删除成功");case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),$=function(){p(!1),g(!1)},Q=function(){var e=l()(i()().mark((function e(){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!A){e.next=7;break}return n=[].concat(D()(B),[A]),e.next=4,(0,P.IV)(G,n);case 4:M(n),V(""),F(!1);case 7:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),re=function(e){return(0,I.jsxs)(ee.Z,{children:[(0,I.jsx)(ee.Z.Item,{onClick:function(){return function(e){b(e),S(e),p(!0),s(null)}(e)},children:"重命名"},"rename"),(0,I.jsx)(ee.Z.Item,{onClick:function(){return function(e){b(e),g(!0),s(null)}(e)},children:"删除"},"delete")]})};return(0,I.jsxs)("div",{style:{padding:"16px",borderRadius:"8px"},children:[(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[(0,I.jsx)("h2",{style:{fontSize:"24px",fontWeight:"bold",color:"#333"},children:"标签管理"}),(0,I.jsx)("button",{style:{padding:"8px 12px",border:"none",borderRadius:"4px",backgroundColor:"#108ee9",color:"#fff",cursor:"pointer"},onClick:function(){return F(!0)},children:"创建标签"})]}),(0,I.jsx)("div",{style:{display:"flex",flexWrap:"wrap"},children:B.map((function(e,n){return(0,I.jsx)("div",{style:{position:"relative",display:"inline-block",marginRight:"16px",padding:"8px 12px",border:"1px solid #ccc",borderRadius:"4px",backgroundColor:"#fff",cursor:"pointer"},children:(0,I.jsx)(ne.Z,{overlay:re(e),trigger:["click"],children:(0,I.jsx)("span",{children:e})})},n)}))}),(0,I.jsx)(q.Z,{title:"重命名标签",visible:d,onOk:U,onCancel:$,children:(0,I.jsx)(R.Z,{value:C,onChange:function(e){return S(e.target.value)}})}),(0,I.jsx)(q.Z,{title:"确认删除",visible:m,onOk:Y,onCancel:$,children:(0,I.jsxs)("p",{children:['您确定要删除标签 "',j,'" 吗？']})}),(0,I.jsx)(q.Z,{title:"创建标签",visible:L,onOk:Q,onCancel:function(){return F(!1)},children:(0,I.jsx)(R.Z,{value:A,onChange:function(e){return V(e.target.value)},placeholder:"请输入标签名称"})})]})}),ae=t(42075),se=(W.Z.Dragger,function(e){e.knowledgeId;var n=(0,h.useState)(!1),t=u()(n,2),r=(t[0],t[1]);return(0,I.jsxs)("div",{style:{padding:"16px",borderRadius:"8px"},children:[(0,I.jsx)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:(0,I.jsx)("h2",{style:{fontSize:"24px",fontWeight:"bold",color:"#333"},children:"权限控制"})}),(0,I.jsxs)("div",{children:[(0,I.jsx)(Z.Z,{itemLayout:"horizontal",dataSource:[{title:"权限控制",description:"开启后将允许其他用户访问此知识库",fieldName:"permission"}],renderItem:function(e){return(0,I.jsx)(Z.Z.Item,{actions:[(0,I.jsx)(j.Z,{name:e.fieldName,noStyle:!0},e.fieldName)],children:(0,I.jsx)(Z.Z.Item.Meta,{title:(0,I.jsx)("span",{children:e.title}),description:(0,I.jsx)("span",{children:e.description})})})}}),(0,I.jsxs)("div",{style:{marginTop:"16px"},children:[(0,I.jsx)("div",{style:{marginBottom:"8px"},children:(0,I.jsxs)(ae.Z,{children:[(0,I.jsx)("span",{children:"选择用户"}),(0,I.jsx)(G.Z,{onClick:function(){return r(!0)},style:{cursor:"pointer"}})]})}),(0,I.jsx)(y.Z,{name:"knowledge_bases",rules:[{required:!0,message:"请选择至少一个用户"}],width:"xl",mode:"multiple",options:[]})]}),(0,I.jsxs)("div",{style:{marginTop:"16px"},children:[(0,I.jsx)("div",{style:{marginBottom:"8px"},children:(0,I.jsxs)(ae.Z,{children:[(0,I.jsx)("span",{children:"选择组"}),(0,I.jsx)(G.Z,{onClick:function(){return r(!0)},style:{cursor:"pointer"}})]})}),(0,I.jsx)(y.Z,{name:"knowledge_bases",rules:[{required:!0,message:"请选择至少一个组"}],width:"xl",mode:"multiple",options:[]})]})]})]})}),ie=t(84567),oe=t(86125),le=t(74330),ce=t(40411),ue=t(66309),de=t(32983),pe=t(11941),fe=t(43471),xe=t(40110),he=t(13923),me=t(15360),ge=t(85175),ve=t(55287),ye=(t(93162),b.Z.Text),je=b.Z.Title,be=b.Z.Paragraph,ke=function(e){return e>=.8?"green":e>=.6?"cyan":e>=.4?"blue":e>=.2?"orange":"red"},we=function(e){var n;return null!==(n=e.rerank_score)&&void 0!==n?n:e.score},Ze=function(){var e,n,t,r,s=(0,h.useState)(!1),o=u()(s,2),c=o[0],d=o[1],p=(0,h.useState)("1"),f=u()(p,2),m=f[0],g=f[1],v=(0,h.useState)(""),y=u()(v,2),j=y[0],b=y[1],C=(0,h.useState)([]),S=u()(C,2),_=S[0],T=S[1],B=(0,h.useState)(!1),M=u()(B,2),N=M[0],E=M[1],L=(0,h.useState)([]),F=u()(L,2),O=F[0],z=F[1],A=(0,h.useState)("desc"),V=u()(A,2),W=(V[0],V[1],(0,h.useState)({searchModes:["semantic"],reRankEnabled:!1,maxTokens:1e3,minRelevance:.3})),H=u()(W,2),G=H[0],K=H[1],X=(0,h.useState)(1e3),U=u()(X,2),Y=U[0],$=U[1],Q=(0,h.useState)(.3),ee=u()(Q,2),ne=ee[0],re=ee[1],se=(0,h.useState)(!1),Ze=u()(se,2),Ce=Ze[0],Se=Ze[1],Pe=(0,h.useState)(null),Ie=u()(Pe,2),_e=Ie[0],Te=Ie[1],Re=(0,h.useRef)(""),Be=(0,te.TH)(),Me=new URLSearchParams(Be.search).get("id")||"",Ne=function(){var e=l()(i()().mark((function e(){var n,t,r,s,o;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(j.trim()){e.next=3;break}return k.ZP.error("请输入搜索内容"),e.abrupt("return");case 3:if(Me){e.next=6;break}return k.ZP.error("知识库ID不能为空"),e.abrupt("return");case 6:return n=0===G.searchModes.length?"semantic":1===G.searchModes.length?"full"===G.searchModes[0]?"fulltext":"semantic":"hybrid",t=a()({knowledge_base_id:Me,query:j,search_mode:n,max_tokens:G.maxTokens,min_similarity:G.minRelevance,topk:10},G.reRankEnabled&&{rerank_id:1}),console.log("开始搜索，参数:",t),Re.current=j,E(!0),e.prev=11,e.next=14,(0,P.SS)(t);case 14:r=e.sent,console.log("搜索响应:",r),r.success?(T(r.results||[]),s=new Date,o=s.toLocaleTimeString(),z((function(e){return[{query:j,time:o}].concat(D()(e.slice(0,9)))})),0===(r.results||[]).length&&k.ZP.info("未找到相关结果")):k.ZP.error("测试失败，请重试"),e.next=24;break;case 19:e.prev=19,e.t0=e.catch(11),console.error("搜索失败:",e.t0),e.t0.response?(console.error("错误响应状态:",e.t0.response.status),console.error("错误响应数据:",e.t0.response.data)):e.t0.request&&console.error("未收到响应，请求信息:",e.t0.request),k.ZP.error("搜索请求失败，请稍后重试");case 24:return e.prev=24,E(!1),e.finish(24);case 27:case"end":return e.stop()}}),e,null,[[11,19,24,27]])})));return function(){return e.apply(this,arguments)}}(),Ee=function(e){navigator.clipboard.writeText(e).then((function(){return k.ZP.success("复制成功")})).catch((function(){return k.ZP.error("复制失败")}))},Le=[{key:"1",label:(0,I.jsx)("span",{style:{display:"flex",alignItems:"center",gap:"4px"},children:"搜索模式"}),children:(0,I.jsxs)("div",{style:{padding:"16px 0"},children:[(0,I.jsx)("div",{style:{border:"1px solid #e6e6e6",borderRadius:"8px",padding:"16px",marginBottom:"16px"},children:(0,I.jsxs)(ie.Z.Group,{value:G.searchModes,onChange:function(e){K(a()(a()({},G),{},{searchModes:e}))},children:[(0,I.jsx)("div",{style:{padding:"12px",borderRadius:"8px",background:G.searchModes.includes("semantic")?"#f5f3ff":"transparent",marginBottom:"12px",cursor:"pointer"},children:(0,I.jsx)(ie.Z,{value:"semantic",children:(0,I.jsxs)("div",{children:[(0,I.jsx)("div",{children:"语义检索"}),(0,I.jsx)("div",{style:{fontSize:"12px",color:"#666",marginTop:"4px"},children:"使用向量进行文本相关性查询"})]})})}),(0,I.jsx)("div",{style:{padding:"12px",cursor:"pointer",background:G.searchModes.includes("full")?"#f5f3ff":"transparent",borderRadius:"8px"},children:(0,I.jsx)(ie.Z,{value:"full",children:(0,I.jsxs)("div",{children:[(0,I.jsx)("div",{children:"全文检索"}),(0,I.jsx)("div",{style:{fontSize:"12px",color:"#666",marginTop:"4px"},children:"使用传统的全文检索，适合查找一些关键词和主语语境特殊的数据"})]})})})]})}),(0,I.jsx)("div",{style:{border:"1px solid #e6e6e6",borderRadius:"8px",padding:"16px"},children:(0,I.jsx)("div",{style:{padding:"12px",cursor:"pointer",background:G.reRankEnabled?"#f5f3ff":"transparent",borderRadius:"8px"},children:(0,I.jsx)(ie.Z,{checked:G.reRankEnabled,onChange:function(e){K(a()(a()({},G),{},{reRankEnabled:e.target.checked}))},children:(0,I.jsxs)("div",{children:[(0,I.jsx)("div",{children:"结果重排"}),(0,I.jsx)("div",{style:{fontSize:"12px",color:"#666",marginTop:"4px"},children:"使用重排模型来进行二次排序，可增强综合排名"})]})})})})]})},{key:"2",label:(0,I.jsx)("span",{style:{display:"flex",alignItems:"center",gap:"4px"},children:"搜索过滤"}),children:(0,I.jsxs)("div",{style:{padding:"16px 0"},children:[(0,I.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,I.jsxs)("div",{style:{marginBottom:"8px"},children:["最大token数 ",(0,I.jsx)("span",{style:{cursor:"help"},title:"控制检索时返回的最大引用数量",children:"ⓘ"})]}),(0,I.jsx)(oe.Z,{min:100,max:2e4,defaultValue:Y,value:Y,onChange:function(e){return $(e)},style:{width:"100%"}}),(0,I.jsx)("div",{style:{textAlign:"right"},children:Y})]}),(0,I.jsxs)("div",{children:[(0,I.jsxs)("div",{style:{marginBottom:"8px"},children:["最小相似度阈值 ",(0,I.jsx)("span",{style:{cursor:"help"},title:"设置检索结果的最低相关度阈值，低于此值的结果将被过滤",children:"ⓘ"})]}),(0,I.jsx)(oe.Z,{min:0,max:1,step:.01,defaultValue:ne,value:ne,onChange:function(e){return re(e)},style:{width:"100%"}}),(0,I.jsx)("div",{style:{textAlign:"right"},children:ne.toFixed(2)})]})]})}];return(0,I.jsxs)("div",{style:{padding:"24px",display:"flex",gap:"20px",height:"calc(100vh - 200px)"},children:[(0,I.jsxs)("div",{style:{flex:"1",border:"1px solid #e6e6e6",borderRadius:"8px",padding:"20px",backgroundColor:"white",display:"flex",flexDirection:"column",overflow:"hidden"},children:[(0,I.jsxs)("div",{style:{display:"flex",gap:"12px",marginBottom:"16px",justifyContent:"space-between"},children:[(0,I.jsx)(J.ZP,{icon:(0,I.jsx)(fe.Z,{}),onClick:function(){b(""),T([])},style:{display:"flex",alignItems:"center"},children:"重置"}),(0,I.jsxs)(J.ZP,{icon:(0,I.jsx)(xe.Z,{}),style:{color:"#1677ff",border:"1px solid #1677ff",display:"flex",alignItems:"center",gap:"4px"},onClick:function(){return d(!0)},children:[0===G.searchModes.length?"选择检索模式":1===G.searchModes.length?"semantic"===G.searchModes[0]?"语义检索":"全文检索":"混合检索",G.reRankEnabled?" + 重排":""]})]}),(0,I.jsxs)("div",{style:{flex:1,display:"flex",flexDirection:"column",minHeight:0},children:[(0,I.jsx)(R.Z.TextArea,{placeholder:"请输入搜索内容",value:j,onChange:function(e){return b(e.target.value)},style:{flex:1,marginBottom:"16px",resize:"none",minHeight:"120px"},onPressEnter:function(e){(e.ctrlKey||e.metaKey)&&Ne()}}),(0,I.jsx)("div",{style:{display:"flex",justifyContent:"flex-end",marginBottom:"16px"},children:(0,I.jsx)(J.ZP,{type:"primary",style:{backgroundColor:"#6B4EFF",borderRadius:"4px"},onClick:Ne,loading:N,children:"测试"})})]}),(0,I.jsxs)("div",{style:{flexShrink:0,maxHeight:"300px",overflow:"hidden",display:"flex",flexDirection:"column"},children:[(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"10px"},children:[(0,I.jsxs)("h3",{style:{display:"flex",alignItems:"center",gap:"8px",fontSize:"16px",fontWeight:"normal",margin:0},children:[(0,I.jsx)(he.Z,{}),"测试历史"]}),O.length>0&&(0,I.jsx)(J.ZP,{type:"text",size:"small",onClick:function(){q.Z.confirm({title:"确认清空",content:"确定要清空所有搜索历史吗？",onOk:function(){z([]),k.ZP.success("搜索历史已清空")}})},danger:!0,children:"清空"})]}),(0,I.jsx)("div",{style:{flex:1,overflowY:"auto",minHeight:0},children:(0,I.jsx)(Z.Z,{size:"small",locale:{emptyText:"暂无搜索历史"},dataSource:O,renderItem:function(e){return(0,I.jsx)(Z.Z.Item,{style:{cursor:"pointer",padding:"4px 0"},onClick:function(){return n=e.query,void b(n);var n},children:(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",width:"100%"},children:[(0,I.jsx)(ye,{ellipsis:!0,style:{maxWidth:"70%"},children:e.query}),(0,I.jsx)(ye,{type:"secondary",children:e.time})]})})}})})]})]}),(0,I.jsx)("div",{style:{flex:"1",border:"1px solid #e6e6e6",borderRadius:"8px",padding:"20px",backgroundColor:"white",overflowY:"auto",display:"flex",flexDirection:"column",minHeight:0},children:N?(0,I.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,I.jsx)(le.Z,{size:"large",tip:"搜索中..."})}):_.length>0?(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px",flexShrink:0},children:(0,I.jsxs)(je,{level:4,style:{margin:0},children:["搜索结果",(0,I.jsx)(ce.Z,{count:_.length,style:{backgroundColor:"#6B4EFF",marginLeft:"8px"}})]})}),(0,I.jsx)("div",{style:{flex:1,overflowY:"auto",minHeight:0},children:_.map((function(e,n){var t,r,a,s;return(0,I.jsxs)(x.Z,{style:{marginBottom:"16px"},bodyStyle:{padding:"16px"},hoverable:!0,children:[(0,I.jsxs)("div",{style:{marginBottom:"8px",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,I.jsxs)(ue.Z,{color:ke(we(e)),children:["相似度: ",(100*we(e)).toFixed(2),"%"]}),(0,I.jsxs)(ye,{type:"secondary",style:{fontSize:"12px"},children:[(0,I.jsx)(me.Z,{style:{marginRight:"4px"}}),(null===(t=e.metadata)||void 0===t?void 0:t.file_name)||"未知来源"]})]}),(null===(r=e.metadata)||void 0===r?void 0:r.question)&&(0,I.jsxs)("div",{style:{marginBottom:"8px"},children:[(0,I.jsx)(ye,{strong:!0,children:"问题: "}),(0,I.jsx)(be,{ellipsis:{rows:2,expandable:!0,symbol:"展开"},children:e.metadata.question})]}),(0,I.jsxs)("div",{children:[(0,I.jsx)(ye,{strong:!0,children:"内容: "}),(0,I.jsx)(be,{ellipsis:{rows:3,expandable:!0,symbol:"展开"},children:(null===(a=e.metadata)||void 0===a?void 0:a.answer)||e.content})]}),(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",marginTop:"8px",alignItems:"center"},children:[(null===(s=e.metadata)||void 0===s?void 0:s.created_at)&&(0,I.jsxs)(ye,{type:"secondary",style:{fontSize:"12px"},children:["创建时间: ",e.metadata.created_at]}),(0,I.jsxs)(ae.Z,{children:[(0,I.jsx)(J.ZP,{type:"text",size:"small",icon:(0,I.jsx)(ge.Z,{}),onClick:function(){var n;return Ee((null===(n=e.metadata)||void 0===n?void 0:n.answer)||e.content)},children:"复制"}),(0,I.jsx)(J.ZP,{type:"primary",size:"small",icon:(0,I.jsx)(ve.Z,{}),onClick:function(){return function(e){Te(e),Se(!0)}(e)},style:{backgroundColor:"#6B4EFF"},children:"详情"})]})]})]},n)}))})]}):(0,I.jsx)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px 0",color:"#666",height:"100%"},children:(0,I.jsx)(de.Z,{description:"测试结果将在这里展示",image:de.Z.PRESENTED_IMAGE_SIMPLE})})}),(0,I.jsx)(q.Z,{title:(0,I.jsx)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:"知识库搜索配置"}),open:c,onCancel:function(){return d(!1)},footer:[(0,I.jsx)(J.ZP,{onClick:function(){return d(!1)},children:"关闭"},"cancel"),(0,I.jsx)(J.ZP,{type:"primary",style:{backgroundColor:"#6B4EFF"},onClick:function(){K({searchModes:G.searchModes,reRankEnabled:G.reRankEnabled,maxTokens:Y,minRelevance:ne}),d(!1)},children:"完成"},"submit")],width:500,children:(0,I.jsx)(pe.Z,{activeKey:m,onChange:g,items:Le,style:{marginTop:"16px"}})}),(0,I.jsx)(q.Z,{title:"结果详情",open:Ce,onCancel:function(){return Se(!1)},footer:[(0,I.jsx)(J.ZP,{onClick:function(){var e;return _e&&Ee((null===(e=_e.metadata)||void 0===e?void 0:e.answer)||_e.content)},children:"复制内容"},"copy"),(0,I.jsx)(J.ZP,{type:"primary",onClick:function(){return Se(!1)},children:"关闭"},"close")],width:700,children:_e&&(0,I.jsxs)("div",{children:[(0,I.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,I.jsx)(je,{level:5,children:"基本信息"}),(0,I.jsx)(w.Z,{style:{margin:"8px 0 16px"}}),(0,I.jsxs)("div",{style:{display:"flex",flexWrap:"wrap",gap:"12px"},children:[(0,I.jsxs)("div",{children:[(0,I.jsx)(ye,{type:"secondary",children:"相似度:"}),(0,I.jsxs)(ue.Z,{color:ke(we(_e)),style:{marginLeft:"8px"},children:[(100*we(_e)).toFixed(2),"%"]})]}),(null===(e=_e.metadata)||void 0===e?void 0:e.file_name)&&(0,I.jsxs)("div",{children:[(0,I.jsx)(ye,{type:"secondary",children:"来源文件:"}),(0,I.jsx)(ye,{style:{marginLeft:"8px"},children:_e.metadata.file_name})]}),(null===(n=_e.metadata)||void 0===n?void 0:n.created_at)&&(0,I.jsxs)("div",{children:[(0,I.jsx)(ye,{type:"secondary",children:"创建时间:"}),(0,I.jsx)(ye,{style:{marginLeft:"8px"},children:_e.metadata.created_at})]})]})]}),(null===(t=_e.metadata)||void 0===t?void 0:t.question)&&(0,I.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,I.jsx)(je,{level:5,children:"问题"}),(0,I.jsx)(w.Z,{style:{margin:"8px 0 16px"}}),(0,I.jsx)(be,{children:_e.metadata.question})]}),(0,I.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,I.jsx)(je,{level:5,children:"内容"}),(0,I.jsx)(w.Z,{style:{margin:"8px 0 16px"}}),(0,I.jsx)(be,{children:(null===(r=_e.metadata)||void 0===r?void 0:r.answer)||_e.content})]})]})})]})},Ce=t(63960),Se=t(34804),Pe=t(35183),Ie=t(3159),_e=t(71541),Te=t(78537),Re=t(31825),Be=t(6228);Pe.D([_e.N,Te.N,Re.N,Be.N]);var Me=function(){var e=(0,h.useRef)(null),n=(0,h.useRef)(null),t=(0,h.useState)(window.innerHeight),r=u()(t,1)[0],s=(0,h.useState)(!1),o=u()(s,2),c=o[0],d=o[1],p=(0,h.useState)(!1),f=u()(p,2),x=f[0],m=f[1],g=(0,h.useState)(""),v=u()(g,2),y=v[0],j=v[1],b=(0,h.useState)([]),w=u()(b,2),Z=w[0],C=w[1],S=(0,h.useState)({knowledge_base_id:"",nodes:[],edges:[]}),_=u()(S,2),T=_[0],R=_[1],B=(0,h.useState)([]),M=u()(B,2),N=M[0],E=M[1],L=(0,h.useState)(""),F=u()(L,2),O=F[0],z=F[1],A=(0,h.useState)(null),V=u()(A,2),q=V[0],W=V[1],H=(0,h.useState)({visible:!1,x:0,y:0,nodeId:"",nodeName:""}),G=u()(H,2),K=G[0],X=G[1],U=(0,te.TH)(),Y=new URLSearchParams(U.search).get("id")||"default";(0,h.useEffect)((function(){var e=function(){var e=l()(i()().mark((function e(){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Y&&"default"!==Y){e.next=2;break}return e.abrupt("return");case 2:return d(!0),e.prev=3,console.log("🚀 检查知识库图索引状态..."),e.next=7,(0,P.SJ)(Y);case 7:if(n=e.sent,console.log("🚀 知识库信息:",n),W(n),n&&n.data.graph_index){e.next=14;break}return console.log("🚀 知识库未开启图索引功能"),R({knowledge_base_id:Y,nodes:[],edges:[],error_message:"该数据库并未开启Graph图索引，如需支持图谱，请先开启Graph图索引功能，并重新上传文件"}),e.abrupt("return");case 14:console.log("🚀 知识库已开启图索引功能，等待用户搜索..."),R({knowledge_base_id:Y,nodes:[],edges:[]}),e.next=25;break;case 18:e.prev=18,e.t0=e.catch(3),console.error("获取知识图谱失败:",e.t0),console.error("错误详情:",JSON.stringify(e.t0,null,2)),console.error("知识库ID:",Y),R({knowledge_base_id:Y,nodes:[],edges:[]}),k.ZP.error("获取图谱数据失败: ".concat((null===e.t0||void 0===e.t0?void 0:e.t0.message)||"未知错误"));case 25:return e.prev=25,d(!1),e.finish(25);case 28:case"end":return e.stop()}}),e,null,[[3,18,25,28]])})));return function(){return e.apply(this,arguments)}}();e()}),[Y]);var $=function(){var e=l()(i()().mark((function e(n){var t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n.trim()){e.next=3;break}return C([]),e.abrupt("return");case 3:if(Y&&"default"!==Y){e.next=5;break}return e.abrupt("return");case 5:return e.prev=5,console.log("🚀 开始模糊搜索实体:",n),e.next=9,(0,P.ln)({entity:n,match_type:"contains",limit:5,kb_id:Y});case 9:t=e.sent,console.log("🚀 模糊搜索结果:",t),t&&t.success&&t.entities&&t.entities.length>0?(r=t.entities.map((function(e){return{value:e,label:e}})),C(r)):C([]),e.next=18;break;case 14:e.prev=14,e.t0=e.catch(5),console.error("模糊搜索失败:",e.t0),C([]);case 18:case"end":return e.stop()}}),e,null,[[5,14]])})));return function(n){return e.apply(this,arguments)}}(),Q=function(){var e=l()(i()().mark((function e(){var n,t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(y.trim()){e.next=3;break}return k.ZP.warning("请输入要搜索的实体名称"),e.abrupt("return");case 3:if(Y&&"default"!==Y){e.next=6;break}return k.ZP.error("无效的知识库ID"),e.abrupt("return");case 6:return m(!0),e.prev=7,console.log("🚀 开始搜索实体子图:",y),e.next=11,(0,P.vn)(Y,{entity_name:y,format:"echarts",max_depth:1});case 11:n=e.sent,console.log("🚀 搜索结果:",n),n&&n.nodes&&n.nodes.length>0?(t=n.links||n.edges||[],r={knowledge_base_id:n.knowledge_base_id,nodes:n.nodes,edges:t},console.log("🚀 搜索处理后的数据:",r),R(r),k.ZP.success("找到 ".concat(n.nodes.length," 个相关节点"))):(console.log("🚀 搜索返回空数据"),R({knowledge_base_id:Y,nodes:[],edges:[]}),k.ZP.info("未找到相关实体")),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(7),k.ZP.error("搜索失败，请检查实体名称是否正确");case 19:return e.prev=19,m(!1),e.finish(19);case 22:case"end":return e.stop()}}),e,null,[[7,16,19,22]])})));return function(){return e.apply(this,arguments)}}(),ee=function(){var e=l()(i()().mark((function e(n,t){var r,a,s,o,l,c,u;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Y&&"default"!==Y){e.next=2;break}return e.abrupt("return");case 2:return d(!0),e.prev=3,console.log("🚀 开始下钻节点:",n,t),E((function(e){return[].concat(D()(e),[T])})),z(t),e.next=9,(0,P.vn)(Y,{entity_name:t,format:"echarts",max_depth:1});case 9:r=e.sent,console.log("🚀 下钻搜索结果:",r),r&&r.nodes&&r.nodes.length>0?(a=new Set(T.nodes.map((function(e){return e.id}))),s=T.edges||[],o=new Set(s.map((function(e){return"".concat(e.source,"-").concat(e.target)}))),l=r.nodes.filter((function(e){return!a.has(e.id)})),c=(r.edges||r.links||[]).filter((function(e){var n="".concat(e.source,"-").concat(e.target),t="".concat(e.target,"-").concat(e.source);return!o.has(n)&&!o.has(t)})),u={knowledge_base_id:T.knowledge_base_id,nodes:[].concat(D()(T.nodes),D()(l)),edges:[].concat(D()(s),D()(c))},console.log("🚀 合并后的图谱数据:",u),R(u),k.ZP.success('已展开节点"'.concat(t,'"的关联实体'))):k.ZP.info('节点"'.concat(t,'"没有更多关联实体')),e.next=18;break;case 14:e.prev=14,e.t0=e.catch(3),console.error("下钻失败:",e.t0),k.ZP.error("下钻失败: ".concat((null===e.t0||void 0===e.t0?void 0:e.t0.message)||"未知错误"));case 18:return e.prev=18,d(!1),e.finish(18);case 21:case"end":return e.stop()}}),e,null,[[3,14,18,21]])})));return function(n,t){return e.apply(this,arguments)}}();return(0,h.useEffect)((function(){if(e.current){if(T.nodes&&0!==T.nodes.length){var t=n.current;t||(t=Ie.S1(e.current),n.current=t);var r=[{name:"概念",itemStyle:{color:"#d62728"}},{name:"机构",itemStyle:{color:"#ff7f0e"}},{name:"人员",itemStyle:{color:"#2ca02c"}},{name:"地点",itemStyle:{color:"#1f77b4"}},{name:"事件",itemStyle:{color:"#9467bd"}},{name:"未知",itemStyle:{color:"#999999"}}],s=T.nodes.map((function(e){return{id:e.id,name:e.name.length>15?e.name.substring(0,15)+"...":e.name,symbolSize:e.symbolSize/2,category:e.category,value:e.data.entity_type,label:{show:e.label.show,formatter:"{b}"},itemStyle:e.itemStyle,tooltip:{formatter:e.tooltip.formatter}}})),i=new Set(T.nodes.map((function(e){return e.id})));console.log("🚀 所有节点ID:",Array.from(i));var o=T.edges||[];console.log("🚀 原始边数据:",o);var l=o.map((function(e){return console.log("🚀 处理边: ".concat(e.source," -> ").concat(e.target)),console.log("🚀 source存在: ".concat(i.has(e.source),", target存在: ").concat(i.has(e.target))),{source:e.source,target:e.target,label:e.label?{show:!0,formatter:e.label}:void 0,lineStyle:{color:"#666",width:2}}}));console.log("🚀 转换后的ECharts连接数据:",l);var c={tooltip:{trigger:"item",formatter:function(e){if("node"===e.dataType){var n=T.nodes.find((function(n){return n.id===e.data.id}));return n?n.tooltip.formatter:'<div style="font-weight:bold">'.concat(e.name,"</div>类型: ").concat(e.value)}return e.name||""}},legend:[{data:r.map((function(e){return e.name})),orient:"vertical",right:10,top:20}],series:[{name:"知识图谱",type:"graph",layout:"force",nodes:s,links:l,categories:r,roam:!0,focusNodeAdjacency:!0,label:{show:!0,position:"right",formatter:"{b}",fontSize:12},edgeLabel:{show:!0,fontSize:10,formatter:"{c}"},force:{repulsion:200,edgeLength:[100,200],gravity:.1,layoutAnimation:!0},draggable:!0,emphasis:{focus:"adjacency",lineStyle:{width:3,color:"#409EFF"},label:{show:!0}},lineStyle:{color:"source",curveness:.1,width:2}}]};t.setOption(c,!0),t.off("contextmenu"),t.on("contextmenu",(function(e){if("node"===e.dataType){console.log("🚀 右键点击节点:",e.data);var n=e.data.id,t=e.data.name,r=T.nodes.find((function(e){return e.id===n})),a=r?r.name:t;X({visible:!0,x:e.event.offsetX,y:e.event.offsetY,nodeId:n,nodeName:a}),e.event.event.preventDefault()}})),t.off("click"),t.on("click",(function(){X((function(e){return a()(a()({},e),{},{visible:!1})}))}));var u=function(){var e;null===(e=t)||void 0===e||e.resize()};return window.addEventListener("resize",u),function(){if(window.removeEventListener("resize",u),t)try{t.dispose()}catch(e){console.warn("🚀 组件卸载时清理图表实例出现警告:",e)}n.current=null}}if(n.current){try{n.current.dispose()}catch(e){console.warn("🚀 清理图表实例时出现警告:",e)}n.current=null}}}),[T]),(0,I.jsxs)("div",{style:{width:"100%",height:r-120,padding:"20px",backgroundColor:"#fff",borderRadius:"12px",position:"relative"},onClick:function(){return X((function(e){return a()(a()({},e),{},{visible:!1})}))},children:[q&&q.data.graph_index&&(0,I.jsxs)("div",{style:{marginBottom:"16px"},children:[O&&(0,I.jsxs)("div",{style:{marginBottom:"8px",fontSize:"14px",color:"#666"},children:["当前中心实体: ",(0,I.jsx)("span",{style:{fontWeight:"bold",color:"#1890ff"},children:O})]}),(0,I.jsxs)(ae.Z.Compact,{style:{width:"100%",maxWidth:"500px"},children:[(0,I.jsx)(Ce.Z,{placeholder:"输入实体名称搜索子图...",value:y,options:Z,onSearch:$,onChange:j,onSelect:j,style:{flex:1},filterOption:!1}),(0,I.jsx)(J.ZP,{type:"primary",icon:(0,I.jsx)(xe.Z,{}),loading:x,onClick:Q,children:"搜索"}),(0,I.jsx)(J.ZP,{onClick:function(){if(console.log("🚀 重置图谱，清空显示..."),n.current){try{n.current.dispose(),console.log("🚀 已清理图表实例")}catch(e){console.warn("🚀 清理图表实例时出现警告:",e)}n.current=null}R({knowledge_base_id:Y,nodes:[],edges:[]}),j(""),E([]),z(""),console.log("🚀 重置后的graphData:",{knowledge_base_id:Y,nodes:[],edges:[]}),k.ZP.success("已重置图谱")},children:"重置"}),N.length>0&&(0,I.jsx)(J.ZP,{onClick:function(){if(N.length>0){var e=N[N.length-1];R(e),E((function(e){return e.slice(0,-1)})),z(""),k.ZP.success("已返回上一层")}},style:{marginLeft:"8px"},children:"撤销"})]})]}),(0,I.jsx)("div",{style:{width:"100%",height:"calc(100% - 60px)"},children:(console.log("🚀 渲染条件检查:",{loading:c,nodesLength:T.nodes.length,hasNodes:T.nodes.length>0,graphData:T}),c?(0,I.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,I.jsx)(le.Z,{size:"large",tip:"加载中..."})}):T.nodes.length>0?(0,I.jsx)("div",{ref:e,style:{width:"100%",height:"100%",minHeight:"450px",borderRadius:"12px"}}):(console.log("🚀 显示Empty组件"),(0,I.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,I.jsx)(de.Z,{description:T.error_message||"暂无图谱数据",style:{fontSize:"14px",color:T.error_message?"#ff4d4f":void 0}})})))}),K.visible&&(0,I.jsx)("div",{style:{position:"absolute",left:K.x,top:K.y,backgroundColor:"#fff",border:"1px solid #d9d9d9",borderRadius:"6px",boxShadow:"0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)",zIndex:1e3,minWidth:"120px"},children:(0,I.jsxs)("div",{style:{padding:"8px 12px",cursor:"pointer",borderRadius:"6px",transition:"background-color 0.2s"},onMouseEnter:function(e){e.currentTarget.style.backgroundColor="#f5f5f5"},onMouseLeave:function(e){e.currentTarget.style.backgroundColor="transparent"},onClick:function(){return"drillDown"==="drillDown"&&ee(K.nodeId,K.nodeName),void X((function(e){return a()(a()({},e),{},{visible:!1})}))},children:[(0,I.jsx)(Se.Z,{style:{marginRight:"8px",fontSize:"12px"}}),"下钻"]})})]})},Ne=function(){return(0,I.jsx)("div",{style:{padding:"24px",display:"flex",gap:"20px"},children:(0,I.jsxs)("div",{style:{flex:"1",borderRadius:"8px",backgroundColor:"white"},children:[(0,I.jsx)(Z.Z,{itemLayout:"horizontal",dataSource:[{title:"API服务",description:"API服务",fieldName:"apiService"}],renderItem:function(e){return(0,I.jsx)(Z.Z.Item,{actions:[(0,I.jsx)(j.Z,{name:e.fieldName,noStyle:!0},e.fieldName)],children:(0,I.jsx)(Z.Z.Item.Meta,{title:(0,I.jsx)("span",{children:e.title}),description:(0,I.jsx)("span",{children:e.description})})})}}),(0,I.jsx)(R.Z.TextArea,{placeholder:"请输入文本",style:{height:"200px",marginBottom:"16px",resize:"none"}}),(0,I.jsx)("div",{style:{display:"flex",justifyContent:"flex-end"},children:(0,I.jsx)(J.ZP,{type:"primary",style:{backgroundColor:"#6B4EFF",borderRadius:"4px"},children:"测试"})})]})})},Ee=t(12453),Le=t(67839),Fe=function(e){var n=e.record,t=e.onDelete,r=e.onTransfer,s=e.onMoreClick,o=e.currentItemId,c=e.setTableData,d=e.fetchKnowledgeFolderTree,p=e.actionRef,f=(0,h.useState)(""),x=u()(f,2),m=x[0],g=x[1],v=(0,h.useState)(!1),y=u()(v,2),j=y[0],b=y[1],w=(0,h.useState)(!1),C=u()(w,2),S=C[0],_=C[1],T=(0,h.useState)(""),B=u()(T,2),M=B[0],N=B[1],E=(0,h.useState)(new Set),L=u()(E,2),F=L[0],O=L[1],z=(0,h.useState)(""),A=u()(z,2),V=A[0],H=A[1],G=(0,h.useState)([]),K=u()(G,2),X=K[0],U=K[1],Y=(0,h.useState)(null),$=u()(Y,2),Q=$[0],re=$[1],ae=(0,te.TH)(),se=new URLSearchParams(ae.search).get("id");console.log("🚀 ~ FileOperations ~ id:",se);var oe=(0,h.useState)(n.forbidden),le=u()(oe,2),ce=le[0],ue=le[1];(0,h.useEffect)((function(){se&&g(se)}),[se]),(0,h.useEffect)((function(){ue(n.forbidden)}),[n.forbidden]);var de=function(){var e=l()(i()().mark((function e(s){var u,f,x,h,g,v,y,j,w,Z,C;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(u=o){e.next=4;break}return k.ZP.error("请选择一个文件"),e.abrupt("return");case 4:if("unpublish"!==s.key){e.next=29;break}return e.prev=5,e.next=8,(0,P.au)(u,!0);case 8:if(f=e.sent,console.log("下架成功:",f),!f.success){e.next=20;break}return k.ZP.success(f.message),ue(!0),e.next=15,d(!0);case 15:h=e.sent,c(h),null==p||null===(x=p.current)||void 0===x||x.reload(),e.next=21;break;case 20:k.ZP.error("下架失败");case 21:e.next=27;break;case 23:e.prev=23,e.t0=e.catch(5),console.error("下架失败:",e.t0),k.ZP.error("下架失败，请重试");case 27:e.next=85;break;case 29:if("publish"!==s.key){e.next=54;break}return e.prev=30,e.next=33,(0,P.au)(u,!1);case 33:if(g=e.sent,console.log("上架成功:",g),!g.success){e.next=45;break}return k.ZP.success(g.message),ue(!1),e.next=40,d(!0);case 40:y=e.sent,c(y),null==p||null===(v=p.current)||void 0===v||v.reload(),e.next=46;break;case 45:k.ZP.error(g.message);case 46:e.next=52;break;case 48:e.prev=48,e.t1=e.catch(30),console.error("上架失败:",e.t1),k.ZP.error("上架失败，请重试");case 52:e.next=85;break;case 54:e.t2=s.key,e.next="transfer"===e.t2?57:"tag"===e.t2?59:"update"===e.t2?76:"reset"===e.t2?80:"delete"===e.t2?82:84;break;case 57:return r(n),e.abrupt("break",85);case 59:if(b(!0),e.prev=60,m){e.next=64;break}return console.log("knowledgeId 为空，跳过接口调用"),e.abrupt("return");case 64:return e.next=66,(0,P.Bp)(m);case 66:j=e.sent,console.log("🚀 ~ handleMenuClick ~ response:",j),j&&j.tags&&(U(j.tags),O(new Set(null===(w=n.tags)||void 0===w?void 0:w.map((function(e){return j.tags.indexOf(e)})).filter((function(e){return-1!==e}))))),e.next=75;break;case 71:e.prev=71,e.t3=e.catch(60),console.error("🚀 ~ handleMenuClick ~ error:",e.t3),k.ZP.error("请求失败，请重试");case 75:return e.abrupt("break",85);case 76:return console.log("🚀 ~ handleMenuClick ~ record:",n),Z=n.id,C={name:"file",multiple:!0,progress:{strokeColor:{"0%":"#108ee9","100%":"#87d068"}},maxCount:1e3,beforeUpload:function(e){return!![".pdf",".docx",".csv",".xls",".xlsx",".json"].some((function(n){return e.name.toLowerCase().endsWith(n)}))||(k.ZP.error("只支持 PDF、Word、CSV、Excel 和 JSON 格式的文件"),W.Z.LIST_IGNORE)},onChange:function(){var e=l()(i()().mark((function e(t){var r,a,s,o;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t.fileList.length>0)){e.next=27;break}if(r=t.fileList.map((function(e){return e.originFileObj})),e.prev=2,!0,!n.knowledge_id||!n.parent_id){e.next=20;break}return a={files:r,knowledgeId:n.knowledge_id,parentId:n.parent_id,oldFileId:Z,ocr:"1"},e.next=8,(0,P.bV)(a.files,a.knowledgeId,a.parentId,a.oldFileId,a.ocr);case 8:if(!(s=e.sent).success){e.next=17;break}return k.ZP.success("文件更新成功"),e.next=13,d();case 13:o=e.sent,c(o),e.next=18;break;case 17:k.ZP.error(s.message||"更新失败");case 18:e.next=21;break;case 20:k.ZP.error("缺少必要的文件信息");case 21:e.next=27;break;case 23:e.prev=23,e.t0=e.catch(2),console.error("更新失败:",e.t0),k.ZP.error("更新失败，请重试");case 27:case"end":return e.stop()}}),e,null,[[2,23]])})));return function(n){return e.apply(this,arguments)}}(),onDrop:function(e){console.log("Dropped files",e.dataTransfer.files)}},e.abrupt("return",(0,I.jsx)(W.Z,a()(a()({},C),{},{children:(0,I.jsx)(J.ZP,{children:"选择文件"})})));case 80:return q.Z.confirm({title:"确认重置",content:"确定要重置文件处理结果并重新处理吗？",okText:"确认",cancelText:"取消",onOk:function(){var e=l()(i()().mark((function e(){var n,t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=k.ZP.loading("正在重置"),e.prev=1,e.next=4,(0,P.L9)(u);case 4:if(t=e.sent,console.log(t.success),!t.success){e.next=13;break}return n(),k.ZP.success("重置成功(知识块："+t.deleted_chunk+",索引："+t.deleted_chunk_index+")，开始重新处理"),null==p||null===(r=p.current)||void 0===r||r.reload(),e.abrupt("return",!0);case 13:return n(),k.ZP.error("重置失败，请重试"),e.abrupt("return",!1);case 16:e.next=23;break;case 18:return e.prev=18,e.t0=e.catch(1),n(),k.ZP.error("重置失败，请重试"),e.abrupt("return",!1);case 23:case"end":return e.stop()}}),e,null,[[1,18]])})));return function(){return e.apply(this,arguments)}}()}),e.abrupt("break",85);case 82:return t(n.id),e.abrupt("break",85);case 84:return e.abrupt("break",85);case 85:case"end":return e.stop()}}),e,null,[[5,23],[30,48],[60,71]])})));return function(n){return e.apply(this,arguments)}}(),pe=function(){var e=l()(i()().mark((function e(){var n,t,r,a,s;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("🚀 ~ handleOk ~ currentItemId:",o),n=new Set(F),t=Array.from(n).map((function(e){return X[e]})).filter((function(e){return null!=e})),console.log("🚀 ~ handleTagClick ~ selectedTagValues:",t),e.prev=4,e.next=7,(0,P.ki)(o,t);case 7:if(!(r=e.sent).success){e.next=17;break}return k.ZP.success("标签更新成功"),e.next=12,d(!0);case 12:s=e.sent,c(s),null==p||null===(a=p.current)||void 0===a||a.reload(),e.next=18;break;case 17:k.ZP.error(r.message||"标签更新失败");case 18:e.next=24;break;case 20:e.prev=20,e.t0=e.catch(4),console.error("更新标签失败:",e.t0),k.ZP.error("标签更新失败，请重试");case 24:return e.prev=24,b(!1),e.finish(24);case 27:case"end":return e.stop()}}),e,null,[[4,20,24,27]])})));return function(){return e.apply(this,arguments)}}(),fe=function(e){re(e),H(X[e])},xe=function(){var e=l()(i()().mark((function e(n){var t,r,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("🚀 ~ handleTagClick ~ currentFileId:",o),(t=new Set(F)).has(n)?t.delete(n):t.add(n),O(t),r=Array.from(t).map((function(e){return X[e]})).filter((function(e){return null!=e})),console.log("🚀 ~ handleTagClick ~ selectedTagValues:",r),!o){e.next=13;break}return e.next=9,(0,P.ki)(o,r);case 9:return e.next=11,d();case 11:a=e.sent,c(a);case 13:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),he=function(){var e=l()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:_(!0);case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),me=function(){_(!1),N("")},ge=function(){var e=l()(i()().mark((function e(){var n,t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(U((function(e){return[].concat(D()(e||[]),[M])})),me(),console.log("----+++====="),console.log("🚀 ~ handleTagModal ~ tags:",X),console.log("🚀 ~ handleTagModal ~ knowledgeId:",m),!m){e.next=26;break}return e.prev=6,e.next=9,(0,P.IV)(m,[].concat(D()(X||[]),[M]));case 9:if(n=e.sent,console.log("🚀 ~ handleTagModal ~ response:",n),!n.success){e.next=20;break}return n.data&&n.data.tags&&U(n.data.tags||[]),e.next=15,d();case 15:t=e.sent,c(t),k.ZP.success("标签创建成功"),e.next=21;break;case 20:k.ZP.error("标签创建失败");case 21:e.next=26;break;case 23:e.prev=23,e.t0=e.catch(6),k.ZP.error("请求失败，请重试");case 26:case"end":return e.stop()}}),e,null,[[6,23]])})));return function(){return e.apply(this,arguments)}}(),ve=function(){var e=l()(i()().mark((function e(n){var t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return(t=D()(X))[n]=V,e.prev=2,e.next=5,(0,P.IV)(m,t);case 5:if(!e.sent.success){e.next=15;break}return U(t),k.ZP.success("标签重命名成功"),e.next=11,(0,P.Bp)(m);case 11:(r=e.sent)&&r.tags&&U(r.tags||[]),e.next=16;break;case 15:k.ZP.error("标签重命名失败");case 16:e.next=22;break;case 18:e.prev=18,e.t0=e.catch(2),k.ZP.error("请求失败，请重试"),console.error("重命名标签时出错:",e.t0);case 22:return e.prev=22,re(null),H(""),e.finish(22);case 26:case"end":return e.stop()}}),e,null,[[2,18,22,26]])})));return function(n){return e.apply(this,arguments)}}(),ye=(0,I.jsxs)(ee.Z,{onClick:de,children:[(0,I.jsx)(ee.Z.Item,{children:"转移"},"transfer"),(0,I.jsx)(ee.Z.Item,{children:"标签"},"tag"),(0,I.jsx)(ee.Z.Item,{children:"重置"},"reset"),0!==n.flg&&(ce?(0,I.jsx)(ee.Z.Item,{children:"上架"},"publish"):(0,I.jsx)(ee.Z.Item,{children:"下架"},"unpublish")),(0,I.jsx)(ee.Z.Item,{children:"删除"},"delete")]});return(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)(ne.Z,{overlay:ye,trigger:["click"],children:(0,I.jsx)(J.ZP,{onClick:function(){return s(n.id)},type:"link",children:"更多"})}),(0,I.jsxs)(q.Z,{title:"标签",visible:j,onCancel:function(){b(!1)},onOk:function(){return pe()},okText:"确定",cancelText:"取消",children:[(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",height:"100%",marginBottom:16},children:[(0,I.jsx)("strong",{children:"请选择一个标签"}),(0,I.jsx)(J.ZP,{onClick:he,type:"primary",children:"创建标签"})]}),(0,I.jsx)(Z.Z,{dataSource:X,renderItem:function(e,n){return(0,I.jsx)(Z.Z.Item,{style:{padding:"8px 16px",borderRadius:"4px",marginBottom:"4px",backgroundColor:F.has(n)?"#e6f7ff":"transparent",cursor:"pointer"},children:(0,I.jsx)(ne.Z,{overlay:(0,I.jsxs)(ee.Z,{children:[(0,I.jsx)(ee.Z.Item,{onClick:function(){return fe(n)},children:"重命名"},"rename"),(0,I.jsx)(ee.Z.Item,{onClick:function(){return function(e){var n;q.Z.confirm({title:"确认删除",content:"确定要删除这个标签吗？",okText:"确认",cancelText:"取消",onOk:(n=l()(i()().mark((function n(){var t;return i()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return t=X.filter((function(n,t){return t!==e})),U(t),n.next=4,(0,P.IV)(m,t);case 4:k.ZP.success("标签删除成功");case 5:case"end":return n.stop()}}),n)}))),function(){return n.apply(this,arguments)})})}(n)},children:"删除"},"delete")]}),trigger:["contextMenu"],children:(0,I.jsx)("span",{children:(0,I.jsx)(ie.Z,{checked:F.has(n),onChange:function(e){e.stopPropagation(),xe(n)},children:Q===n?(0,I.jsx)(R.Z,{value:V,onChange:function(e){return H(e.target.value)},onBlur:function(){return re(null)},onKeyPress:function(e){"Enter"===e.key&&ve(n)},autoFocus:!0}):(0,I.jsx)("span",{onDoubleClick:function(){return fe(n)},children:e})})})})})}})]}),(0,I.jsxs)(q.Z,{title:"创建标签",visible:S,onCancel:me,footer:null,style:{marginTop:80},width:400,children:[(0,I.jsx)(R.Z,{value:M,placeholder:"请输入标签名称",onChange:function(e){return N(e.target.value)}}),(0,I.jsx)(J.ZP,{type:"primary",onClick:ge,style:{marginTop:"10px"},children:"确定"})]})]})},De=function(){var e=(0,h.useState)(!1),n=u()(e,2),t=n[0],r=n[1],a=(0,h.useState)([]),s=u()(a,2),i=s[0],o=s[1];return(0,I.jsx)(I.Fragment,{children:(0,I.jsx)(q.Z,{title:"请选择标签",visible:t,onOk:function(){console.log("Selected Tags:",i),r(!1)},onCancel:function(){r(!1)},okText:"确定",cancelText:"取消",children:(0,I.jsx)(ie.Z.Group,{options:[],value:i,onChange:function(e){o(e)}})})})},Oe=function(e){var n=e.knowledgeBaseId,t=(0,h.useState)(!1),r=u()(t,2),s=r[0],o=r[1],c=(0,h.useState)(""),d=u()(c,2),p=d[0],f=d[1],x=(0,h.useState)(""),m=u()(x,2),g=m[0],v=m[1],y=(0,h.useRef)(),j=(0,h.useState)([]),b=u()(j,2),w=b[0],C=b[1],S=(0,h.useState)(!1),_=u()(S,2),T=_[0],B=_[1],M=(0,h.useState)([]),N=u()(M,1)[0],E=(0,h.useState)(""),L=(u()(E,1)[0],(0,h.useState)(null)),F=u()(L,2),O=F[0],z=F[1],A=(0,h.useState)([]),V=u()(A,2),W=V[0],H=V[1],G=(0,h.useState)(null),K=u()(G,2),X=K[0],U=K[1],Y=(0,h.useState)(""),$=u()(Y,2),Q=$[0],re=$[1],ae=(0,h.useState)(!1),se=u()(ae,2),ie=se[0],oe=se[1],le=(0,h.useState)([]),ce=u()(le,2),de=(ce[0],ce[1]),pe=(0,h.useState)([]),fe=u()(pe,2),xe=fe[0],he=fe[1],me=(0,h.useState)(null),ge=u()(me,2),ve=ge[0],ye=ge[1],je=(0,h.useState)(null),be=u()(je,2),ke=be[0],we=be[1],Ze=(0,h.useState)(!1),Ce=u()(Ze,2),Se=Ce[0],Pe=Ce[1],Ie=(0,te.TH)(),_e=new URLSearchParams(Ie.search).get("id");console.log("🚀 ~ FileOperations ~ id:",_e);var Te=(0,te.s0)(),Re=(0,h.useRef)(),Be=(0,h.useState)({}),Me=u()(Be,2),Ne=Me[0],Oe=Me[1];(0,h.useEffect)((function(){_e&&v(_e)}),[_e]);var ze=function(){var e=l()(i()().mark((function e(){var t,r,a,s,o=arguments;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=!(o.length>0&&void 0!==o[0])||o[0],r=o.length>1?o[1]:void 0,e.prev=2,console.log("🚀 ~ fetchKnowledgeFolderTree ~ searchParams:",t?r||Ne:{}),a=t?r||Ne:{},e.next=7,(0,P.E)(n,t&&a.name||"",[],!1,t&&"all"!==a.forbidden?"true"===a.forbidden:void 0,t&&"all"!==a.data_type?a.data_type:void 0,t&&"all"!==a.flg?parseInt(a.flg):void 0);case 7:return s=e.sent,e.abrupt("return",s.data||[]);case 11:return e.prev=11,e.t0=e.catch(2),k.ZP.error("获取文件夹树失败"),e.abrupt("return",[]);case 15:case"end":return e.stop()}}),e,null,[[2,11]])})));return function(){return e.apply(this,arguments)}}();(0,h.useEffect)((function(){var e=function(){var e=l()(i()().mark((function e(){var n,t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,ze(!1);case 3:n=e.sent,t=N.map((function(e){return{id:e.id,name:e.name,isCategory:!0}})),H([].concat(D()(n),D()(t))),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),k.ZP.error("获取文件列表失败");case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(){return e.apply(this,arguments)}}();e()}),[N,n]);var Ae=function(){var e=l()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:q.Z.confirm({title:"确认删除",content:"确定要删除这个文件吗？",okText:"确认",cancelText:"取消",onOk:function(){var e=l()(i()().mark((function e(){var t,r,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=k.ZP.loading("正在删除"),e.prev=1,e.next=4,(0,P._I)(n);case 4:if(!e.sent.success){e.next=15;break}return t(),k.ZP.success("删除成功"),null===(r=y.current)||void 0===r||r.reload(),e.next=11,ze(!0);case 11:a=e.sent,H(a),e.next=17;break;case 15:t(),k.ZP.error("删除失败，请重试");case 17:H((function(e){return e.filter((function(e){return e.id!==n}))})),e.next=24;break;case 20:e.prev=20,e.t0=e.catch(1),t(),k.ZP.error("删除失败，请重试");case 24:case"end":return e.stop()}}),e,null,[[1,20]])})));return function(){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),Ve=function(){var e=l()(i()().mark((function e(n,t){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,P.yN)(n,!0,t);case 3:e.next=9;break;case 5:e.prev=5,e.t0=e.catch(0),k.ZP.error("文件下载失败"),console.error("下载错误:",e.t0);case 9:case"end":return e.stop()}}),e,null,[[0,5]])})));return function(n,t){return e.apply(this,arguments)}}(),qe=function(){var e=l()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(w&&0!==w.length){e.next=3;break}return k.ZP.info("请先选择要删除的文件"),e.abrupt("return");case 3:q.Z.confirm({title:"确认批量删除",content:"确定要删除选中的 ".concat(w.length," 个文件吗？"),okText:"确认",cancelText:"取消",onOk:function(){var e=l()(i()().mark((function e(){var n,t,r,a,s,o;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=k.ZP.loading("正在删除"),e.prev=1,e.next=4,Promise.all(w.map((function(e){return(0,P._I)(e.id)})));case 4:if(t=e.sent,r=t.every((function(e){return e.success})),n(),!r){e.next=19;break}return k.ZP.success("批量删除成功"),H((function(e){return e.filter((function(e){return!w.some((function(n){return n.id===e.id}))}))})),C([]),null===(a=y.current)||void 0===a||a.reload(),e.next=14,ze(!0);case 14:return s=e.sent,H(s),e.abrupt("return",!0);case 19:return k.ZP.error("部分文件删除失败，请重试"),null===(o=y.current)||void 0===o||o.reload(),e.abrupt("return",!1);case 22:e.next=30;break;case 24:return e.prev=24,e.t0=e.catch(1),n(),console.error("批量删除失败:",e.t0),k.ZP.error("批量删除失败，请重试"),e.abrupt("return",!1);case 30:case"end":return e.stop()}}),e,null,[[1,24]])})));return function(){return e.apply(this,arguments)}}()});case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),We=function(){var e=l()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:q.Z.confirm({title:"确认批量重置",content:"确定要重置选中的 ".concat(w.length," 个文件处理结果并重新处理吗？"),okText:"确认",cancelText:"取消",onOk:function(){var e=l()(i()().mark((function e(){var n,t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=k.ZP.loading("正在批量重置"),e.prev=1,t=w.map((function(e){return e.id})),e.next=5,Promise.all(t.map((function(e){return(0,P.L9)(e)})));case 5:if(!e.sent.every((function(e){return e.success}))){e.next=14;break}return n(),k.ZP.success("批量重置成功"),C([]),null===(r=y.current)||void 0===r||r.reload(),e.abrupt("return",!0);case 14:return n(),k.ZP.error("部分重置失败，请重试"),e.abrupt("return",!1);case 17:e.next=24;break;case 19:return e.prev=19,e.t0=e.catch(1),n(),k.ZP.error("批量重置失败，请重试"),e.abrupt("return",!1);case 24:case"end":return e.stop()}}),e,null,[[1,19]])})));return function(){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),He=function(e){B(!0),de([e])},Ge=function(){o(!1),f("")},Ke=function(){var e=l()(i()().mark((function e(){var n,t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(p&&""!==p.trim()){e.next=3;break}return k.ZP.error("分类名称不能为空"),e.abrupt("return");case 3:return e.prev=3,"add",n=g,"",e.next=9,(0,P.l$)("add",p,n,"","");case 9:return(t=e.sent).detail?k.ZP.error(t.detail):k.ZP.success("分类创建成功"),e.next=13,ze(!0);case 13:r=e.sent,H(r),Ge(),e.next=22;break;case 18:e.prev=18,e.t0=e.catch(3),console.error("创建分类失败:",e.t0),k.ZP.error("创建分类失败，请重试");case 22:case"end":return e.stop()}}),e,null,[[3,18]])})));return function(){return e.apply(this,arguments)}}(),Je=function(){var e=l()(i()().mark((function e(n,t){var r,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,"update",e.next=4,(0,P.l$)("update",t,g,"",n);case 4:return r=e.sent,console.log("文件夹名称更新成功:",r),ye(null),e.next=9,ze(!0);case 9:a=e.sent,H(a),e.next=17;break;case 13:e.prev=13,e.t0=e.catch(0),console.error("更新文件夹名称失败:",e.t0),k.ZP.error("更新文件夹名称失败");case 17:case"end":return e.stop()}}),e,null,[[0,13]])})));return function(n,t){return e.apply(this,arguments)}}(),Xe=function(){var e=l()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!X){e.next=13;break}return e.prev=1,e.next=4,(0,P.e_)(X,Q);case 4:k.ZP.success("重命名成功"),H((function(e){return e.map((function(e){return e.id===X?a()(a()({},e),{},{name:Q}):e}))})),z(Q),U(null),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(1),k.ZP.error("重命名失败");case 13:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(){return e.apply(this,arguments)}}(),Ue=function(){var e=l()(i()().mark((function e(){var n,t,r,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!ke||!O){e.next=31;break}return e.prev=1,n=k.ZP.loading("正在移动文件..."),e.next=5,(0,P.SZ)(ke,O);case 5:if(t=e.sent,n(),!t.success){e.next=16;break}return k.ZP.success(t.message||"文件移动成功"),e.next=11,ze(!0);case 11:a=e.sent,H(a),null==y||null===(r=y.current)||void 0===r||r.reload(),e.next=17;break;case 16:k.ZP.error(t.message||"文件移动失败");case 17:B(!1),Pe(!1),z(""),we(""),e.next=29;break;case 23:e.prev=23,e.t0=e.catch(1),console.error("移动文件失败:",e.t0),k.ZP.error("移动文件失败，请重试"),B(!1),Pe(!1);case 29:e.next=32;break;case 31:k.ZP.error("请选择要移动的文件和目标分类");case 32:case"end":return e.stop()}}),e,null,[[1,23]])})));return function(){return e.apply(this,arguments)}}(),Ye=function(){var e=l()(i()().mark((function e(n){var t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:console.log("🚀 ~ handleCategoryDoubleClick ~ categoryId:",n),ye(n),(t=W.find((function(e){return e.id===n})))&&f(t.name);case 4:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),$e=function(e){console.log("🚀 ~ handleItemClick ~ id:",e),we(e)},Qe=function(e,n){"rename"===e.key?Ye(n):"move"===e.key&&function(e){z(e),Pe(!0)}(n)},en=[{title:"文件名",dataIndex:"name",ellipsis:!0,fieldProps:{placeholder:"请输入文件名"},render:function(e,n){return(0,I.jsx)("span",{style:{fontSize:"12px",fontWeight:n.isCategory?"bold":"normal",cursor:n.isCategory?"pointer":"default"},onClick:function(){return n.isCategory&&(he(n.children||[]),void oe(!0))},children:n.isCategory?(0,I.jsxs)("span",{style:{display:"flex",alignItems:"center"},children:[e,"(文件夹)"]}):e})}},{title:"大小",width:100,dataIndex:"size",search:!1,render:function(e,n){return n.isCategory||"folder"===n.data_type?"-":n.size?"".concat((n.size/1024).toFixed(2)," KB"):"-"}},{title:"文件格式",width:120,dataIndex:"data_type",valueType:"select",fieldProps:{placeholder:"选择文件格式"},valueEnum:{all:{text:"全部"},TXT:{text:"TXT"},PDF:{text:"PDF"},DOC:{text:"DOC"},DOCX:{text:"DOCX"},MD:{text:"MD"},CSV:{text:"CSV"},XLS:{text:"XLS"},XLSX:{text:"XLSX"},JSON:{text:"JSON"},JSONL:{text:"JSONL"},PPTX:{text:"PPTX"},HTML:{text:"HTML"},HTM:{text:"HTM"},JPG:{text:"JPG"},JPEG:{text:"JPEG"},PNG:{text:"PNG"},WAV:{text:"WAV"},MP3:{text:"MP3"},Markdown:{text:"Markdown"},folder:{text:"文件夹"}},initialValue:"all",render:function(e,n){if("folder"===n.data_type)return(0,I.jsx)(ue.Z,{color:"blue",children:"文件夹"});var t=function(e,n){var t,r=(null===(t=e.split(".").pop())||void 0===t?void 0:t.toLowerCase())||"";return"folder"===n?{label:"文件夹",color:"blue"}:{label:r?r.toUpperCase():n||"未知",color:{pdf:"red",doc:"blue",docx:"blue",txt:"default",md:"green",markdown:"green",xlsx:"green",xls:"green",csv:"orange",json:"purple",jsonl:"purple",pptx:"orange",ppt:"orange",html:"cyan",htm:"cyan",jpg:"magenta",jpeg:"magenta",png:"magenta",gif:"magenta",mp3:"gold",wav:"gold"}[r]||"default"}}(n.name,n.data_type);return(0,I.jsx)(ue.Z,{color:t.color,children:t.label})}},{title:"上传时间",dataIndex:"created_at",valueType:"dateTime",search:!1,render:function(e,n){return n.isCategory?"--":e}},{title:"状态",dataIndex:"flg",width:120,valueType:"select",fieldProps:{placeholder:"选择状态"},valueEnum:{all:{text:"全部"},0:{text:"等待处理",status:"default"},1:{text:"识别完成",status:"processing"},2:{text:"索引完成",status:"success"},3:{text:"识别失败",status:"error"},4:{text:"索引失败",status:"error"}},initialValue:"all",render:function(e,n){if("folder"===n.data_type)return"--";switch(n.flg){case 0:return(0,I.jsx)(ue.Z,{color:"default",children:"等待处理"});case 1:return(0,I.jsx)(ue.Z,{color:"processing",children:"识别完成"});case 2:return(0,I.jsx)(ue.Z,{color:"success",children:"索引完成"});case 3:return(0,I.jsx)(ue.Z,{color:"error",children:"识别失败"});case 4:return(0,I.jsx)(ue.Z,{color:"error",children:"索引失败"});default:return"--"}}},{title:"上线状态",dataIndex:"forbidden",width:80,valueType:"select",fieldProps:{placeholder:"选择上线状态"},valueEnum:{all:{text:"全部"},false:{text:"已上线",status:"Success"},true:{text:"已下线",status:"Error"}},initialValue:"all",render:function(e,n){return"folder"===n.data_type?"--":n.forbidden?(0,I.jsx)(ue.Z,{color:"red",children:"已下线"}):(0,I.jsx)(ue.Z,{color:"green",children:"已上线"})}},{title:"标签",dataIndex:"tags",search:!1,render:function(e,n){return"folder"===n.data_type?"--":Array.isArray(n.tags)?n.tags.join(", "):"无"}},{title:"视觉",dataIndex:"ocr",width:60,search:!1,render:function(e,n){return"folder"===n.data_type?"--":1===n.ocr?"启用":"-"}},{title:"操作",valueType:"option",fixed:"right",render:function(e,n){return"folder"===n.data_type?[(0,I.jsx)(J.ZP,{size:"small",type:"link",onClick:function(){return function(e){U(e.id),re(e.name),z(e.name)}(n)},children:"重命名"},"rename"),(0,I.jsx)(J.ZP,{type:"link",size:"small",danger:!0,onClick:function(){return Ae(n.id)},children:"删除"},"delete")]:[(0,I.jsx)(J.ZP,{type:"link",style:{display:"inline-block",width:40,textAlign:"left"},onClick:function(){return Te("/knowledgeManagement/fileInfo?id=".concat(n.id))},children:"查看"},"view"),(0,I.jsx)(J.ZP,{type:"link",style:{display:"inline-block",width:40,textAlign:"left"},onClick:function(){return Ve(n.id,n.name)},children:"下载"},"download"),(0,I.jsx)(Fe,{record:n,onDelete:Ae,onTransfer:He,onMoreClick:$e,currentItemId:ke,setTableData:H,fetchKnowledgeFolderTree:ze,actionRef:y},"operations")]}}],nn=W.filter((function(e){return"folder"===e.data_type})),tn=function(){var e=l()(i()().mark((function e(n){var t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("提交的表单值:",n),Oe(n),e.next=4,ze(!0,n);case 4:t=e.sent,H(t);case 6:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}();return(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)(Ee.Z,{actionRef:y,formRef:Re,columns:en,scroll:{x:1200},toolBarRender:!1,rowKey:"id",params:a()({data_type:"all",flg:"all",forbidden:"all"},Ne),request:function(){var e=l()(i()().mark((function e(n){var t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("请求参数:",JSON.stringify(n,null,2)),delete(t=a()({},n)).current,delete t.pageSize,Oe(t),e.next=7,ze(!0,t);case 7:return r=e.sent,e.abrupt("return",{data:r,success:!0,total:r.length});case 9:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),options:!1,search:{labelWidth:"auto",layout:"vertical",defaultCollapsed:!1,searchText:"查询",resetText:"重置",optionRender:function(e,n){return[(0,I.jsx)(J.ZP,{type:"primary",onClick:function(){var e,t=null===(e=n.form)||void 0===e?void 0:e.getFieldsValue();tn(t)},children:"查询"},"submit"),(0,I.jsx)(J.ZP,{onClick:function(){var e;null===(e=Re.current)||void 0===e||e.resetFields(),Oe({}),ze(!1).then((function(e){H(e)}))},children:"重置"},"reset")]}},rowSelection:{onChange:function(e,n){C(n)},getCheckboxProps:function(e){return{disabled:e.isCategory}}},tableAlertRender:function(e){var n=e.selectedRowKeys,t=e.onCleanSelected;return(0,I.jsxs)("div",{children:["已选择 ",n.length," 项",(0,I.jsx)("a",{style:{marginLeft:8},onClick:t,children:"取消选择"})]})},tableAlertOptionRender:function(){return(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)(J.ZP,{danger:!0,onClick:qe,children:"批量删除"}),(0,I.jsx)(J.ZP,{onClick:We,style:{marginLeft:8},children:"批量重置"},"batchReset")]})},size:"small"}),(0,I.jsx)(q.Z,{title:"转移文件",visible:T,onCancel:function(){return B(!1)},onOk:function(){Pe(!0)},okText:"确定",cancelText:"取消",children:(0,I.jsxs)("div",{children:[(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",height:"100%",marginBottom:16},children:[(0,I.jsx)("strong",{children:"请选择一个分类"}),(0,I.jsx)(J.ZP,{onClick:function(){o(!0)},type:"primary",children:"创建分类"})]}),(0,I.jsx)(Z.Z,{dataSource:nn,renderItem:function(e){return(0,I.jsx)(Z.Z.Item,{style:{padding:"8px 16px",borderRadius:"4px",marginBottom:"4px",backgroundColor:O===e.id?"#e6f7ff":"transparent",cursor:"pointer"},onClick:function(){z(e.id)},onContextMenu:function(e){e.preventDefault()},children:(0,I.jsx)(ne.Z,{overlay:(0,I.jsxs)(ee.Z,{onClick:function(n){return Qe(n,e.id)},children:[(0,I.jsx)(ee.Z.Item,{children:"重命名"},"rename"),(0,I.jsx)(ee.Z.Item,{children:"移动文件"},"move")]}),trigger:["contextMenu"],children:(0,I.jsx)("span",{children:ve===e.id?(0,I.jsx)(R.Z,{value:p,onChange:function(e){return f(e.target.value)},onBlur:function(){return ye(null)},onKeyPress:function(n){return function(e,n){"Enter"===e.key&&(Je(n,p),ye(null))}(n,e.id)},style:{flex:1},autoFocus:!0}):(0,I.jsx)("span",{children:e.name})})})})}})]})}),(0,I.jsx)(q.Z,{title:"确认移动",visible:Se,onOk:Ue,onCancel:function(){return Pe(!1)},style:{marginTop:80},width:400,children:(0,I.jsx)("p",{children:"确认移动到选中的分类吗？"})}),(0,I.jsx)(q.Z,{title:"重命名文件夹",visible:!!X,onCancel:function(){return U(null)},onOk:Xe,children:(0,I.jsx)(R.Z,{value:Q,onChange:function(e){return re(e.target.value)},placeholder:"输入新名称"})}),(0,I.jsxs)(q.Z,{title:"创建分类",visible:s,onCancel:Ge,footer:null,style:{marginTop:80},width:400,children:[(0,I.jsx)(R.Z,{value:p,placeholder:"请输入分类名称",onChange:function(e){return f(e.target.value)}}),(0,I.jsx)(J.ZP,{type:"primary",onClick:Ke,style:{marginTop:"10px"},children:"确定"})]}),(0,I.jsx)(q.Z,{title:"文件夹内容",visible:ie,onCancel:function(){return oe(!1)},footer:null,children:(0,I.jsx)(Le.Z,{dataSource:xe,columns:[{title:"文件名",dataIndex:"name",key:"name"},{title:"大小",dataIndex:"size",key:"size",render:function(e){return"".concat((e/1024).toFixed(2)," KB")}},{title:"上传时间",dataIndex:"created_at",key:"created_at"}],rowKey:"id"})}),(0,I.jsx)(De,{})]})},ze=t(8232),Ae=t(83062),Ve=t(38925),qe=function(e){var n=e.initialValues,t=e.onChange,r=ze.Z.useForm(),a=u()(r,1)[0];return(0,h.useEffect)((function(){a.setFieldsValue(n)}),[n]),(0,I.jsxs)(ze.Z,{form:a,layout:"vertical",onValuesChange:t,children:[(0,I.jsx)(ze.Z.Item,{label:"问题",name:"question",rules:[{required:!0}],children:(0,I.jsx)(R.Z.TextArea,{rows:3})}),(0,I.jsx)(ze.Z.Item,{label:"答案",name:"answer",rules:[{required:!0}],children:(0,I.jsx)(R.Z.TextArea,{rows:5})})]})},We=function(e){var n=e.knowledgeBaseId,t=(0,h.useRef)(),r=(0,h.useState)([]),a=u()(r,2),s=a[0],o=a[1],c=(0,h.useState)(!1),d=u()(c,2),p=d[0],f=d[1],x=(0,h.useState)(null),m=u()(x,2),g=m[0],v=m[1],y=(0,h.useState)({answer:"",question:""}),j=u()(y,2),b=j[0],w=j[1],Z=(0,h.useState)(!1),C=u()(Z,2),S=C[0],_=C[1],T=(0,h.useState)(""),R=u()(T,2),B=(R[0],R[1]),M=function(){var e=l()(i()().mark((function e(){var r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(s.length){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,e.next=5,Promise.all(s.map((function(e){return(0,P.Pq)(n,"chunkId_placeholder",e.id)})));case 5:k.ZP.success("批量删除成功"),null===(r=t.current)||void 0===r||r.reload(),o([]),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(2),k.ZP.error("批量删除失败");case 13:case"end":return e.stop()}}),e,null,[[2,10]])})));return function(){return e.apply(this,arguments)}}(),N=function(){v(null),w({answer:"",question:""}),f(!0)},E=function(){var e=l()(i()().mark((function e(){var r,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,a={question:b.question.trim(),answer:b.answer.trim()},!g){e.next=7;break}return e.next=5,updateKnowledgeChunk(g.id,a);case 5:e.next=9;break;case 7:return e.next=9,createKnowledgeChunk(n,a);case 9:k.ZP.success(g?"更新成功":"创建成功"),null===(r=t.current)||void 0===r||r.reload(),f(!1),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(0),k.ZP.error(g?"操作失败":"创建失败");case 17:case"end":return e.stop()}}),e,null,[[0,14]])})));return function(){return e.apply(this,arguments)}}(),L=[{title:"开源文件",dataIndex:"file_name",width:"20%",search:{transform:function(e){return{file_name:e}}},render:function(e){return(0,I.jsx)(Ae.Z,{overlayStyle:{maxWidth:800,minWidth:300},title:(0,I.jsx)("pre",{style:{margin:0,whiteSpace:"pre-wrap"},children:e}),children:(0,I.jsx)("span",{style:{display:"inline-block",width:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e})})}},{title:"question",dataIndex:"question",width:"20%",ellipsis:{showTitle:!0,tooltip:{overlayStyle:{width:600}}},search:{transform:function(e){return{question:e}}}},{title:"answer",dataIndex:"answer",width:"20%",search:{transform:function(e){return{file_name:e}}},render:function(e){return(0,I.jsx)(Ae.Z,{overlayStyle:{maxWidth:800,minWidth:300},title:(0,I.jsx)("pre",{style:{margin:0,whiteSpace:"pre-wrap"},children:e}),children:(0,I.jsx)("span",{style:{display:"inline-block",width:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e})})}},{title:"创建时间",dataIndex:"created_at",width:"10%",valueType:"dateTime",sorter:!0,search:!1},{title:"操作",valueType:"option",width:120,render:function(e,n){return[(0,I.jsx)(J.ZP,{type:"link",style:{display:"inline-block",width:40,textAlign:"left"},onClick:function(){return function(e){v(e),w({question:e.question,answer:e.answer}),f(!0)}(n)},children:"编辑"},"edit"),(0,I.jsx)(J.ZP,{type:"link",style:{display:"inline-block",width:60,textAlign:"left"},onClick:function(){return e=n.id,B(e),void _(!0);var e},children:"查看索引"},"view-index"),(0,I.jsx)(J.ZP,{type:"link",style:{display:"inline-block",width:40,textAlign:"left"},danger:!0,onClick:l()(i()().mark((function e(){var r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,P.Vk)(n.id);case 3:k.ZP.success("删除成功"),null===(r=t.current)||void 0===r||r.reload(),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),k.ZP.error("删除失败");case 10:case"end":return e.stop()}}),e,null,[[0,7]])}))),children:"删除"},"delete")]}}];return(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)(Ee.Z,{actionRef:t,columns:L,toolBarRender:function(){return[(0,I.jsx)(J.ZP,{type:"primary",onClick:N,children:"新建知识条目"},"create")]},request:function(){var e=l()(i()().mark((function e(t){var r,a,s;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,P.dx)(n,null!==(r=t.current)&&void 0!==r?r:1,null!==(a=t.pageSize)&&void 0!==a?a:10,t.question,t.answer);case 3:return s=e.sent,e.abrupt("return",{data:s.data||[],success:!0,total:s.total||0});case 7:return e.prev=7,e.t0=e.catch(0),k.ZP.error("获取知识索引失败"),e.abrupt("return",{data:[],success:!1,total:0});case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(n){return e.apply(this,arguments)}}(),rowKey:"id",rowSelection:{onChange:function(e,n){o(n)}},tableAlertRender:function(e){var n=e.selectedRowKeys,t=e.onCleanSelected;return(0,I.jsxs)("div",{children:["已选择 ",n.length," 项",(0,I.jsx)("a",{style:{marginLeft:8},onClick:t,children:"取消选择"})]})},tableAlertOptionRender:function(){return(0,I.jsx)(J.ZP,{danger:!0,onClick:M,children:"批量删除"})},size:"small"}),(0,I.jsxs)(q.Z,{title:g?"编辑知识条目":"新建知识条目",visible:p,onOk:E,onCancel:function(){return f(!1)},destroyOnClose:!0,children:[g?(0,I.jsx)(Ve.Z,{message:"编辑提示",description:"修改内容会自动创建一条新的索引，原索引将保留历史版本",type:"info",showIcon:!0,style:{marginBottom:16}}):(0,I.jsx)(Ve.Z,{message:"新建提示",description:"新建索引会基于内容自动创建一条优化后的索引信息",type:"info",showIcon:!0,style:{marginBottom:16}}),(0,I.jsx)(qe,{initialValues:b,onChange:function(e){return w(e)}})]}),(0,I.jsx)(q.Z,{title:"索引详情",visible:S,onCancel:function(){return _(!1)},footer:null,width:800,destroyOnClose:!0,children:(0,I.jsx)("div",{style:{maxHeight:"60vh",overflow:"auto",padding:"16px",backgroundColor:"#f5f5f5",borderRadius:"4px"},children:(0,I.jsx)("pre",{style:{whiteSpace:"pre-wrap"},children:JSON.stringify({},null,2)})})})]})},He=t(35312),Ge=t(49185),Ke=t(27484),Je=t.n(Ke),Xe=(0,C.kc)((function(e){var n=e.token;return{main:{background:n.colorBgContainer,borderRadius:n.borderRadius,"& .ant-card":{borderRadius:0}},headerList:{margin:"20px 0"},moreInfo:{display:"flex",justifyContent:"space-between",width:"200px"}}})),Ue=function(){var e=(0,te.s0)(),n=Xe().styles,t=(0,He.useSearchParams)(),r=u()(t,1)[0].get("id"),s=(0,h.useState)("files"),o=u()(s,2),c=o[0],m=o[1],g=(0,h.useState)(null),v=u()(g,2),y=v[0],j=v[1],b=(0,h.useState)(!0),k=u()(b,2),w=k[0],Z=k[1];(0,h.useEffect)((function(){var e=function(){var e=l()(i()().mark((function e(){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!r){e.next=15;break}return e.prev=1,Z(!0),e.next=5,(0,Ge.IV)(r);case 5:(n=e.sent).success?j(n.data):console.error("获取知识库数据失败"),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(1),console.error("加载知识库失败:",e.t0);case 12:return e.prev=12,Z(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[1,9,12,15]])})));return function(){return e.apply(this,arguments)}}();e()}),[r]);var C=(0,I.jsxs)(f.Z,{className:n.headerList,size:"small",column:2,children:[(0,I.jsx)(f.Z.Item,{label:"描述",children:(null==y?void 0:y.description)||"-"}),(0,I.jsx)(f.Z.Item,{label:"创建时间",children:null!=y&&y.created_at?Je()(y.created_at).format("YYYY-MM-DD HH:mm:ss"):"-"})]}),S=(0,I.jsx)("div",{className:n.moreInfo}),P={files:r?(0,I.jsx)(Oe,{knowledgeBaseId:r}):null,uploadDatasetFile:r?(0,I.jsx)(Q,{knowledgeId:r}):null,base:y?(0,I.jsx)(T,a()({},y)):null,tagManagement:y?(0,I.jsx)(re,{knowledgeBaseId:r}):null,permissionManagement:y?(0,I.jsx)(se,{knowledgeBaseId:r}):null,searchTest:y?(0,I.jsx)(Ze,{knowledgeBaseId:r}):null,graph:r?(0,I.jsx)(Me,{knowledgeBaseId:r}):null,apiService:y?(0,I.jsx)(Ne,{knowledgeBaseId:r}):null,binding:(0,I.jsx)(B,{}),parse:(0,I.jsx)(N,{}),"search-test":(0,I.jsx)(L,{}),"knowledge-index":r?(0,I.jsx)(We,{knowledgeBaseId:r}):null};return(0,I.jsx)(d._z,{title:(null==y?void 0:y.name)||"加载中...",onBack:function(){return e("/knowledgeManagement/knowledgeBase")},content:w?(0,I.jsx)("div",{children:"加载中..."}):C,extraContent:w?(0,I.jsx)("div",{children:"加载中..."}):S,tabActiveKey:c,onTabChange:function(e){m(e)},tabList:[{key:"files",tab:"文件管理"},{key:"uploadDatasetFile",tab:"上传文件"},{key:"permissionManagement",tab:"权限设置"},{key:"searchTest",tab:"检索测试"},{key:"graph",tab:"图谱"},{key:"base",tab:"基本设置"},{key:"tagManagement",tab:"文件标签"}],children:(0,I.jsx)(p.f,{children:(0,I.jsx)("div",{className:n.main,children:(0,I.jsx)(x.Z,{bordered:!1,style:{padding:0},children:P[c]})})})})}}}]);