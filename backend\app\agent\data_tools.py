#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
微信公众号数据获取脚本
从ES中获取指定时间范围内的微信公众号数据，提取source字段，并保存为JSON数组文件。
"""

import json
import logging
import os
import time
import traceback
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional

from elasticsearch import Elasticsearch

# 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ES配置
TARGET_ES_HOST = "**************"
TARGET_ES_PORT = 9600
TARGET_ES_INDEX = "pro_mcp_data_weixin"

# 公众号ID列表
BIZ_LIST = [
    "MzA4MDY1NTUyMg==",
    "MzA5OTY5MzM4Ng==",
    "Mzg2Mjg1NTg3NA==",
    "Mzg4NTEwMzA5NQ==",
    "Mzg5MjU4MDkyMw==",
    "MzI4NTU0NDE4Mw==",
    "MzI5MzQxOTI0MQ==",
    "MzIwMDI3NjM2Mg==",
    "MzIzMjc3NTYyNQ==",
    "Mzk0NjUwMDIxOQ==",
    "MzkxMDYyNzExMA==",
    "MzkzNTYzMDYxMg==",
    "MzU3MDMwODc2MA==",
    "MzU4NzcwNDcxOA==",
    "MzU4ODM4NzI5Nw==",
    "MzU5MzkzMTY3Mg==",
    "MzUxMDk5NDgwNQ==",
    "MzUxMDkyMzA4Mw==",
    "MzUzMzEyODIyMA==",
    "MzUzNDcxMDgzNg==",
    "MzA3MTIzNDcwMg==",
    "MzA3NjU1NTQwMA==",
    "MzA4MzY2ODYwMw==",
    "MzAwNzMxODYyNg==",
    "Mzg4NDU2MDM3Mw==",
    "MzI1NzAwODc3Nw==",
    "MzU3MDMwODc2MA==",
    "MzU3NTYyNTIyMQ==",
    "MzUzMzYwODI2MA=="
]

def build_query_body(start_time, end_time, biz=None):
    """构建ES查询体"""
    body = {
        "query": {
            "bool": {
                "filter": [
                    {
                        "range": {
                            "publishtime": {
                                "gte": start_time,
                                "lte": end_time
                             }
                        }
                    }
                ]
            }
        },
        "sort": [{"publishtime": "desc"}]  # 按发布时间降序排序
    }

    # 如果指定了biz参数，则添加biz过滤条件
    if biz:
        # 如果biz是字符串，则使用term查询
        if isinstance(biz, str):
            body["query"]["bool"]["filter"].append({
                "term": {
                    "site_id": {
                        "value": biz
                    }
                }
            })
        # 如果biz是列表，则使用terms查询
        elif isinstance(biz, list) and len(biz) > 0:
            body["query"]["bool"]["filter"].append({
                "terms": {
                    "site_id": biz
                }
            })

    return body

def get_es_scroll_data_batched(index, query_body, batch_size=1000, es_host=None, es_port=None, max_retries=3):
    """滚动查询ES数据，并以批次方式返回"""
    if not es_host:
        es_host = TARGET_ES_HOST
    if not es_port:
        es_port = TARGET_ES_PORT

    retry_count = 0

    while retry_count < max_retries:
        es = None
        sid = None
        try:
            # 创建ES连接
            es = Elasticsearch([f"{es_host}:{es_port}"], timeout=60)
            
            # 检查ES连接
            if not es.ping():
                raise ConnectionError("无法连接到 Elasticsearch")

            # 初始搜索
            result = es.search(index=index, scroll='10m', body=query_body, size=batch_size, request_timeout=3600)
            sid = result['_scroll_id']
            scroll_size = result['hits']['total']['value']
            logger.info(f"索引 {index} 总数据量: {scroll_size}")

            # 如果有结果，返回第一批数据
            if len(result['hits']['hits']) > 0:
                yield result['hits']['hits']

            # 继续滚动直到没有更多数据
            scroll_count = len(result['hits']['hits'])
            while scroll_count > 0:
                try:
                    result = es.scroll(scroll_id=sid, scroll='10m', request_timeout=3600)
                    batch_data = result['hits']['hits']
                    scroll_count = len(batch_data)
                    if scroll_count == 0:
                        break
                    yield batch_data
                except Exception as scroll_error:
                    logger.error(f"滚动查询时出错: {str(scroll_error)}")
                    break

            # 如果成功完成，跳出重试循环
            break

        except Exception as e:
            retry_count += 1
            logger.error(f"获取索引 {index} 数据时出错 (尝试 {retry_count}/{max_retries}): {str(e)}")
            if retry_count < max_retries:
                logger.info(f"等待 5 秒后重试...")
                time.sleep(5)
            else:
                logger.error(f"达到最大重试次数，放弃获取数据")
                raise
        finally:
            if sid and es:
                try:
                    es.clear_scroll(scroll_id=sid)
                except:
                    pass

            if es:
                try:
                    es.close()
                except:
                    pass
        
    logger.info(f"索引 {index} 查询完成")

def get_all_sources(days=3):
    """获取指定天数内所有公众号文章的_source，并返回一个列表。"""
    logger.info(f"开始获取最近 {days} 天的所有公众号文章...")

    # 1. 计算时间范围
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)
    end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
    start_time_str = start_time.strftime("%Y-%m-%d %H:%M:%S")

    # 2. 构建查询
    # 使用 BIZ_LIST 来查询所有指定的公众号
    query_body = build_query_body(start_time_str, end_time_str, biz=BIZ_LIST)

    all_sources = []
    try:
        # 3. 使用滚动查询获取数据
        data_generator = get_es_scroll_data_batched(
            index=TARGET_ES_INDEX,
            query_body=query_body
        )

        # 4. 提取 _source 并移除 embedding 字段
        for batch in data_generator:
            for hit in batch:
                if '_source' in hit:
                    source_data = hit['_source']
                    # 移除 embedding 字段（如果存在）
                    if 'embedding' in source_data:
                        del source_data['embedding']
                    all_sources.append(source_data)
        
        logger.info(f"成功获取 {len(all_sources)} 篇文章。")
        return all_sources

    except Exception as e:
        logger.error(f"获取数据时出错: {e}")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # 获取最近3天的报告数据，并将source保存为JSON数组
    days = 3
    all_sources = get_all_sources(days)
    
    if all_sources is not None:
        # 保存为JSON文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        json_file = f"weixin_all_sources_{timestamp}.json"
        
        try:
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(all_sources, f, ensure_ascii=False, indent=2)
            
            logger.info(f"数据已保存到 {json_file}")
            print(f"数据已成功保存到 {json_file}")
        except Exception as e:
            logger.error(f"保存JSON文件时出错: {str(e)}")
            traceback.print_exc()
    else:
        logger.error("无法获取有效数据")
