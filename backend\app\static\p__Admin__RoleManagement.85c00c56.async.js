"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7535],{51042:function(e,n,r){var t=r(1413),a=r(67294),u=r(42110),s=r(91146),c=function(e,n){return a.createElement(s.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:n,icon:u.Z}))},o=a.forwardRef(c);n.Z=o},5966:function(e,n,r){var t=r(97685),a=r(1413),u=r(91),s=r(21770),c=r(47019),o=r(55241),i=r(98423),p=r(67294),l=r(62633),f=r(85893),d=["fieldProps","proFieldProps"],h=["fieldProps","proFieldProps"],v="text",m=function(e){var n=(0,s.Z)(e.open||!1,{value:e.open,onChange:e.onOpenChange}),r=(0,t.Z)(n,2),u=r[0],i=r[1];return(0,f.jsx)(c.Z.Item,{shouldUpdate:!0,noStyle:!0,children:function(n){var r,t=n.getFieldValue(e.name||[]);return(0,f.jsx)(o.Z,(0,a.Z)((0,a.Z)({getPopupContainer:function(e){return e&&e.parentNode?e.parentNode:e},onOpenChange:function(e){return i(e)},content:(0,f.jsxs)("div",{style:{padding:"4px 0"},children:[null===(r=e.statusRender)||void 0===r?void 0:r.call(e,t),e.strengthText?(0,f.jsx)("div",{style:{marginTop:10},children:(0,f.jsx)("span",{children:e.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},e.popoverProps),{},{open:u,children:e.children}))}})},g=function(e){var n=e.fieldProps,r=e.proFieldProps,t=(0,u.Z)(e,d);return(0,f.jsx)(l.Z,(0,a.Z)({valueType:v,fieldProps:n,filedConfig:{valueType:v},proFieldProps:r},t))};g.Password=function(e){var n=e.fieldProps,r=e.proFieldProps,s=(0,u.Z)(e,h),c=(0,p.useState)(!1),o=(0,t.Z)(c,2),d=o[0],g=o[1];return null!=n&&n.statusRender&&s.name?(0,f.jsx)(m,{name:s.name,statusRender:null==n?void 0:n.statusRender,popoverProps:null==n?void 0:n.popoverProps,strengthText:null==n?void 0:n.strengthText,open:d,onOpenChange:g,children:(0,f.jsx)("div",{children:(0,f.jsx)(l.Z,(0,a.Z)({valueType:"password",fieldProps:(0,a.Z)((0,a.Z)({},(0,i.Z)(n,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(e){var r;null==n||null===(r=n.onBlur)||void 0===r||r.call(n,e),g(!1)},onClick:function(e){var r;null==n||null===(r=n.onClick)||void 0===r||r.call(n,e),g(!0)}}),proFieldProps:r,filedConfig:{valueType:v}},s))})}):(0,f.jsx)(l.Z,(0,a.Z)({valueType:"password",fieldProps:n,proFieldProps:r,filedConfig:{valueType:v}},s))},g.displayName="ProFormComponent",n.Z=g},27504:function(e,n,r){r.r(n),r.d(n,{default:function(){return S}});var t=r(9783),a=r.n(t),u=r(19632),s=r.n(u),c=r(15009),o=r.n(c),i=r(97857),p=r.n(i),l=r(99289),f=r.n(l),d=r(5574),h=r.n(d),v=r(67294),m=r(97131),g=r(12453),y=r(2453),x=r(17788),w=r(83622),k=r(51042),b=r(69044),Z=r(37476),P=r(5966),T=r(63496),M=r(85893),C=function(e){var n=e.formRef,r=e.onSubmit,t=e.modalVisible,u=e.values,s=e.treeData,c=e.checkedKeys,i=e.onTreeCheck,l=e.onCancel;return(0,M.jsxs)(Z.Y,{formRef:n,title:null!=u&&u.id?"编辑角色":"新建角色",visible:t,onVisibleChange:function(e){e||l()},onFinish:function(){var e=f()(o()().mark((function e(n){var t,s;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=p()(p()({},n),{},{id:null==u?void 0:u.id,access:c.reduce((function(e,n){return p()(p()({},e),{},a()({},n,!0))}),{})}),e.next=3,r(t);case 3:return s=e.sent,e.abrupt("return",s);case 5:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),initialValues:u,children:[(0,M.jsx)(P.Z,{name:"id",hidden:!0}),(0,M.jsx)(P.Z,{name:"name",label:"角色名称",placeholder:"请输入角色名称",rules:[{required:!0,message:"角色名称为必填项"}]}),(0,M.jsx)(P.Z,{name:"description",label:"描述",placeholder:"请输入角色描述"}),(0,M.jsx)(T.Z,{checkable:!0,onCheck:function(e){return i(e)},checkedKeys:c,treeData:s})]})},j=r(76772),S=function(){var e=(0,j.useIntl)(),n=(0,v.useState)(!1),r=h()(n,2),t=r[0],u=r[1],c=(0,v.useState)(!1),i=h()(c,2),l=i[0],d=i[1],Z=(0,v.useState)(void 0),P=h()(Z,2),T=P[0],S=P[1],E=(0,v.useState)([]),N=h()(E,2),R=N[0],A=N[1],K=(0,v.useRef)(),F=(0,v.useRef)(),O=(0,v.useState)([]),D=h()(O,2),I=D[0],_=D[1],G=(0,v.useState)({}),L=h()(G,2),V=L[0],W=L[1],U=(0,v.useState)({}),q=h()(U,2),z=q[0],B=q[1],Y=function e(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=arguments.length>3?arguments[3]:void 0,u=arguments.length>4?arguments[4]:void 0;return n.forEach((function(n){var s=n.key,c=n.children;a&&(r[a]||(r[a]=[]),r[a].push(s));var o=void 0;if(s.startsWith("canAccess")&&!a){var i=s.replace("canAccess","").charAt(0).toLowerCase()+s.replace("canAccess","").slice(1);o="menu.".concat(i)}else if(s.startsWith("canAccess")&&a&&u){var p=s.replace("canAccess","").charAt(0).toLowerCase()+s.replace("canAccess","").slice(1);o="".concat(u,".").concat(p)}t[s]={parentKey:a,menuKey:o,children:{}},c&&c.length>0&&e(c,r,t,s,o)})),[r,t]},$=function(n){var r;if(!n.startsWith("canAccess"))return n;var t=z[n];if(!t||!t.menuKey)return n.replace("canAccess","").replace(/([A-Z])/g," $1").trim();var a=e.formatMessage({id:t.menuKey},{defaultMessage:""});if(t.parentKey&&null!==(r=z[t.parentKey])&&void 0!==r&&r.menuKey){var u=z[t.parentKey].menuKey,s=e.formatMessage({id:u},{defaultMessage:""});if(s&&s!==u&&a&&a!==t.menuKey)return"".concat(s," - ").concat(a)}if(a&&a!==t.menuKey)return a;var c=n.replace("canAccess",""),o=e.formatMessage({id:c},{defaultMessage:""});if(o&&o!==c)return o;var i=c.charAt(0).toLowerCase()+c.slice(1),p="menu.".concat(i),l=e.formatMessage({id:p},{defaultMessage:""});return l&&l!==p?l:c.replace(/([A-Z])/g," $1").trim()};(0,v.useEffect)((function(){var e=function(){var e=f()(o()().mark((function e(){var n,r,t,a,u,s;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,b.CW)();case 3:n=e.sent,console.log("data===>",n),r=Y(n),t=h()(r,2),a=t[0],u=t[1],console.log("newNodeRelations===>",a),console.log("newMenuRelations===>",u),W(a),B(u),s=function e(n){return n.map((function(n){return p()(p()({},n),{},{title:$(n.key),children:n.children?e(n.children):void 0})}))}(n),_(s),e.next=18;break;case 15:e.prev=15,e.t0=e.catch(0),y.ZP.error("获取权限树据失败，请重试");case 18:case"end":return e.stop()}}),e,null,[[0,15]])})));return function(){return e.apply(this,arguments)}}();e()}),[e]),(0,v.useEffect)((function(){var e;t||(null===(e=F.current)||void 0===e||e.resetFields(),A([]))}),[t]),(0,v.useEffect)((function(){l||(S(void 0),A([]))}),[l]);var H=function(e){var n=function e(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set;if(r.has(n))return[];r.add(n);for(var t=[],a=0,u=Object.entries(V);a<u.length;a++){var c=h()(u[a],2),o=c[0],i=c[1];i.includes(n)&&(t=[].concat(s()(t),[o],s()(e(o,r))))}return t},r=new Set;e.forEach((function(e){n(e).forEach((function(e){return r.add(e)}))}));var t=s()(new Set([].concat(s()(e),s()(Array.from(r)))));return console.log("原始选中keys:",e),console.log("包含父节点的完整keys:",t),t},J=function(){var n=f()(o()().mark((function n(r){var t,s,c;return o()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return t=y.ZP.loading(e.formatMessage({id:"adding",defaultMessage:"正在添加"})),n.prev=1,c=H(R),n.next=5,(0,b._d)(p()(p()({},r),{},{access:c.reduce((function(e,n){return p()(p()({},e),{},a()({},n,!0))}),{})}));case 5:return t(),y.ZP.success(e.formatMessage({id:"add.success",defaultMessage:"添加成功"})),u(!1),null===(s=K.current)||void 0===s||s.reload(),n.abrupt("return",!0);case 12:return n.prev=12,n.t0=n.catch(1),t(),y.ZP.error(e.formatMessage({id:"add.fail",defaultMessage:"添加失败，请重试"})),n.abrupt("return",!1);case 17:case"end":return n.stop()}}),n,null,[[1,12]])})));return function(e){return n.apply(this,arguments)}}(),Q=function(){var n=f()(o()().mark((function n(r){var t,u,s,c;return o()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(t=y.ZP.loading(e.formatMessage({id:"updating",defaultMessage:"正在更新"})),n.prev=1,r.id){n.next=4;break}throw new Error("Role ID is undefined");case 4:return s=H(R),c=s.reduce((function(e,n){return p()(p()({},e),{},a()({},n,!0))}),{}),n.next=8,(0,b.ul)(r.id,p()(p()({},r),{},{access:c}));case 8:return t(),y.ZP.success(e.formatMessage({id:"update.success",defaultMessage:"更新成功"})),d(!1),S(void 0),null===(u=K.current)||void 0===u||u.reload(),n.abrupt("return",!0);case 16:return n.prev=16,n.t0=n.catch(1),t(),y.ZP.error(e.formatMessage({id:"update.fail",defaultMessage:"更新失败，请重试"})),n.abrupt("return",!1);case 21:case"end":return n.stop()}}),n,null,[[1,16]])})));return function(e){return n.apply(this,arguments)}}(),X=function(){var e=f()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:x.Z.confirm({title:"确认删除",content:"确定要删除这个角色吗？",okText:"确认",cancelText:"取消",onOk:function(){var e=f()(o()().mark((function e(){var r,t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=y.ZP.loading("正在删除"),e.prev=1,e.next=4,(0,b.Rd)(n.id);case 4:return r(),y.ZP.success("删除成功"),null===(t=K.current)||void 0===t||t.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),r(),y.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),ee=function(){var e=f()(o()().mark((function e(n){var r,t,a,u;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,b.cY)(n.id);case 3:t=e.sent,console.log("roleDetails===>",t),d(!0),S(t),a=Object.entries(t.access||{}).filter((function(e){return h()(e,2)[1]})).map((function(e){return h()(e,1)[0]})),console.log("selectedKeys===>",a),u=a.filter((function(e){return void 0!==z[e]||Object.values(V).some((function(n){return n.includes(e)}))})),console.log("编辑角色权限原始数据:",a),console.log("有效的权限keys:",u),A(u),null===(r=F.current)||void 0===r||r.setFieldsValue({name:t.name,description:t.description}),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(0),y.ZP.error("获取角色详情失败，请重试");case 19:case"end":return e.stop()}}),e,null,[[0,16]])})));return function(n){return e.apply(this,arguments)}}(),ne=[{title:e.formatMessage({id:"role.name",defaultMessage:"名称"}),dataIndex:"name",valueType:"text"},{title:e.formatMessage({id:"role.description",defaultMessage:"描述"}),dataIndex:"description",valueType:"textarea"},{title:e.formatMessage({id:"role.createdAt",defaultMessage:"创建时间"}),dataIndex:"created_at",valueType:"dateTime",search:!1},{title:e.formatMessage({id:"role.deletable",defaultMessage:"可删除"}),dataIndex:"deletable",valueType:"text",search:!1,render:function(e){return e?"是":"否"}},{title:e.formatMessage({id:"role.action",defaultMessage:"操作"}),dataIndex:"option",valueType:"option",render:function(n,r){return[(0,M.jsx)(w.ZP,{type:"link",onClick:function(){return ee(r)},children:e.formatMessage({id:"edit",defaultMessage:"编辑"})},"edit-".concat(r.id)),(0,M.jsx)(w.ZP,{type:"link",danger:!0,onClick:function(){return X(r)},disabled:!r.deletable,children:e.formatMessage({id:"delete",defaultMessage:"删除"})},"delete-".concat(r.id))]}}];return(0,M.jsxs)(m._z,{children:[(0,M.jsx)(g.Z,{headerTitle:e.formatMessage({id:"menu.roleManagement",defaultMessage:"角色管理"}),actionRef:K,rowKey:"id",search:{labelWidth:120},toolBarRender:function(){return[(0,M.jsxs)(w.ZP,{type:"primary",onClick:function(){u(!0),A([])},children:[(0,M.jsx)(k.Z,{})," ",e.formatMessage({id:"new",defaultMessage:"新建"})]},"primary")]},request:function(){var e=f()(o()().mark((function e(n){var r;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,b.F3)(n);case 2:return r=e.sent,e.abrupt("return",{data:r.data||[],success:r.success||!0,total:r.total||0});case 4:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),columns:ne,pagination:{pageSize:10}}),(0,M.jsx)(C,{modalVisible:t,onCancel:function(){return u(!1)},onSubmit:J,formRef:F,values:{},treeData:I,checkedKeys:R,onTreeCheck:A}),T&&l&&I.length>0&&(0,M.jsx)(C,{modalVisible:l,onCancel:function(){d(!1)},onSubmit:Q,formRef:F,values:T,treeData:I,checkedKeys:R,onTreeCheck:A})]})}},69044:function(e,n,r){r.d(n,{CW:function(){return I},F3:function(){return E},Nq:function(){return m},Rd:function(){return O},Rf:function(){return f},Rp:function(){return P},_d:function(){return R},az:function(){return w},cY:function(){return G},cn:function(){return h},h8:function(){return y},iE:function(){return j},jA:function(){return b},mD:function(){return M},ul:function(){return K},w1:function(){return V},wG:function(){return U}});var t=r(5574),a=r.n(t),u=r(97857),s=r.n(u),c=r(15009),o=r.n(c),i=r(99289),p=r.n(i),l=r(78158);function f(e){return d.apply(this,arguments)}function d(){return(d=p()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/users",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function h(e){return v.apply(this,arguments)}function v(){return(v=p()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/users",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function m(e){return g.apply(this,arguments)}function g(){return(g=p()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/users/".concat(n.id),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function y(e){return x.apply(this,arguments)}function x(){return(x=p()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/users/".concat(n),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function w(e,n){return k.apply(this,arguments)}function k(){return(k=p()(o()().mark((function e(n,r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/users/changeStatus",{method:"POST",data:{id:n,status:r}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function b(e){return Z.apply(this,arguments)}function Z(){return(Z=p()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/groups",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function P(e){return T.apply(this,arguments)}function T(){return(T=p()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/groups",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function M(e){return C.apply(this,arguments)}function C(){return(C=p()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/groups/".concat(n.id),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function j(e,n){return S.apply(this,arguments)}function S(){return(S=p()(o()().mark((function e(n,r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/groups/".concat(n),s()({method:"DELETE"},r)));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function E(e){return N.apply(this,arguments)}function N(){return(N=p()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/roles",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function R(e){return A.apply(this,arguments)}function A(){return(A=p()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/roles",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function K(e,n){return F.apply(this,arguments)}function F(){return(F=p()(o()().mark((function e(n,r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/roles/".concat(n),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function O(e){return D.apply(this,arguments)}function D(){return(D=p()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/roles/".concat(n),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function I(){return _.apply(this,arguments)}function _(){return(_=p()(o()().mark((function e(){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/role/tree",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function G(e){return L.apply(this,arguments)}function L(){return(L=p()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/roles/".concat(n),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function V(e){return W.apply(this,arguments)}function W(){return(W=p()(o()().mark((function e(n){var r;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=new URLSearchParams,Object.entries(n).forEach((function(e){var n=a()(e,2),t=n[0],u=n[1];r.append(t,String(u))})),e.abrupt("return",(0,l.N)("/api/system/config?".concat(r.toString()),{method:"POST"}));case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function U(e){return q.apply(this,arguments)}function q(){return(q=p()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/useActiveCases",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}}}]);