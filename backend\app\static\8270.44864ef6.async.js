"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8270,5746],{71879:function(e,o){o.Z={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M885.2 446.3l-.2-.8-112.2-285.1c-5-16.1-19.9-27.2-36.8-27.2H281.2c-17 0-32.1 11.3-36.9 27.6L139.4 443l-.3.7-.2.8c-1.3 4.9-1.7 9.9-1 14.8-.1 1.6-.2 3.2-.2 4.8V830a60.9 60.9 0 0060.8 60.8h627.2c33.5 0 60.8-27.3 60.9-60.8V464.1c0-1.3 0-2.6-.1-3.7.4-4.9 0-9.6-1.3-14.1zm-295.8-43l-.3 15.7c-.8 44.9-31.8 75.1-77.1 75.1-22.1 0-41.1-7.1-54.8-20.6S436 441.2 435.6 419l-.3-15.7H229.5L309 210h399.2l81.7 193.3H589.4zm-375 76.8h157.3c24.3 57.1 76 90.8 140.4 90.8 33.7 0 65-9.4 90.3-27.2 22.2-15.6 39.5-37.4 50.7-63.6h156.5V814H214.4V480.1z"}}]},name:"inbox",theme:"outlined"}},34804:function(e,o,r){var n=r(1413),t=r(67294),l=r(66023),a=r(91146),s=function(e,o){return t.createElement(a.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:o,icon:l.Z}))},i=t.forwardRef(s);o.Z=i},33914:function(e,o,r){var n=r(1413),t=r(67294),l=r(71879),a=r(91146),s=function(e,o){return t.createElement(a.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:o,icon:l.Z}))},i=t.forwardRef(s);o.Z=i},64317:function(e,o,r){var n=r(1413),t=r(91),l=r(22270),a=r(67294),s=r(66758),i=r(62633),c=r(85893),p=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","showSearch","options"],u=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","options"],d=function(e,o){var r=e.fieldProps,u=e.children,d=e.params,f=e.proFieldProps,m=e.mode,g=e.valueEnum,v=e.request,h=e.showSearch,b=e.options,y=(0,t.Z)(e,p),C=(0,a.useContext)(s.Z);return(0,c.jsx)(i.Z,(0,n.Z)((0,n.Z)({valueEnum:(0,l.h)(g),request:v,params:d,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:(0,n.Z)({options:b,mode:m,showSearch:h,getPopupContainer:C.getPopupContainer},r),ref:o,proFieldProps:f},y),{},{children:u}))},f=a.forwardRef((function(e,o){var r=e.fieldProps,p=e.children,d=e.params,f=e.proFieldProps,m=e.mode,g=e.valueEnum,v=e.request,h=e.options,b=(0,t.Z)(e,u),y=(0,n.Z)({options:h,mode:m||"multiple",labelInValue:!0,showSearch:!0,suffixIcon:null,autoClearSearchValue:!0,optionLabelProp:"label"},r),C=(0,a.useContext)(s.Z);return(0,c.jsx)(i.Z,(0,n.Z)((0,n.Z)({valueEnum:(0,l.h)(g),request:v,params:d,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:(0,n.Z)({getPopupContainer:C.getPopupContainer},y),ref:o,proFieldProps:f},b),{},{children:p}))})),m=a.forwardRef(d);m.SearchSelect=f,m.displayName="ProFormComponent",o.Z=m},90672:function(e,o,r){var n=r(1413),t=r(91),l=r(67294),a=r(62633),s=r(85893),i=["fieldProps","proFieldProps"],c=function(e,o){var r=e.fieldProps,l=e.proFieldProps,c=(0,t.Z)(e,i);return(0,s.jsx)(a.Z,(0,n.Z)({ref:o,valueType:"textarea",fieldProps:r,proFieldProps:l},c))};o.Z=l.forwardRef(c)},5966:function(e,o,r){var n=r(97685),t=r(1413),l=r(91),a=r(21770),s=r(47019),i=r(55241),c=r(98423),p=r(67294),u=r(62633),d=r(85893),f=["fieldProps","proFieldProps"],m=["fieldProps","proFieldProps"],g="text",v=function(e){var o=(0,a.Z)(e.open||!1,{value:e.open,onChange:e.onOpenChange}),r=(0,n.Z)(o,2),l=r[0],c=r[1];return(0,d.jsx)(s.Z.Item,{shouldUpdate:!0,noStyle:!0,children:function(o){var r,n=o.getFieldValue(e.name||[]);return(0,d.jsx)(i.Z,(0,t.Z)((0,t.Z)({getPopupContainer:function(e){return e&&e.parentNode?e.parentNode:e},onOpenChange:function(e){return c(e)},content:(0,d.jsxs)("div",{style:{padding:"4px 0"},children:[null===(r=e.statusRender)||void 0===r?void 0:r.call(e,n),e.strengthText?(0,d.jsx)("div",{style:{marginTop:10},children:(0,d.jsx)("span",{children:e.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},e.popoverProps),{},{open:l,children:e.children}))}})},h=function(e){var o=e.fieldProps,r=e.proFieldProps,n=(0,l.Z)(e,f);return(0,d.jsx)(u.Z,(0,t.Z)({valueType:g,fieldProps:o,filedConfig:{valueType:g},proFieldProps:r},n))};h.Password=function(e){var o=e.fieldProps,r=e.proFieldProps,a=(0,l.Z)(e,m),s=(0,p.useState)(!1),i=(0,n.Z)(s,2),f=i[0],h=i[1];return null!=o&&o.statusRender&&a.name?(0,d.jsx)(v,{name:a.name,statusRender:null==o?void 0:o.statusRender,popoverProps:null==o?void 0:o.popoverProps,strengthText:null==o?void 0:o.strengthText,open:f,onOpenChange:h,children:(0,d.jsx)("div",{children:(0,d.jsx)(u.Z,(0,t.Z)({valueType:"password",fieldProps:(0,t.Z)((0,t.Z)({},(0,c.Z)(o,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(e){var r;null==o||null===(r=o.onBlur)||void 0===r||r.call(o,e),h(!1)},onClick:function(e){var r;null==o||null===(r=o.onClick)||void 0===r||r.call(o,e),h(!0)}}),proFieldProps:r,filedConfig:{valueType:g}},a))})}):(0,d.jsx)(u.Z,(0,t.Z)({valueType:"password",fieldProps:o,proFieldProps:r,filedConfig:{valueType:g}},a))},h.displayName="ProFormComponent",o.Z=h},53531:function(e,o,r){r.d(o,{Z:function(){return k}});var n=r(67294),t=r(56790),l=r(75164),a=r(57838),s=r(96159),i=r(93967),c=r.n(i),p=r(64217),u=r(53124),d=r(48054);var f=e=>{const{value:o,formatter:r,precision:t,decimalSeparator:l,groupSeparator:a="",prefixCls:s}=e;let i;if("function"==typeof r)i=r(o);else{const e=String(o),r=e.match(/^(-?)(\d*)(\.(\d+))?$/);if(r&&"-"!==e){const e=r[1];let o=r[2]||"0",c=r[4]||"";o=o.replace(/\B(?=(\d{3})+(?!\d))/g,a),"number"==typeof t&&(c=c.padEnd(t,"0").slice(0,t>0?t:0)),c&&(c=`${l}${c}`),i=[n.createElement("span",{key:"int",className:`${s}-content-value-int`},e,o),c&&n.createElement("span",{key:"decimal",className:`${s}-content-value-decimal`},c)]}else i=e}return n.createElement("span",{className:`${s}-content-value`},i)},m=r(14747),g=r(83559),v=r(83262);const h=e=>{const{componentCls:o,marginXXS:r,padding:n,colorTextDescription:t,titleFontSize:l,colorTextHeading:a,contentFontSize:s,fontFamily:i}=e;return{[o]:Object.assign(Object.assign({},(0,m.Wf)(e)),{[`${o}-title`]:{marginBottom:r,color:t,fontSize:l},[`${o}-skeleton`]:{paddingTop:n},[`${o}-content`]:{color:a,fontSize:s,fontFamily:i,[`${o}-content-value`]:{display:"inline-block",direction:"ltr"},[`${o}-content-prefix, ${o}-content-suffix`]:{display:"inline-block"},[`${o}-content-prefix`]:{marginInlineEnd:r},[`${o}-content-suffix`]:{marginInlineStart:r}}})}};var b=(0,g.I$)("Statistic",(e=>{const o=(0,v.IX)(e,{});return[h(o)]}),(e=>{const{fontSizeHeading3:o,fontSize:r}=e;return{titleFontSize:r,contentFontSize:o}})),y=function(e,o){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&o.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var t=0;for(n=Object.getOwnPropertySymbols(e);t<n.length;t++)o.indexOf(n[t])<0&&Object.prototype.propertyIsEnumerable.call(e,n[t])&&(r[n[t]]=e[n[t]])}return r};var C=e=>{const{prefixCls:o,className:r,rootClassName:t,style:l,valueStyle:a,value:s=0,title:i,valueRender:m,prefix:g,suffix:v,loading:h=!1,formatter:C,precision:x,decimalSeparator:P=".",groupSeparator:S=",",onMouseEnter:Z,onMouseLeave:$}=e,O=y(e,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:k,direction:j,className:w,style:E}=(0,u.dj)("statistic"),T=k("statistic",o),[F,N,I]=b(T),z=n.createElement(f,{decimalSeparator:P,groupSeparator:S,prefixCls:T,formatter:C,precision:x,value:s}),R=c()(T,{[`${T}-rtl`]:"rtl"===j},w,r,t,N,I),B=(0,p.Z)(O,{aria:!0,data:!0});return F(n.createElement("div",Object.assign({},B,{className:R,style:Object.assign(Object.assign({},E),l),onMouseEnter:Z,onMouseLeave:$}),i&&n.createElement("div",{className:`${T}-title`},i),n.createElement(d.Z,{paragraph:!1,loading:h,className:`${T}-skeleton`},n.createElement("div",{style:a,className:`${T}-content`},g&&n.createElement("span",{className:`${T}-content-prefix`},g),m?m(z):z,v&&n.createElement("span",{className:`${T}-content-suffix`},v)))))};const x=[["Y",31536e6],["M",2592e6],["D",864e5],["H",36e5],["m",6e4],["s",1e3],["S",1]];function P(e,o,r){const{format:n=""}=o,t=new Date(e).getTime(),l=Date.now();return function(e,o){let r=e;const n=/\[[^\]]*]/g,t=(o.match(n)||[]).map((e=>e.slice(1,-1))),l=o.replace(n,"[]"),a=x.reduce(((e,o)=>{let[n,t]=o;if(e.includes(n)){const o=Math.floor(r/t);return r-=o*t,e.replace(new RegExp(`${n}+`,"g"),(e=>{const r=e.length;return o.toString().padStart(r,"0")}))}return e}),l);let s=0;return a.replace(n,(()=>{const e=t[s];return s+=1,e}))}(r?Math.max(t-l,0):Math.max(l-t,0),n)}var S=function(e,o){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&o.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var t=0;for(n=Object.getOwnPropertySymbols(e);t<n.length;t++)o.indexOf(n[t])<0&&Object.prototype.propertyIsEnumerable.call(e,n[t])&&(r[n[t]]=e[n[t]])}return r};var Z=e=>{const{value:o,format:r="HH:mm:ss",onChange:i,onFinish:c,type:p}=e,u=S(e,["value","format","onChange","onFinish","type"]),d="countdown"===p,f=(0,a.Z)(),m=(0,t.zX)((()=>{const e=Date.now(),r=function(e){return new Date(e).getTime()}(o);f();return null==i||i(d?r-e:e-r),!(d&&r<e)||(null==c||c(),!1)}));n.useEffect((()=>{let e;const o=()=>{e=(0,l.Z)((()=>{m()&&o()}))};return o(),()=>l.Z.cancel(e)}),[o,d]);return n.createElement(C,Object.assign({},u,{value:o,valueRender:e=>(0,s.Tm)(e,{title:void 0}),formatter:(e,o)=>P(e,Object.assign(Object.assign({},o),{format:r}),d)}))};const $=e=>n.createElement(Z,Object.assign({},e,{type:"countdown"}));var O=n.memo($);C.Timer=Z,C.Countdown=O;var k=C},66309:function(e,o,r){r.d(o,{Z:function(){return E}});var n=r(67294),t=r(93967),l=r.n(t),a=r(98423),s=r(98787),i=r(69760),c=r(96159),p=r(45353),u=r(53124),d=r(11568),f=r(15063),m=r(14747),g=r(83262),v=r(83559);const h=e=>{const{lineWidth:o,fontSizeIcon:r,calc:n}=e,t=e.fontSizeSM;return(0,g.IX)(e,{tagFontSize:t,tagLineHeight:(0,d.bf)(n(e.lineHeightSM).mul(t).equal()),tagIconSize:n(r).sub(n(o).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},b=e=>({defaultBg:new f.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var y=(0,v.I$)("Tag",(e=>(e=>{const{paddingXXS:o,lineWidth:r,tagPaddingHorizontal:n,componentCls:t,calc:l}=e,a=l(n).sub(r).equal(),s=l(o).sub(r).equal();return{[t]:Object.assign(Object.assign({},(0,m.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:a,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,d.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${t}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${t}-close-icon`]:{marginInlineStart:s,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${t}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${t}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:a}}),[`${t}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(h(e))),b),C=function(e,o){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&o.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var t=0;for(n=Object.getOwnPropertySymbols(e);t<n.length;t++)o.indexOf(n[t])<0&&Object.prototype.propertyIsEnumerable.call(e,n[t])&&(r[n[t]]=e[n[t]])}return r};const x=n.forwardRef(((e,o)=>{const{prefixCls:r,style:t,className:a,checked:s,onChange:i,onClick:c}=e,p=C(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:d,tag:f}=n.useContext(u.E_),m=d("tag",r),[g,v,h]=y(m),b=l()(m,`${m}-checkable`,{[`${m}-checkable-checked`]:s},null==f?void 0:f.className,a,v,h);return g(n.createElement("span",Object.assign({},p,{ref:o,style:Object.assign(Object.assign({},t),null==f?void 0:f.style),className:b,onClick:e=>{null==i||i(!s),null==c||c(e)}})))}));var P=x,S=r(98719);var Z=(0,v.bk)(["Tag","preset"],(e=>(e=>(0,S.Z)(e,((o,r)=>{let{textColor:n,lightBorderColor:t,lightColor:l,darkColor:a}=r;return{[`${e.componentCls}${e.componentCls}-${o}`]:{color:n,background:l,borderColor:t,"&-inverse":{color:e.colorTextLightSolid,background:a,borderColor:a},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(h(e))),b);const $=(e,o,r)=>{const n="string"!=typeof(t=r)?t:t.charAt(0).toUpperCase()+t.slice(1);var t;return{[`${e.componentCls}${e.componentCls}-${o}`]:{color:e[`color${r}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var O=(0,v.bk)(["Tag","status"],(e=>{const o=h(e);return[$(o,"success","Success"),$(o,"processing","Info"),$(o,"error","Error"),$(o,"warning","Warning")]}),b),k=function(e,o){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&o.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var t=0;for(n=Object.getOwnPropertySymbols(e);t<n.length;t++)o.indexOf(n[t])<0&&Object.prototype.propertyIsEnumerable.call(e,n[t])&&(r[n[t]]=e[n[t]])}return r};const j=n.forwardRef(((e,o)=>{const{prefixCls:r,className:t,rootClassName:d,style:f,children:m,icon:g,color:v,onClose:h,bordered:b=!0,visible:C}=e,x=k(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:P,direction:S,tag:$}=n.useContext(u.E_),[j,w]=n.useState(!0),E=(0,a.Z)(x,["closeIcon","closable"]);n.useEffect((()=>{void 0!==C&&w(C)}),[C]);const T=(0,s.o2)(v),F=(0,s.yT)(v),N=T||F,I=Object.assign(Object.assign({backgroundColor:v&&!N?v:void 0},null==$?void 0:$.style),f),z=P("tag",r),[R,B,H]=y(z),M=l()(z,null==$?void 0:$.className,{[`${z}-${v}`]:N,[`${z}-has-color`]:v&&!N,[`${z}-hidden`]:!j,[`${z}-rtl`]:"rtl"===S,[`${z}-borderless`]:!b},t,d,B,H),L=e=>{e.stopPropagation(),null==h||h(e),e.defaultPrevented||w(!1)},[,q]=(0,i.Z)((0,i.w)(e),(0,i.w)($),{closable:!1,closeIconRender:e=>{const o=n.createElement("span",{className:`${z}-close-icon`,onClick:L},e);return(0,c.wm)(e,o,(e=>({onClick:o=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,o),L(o)},className:l()(null==e?void 0:e.className,`${z}-close-icon`)})))}}),D="function"==typeof x.onClick||m&&"a"===m.type,X=g||null,V=X?n.createElement(n.Fragment,null,X,m&&n.createElement("span",null,m)):m,W=n.createElement("span",Object.assign({},E,{ref:o,className:M,style:I}),V,q,T&&n.createElement(Z,{key:"preset",prefixCls:z}),F&&n.createElement(O,{key:"status",prefixCls:z}));return R(D?n.createElement(p.Z,{component:"Tag"},W):W)})),w=j;w.CheckableTag=P;var E=w}}]);