"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8956],{47046:function(e,n){n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"}},82947:function(e,n){n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"}},52197:function(e,n){n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"}}]},name:"star",theme:"filled"}},92287:function(e,n){n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"}},28508:function(e,n,t){var r=t(1413),o=t(67294),s=t(89503),a=t(91146),c=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},i=o.forwardRef(c);n.Z=i},16596:function(e,n,t){t.d(n,{Z:function(){return i}});var r=t(1413),o=t(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M518.3 459a8 8 0 00-12.6 0l-112 141.7a7.98 7.98 0 006.3 12.9h73.9V856c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V613.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 459z"}},{tag:"path",attrs:{d:"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4a245.6 245.6 0 0152.4-49.9c41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10C846.1 454.5 884 503.8 884 560c0 33.1-12.9 64.3-36.3 87.7a123.07 123.07 0 01-87.6 36.3H720c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C870.5 760 960 670.5 960 560c0-92.7-63.1-170.7-148.6-193.3z"}}]},name:"cloud-upload",theme:"outlined"},a=t(91146),c=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s}))};var i=o.forwardRef(c)},82061:function(e,n,t){var r=t(1413),o=t(67294),s=t(47046),a=t(91146),c=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},i=o.forwardRef(c);n.Z=i},34804:function(e,n,t){var r=t(1413),o=t(67294),s=t(66023),a=t(91146),c=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},i=o.forwardRef(c);n.Z=i},47389:function(e,n,t){var r=t(1413),o=t(67294),s=t(27363),a=t(91146),c=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},i=o.forwardRef(c);n.Z=i},12906:function(e,n,t){t.d(n,{Z:function(){return i}});var r=t(1413),o=t(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM512 533c-85.5 0-155.6 67.3-160 151.6a8 8 0 008 8.4h48.1c4.2 0 7.8-3.2 8.1-7.4C420 636.1 461.5 597 512 597s92.1 39.1 95.8 88.6c.3 4.2 3.9 7.4 8.1 7.4H664a8 8 0 008-8.4C667.6 600.3 597.5 533 512 533z"}}]},name:"frown",theme:"outlined"},a=t(91146),c=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s}))};var i=o.forwardRef(c)},38545:function(e,n,t){t.d(n,{Z:function(){return i}});var r=t(1413),o=t(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 512a48 48 0 1096 0 48 48 0 10-96 0zm200 0a48 48 0 1096 0 48 48 0 10-96 0zm-400 0a48 48 0 1096 0 48 48 0 10-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z"}}]},name:"message",theme:"outlined"},a=t(91146),c=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s}))};var i=o.forwardRef(c)},43471:function(e,n,t){var r=t(1413),o=t(67294),s=t(82947),a=t(91146),c=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},i=o.forwardRef(c);n.Z=i},25820:function(e,n,t){var r=t(1413),o=t(67294),s=t(52197),a=t(91146),c=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},i=o.forwardRef(c);n.Z=i},75750:function(e,n,t){t.d(n,{Z:function(){return i}});var r=t(1413),o=t(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"},a=t(91146),c=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s}))};var i=o.forwardRef(c)},87784:function(e,n,t){t.d(n,{Z:function(){return i}});var r=t(1413),o=t(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"},a=t(91146),c=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s}))};var i=o.forwardRef(c)},98165:function(e,n,t){t.d(n,{Z:function(){return i}});var r=t(1413),o=t(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"},a=t(91146),c=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s}))};var i=o.forwardRef(c)},64029:function(e,n,t){var r=t(1413),o=t(67294),s=t(92287),a=t(91146),c=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},i=o.forwardRef(c);n.Z=i},52542:function(e,n,t){t.r(n),t.d(n,{default:function(){return De}});var r=t(64599),o=t.n(r),s=t(19632),a=t.n(s),c=t(15009),i=t.n(c),l=t(99289),u=t.n(l),d=t(97857),f=t.n(d),p=t(9783),x=t.n(p),g=t(13769),h=t.n(g),m=t(5574),v=t.n(m),y=t(93461),b=t(34114),k=t(78205),j=t(78919),w=t(4628),Z=t(9502),C=t(76654),_=t(26058),S=t(83622),z=t(2453),P=t(17788),B=t(42075),I=t(86250),R=t(74330),E=t(83062),T=t(55102),D=t(85265),N=t(2487),W=t(38703),M=t(67294),A=t(10981),H=t(78404),O=t(16596),L=t(14079),K=t(29158),F=t(38545),Y=t(51042),q=t(82061),G=t(15360),V=t(64029),J=t(34804),U=t(47389),$=t(87784),X=t(25820),Q=t(75750),ee=t(12906),ne=t(85175),te=t(43471),re=t(28508),oe=t(98165),se=t(43425),ae=t(27484),ce=t.n(ae),ie=(0,t(24444).kc)((function(e){var n=e.token;return{reference:{background:"".concat(n.colorBgLayout,"80"),minWidth:"400px",maxWidth:"720px",width:"100%"},layout:{width:"100%",minWidth:"800px",borderRadius:n.borderRadius,display:"flex",background:n.colorBgContainer,fontFamily:"AlibabaPuHuiTi, ".concat(n.fontFamily,", sans-serif"),".ant-prompts":{color:n.colorText},border:"3px solid",borderImage:"linear-gradient(45deg, ".concat(n.colorPrimary,", ").concat(n.colorSplit,") 1")},menu:{height:"100%",display:"flex",flexDirection:"column"},conversations:{padding:"0 12px",flex:1,overflowY:"auto"},chat:{height:"100%",width:"100%",maxWidth:"900px",margin:"0 auto",boxSizing:"border-box",display:"flex",justifyContent:"center",alignItems:"center",flexDirection:"column",padding:n.paddingLG,gap:"16px"},messages:{flex:1},placeholder:{paddingTop:"32px"},sender:{boxShadow:n.boxShadow},logo:{display:"flex",height:"72px",alignItems:"center",justifyContent:"start",padding:"0 24px",boxSizing:"border-box",img:{width:"24px",height:"24px",display:"inline-block"},span:{display:"inline-block",margin:"0 8px",fontWeight:"bold",color:n.colorText,fontSize:"16px"}},addBtn:{background:"#1677ff0f",border:"1px solid #1677ff34",width:"calc(100% - 24px)",margin:"0 12px 24px 12px"},wiseAnswer:{display:"block",padding:"12px",background:"#e6f7ff",borderRadius:"8px",borderLeft:"4px solid #1890ff",margin:"8px 0"}}})),le=t(93933),ue=t(79554),de=t(45435),fe=t(78158);function pe(e){return xe.apply(this,arguments)}function xe(){return(xe=u()(i()().mark((function e(n){var t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch("/api/source_files/".concat(n),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat((0,A.bW)())}});case 3:if((t=e.sent).ok){e.next=9;break}return e.next=7,t.json();case 7:throw r=e.sent,new Error(r.detail||"删除文件失败");case 9:return e.next=11,t.json();case 11:return e.abrupt("return",e.sent);case 14:throw e.prev=14,e.t0=e.catch(0),console.error("删除文件API错误:",e.t0),e.t0;case 18:case"end":return e.stop()}}),e,null,[[0,14]])})))).apply(this,arguments)}function ge(e,n){return he.apply(this,arguments)}function he(){return(he=u()(i()().mark((function e(n,t){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,fe.N)("/api/conversations/".concat(n,"/knowledge_ids"),{method:"PUT",data:{knowledge_ids:t}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function me(e){return ve.apply(this,arguments)}function ve(){return(ve=u()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,fe.N)("/api/conversation_source_files",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var ye=t(13973),be=t(85893),ke=["href","children"],je=["href","children"],we=_.Z.Sider;function Ze(e){return e+"-"+Date.now()}var Ce=M.createContext({expandedRefs:{},setExpandedRefs:function(){}}),_e={key:"1",label:"热门投诉主题",children:[{key:"1-1",description:"如何查找金融产品误导销售类投诉？",icon:(0,be.jsx)("span",{style:{color:"#f93a4a",fontWeight:700},children:"1"})},{key:"1-2",description:"近期高发投诉类型有哪些？",icon:(0,be.jsx)("span",{style:{color:"#ff6565",fontWeight:700},children:"2"})},{key:"1-3",description:"投诉处理规范流程是什么？",icon:(0,be.jsx)("span",{style:{color:"#ff8f1f",fontWeight:700},children:"3"})},{key:"1-4",description:"消费者权益保护要点有哪些？",icon:(0,be.jsx)("span",{style:{color:"#00000040",fontWeight:700},children:"4"})},{key:"1-5",description:"如何预防和化解投诉风险？",icon:(0,be.jsx)("span",{style:{color:"#00000040",fontWeight:700},children:"5"})}]},Se={key:"2",label:"投诉处理指南",children:[{key:"2-1",icon:(0,be.jsx)(O.Z,{}),label:"文件上传",description:"上传投诉相关文件，支持PDF、Word、Excel等多种格式"},{key:"2-2",icon:(0,be.jsx)(L.Z,{}),label:"知识库选择",description:"选择消保投诉相关知识库进行精准问答"},{key:"2-3",icon:(0,be.jsx)(K.Z,{}),label:"引用查看",description:"查看回答的法规依据，点击引用可查看原文详情"},{key:"2-4",icon:(0,be.jsx)(F.Z,{}),label:"对话交互",description:"与知识库进行多轮对话，获取投诉处理建议"}]},ze=[{key:"historyConversation",description:"历史对话",icon:(0,be.jsx)(L.Z,{style:{color:"#FF4D4F"}})},{key:"newConversation",description:"新建对话",icon:(0,be.jsx)(Y.Z,{style:{color:"#1890FF"}})},{key:"clearConversation",description:"清空对话",icon:(0,be.jsx)(q.Z,{style:{color:"#1890FF"}})},{key:"promptTemplate",description:"提示词模版",icon:(0,be.jsx)(G.Z,{style:{color:"#52C41A"}})}],Pe=(0,A.bG)(),Be=(0,H.kH)(),Ie="chat2kb",Re=function(e){return"string"!=typeof e?"":e.replace(/\[\[citation:(\d+)\]\]/g,(function(e,n){return"[".concat(n,"](#citation-").concat(n,")")}))},Ee=function(e){var n=e.content,t=e.messageId,r=(0,M.useState)(!1),o=v()(r,2),s=o[0],a=o[1],c=function(e){if("string"!=typeof e)return{processedContent:"",thinkBlocks:[]};var n=[];return{processedContent:e.replace(/<think>([\s\S]*?)<\/think>/g,(function(e,t){return n.push(t.trim()),""})).trim(),thinkBlocks:n}}(n),i=c.processedContent,l=c.thinkBlocks,u=M.useContext(Ce).setExpandedRefs;return(0,be.jsxs)("div",{style:{position:"relative"},children:[function(){try{if(!i)return null;var e=Re(i),n={a:function(e){var n=e.href,r=e.children,o=h()(e,ke);if(n&&n.startsWith("#citation-")){var s=n.replace("#citation-",""),a=parseInt(s)-1;return(0,be.jsx)("span",{style:{color:"#1890ff",fontWeight:"bold",fontSize:"0.9em",cursor:"pointer",textDecoration:"underline",margin:"0 2px"},onClick:function(){var e="".concat(t,"-").concat(a);u((function(n){return n[e]?{}:x()({},e,!0)})),setTimeout((function(){var n=document.querySelector('[data-ref-key="'.concat(e,'"]'));n&&n.scrollIntoView({behavior:"smooth",block:"center"})}),100)},children:r})}return(0,be.jsx)("a",f()(f()({href:n},o),{},{children:r}))},p:function(e){var n=e.children;return(0,be.jsx)("p",{style:{marginBottom:"0.6em",marginTop:"0.6em"},children:n})}};return(0,be.jsx)("div",{className:"markdown-content",style:{lineHeight:1.5},children:(0,be.jsx)(de.UG,{components:n,children:e})})}catch(e){return console.error("渲染内容时出错:",e),(0,be.jsx)("div",{style:{color:"red"},children:"内容渲染失败"})}}(),l.length>0&&(0,be.jsxs)("div",{style:{marginTop:12},children:[(0,be.jsx)(S.ZP,{type:"text",size:"small",icon:s?(0,be.jsx)(V.Z,{}):(0,be.jsx)(J.Z,{}),onClick:function(){return a(!s)},style:{color:"#666",fontSize:12,padding:0,height:"auto",background:"transparent"},children:(0,be.jsxs)("span",{style:{marginLeft:4},children:[s?"收起":"展开","思考过程 (",l.length,")"]})}),s&&(0,be.jsx)("div",{style:{marginTop:8,padding:12,backgroundColor:"#f8f9fa",border:"1px solid #e9ecef",borderRadius:6,fontSize:"13px",lineHeight:1.4,color:"#495057"},children:l.map((function(e,n){return(0,be.jsx)("div",{style:{marginBottom:n<l.length-1?8:0},children:(0,be.jsx)(de.UG,{components:{p:function(e){var n=e.children;return(0,be.jsx)("p",{style:{marginBottom:"0.6em",marginTop:"0.6em"},children:n})}},children:e})},n)}))})]})]})},Te=function(e,n){if("string"!=typeof e)return String(e);if(!e.includes("<think>")){var t=Re(e),r={a:function(e){var t=e.href,r=e.children,o=h()(e,je);if(t&&t.startsWith("#citation-")){var s=t.replace("#citation-",""),a=parseInt(s)-1;return(0,be.jsx)("span",{style:{color:"#1890ff",fontWeight:"bold",fontSize:"0.9em",cursor:"pointer",textDecoration:"underline",margin:"0 2px"},onClick:function(){var e="".concat(n,"-").concat(a);setExpandedRefs((function(n){return n[e]?{}:x()({},e,!0)})),setTimeout((function(){var n=document.querySelector('[data-ref-key="'.concat(e,'"]'));n&&n.scrollIntoView({behavior:"smooth",block:"center"})}),100)},children:r})}return(0,be.jsx)("a",f()(f()({href:t},o),{},{children:r}))},p:function(e){var n=e.children;return(0,be.jsx)("p",{style:{marginBottom:"0.6em",marginTop:"0.6em"},children:n})}};return(0,be.jsx)("div",{className:"markdown-content",style:{lineHeight:1.5},children:(0,be.jsx)(de.UG,{components:r,children:t})})}return(0,be.jsx)(Ee,{content:e,messageId:n})},De=function(){var e,n=(0,M.useState)({}),t=v()(n,2),r=t[0],s=t[1],c=(0,M.useState)(!1),l=v()(c,2),d=l[0],p=l[1],g=(0,M.useState)(null),h=v()(g,2),m=h[0],_=h[1],H=ie().styles,F=(0,M.useState)(window.innerHeight),G=v()(F,1)[0],V=M.useRef(),J=M.useRef(null),ae=M.useRef(null),de=M.useState(""),fe=v()(de,2),xe=fe[0],he=fe[1],ve=M.useState([]),ke=v()(ve,2),je=ke[0],Re=ke[1],Ee=M.useState(),De=v()(Ee,2),Ne=De[0],We=De[1],Me=M.useState(void 0),Ae=v()(Me,2),He=Ae[0],Oe=Ae[1],Le=(0,M.useState)(!1),Ke=v()(Le,2),Fe=Ke[0],Ye=Ke[1],qe=(0,M.useState)(!1),Ge=v()(qe,2),Ve=Ge[0],Je=Ge[1],Ue=(0,M.useState)(!1),$e=v()(Ue,2),Xe=$e[0],Qe=$e[1],en=(0,M.useState)(!1),nn=v()(en,2),tn=nn[0],rn=nn[1],on=(0,M.useState)(!1),sn=v()(on,2),an=sn[0],cn=sn[1],ln=(0,M.useState)(""),un=v()(ln,2),dn=un[0],fn=un[1],pn=(0,M.useState)(""),xn=v()(pn,2),gn=xn[0],hn=xn[1],mn=(0,M.useState)([]),vn=v()(mn,2),yn=vn[0],bn=vn[1],kn=(0,M.useRef)(null),jn=(0,M.useState)(!1),wn=v()(jn,2),Zn=wn[0],Cn=wn[1],_n=(0,M.useState)(""),Sn=v()(_n,2),zn=Sn[0],Pn=Sn[1],Bn=(0,M.useState)([]),In=v()(Bn,2),Rn=In[0],En=In[1],Tn=(0,M.useState)([]),Dn=v()(Tn,2),Nn=Dn[0],Wn=Dn[1],Mn=(0,M.useState)(!1),An=v()(Mn,2),Hn=An[0],On=An[1],Ln=M.useState([]),Kn=v()(Ln,2),Fn=(Kn[0],Kn[1]),Yn=M.useState([]),qn=v()(Yn,2),Gn=qn[0],Vn=qn[1],Jn=(0,M.useState)({}),Un=v()(Jn,2),$n=Un[0],Xn=Un[1],Qn=function(){var e=u()(i()().mark((function e(n){var t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n){e.next=3;break}return z.ZP.warning("没有活跃的对话"),e.abrupt("return");case 3:return e.prev=3,e.next=6,me({knowledge_base_id:n,pageSize:100});case 6:(t=e.sent)&&t.success&&Array.isArray(t.data)&&(r=t.data.map((function(e){return{uid:e.id,name:e.filename,status:"completed"===e.processing_status?"done":"error"===e.processing_status?"error":"uploading",url:e.url,size:e.size,type:e.data_type,created_at:e.created_at,chunk_count:e.chunk_count,processing_status:e.processing_status}})),Vn(r)),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(3),console.error("刷新文件状态失败:",e.t0),z.ZP.error("刷新文件状态失败");case 14:case"end":return e.stop()}}),e,null,[[3,10]])})));return function(n){return e.apply(this,arguments)}}(),et=function(e){Pn(e),Cn(!0)},nt=function(e){var n=yn.find((function(n){return n.message_id===e}));n&&navigator.clipboard.writeText(n.content).then((function(){z.ZP.success("复制成功")})).catch((function(){z.ZP.error("复制失败")}))},tt=(0,M.useRef)({selectedKnowledgeBaseIds:Nn,availableKnowledgeBases:Rn,activeConversationKey:Ne,conversationKnowledgeBaseId:He});(0,M.useEffect)((function(){tt.current={selectedKnowledgeBaseIds:Nn,availableKnowledgeBases:Rn,activeConversationKey:Ne,conversationKnowledgeBaseId:He}}),[Nn,Rn,Ne,He]);var rt,ot=function(){var e=u()(i()().mark((function e(){var n,t,r,o;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=(0,A.bG)(),e.prev=1,e.next=4,(0,le.fx)({user_id:parseInt((null==n?void 0:n.id)||"0")});case 4:return t=e.sent,r=t.data||[],En(r),Nn.length>0&&(o=Nn.filter((function(e){return r.some((function(n){return n._id===e}))}))).length!==Nn.length&&Wn(o),e.abrupt("return",r);case 11:return e.prev=11,e.t0=e.catch(1),console.error("Error fetching knowledge bases:",e.t0),e.abrupt("return",[]);case 15:case"end":return e.stop()}}),e,null,[[1,11]])})));return function(){return e.apply(this,arguments)}}(),st=(0,y.Z)({request:(rt=u()(i()().mark((function e(n,t){var r,s,c,l,u,d,f,p,x,g,h,m,v,y,b,k,j,w,Z,C,_,S,P,B,I,R,E,T,D,N,W,M,A,H,O,L,K,F,Y,q;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.messages,s=n.message,c=t.onSuccess,l=t.onUpdate,u=t.onError,e.prev=2,f=tt.current,p=f.selectedKnowledgeBaseIds,f.availableKnowledgeBases,x=f.conversationKnowledgeBaseId,!Ve){e.next=7;break}return z.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 7:if(Je(!0),g=s?s.id:Ze(V.current),s){e.next=13;break}return c({content:"出现了异常: 消息为空",role:"assistant",id:g,references:[],collected:!1,query:[]}),Je(!1),e.abrupt("return");case 13:if(h=p||[],x&&(h=[].concat(a()(h),[x])),m={conversation_id:V.current||"",message_id:g,meta_data:{},extra:{},role:s?s.role:"user",content:s?s.content:"",app_info:Ie,user_id:null==Pe?void 0:Pe.id,user_name:null==Pe?void 0:Pe.name,references:[],token_count:null,price:null,collected:!1,created_at:ce()().format("YYYY-MM-DD HH:mm:ss"),knowledge_ids:h,contextData:[],query:[]},bn((function(e){var n=[].concat(a()(e),[m]);return console.log("更新后的消息列表:",n),n})),V.current){e.next=20;break}throw z.ZP.error("No active conversation selected"),new Error("No active conversation selected");case 20:return v={conversation_id:V.current,app_info:Ie,user_id:parseInt(null==Pe?void 0:Pe.id),user_name:null==Pe?void 0:Pe.name,extra:{},messages:r,kb_id:h},y={id:Ze(V.current),role:"assistant",content:"",references:[],collected:!1,query:[]},b=!1,k="",j=[],l(y),e.next=28,(0,le.zl)(v);case 28:if((w=e.sent).ok){e.next=31;break}throw new Error("HTTP 错误！状态码：".concat(w.status));case 31:if(Z=null===(d=w.body)||void 0===d?void 0:d.getReader()){e.next=34;break}throw new Error("当前浏览器不支持 ReadableStream。");case 34:C=new TextDecoder("utf-8"),_={conversation_id:V.current||"",message_id:y.id,meta_data:{},extra:{},role:"assistant",content:"",app_info:Ie,user_id:null==Pe?void 0:Pe.id,user_name:null==Pe?void 0:Pe.name,references:[],token_count:null,price:null,collected:!1,created_at:ce()().format("YYYY-MM-DD HH:mm:ss"),knowledge_ids:h,contextData:[],query:[]};case 36:if(b){e.next=107;break}return e.next=39,Z.read();case 39:S=e.sent,P=S.value,S.done&&(b=!0),k+=C.decode(P,{stream:!0}),B=k.split("\n\n"),k=B.pop()||"",I=o()(B),e.prev=47,I.s();case 49:if((R=I.n()).done){e.next=97;break}if(""!==(E=R.value).trim()){e.next=53;break}return e.abrupt("continue",95);case 53:T=E.split("\n"),D=null,N=null,W=o()(T);try{for(W.s();!(M=W.n()).done;)(A=M.value).startsWith("event: ")?D=A.substring(7).trim():A.startsWith("data: ")&&(N=A.substring(6))}catch(e){W.e(e)}finally{W.f()}if(!N){e.next=95;break}e.t0=D,e.next="answer"===e.t0?62:"moduleStatus"===e.t0?74:"appStreamResponse"===e.t0?76:"flowResponses"===e.t0?78:"end"===e.t0?80:"error"===e.t0?82:95;break;case 62:if("[DONE]"===N){e.next=73;break}e.prev=63,O=JSON.parse(N),(L=(null===(H=O.choices[0])||void 0===H||null===(H=H.delta)||void 0===H?void 0:H.content)||"")&&(y.content+=L,l(y)),e.next=73;break;case 69:return e.prev=69,e.t1=e.catch(63),console.error("Error parsing answer data:",e.t1),e.abrupt("return",c({content:"出现了异常:"+N,role:"assistant",id:Ze(V.current),references:[],query:[],collected:!1}));case 73:return e.abrupt("break",95);case 74:try{K=JSON.parse(N),console.log("模块状态：",K)}catch(e){console.error("Error parsing moduleStatus data:",e)}return e.abrupt("break",95);case 76:try{F=JSON.parse(N),console.log("appStreamData===>",F),console.log("appStreamData[0].context===>",F[0].context),j=F[0].context,y.references=j}catch(e){console.error("Error parsing appStreamResponse data:",e)}return e.abrupt("break",95);case 78:try{console.log("flowResponsesData",N)}catch(e){console.error("Error parsing flowResponses data:",e)}return e.abrupt("break",95);case 80:return b=!0,e.abrupt("break",95);case 82:e.prev=82,b=!0,Y=JSON.parse(N),y.content=Y.message,_.role="assistant",l(y),e.next=94;break;case 90:throw e.prev=90,e.t2=e.catch(82),console.error("Error event received:",e.t2),e.t2;case 94:return e.abrupt("break",95);case 95:e.next=49;break;case 97:e.next=102;break;case 99:e.prev=99,e.t3=e.catch(47),I.e(e.t3);case 102:return e.prev=102,I.f(),e.finish(102);case 105:e.next=36;break;case 107:if(console.info(y),c(y),!y.content||""===y.content.trim()){e.next=116;break}return _.content=y.content,_.references=j,e.next=114,(0,le.tn)(_);case 114:(q=e.sent)&&q.success&&q.data?(_.message_id=q.data.message_id,bn((function(e){var n=[].concat(a()(e),[q.data]);return console.log("更新后的消息列表:",n),n}))):z.ZP.error("消息上报失败");case 116:e.next=123;break;case 118:e.prev=118,e.t4=e.catch(2),console.log("error===>",e.t4),e.t4 instanceof Error?z.ZP.error(e.t4.message||"对话发生错误"):z.ZP.error("对话发生未知错误"),u(e.t4 instanceof Error?e.t4:new Error("Unknown error"));case 123:return e.prev=123,Je(!1),e.finish(123);case 126:case"end":return e.stop()}}),e,null,[[2,118,123,126],[47,99,102,105],[63,69],[82,90]])}))),function(e,n){return rt.apply(this,arguments)})}),at=v()(st,1)[0],ct=(0,b.Z)({agent:at}),it=ct.onRequest,lt=ct.messages,ut=ct.setMessages,dt=function(e){We(e),console.log("activeKey 设置",e),V.current=e},ft=function(e){var n=e.filter((function(e){return e.pinned})).sort((function(e,n){return new Date(n.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return f()(f()({},e),{},{key:e.id||"",label:e.conversation_name||e.id,group:"置顶"})})),t=e.filter((function(e){return!e.pinned})).sort((function(e,n){return new Date(n.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return f()(f()({},e),{},{key:e.id||"",label:e.conversation_name||"",group:"对话"})}));Re([].concat(a()(n),a()(t)))},pt=function(e){return 0===e.length?null:e.reduce((function(e,n){return new Date(n.active_at)>new Date(e.active_at)?n:e}))},xt=function(){var e=u()(i()().mark((function e(n){var t,r,o,s,a,c;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,Je(!0),console.info("获取对话信息",n),(t=Rn)&&0!==t.length||console.log("🚀 ~ fetchConversationMessages ~ 可用知识库列表为空，但是我们应该在页面加载时已经获取了"),r=ce()().format("YYYY-MM-DD HH:mm:ss"),e.next=8,(0,le.$o)(n,{conversation_name:null,active_at:r,pinned_at:null,pinned:null});case 8:(o=e.sent)&&o.knowledge_ids&&console.log("🚀 ~ 获取到对话绑定的知识库IDs:",o.knowledge_ids),Vn([]),o&&o.uploaded_files&&Array.isArray(o.uploaded_files)&&(console.log("🚀 ~ 获取到对话上传的文件:",o.uploaded_files),(s=o.uploaded_files.map((function(e){return{uid:e.id,name:e.filename,status:"completed"===e.processing_status?"done":"error"===e.processing_status?"error":"uploading",url:e.url,size:e.size,type:e.data_type,created_at:e.created_at,chunk_count:e.chunk_count,processing_status:e.processing_status}}))).length>0&&Vn(s)),null!=o&&o.messages?(console.info("设置对话信息",o.messages),a=o.messages.map((function(e){return{id:e.message_id,message:{id:e.message_id,content:e.content,role:e.role,references:e.references||[],collected:e.collected||!1,query:e.query||[]},status:"assistant"===e.role?"success":"local",meta:{avatar:"assistant"===e.role?(null==Be?void 0:Be.logo)||"/static/logo.png":(null==Pe?void 0:Pe.avatar)||"/avatar/default.jpeg"}}})),bn(o.messages),ut(a),dt(n),Wn([]),Oe(void 0),o.knowledge_ids&&Array.isArray(o.knowledge_ids)&&o.knowledge_ids.length>0?(console.log("🚀 ~ 获取到对话绑定的知识库IDs:",o.knowledge_ids),c=o.knowledge_ids.filter((function(e){return t.some((function(n){return n._id===e}))})),console.log("🚀 ~ 筛选后的知识库IDs:",c),c.length>0&&Wn(c)):console.log("🚀 ~ 对话没有绑定知识库IDs")):z.ZP.error("获取对话信息失败"),e.next=18;break;case 15:e.prev=15,e.t0=e.catch(0),console.error("切换对话时出错：",e.t0);case 18:return e.prev=18,Je(!1),We(n),e.finish(18);case 22:case"end":return e.stop()}}),e,null,[[0,15,18,22]])})));return function(n){return e.apply(this,arguments)}}(),gt=function(){var e=u()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(V.current){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,(0,le.Db)(V.current);case 4:e.sent.success?(bn([]),ut([]),kn.current&&kn.current.updateReferenceList([])):z.ZP.error("清空对话失败");case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ht=function(){var e=u()(i()().mark((function e(){var n,t,r,o,s,c,l,u;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!Ve){e.next=3;break}return z.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(!(n=(0,A.bG)())){e.next=27;break}return e.prev=5,Wn([]),Vn([]),Xn({}),Oe(void 0),t=(new Date).toLocaleString(),r="对话-".concat(t),o=[],e.next=15,(0,le.Xw)({user_id:parseInt(n.id),user_name:n.name,conversation_name:r,app_info:Ie,knowledge_ids:o});case 15:s=e.sent,c={key:s.id||"",id:s.id||"",label:s.conversation_name||"",group:"对话",conversation_name:s.conversation_name||"",app_info:Ie,active_at:s.active_at||"",pinned_at:s.pinned_at,pinned:s.pinned||!1,messages:[],knowledge_ids:o,uploaded_files:[]},l=je.filter((function(e){return!e.pinned})),u=je.filter((function(e){return e.pinned})),Re([].concat(a()(u),[c],a()(l))),dt(s.id||""),gt(),e.next=27;break;case 24:e.prev=24,e.t0=e.catch(5),console.error("创建新对话时出错：",e.t0);case 27:case"end":return e.stop()}}),e,null,[[5,24]])})));return function(){return e.apply(this,arguments)}}(),mt=function(){var e=u()(i()().mark((function e(n){var t,r,o,s,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=je.find((function(e){return e.key===n}))){e.next=3;break}return e.abrupt("return");case 3:return r=t.pinned,o=!r,e.prev=6,s=ce()().format("YYYY-MM-DD HH:mm:ss"),e.next=10,(0,le.X1)(n,{conversation_name:null,active_at:null,pinned:o,pinned_at:s});case 10:a=je.map((function(e){return e.key===n?f()(f()({},e),{},{pinned:o}):e})),ft(a),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(6),console.error("更新置顶状态时出错：",e.t0);case 17:case"end":return e.stop()}}),e,null,[[6,14]])})));return function(n){return e.apply(this,arguments)}}(),vt=function(){var e=u()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:P.Z.confirm({title:"确认删除",content:"确定要删除这个对话吗？删除后不可恢复。",okText:"确认删除",okType:"danger",cancelText:"取消",onOk:function(){return u()(i()().mark((function e(){var t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,t=null,r=je.filter((function(e){return e.key!==n})),console.log("更新 updatedItems===>",r),r.length>0&&(t=r[0].key),console.log(" nextActiveId===>",t),console.log("删除 deleteConversation===>",n),e.next=9,(0,le.SJ)(n);case 9:ft(r),setTimeout((function(){t?(console.log("激活 ===>",t),bt(t)):(console.log("新建 ===>",t),ht())}),0),e.next=17;break;case 13:e.prev=13,e.t0=e.catch(0),console.error("删除对话时出错：",e.t0),z.ZP.error("删除对话失败");case 17:case"end":return e.stop()}}),e,null,[[0,13]])})))()}});case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),yt=function(){var e=u()(i()().mark((function e(n,t){var r,o;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,je.find((function(e){return e.key===n}))){e.next=4;break}return e.abrupt("return");case 4:return r={conversation_name:t,active_at:null,pinned_at:null,pinned:null},e.next=7,(0,le.X1)(n,r);case 7:null!=(o=e.sent)&&o.success?Re((function(e){return e.map((function(e){return e.key===n?f()(f()({},e),{},{label:t}):e}))})):z.ZP.error("更新对话标题失败"),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("更新对话标题时出错：",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(n,t){return e.apply(this,arguments)}}(),bt=function(){var e=u()(i()().mark((function e(n){var t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!Ve){e.next=3;break}return z.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(t=je.some((function(e){return e.key===n}))){e.next=9;break}return z.ZP.error("对话不存在或已被删除"),e.abrupt("return");case 9:console.info("对话存在",t);case 10:return console.log("onConversationClick===>",n),Xn({}),e.next=14,xt(n);case 14:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}();(0,M.useEffect)((function(){var e=function(){var e=u()(i()().mark((function e(){var n,t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=(0,A.bG)(),e.next=3,ot();case 3:if(!n){e.next=32;break}return e.prev=4,Je(!0),e.next=8,(0,le.Mw)({user_id:n.id,app_info:Ie});case 8:if(!(t=e.sent).success||!Array.isArray(t.data)){e.next=24;break}if(0!==t.data.length){e.next=15;break}return e.next=13,ht();case 13:e.next=24;break;case 15:if(r=pt(t.data),ft(t.data),!r){e.next=22;break}return e.next=20,xt(r.id);case 20:e.next=24;break;case 22:return e.next=24,xt(t.data[0].id||"");case 24:e.next=29;break;case 26:e.prev=26,e.t0=e.catch(4),console.error("初始化对话时出错：",e.t0);case 29:return e.prev=29,Je(!1),e.finish(29);case 32:case"end":return e.stop()}}),e,null,[[4,26,29,32]])})));return function(){return e.apply(this,arguments)}}();e()}),[Ie]);var kt=function(){var e=u()(i()().mark((function e(n,t){var r,o,s,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("重新生成消息:",n),e.prev=1,r=yn.findIndex((function(e){return e.message_id===n})),console.log("currentIndex===>",r),-1!==r){e.next=7;break}return z.ZP.error("未找到指定消息"),e.abrupt("return");case 7:return o=yn[r],s=yn.slice(r),console.log("将要删除的消息:",s),e.next=12,(0,le.qP)(s.map((function(e){return e.message_id})));case 12:e.sent.success||z.ZP.error("删除消息失败"),bn((function(e){return e.slice(0,r)})),ut((function(e){return e.slice(0,r)})),"assistant"===o.role?(a=yn.slice(0,r).reverse().find((function(e){return"user"===e.role})))&&it({id:n,role:"user",content:a.content,references:[],query:[],collected:!1}):it({id:n,role:"user",content:o.content,references:[],query:[],collected:!1}),z.ZP.success("正在重新生成回复..."),e.next=24;break;case 20:e.prev=20,e.t0=e.catch(1),console.error("重新生成消息时出错：",e.t0),z.ZP.error("重新生成消息失败");case 24:case"end":return e.stop()}}),e,null,[[1,20]])})));return function(n,t){return e.apply(this,arguments)}}(),jt=function(){var e=u()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:P.Z.confirm({title:"确认删除",content:"确定要删除这条消息吗？删除后不可恢复。",okText:"确认",cancelText:"取消",onOk:function(){return u()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("delete messageId===>",n),e.next=4,(0,le.$Z)(n);case 4:e.sent.success?(bn((function(e){return e.filter((function(e){return e.message_id!==n}))})),console.log("delete currentConversationMessages===>",yn),ut((function(e){return e.filter((function(e){return e.message.id!==n}))})),z.ZP.success("消息及相关引用已删除")):z.ZP.error("删除消息失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除消息时出错：",e.t0),z.ZP.error("删除消息失败");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})))()}});case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),wt=function(){var e=u()(i()().mark((function e(n){var t,r,o;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("重新生成消息:",n),console.log("收藏状态切换:",n,isCollected),e.next=4,(0,le.bk)({message_id:n,collected:!isCollected});case 4:if(e.sent.success?(z.ZP.success(isCollected?"取消收藏成功":"收藏成功"),ut((function(e){return e.map((function(e){return e.id===n?f()(f()({},e),{},{message:f()(f()({},e.message),{},{collected:!isCollected})}):e}))})),bn((function(e){return e.map((function(e){return e.message_id===n?f()(f()({},e),{},{collected:!isCollected}):e}))}))):z.ZP.error(isCollected?"取消收藏失败":"收藏失败"),function(e,n){n&&n.stopPropagation(),_(e),p(!0)},nextContent){e.next=9;break}return e.abrupt("return");case 9:if(t=Nn&&Nn.length>0,r=Gn&&Gn.length>0,o=!!He,t||r||o){e.next=15;break}return z.ZP.warning("您还尚未选择知识库，请先选择知识库！"),e.abrupt("return");case 15:it({id:Ze(V.current),role:"user",content:nextContent,references:[],collected:!1,query:[]}),Fn([]),he("");case 18:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),Zt=function(){var e=u()(i()().mark((function e(n){var t,r,o;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!Ve){e.next=3;break}return z.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(t=n.data,r=t.key,o=t.description,"historyConversation"!==r){e.next=8;break}Ye(!0),e.next=19;break;case 8:if("newConversation"!==r){e.next=13;break}return e.next=11,ht();case 11:e.next=19;break;case 13:if("clearConversation"!==r){e.next=18;break}return e.next=16,gt();case 16:e.next=19;break;case 18:if("promptTemplate"===r)Qe(!0);else if("knowledgeBaseSetting"===r)try{Je(!0),On(!0)}catch(e){console.error("加载知识库数据失败:",e),z.ZP.error("加载知识库数据失败"),On(!1)}finally{Je(!1)}else it({id:Ze(V.current),role:"user",content:o,references:[],collected:!1,query:[]});case 19:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),Ct=M.useState(!1),_t=v()(Ct,2),St=_t[0],zt=_t[1],Pt=(0,be.jsxs)(B.Z,{direction:"vertical",size:16,style:{paddingInline:"calc(calc(100% - 700px) /2)"},className:H.placeholder,children:[(0,be.jsx)(k.Z,{variant:"borderless",icon:(0,be.jsx)("img",{src:(null==Be?void 0:Be.logo)||"/static/logo.png",alt:"logo"}),title:"你好，我是消费者权益保护助手",description:"基于您的知识库内容，为您提供专业的投诉处理建议和法规查询服务"}),(0,be.jsxs)(I.Z,{gap:16,children:[(0,be.jsx)(j.Z,{items:[_e],styles:{list:{height:"100%"},item:{flex:1,backgroundImage:"linear-gradient(123deg, #e5f4ff 0%, #efe7ff 100%)",borderRadius:12,border:"none"},subItem:{padding:0,background:"transparent"}},onItemClick:Zt}),(0,be.jsx)(j.Z,{items:[Se],styles:{item:{flex:1,backgroundImage:"linear-gradient(123deg, #e5f4ff 0%, #efe7ff 100%)",borderRadius:12,border:"none"},subItem:{background:"#ffffffa6"}}})]})]}),Bt=lt.length>0?lt.map((function(e){var n=e.id,t=e.message,o=e.status;return{key:V.current+"_"+n,loadingRender:function(){return(0,be.jsxs)(B.Z,{children:[(0,be.jsx)(R.Z,{size:"small"}),"模型思考中..."]})},loading:"loading"===o&&t.content.length<1,content:t.content,messageRender:function(e){return Te(e,t.id)},shape:"local"===o?"corner":"round",variant:"local"===o?"filled":"borderless",avatar:"local"===o?{src:(null==Pe?void 0:Pe.avatar)||"/avatar/default.jpeg"}:{src:(null==Be?void 0:Be.logo)||"/static/logo.png"},placement:"local"!==o?"start":"end",footer:"local"!==o?(0,be.jsxs)(I.Z,{children:[t.references&&t.references.length>0&&(0,be.jsxs)("div",{style:{marginTop:8,width:"100%"},children:[(0,be.jsxs)("div",{style:{fontWeight:"bold",marginBottom:8,fontSize:13},children:["引用来源 (",t.references.length,")"]}),(0,be.jsx)("div",{children:t.references.map((function(e,n){return(0,be.jsxs)("div",{style:{marginBottom:8},"data-ref-index":n,"data-ref-key":"".concat(t.id,"-").concat(n),children:[(0,be.jsxs)("div",{style:{display:"flex",alignItems:"center",cursor:"pointer",padding:"8px 13px",border:"1px solid #f0f0f0",borderRadius:r["".concat(t.id,"-").concat(n)]?"4px 4px 0 0":4,backgroundColor:"#fafafa",justifyContent:"space-between",width:"100%"},onClick:function(n){n.stopPropagation(),handleReferenceDetail(e,n)},children:[(0,be.jsxs)("div",{style:{fontWeight:"bold",fontSize:12,color:"#666",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:"80%"},children:[n+1,". ",e.source_name&&e.source_name.length>25?e.source_name.slice(0,25)+"...":e.source_name||"来源 ".concat(n+1)]}),(0,be.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:8,flexShrink:0},children:[(0,be.jsx)(S.ZP,{type:"text",size:"small",icon:(0,be.jsx)(L.Z,{}),onClick:function(n){return handleReferenceDetail(e,n)},style:{fontSize:12,color:"#1890ff",padding:"0 4px",height:20,border:"none",flexShrink:0},title:"查看详情"}),(0,be.jsx)("span",{style:{fontSize:12,color:"#999",cursor:"pointer",flexShrink:0},onClick:function(e){e.stopPropagation();var r="".concat(t.id,"-").concat(n);s((function(e){return e[r]?{}:x()({},r,!0)}))},children:r["".concat(t.id,"-").concat(n)]?"▲":"▼"})]})]}),r["".concat(t.id,"-").concat(n)]&&(0,be.jsx)("div",{style:{padding:8,border:"1px solid #f0f0f0",borderTop:"none",borderRadius:"0 0 4px 4px",backgroundColor:"#fff",fontSize:12,lineHeight:1.4,color:"#333",width:"100%",wordBreak:"break-word",overflow:"auto",maxHeight:"300px"},children:e.content})]},n)}))})]}),(0,be.jsx)(S.ZP,{size:"small",type:"text",icon:(0,be.jsx)(q.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),jt(t.id)}}),(0,be.jsx)(S.ZP,{size:"small",type:"text",icon:t.collected?(0,be.jsx)(X.Z,{style:{color:"#FFD700"}}):(0,be.jsx)(Q.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),kt(t.id,t.collected)}}),(0,be.jsx)(S.ZP,{size:"small",type:"text",icon:(0,be.jsx)(ee.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),et(t.id)}}),(0,be.jsx)(S.ZP,{size:"small",type:"text",icon:(0,be.jsx)(ne.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),nt(t.id)}})]}):(0,be.jsxs)(I.Z,{children:[(0,be.jsx)(S.ZP,{size:"small",type:"text",icon:(0,be.jsx)(te.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),wt(t.id)}}),(0,be.jsx)(S.ZP,{size:"small",type:"text",icon:(0,be.jsx)(q.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),jt(t.id)}}),(0,be.jsx)(S.ZP,{size:"small",type:"text",icon:t.collected?(0,be.jsx)(X.Z,{style:{color:"#FFD700"}}):(0,be.jsx)(Q.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),kt(t.id,t.collected)}}),(0,be.jsx)(S.ZP,{size:"small",type:"text",icon:(0,be.jsx)(ee.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),et(t.id)}}),(0,be.jsx)(S.ZP,{size:"small",type:"text",icon:(0,be.jsx)(ne.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),nt(t.id)}})]})}})):[{content:Pt,variant:"borderless"}],It=(0,be.jsx)(w.Z.Header,{title:"Attachments",open:tn,onOpenChange:rn,styles:{content:{padding:0}}}),Rt=(0,be.jsxs)("div",{className:H.logo,children:[(0,be.jsx)("span",{children:"对话记录"}),(0,be.jsx)(E.Z,{title:"新对话",children:(0,be.jsx)(S.ZP,{type:"text",icon:(0,be.jsx)(Y.Z,{}),onClick:ht,style:{fontSize:"16px"}})})]}),Et=(0,be.jsx)(P.Z,{title:"修改对话标题",open:an,onOk:function(){gn&&dn.trim()&&(yt(gn,dn.trim()),cn(!1))},onCancel:function(){cn(!1),fn(""),hn("")},children:(0,be.jsx)(T.Z,{value:dn,onChange:function(e){return fn(e.target.value)},placeholder:"请输入新的对话标题"})}),Tt=(0,be.jsx)(D.Z,{title:"历史对话",placement:"right",width:400,onClose:function(){return Ye(!1)},open:Fe,children:(0,be.jsxs)("div",{className:H.menu,children:[Rt,(0,be.jsx)(Z.Z,{items:je,activeKey:Ne,onActiveChange:bt,menu:function(e){return{items:[{label:"重命名",key:"edit",icon:(0,be.jsx)(U.Z,{})},{label:"置顶",key:"pin",icon:(0,be.jsx)($.Z,{})},{label:"删除",key:"delete",icon:(0,be.jsx)(q.Z,{}),danger:!0}],onClick:function(n){switch(console.log("menuInfo","Click ".concat(e.key," - ").concat(n.key)),n.key){case"edit":hn(e.key),fn(e.label),cn(!0);break;case"pin":mt(e.key);break;case"delete":if(Ve)return void z.ZP.error("系统正在处理其他对话。请稍😊");vt(e.key)}}}},groupable:!0})]})}),Dt=M.memo((function(e){var n=e.isOpen,t=e.onClose,r=e.availableKnowledgeBases,o=e.selectedKnowledgeBaseIds,s=e.setSelectedKnowledgeBaseIds,c=(0,M.useState)([]),l=v()(c,2),d=l[0],f=l[1],p=(0,M.useState)(!1),x=v()(p,2),g=x[0],h=x[1],m=(0,M.useState)(!1),y=v()(m,2),b=y[0],k=y[1];(0,M.useEffect)((function(){if(n){f(a()(o));var e=setTimeout((function(){k(!0)}),100);return function(){clearTimeout(e),k(!1)}}}),[n,o]);var j=(0,M.useCallback)((function(e,n){n&&(n.stopPropagation(),n.preventDefault()),f((function(n){return n.includes(e)?n.filter((function(n){return n!==e})):[].concat(a()(n),[e])}))}),[]),w=function(){var e=u()(i()().mark((function e(){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,h(!0),!V.current){e.next=19;break}return n=d,e.next=6,ge(V.current,n);case 6:if(!e.sent.success){e.next=16;break}return s(d),z.ZP.success("知识库设置已更新"),h(!1),k(!1),t(),e.abrupt("return");case 16:z.ZP.error("更新知识库设置失败");case 17:e.next=25;break;case 19:return s(d),z.ZP.warning("未选择对话，知识库设置仅保存在本地"),h(!1),k(!1),t(),e.abrupt("return");case 25:e.next=31;break;case 27:e.prev=27,e.t0=e.catch(0),console.error("保存知识库设置时出错:",e.t0),z.ZP.error("保存知识库设置失败");case 31:h(!1);case 32:case"end":return e.stop()}}),e,null,[[0,27]])})));return function(){return e.apply(this,arguments)}}();return(0,be.jsx)(P.Z,{title:"知识库设置",open:n,onCancel:function(){k(!1),setTimeout((function(){t()}),100)},width:1200,styles:{body:{padding:"12px 16px"},mask:{backgroundColor:"rgba(0, 0, 0, 0.45)"},content:{boxShadow:"0 6px 16px rgba(0, 0, 0, 0.08), 0 3px 6px rgba(0, 0, 0, 0.12)",transition:"all 0.3s ease"}},destroyOnClose:!0,maskClosable:!1,footer:[(0,be.jsx)(S.ZP,{onClick:function(){k(!1),setTimeout((function(){t()}),100)},children:"关闭"},"close"),(0,be.jsx)(S.ZP,{type:"primary",onClick:w,loading:g,children:"保存"},"save")],children:b?(0,be.jsxs)("div",{style:{display:"flex",height:"500px",opacity:b?1:0,transition:"opacity 0.3s ease"},children:[(0,be.jsxs)("div",{style:{width:"50%",paddingRight:"12px",overflowY:"auto"},children:[(0,be.jsx)("h3",{style:{marginBottom:"16px",fontSize:"16px",fontWeight:500},children:"可选知识库"}),r.length>0?(0,be.jsx)(N.Z,{grid:{gutter:16,column:2},dataSource:r,renderItem:function(e){var n=d.includes(e._id);return(0,be.jsx)(N.Z.Item,{onClick:function(n){return j(e._id,n)},style:{border:n?"1px solid #1890ff":"1px solid #e0e0e0",padding:"12px",backgroundColor:n?"#e6f7ff":"#fff",color:n?"#1890ff":"inherit",borderRadius:"4px",transition:"all 0.3s ease",boxShadow:n?"0 2px 8px rgba(24, 144, 255, 0.15)":"none",cursor:"pointer",marginBottom:"8px"},children:(0,be.jsx)(N.Z.Item.Meta,{title:(0,be.jsx)("span",{style:{color:n?"#1890ff":"inherit",fontWeight:n?500:400,fontSize:"14px"},children:e.name}),description:(0,be.jsx)("div",{style:{fontSize:"12px",color:"#999",marginTop:"4px"},children:e.description||"暂无描述"})})})}}):(0,be.jsx)("div",{style:{textAlign:"center",padding:"40px 0",color:"#999"},children:"暂无可用知识库"})]}),(0,be.jsxs)("div",{style:{width:"50%",paddingLeft:"12px",borderLeft:"1px solid #f0f0f0",overflowY:"auto"},children:[(0,be.jsx)("h4",{style:{marginBottom:"16px",fontSize:"16px",fontWeight:500},children:"已选知识库"}),d.length>0?(0,be.jsx)(N.Z,{grid:{gutter:16,column:2},dataSource:d,renderItem:function(e){var n=r.find((function(n){return n._id===e}));return(0,be.jsx)(N.Z.Item,{onClick:function(n){return j(e,n)},style:{border:"1px solid #1890ff",padding:"12px",backgroundColor:"#e6f7ff",color:"#1890ff",borderRadius:"4px",transition:"all 0.3s ease",boxShadow:"0 2px 8px rgba(24, 144, 255, 0.15)",cursor:"pointer",marginBottom:"8px"},children:(0,be.jsx)(N.Z.Item.Meta,{title:(0,be.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,be.jsx)("span",{style:{color:"#1890ff",fontWeight:500,fontSize:"14px"},children:(null==n?void 0:n.name)||"未知知识库"}),(0,be.jsx)(S.ZP,{type:"text",size:"small",icon:(0,be.jsx)(re.Z,{}),onClick:function(n){n.stopPropagation(),f((function(n){return n.filter((function(n){return n!==e}))}))},style:{color:"#1890ff"}})]}),description:(0,be.jsx)("div",{style:{fontSize:"12px",color:"#666",marginTop:"4px"},children:(null==n?void 0:n.description)||"暂无描述"})})})}}):(0,be.jsx)("div",{style:{textAlign:"center",padding:"40px 0",color:"#999"},children:"暂未选择知识库"})]})]}):(0,be.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"500px"},children:(0,be.jsx)(R.Z,{size:"large",tip:"正在加载知识库数据..."})})})})),Nt=function(){var e=u()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(V.current){e.next=3;break}return z.ZP.warning("没有活跃的对话"),e.abrupt("return");case 3:try{P.Z.confirm({title:"确认删除",content:"确定要删除此文件吗？删除后将无法恢复，且会影响相关的知识库索引。",okText:"确认删除",okType:"danger",cancelText:"取消",onOk:function(){var e=u()(i()().mark((function e(){var t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,pe(n);case 3:(t=e.sent)&&t.success?(z.ZP.success("文件删除成功"),Vn((function(e){return e.filter((function(e){return e.uid!==n}))})),V.current&&Qn(V.current)):z.ZP.error("文件删除失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("删除文件失败:",e.t0),z.ZP.error("删除文件失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}()})}catch(e){console.error("删除文件失败:",e),z.ZP.error("删除文件失败")}case 4:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),Wt=(0,be.jsx)(D.Z,{title:"引用来源详情",placement:"right",width:500,onClose:function(){return p(!1)},open:d,children:m&&(0,be.jsxs)("div",{style:{padding:"16px 0"},children:[(0,be.jsxs)("div",{style:{marginBottom:24},children:[(0,be.jsx)("h3",{style:{fontSize:16,fontWeight:"bold",marginBottom:12,color:"#333"},children:"文档标题"}),(0,be.jsx)("div",{style:{padding:12,backgroundColor:"#f5f5f5",borderRadius:6,fontSize:14,color:"#666"},children:m.source_name||"未知来源"})]}),(0,be.jsxs)("div",{style:{marginBottom:24},children:[(0,be.jsx)("h3",{style:{fontSize:16,fontWeight:"bold",marginBottom:12,color:"#333"},children:"引用内容"}),(0,be.jsx)("div",{style:{padding:16,backgroundColor:"#fafafa",border:"1px solid #e8e8e8",borderRadius:6,fontSize:14,lineHeight:1.6,color:"#333",maxHeight:500,overflowY:"auto"},children:m.content||"暂无内容"})]}),m.metadata&&(0,be.jsxs)("div",{style:{marginBottom:24},children:[(0,be.jsx)("h3",{style:{fontSize:16,fontWeight:"bold",marginBottom:12,color:"#333"},children:"其他信息"}),(0,be.jsx)("div",{style:{fontSize:12,color:"#888"},children:Object.entries(m.metadata).map((function(e){var n=v()(e,2),t=n[0],r=n[1];return(0,be.jsxs)("div",{style:{marginBottom:4},children:[(0,be.jsxs)("span",{style:{fontWeight:"bold"},children:[t,":"]})," ",String(r)]},t)}))})]})]})});return(0,M.useEffect)((function(){var e=null;return Gn&&Gn.some((function(e){return"pending"===e.processing_status||"processing"===e.processing_status}))&&Ne&&(e=setInterval(u()(i()().mark((function n(){var t,r,o;return i()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(console.log("检查文件处理状态..."),n.prev=1,!Ne){n.next=7;break}return n.next=5,me({knowledge_base_id:Ne,pageSize:100});case 5:(t=n.sent)&&t.success&&Array.isArray(t.data)&&(r=t.data.map((function(e){return{uid:e.id,name:e.filename,status:"completed"===e.processing_status?"done":"error"===e.processing_status?"error":"uploading",url:e.url,size:e.size,type:e.data_type,created_at:e.created_at,chunk_count:e.chunk_count,processing_status:e.processing_status}})),o=r.every((function(e){return"completed"===e.processing_status||"error"===e.processing_status})),Vn(r),o&&e&&(console.log("所有文件处理完成，停止检查"),clearInterval(e),e=null));case 7:n.next=12;break;case 9:n.prev=9,n.t0=n.catch(1),console.error("检查文件处理状态出错:",n.t0);case 12:case"end":return n.stop()}}),n,null,[[1,9]])}))),1e4)),function(){e&&clearInterval(e)}}),[Gn,Ne]),(0,M.useEffect)((function(){console.log("currentConversationMessages 更新了:",yn)}),[yn]),(0,M.useEffect)((function(){}),[]),(0,M.useEffect)((function(){if(console.log("🚀 ~ 监听到状态变化 ~ selectedKnowledgeBaseIds:",Nn),console.log("🚀 ~ 监听到状态变化 ~ availableKnowledgeBases:",Rn),Nn.length>0&&Rn.length>0){var e=Nn.filter((function(e){return!Rn.some((function(n){return n._id===e}))}));e.length>0&&console.warn("🚀 ~ 警告：有选中的知识库ID在可用知识库列表中找不到:",e)}}),[Nn,Rn]),(0,M.useEffect)((function(){var e=function(){var e=u()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:Nn.length>0&&(console.log("🚀 ~ 已选知识库IDs:",Nn),0===Rn.length&&console.log("🚀 ~ 可用知识库列表为空，但我们期望它在初始化时已被填充"));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();e()}),[Ne,Nn]),e=function(){var e=u()(i()().mark((function e(){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(xe.trim()){e.next=2;break}return e.abrupt("return");case 2:return n={id:Ze(V.current),content:xe,role:"user"},he(""),e.prev=4,e.next=7,at.request({messages:a()(yn.map((function(e){return{id:e.message_id,content:e.content,role:e.role}}))),message:n},{onSuccess:function(e){console.log("消息发送成功",e)},onUpdate:function(e){console.log("接收到更新",e)},onError:function(e){console.error("消息发送失败",e),z.ZP.error("发送消息失败："+(e.message||"未知错误"))}});case 7:e.next=13;break;case 9:e.prev=9,e.t0=e.catch(4),console.error("发送消息时发生错误",e.t0),z.ZP.error("发送消息失败");case 13:case"end":return e.stop()}}),e,null,[[4,9]])})));return function(){return e.apply(this,arguments)}}(),(0,be.jsx)(Ce.Provider,{value:{expandedRefs:r,setExpandedRefs:s},children:(0,be.jsxs)("div",{className:H.layout,style:{height:G-56},children:[(0,be.jsxs)("div",{className:H.chat,children:[(0,be.jsx)(C.Z.List,{items:Bt,className:H.messages}),(0,be.jsx)(j.Z,{items:ze,onItemClick:Zt}),(0,be.jsx)(w.Z,{ref:ae,header:It,prefix:(0,be.jsx)(S.ZP,{type:"text",icon:(0,be.jsx)(K.Z,{}),onClick:function(){zt(!St)}}),onPasteFile:function(e){var n;null===(n=J.current)||void 0===n||n.upload(e),zt(!0)},onSubmit:e,value:xe,onChange:he,loading:at.isRequesting(),className:H.sender,placeholder:"请输入...",onKeyPress:function(){},onFocus:function(){},onBlur:function(){}})]}),(0,be.jsx)("div",{children:(0,be.jsx)(we,{width:450,style:{background:"#fff",padding:"12px",borderLeft:"1px solid #d9d9d9",height:"100%",overflow:"auto"},children:(0,be.jsxs)("div",{style:{padding:"8px 0"},children:[(0,be.jsx)("div",{style:{marginBottom:"16px",display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"1px solid #f0f0f0",paddingBottom:"12px"},children:(0,be.jsx)("div",{style:{fontWeight:500,fontSize:"16px",color:"#333"},children:"知识资源"})}),(0,be.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,be.jsxs)("div",{style:{fontWeight:500,fontSize:"16px",color:"#333",marginBottom:"16px",display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"1px solid #f0f0f0",paddingBottom:"12px"},children:[(0,be.jsx)("span",{children:"已选知识库"}),(0,be.jsx)(S.ZP,{type:"primary",ghost:!0,size:"small",icon:(0,be.jsx)(se.Z,{}),onClick:u()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:try{Je(!0),On(!0)}catch(e){console.error("加载知识库数据失败:",e),z.ZP.error("加载知识库数据失败"),On(!1)}finally{Je(!1)}case 1:case"end":return e.stop()}}),e)}))),children:"设置知识库"})]}),Nn&&0!==Nn.length?(0,be.jsx)("div",{children:(0,be.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:"10px"},children:Nn.map((function(e){var n=Rn.find((function(n){return n.id===e||n._id===e}));return(0,be.jsx)("div",{style:{width:"calc(50% - 5px)",padding:"10px",borderRadius:"8px",border:"1px solid #e6f7ff",backgroundColor:"#f0f8ff",boxSizing:"border-box",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:(0,be.jsx)("span",{style:{fontWeight:500,fontSize:"14px",color:"#1890ff",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",display:"block"},children:(null==n?void 0:n.name)||"未知知识库"})},e)}))})}):(0,be.jsxs)("div",{style:{textAlign:"center",padding:"32px 0",color:"#999",backgroundColor:"#f9f9f9",borderRadius:"8px",border:"1px dashed #d9d9d9"},children:[(0,be.jsx)(L.Z,{style:{fontSize:"36px",marginBottom:"16px",color:"#1890ff"}}),(0,be.jsx)("div",{style:{marginBottom:"16px"},children:"暂未选择知识库"}),(0,be.jsx)(S.ZP,{type:"primary",size:"small",onClick:u()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:try{Je(!0),On(!0)}catch(e){console.error("加载知识库数据失败:",e),z.ZP.error("加载知识库数据失败"),On(!1)}finally{Je(!1)}case 1:case"end":return e.stop()}}),e)}))),children:"选择知识库"})]})]}),Gn&&Gn.length>0&&(0,be.jsxs)("div",{style:{marginTop:"24px"},children:[(0,be.jsxs)("div",{style:{fontWeight:500,fontSize:"16px",color:"#333",marginBottom:"16px",display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"1px solid #f0f0f0",paddingBottom:"12px"},children:[(0,be.jsx)("span",{children:"上传文件"}),(0,be.jsx)(S.ZP,{type:"primary",ghost:!0,icon:(0,be.jsx)(oe.Z,{}),size:"small",onClick:function(){Ne&&Qn(Ne)},children:"刷新状态"})]}),(0,be.jsxs)("div",{style:{fontSize:"12px",color:"#666",marginBottom:"12px",backgroundColor:"#f9f9f9",padding:"12px",borderRadius:"8px"},children:[(0,be.jsx)("div",{style:{marginBottom:"8px",fontWeight:500},children:"支持的文件类型：PDF、Word、Excel、CSV、PPT、HTML、Markdown、JSON、图片(JPG/PNG)、音频(WAV/MP3)"}),(0,be.jsx)("div",{style:{marginTop:"8px",color:"#ff7875"},children:"注意：文件上传后需要处理完成才会被检索到，请耐心等待"})]}),Gn&&0!==Gn.length?(console.log("渲染文件列表:",Gn),console.log("上传进度状态:",$n),(0,be.jsx)(N.Z,{dataSource:Gn,size:"small",itemLayout:"horizontal",renderItem:function(e){var n=$n[e.uid];console.log("文件 ".concat(e.name," (").concat(e.uid,") 的上传进度:"),n);var t=e.processing_status;return"string"==typeof t&&(t="completed"===t?1:"error"===t?3:"processing"===t?2:"uploading"===t?4:0),console.log("文件 ".concat(e.name," 的处理状态:"),t,e.processing_status),(0,be.jsx)(N.Z.Item,{style:{marginBottom:"10px",padding:"12px 16px",borderRadius:"8px",border:"1px solid #eee",backgroundColor:"#fafafa",transition:"all 0.3s",boxShadow:"0 1px 2px rgba(0,0,0,0.03)"},actions:[(0,be.jsx)(S.ZP,{type:"link",size:"small",danger:!0,icon:(0,be.jsx)(q.Z,{}),onClick:function(){return Nt(e.uid)},disabled:2===t},"delete")],children:(0,be.jsx)(N.Z.Item.Meta,{title:(0,be.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"4px"},children:[(0,be.jsx)("span",{style:{fontWeight:500,fontSize:"14px",maxWidth:"260px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e.name}),e.size&&(0,be.jsxs)("div",{style:{fontSize:"12px",color:"#999",marginBottom:"4px"},children:[(e.size/1024).toFixed(2)," KB"]})]}),description:(0,be.jsx)("div",{children:n&&"uploading"===n.status?(0,be.jsxs)("div",{children:[(0,be.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",marginBottom:"2px"},children:[(0,be.jsx)("span",{children:"上传中..."}),(0,be.jsxs)("span",{children:[n.percent,"%"]})]}),(0,be.jsx)(W.Z,{percent:n.percent,size:"small",status:"active",strokeColor:{"0%":"#108ee9","100%":"#87d068"},showInfo:!1})]}):n&&"error"===n.status?(0,be.jsxs)("div",{children:[(0,be.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",marginBottom:"2px",color:"#ff4d4f"},children:[(0,be.jsx)("span",{children:"上传失败"}),(0,be.jsx)("span",{children:"请重试"})]}),(0,be.jsx)(W.Z,{percent:100,size:"small",status:"exception",showInfo:!1})]}):n&&"done"===n.status?(0,be.jsxs)("div",{children:[(0,be.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",marginBottom:"2px",color:"#52c41a"},children:[(0,be.jsx)("span",{children:"上传完成"}),(0,be.jsx)("span",{children:"100%"})]}),(0,be.jsx)(W.Z,{percent:100,size:"small",status:"success",showInfo:!1})]}):1===t?(0,be.jsx)("div",{style:{fontSize:"12px",color:"#52c41a"},children:"处理完成"}):3===t?(0,be.jsx)("div",{style:{fontSize:"12px",color:"#f5222d"},children:"处理失败"}):2===t?(0,be.jsxs)("div",{style:{fontSize:"12px",color:"#1890ff",display:"flex",alignItems:"center"},children:[(0,be.jsx)(oe.Z,{spin:!0,style:{marginRight:"5px"}}),"处理中..."]}):(0,be.jsx)("div",{style:{fontSize:"12px",color:"#faad14"},children:"等待处理"})})})})}})):(0,be.jsxs)("div",{style:{textAlign:"center",padding:"24px 0",color:"#999",backgroundColor:"#f9f9f9",borderRadius:"8px",border:"1px dashed #d9d9d9"},children:[(0,be.jsx)(O.Z,{style:{fontSize:"36px",marginBottom:"16px",color:"#1890ff"}}),(0,be.jsx)("div",{children:"暂无上传文件"})]})]})]})})}),Et,Tt,Wt,(0,be.jsx)(Dt,{isOpen:Hn,onClose:function(){return On(!1)},availableKnowledgeBases:Rn,selectedKnowledgeBaseIds:Nn,setSelectedKnowledgeBaseIds:Wn}),(0,be.jsx)(ye.Z,{visible:Zn,messageId:zn,conversationId:Ne,appInfo:Ie,onClose:function(){return Cn(!1)}}),(0,be.jsx)(ue.Z,{visible:Xe,onCancel:function(){return Qe(!1)},sceneDescription:Nn.length>0?Nn.map((function(e){var n=Rn.find((function(n){return n._id===e}));return(null==n?void 0:n.name)||""})).filter(Boolean).join("、")+"知识库问答":"知识库问答",onSelectPrompt:function(e){he(e),Qe(!1)},app_info:Ie})]})})}},26058:function(e,n,t){t.d(n,{Z:function(){return j}});var r=t(74902),o=t(67294),s=t(93967),a=t.n(s),c=t(98423),i=t(53124),l=t(82401),u=t(50344),d=t(70985);var f=t(24793),p=function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)n.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(t[r[o]]=e[r[o]])}return t};function x(e){let{suffixCls:n,tagName:t,displayName:r}=e;return e=>o.forwardRef(((r,s)=>o.createElement(e,Object.assign({ref:s,suffixCls:n,tagName:t},r))))}const g=o.forwardRef(((e,n)=>{const{prefixCls:t,suffixCls:r,className:s,tagName:c}=e,l=p(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:u}=o.useContext(i.E_),d=u("layout",t),[x,g,h]=(0,f.ZP)(d),m=r?`${d}-${r}`:d;return x(o.createElement(c,Object.assign({className:a()(t||m,s,g,h),ref:n},l)))})),h=o.forwardRef(((e,n)=>{const{direction:t}=o.useContext(i.E_),[s,x]=o.useState([]),{prefixCls:g,className:h,rootClassName:m,children:v,hasSider:y,tagName:b,style:k}=e,j=p(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),w=(0,c.Z)(j,["suffixCls"]),{getPrefixCls:Z,className:C,style:_}=(0,i.dj)("layout"),S=Z("layout",g),z=function(e,n,t){return"boolean"==typeof t?t:!!e.length||(0,u.Z)(n).some((e=>e.type===d.Z))}(s,v,y),[P,B,I]=(0,f.ZP)(S),R=a()(S,{[`${S}-has-sider`]:z,[`${S}-rtl`]:"rtl"===t},C,h,m,B,I),E=o.useMemo((()=>({siderHook:{addSider:e=>{x((n=>[].concat((0,r.Z)(n),[e])))},removeSider:e=>{x((n=>n.filter((n=>n!==e))))}}})),[]);return P(o.createElement(l.V.Provider,{value:E},o.createElement(b,Object.assign({ref:n,className:R,style:Object.assign(Object.assign({},_),k)},w),v)))})),m=x({tagName:"div",displayName:"Layout"})(h),v=x({suffixCls:"header",tagName:"header",displayName:"Header"})(g),y=x({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(g),b=x({suffixCls:"content",tagName:"main",displayName:"Content"})(g);const k=m;k.Header=v,k.Footer=y,k.Content=b,k.Sider=d.Z,k._InternalSiderContext=d.D;var j=k}}]);