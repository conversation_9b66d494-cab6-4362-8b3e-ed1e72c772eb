"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1614],{50675:function(e,r,t){var n=t(1413),a=t(67294),l=t(72961),s=t(91146),o=function(e,r){return a.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:l.Z}))},i=a.forwardRef(o);r.Z=i},8913:function(e,r,t){var n=t(1413),a=t(67294),l=t(1085),s=t(91146),o=function(e,r){return a.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:l.Z}))},i=a.forwardRef(o);r.Z=i},51042:function(e,r,t){var n=t(1413),a=t(67294),l=t(42110),s=t(91146),o=function(e,r){return a.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:l.Z}))},i=a.forwardRef(o);r.Z=i},45216:function(e,r,t){t.r(r),t.d(r,{default:function(){return K}});var n=t(9783),a=t.n(n),l=t(15009),s=t.n(l),o=t(97857),i=t.n(o),c=t(99289),u=t.n(c),d=t(5574),p=t.n(d),m=t(51042),f=t(50675),h=t(8913),x=t(97131),v=t(12453),b=t(17788),g=t(8232),Z=t(2453),y=t(42075),j=t(66309),k=t(83622),I=t(74330),C=t(55102),O=t(34041),P=t(26412),w=t(67294),E=t(78158);function _(e){return S.apply(this,arguments)}function S(){return(S=u()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,E.N)("/api/llms",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function T(e){return $.apply(this,arguments)}function $(){return($=u()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,E.N)("/api/llms",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function A(e){return M.apply(this,arguments)}function M(){return(M=u()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,E.N)("/api/llms/".concat(r,"/set_default"),{method:"POST"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function L(e){return N.apply(this,arguments)}function N(){return(N=u()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,E.N)("/api/llms/test",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function q(e){return z.apply(this,arguments)}function z(){return(z=u()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,E.N)("/api/llms/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function B(e){return D.apply(this,arguments)}function D(){return(D=u()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,E.N)("/api/llms/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var R=t(77885),F=t(85893),U=b.Z.confirm,K=function(){var e=(0,w.useState)(!1),r=p()(e,2),t=r[0],n=r[1],l=(0,w.useState)(!1),o=p()(l,2),c=o[0],d=o[1],E=(0,w.useState)(!1),S=p()(E,2),$=S[0],M=S[1],N=(0,w.useState)(void 0),z=p()(N,2),D=z[0],K=z[1],H=(0,w.useRef)(),W=g.Z.useForm(),J=p()(W,1)[0],V=(0,w.useState)({}),X=p()(V,2),G=X[0],Q=X[1],Y=function(){var e=u()(s()().mark((function e(r){var t,a;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=Z.ZP.loading("正在添加"),e.prev=1,e.next=4,T(i()({},r));case 4:return t(),Z.ZP.success("添加模型成功"),n(!1),null===(a=H.current)||void 0===a||a.reload(),J.resetFields(),e.abrupt("return",!0);case 12:return e.prev=12,e.t0=e.catch(1),t(),Z.ZP.error("添加失败，请重试"),e.abrupt("return",!1);case 17:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(r){return e.apply(this,arguments)}}(),ee=function(){var e=u()(s()().mark((function e(r){var t,n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.info(r),r.id){e.next=4;break}return Z.ZP.error("更新失败，缺少模型 ID"),e.abrupt("return",!1);case 4:return t=Z.ZP.loading("正在更新"),e.prev=5,e.next=8,q(r);case 8:return t(),Z.ZP.success("更新成功"),d(!1),K(void 0),null===(n=H.current)||void 0===n||n.reload(),J.resetFields(),e.abrupt("return",!0);case 17:return e.prev=17,e.t0=e.catch(5),t(),Z.ZP.error("更新失败，请重试"),e.abrupt("return",!1);case 22:case"end":return e.stop()}}),e,null,[[5,17]])})));return function(r){return e.apply(this,arguments)}}(),re=function(){var e=u()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:U({title:"确认删除",content:"你确定要删除这个模型吗？",onOk:function(){var e=u()(s()().mark((function e(){var t,n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=Z.ZP.loading("正在删除"),e.prev=1,e.next=4,B(r.id);case 4:return t(),Z.ZP.success("删除成功"),null===(n=H.current)||void 0===n||n.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),t(),Z.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(){return e.apply(this,arguments)}}(),onCancel:function(){console.log("取消删除")}});case 1:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),te=function(){var e=u()(s()().mark((function e(r){var t,n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return Q((function(e){return i()(i()({},e),{},a()({},r.id,!0))})),t=Z.ZP.loading("开始测试"),e.prev=2,e.next=5,L({id:r.id});case 5:"success"===(null==(n=e.sent)?void 0:n.status)?(t(),Z.ZP.success("测试成功: ".concat(n.message))):(t(),Z.ZP.error("测试失败: ".concat(n.message))),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(2),t(),Z.ZP.error("测试失败，请重试");case 13:return e.prev=13,Q((function(e){return i()(i()({},e),{},a()({},r.id,!1))})),e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[2,9,13,16]])})));return function(r){return e.apply(this,arguments)}}(),ne=[{title:"ID",dataIndex:"id",valueType:"digit",width:60},{title:"名称",width:360,dataIndex:"name",valueType:"text",render:function(e,r){return(0,F.jsxs)(y.Z,{children:[r.name,(0,F.jsx)(j.Z,{color:r.is_active?"success":"default",style:{fontWeight:"bold",padding:"0 8px",display:"flex",alignItems:"center",gap:"4px"},children:r.is_active?"已上线":"已下线"}),(0,F.jsx)(j.Z,{color:"blue",style:{display:"flex",alignItems:"center",gap:"4px"},children:"local"===r.provider?"本地化":r.provider})]})}},{title:"模型名称",dataIndex:"m_name",valueType:"text"},{title:"最大令牌数",dataIndex:"max_tokens",valueType:"digit",width:120,search:!1},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",width:200,search:!1},{title:"操作",dataIndex:"option",valueType:"option",fixed:"right",width:340,render:function(e,r){return[(0,F.jsx)(k.ZP,{type:r.is_active?"default":"primary",danger:r.is_active,size:"small",onClick:function(){var e;U({title:"确认".concat(r.is_active?"下线":"上线"),content:"确定要".concat(r.is_active?"下线":"上线","该模型吗？"),onOk:(e=u()(s()().mark((function e(){var t,n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=Z.ZP.loading("正在更新状态"),e.prev=1,e.next=4,q({id:r.id,is_active:!r.is_active});case 4:t(),Z.ZP.success("状态更新成功"),null===(n=H.current)||void 0===n||n.reload(),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(1),t(),Z.ZP.error("状态更新失败，请重试");case 13:case"end":return e.stop()}}),e,null,[[1,9]])}))),function(){return e.apply(this,arguments)})})},children:r.is_active?"下线":"上线"},"toggle-".concat(r.id)),(0,F.jsx)(k.ZP,{type:"link",size:"small",style:{width:"50px"},onClick:function(){K(r),d(!0),J.resetFields(),J.setFieldsValue(r)},children:"编辑"},"edit-".concat(r.id)),(0,F.jsx)(k.ZP,{size:"small",type:"link",style:{width:"50px"},onClick:function(){K(r),M(!0)},children:"查看"},"view-".concat(r.id)),(0,F.jsx)(k.ZP,{size:"small",type:"link",danger:!0,style:{width:"50px"},onClick:function(){return re(r)},children:"删除"},"delete-".concat(r.id)),(0,F.jsxs)(k.ZP,{size:"small",type:"link",style:{width:"50px"},onClick:function(){return te(r)},disabled:G[r.id],children:["测试",G[r.id]&&(0,F.jsx)(I.Z,{size:"small",style:{marginLeft:8}})]},"test-".concat(r.id)),(0,F.jsx)(k.ZP,{size:"small",type:"link",disabled:r.is_default,onClick:u()(s()().mark((function e(){var t,n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=Z.ZP.loading("正在设置为系统默认..."),e.prev=1,e.next=4,A(r.id);case 4:t(),Z.ZP.success("设置成功"),null===(n=H.current)||void 0===n||n.reload(),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(1),t(),Z.ZP.error("设置失败，请重试");case 13:case"end":return e.stop()}}),e,null,[[1,9]])}))),children:"设为默认"},"default-".concat(r.id))]}}];return(0,F.jsxs)(x._z,{children:[(0,F.jsx)(v.Z,{headerTitle:"大语言模型管理",actionRef:H,rowKey:"id",scroll:{x:1200},search:{labelWidth:120,defaultCollapsed:!0},toolBarRender:function(){return[(0,F.jsxs)(k.ZP,{type:"primary",onClick:function(){n(!0)},children:[(0,F.jsx)(m.Z,{})," 新建"]},"primary")]},request:function(){var e=u()(s()().mark((function e(r){var t;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,_(i()({current:r.current,pageSize:r.pageSize},r));case 2:return t=e.sent,e.abrupt("return",{data:t.data,success:t.success,total:t.total});case 4:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),columns:ne}),(0,F.jsx)(b.Z,{visible:t,title:"新建模型",onCancel:function(){return n(!1)},onOk:function(){return J.submit()},children:(0,F.jsxs)(g.Z,{form:J,layout:"horizontal",onFinish:Y,initialValues:{temperature:.7,max_tokens:4096,top_p:2,provider:R.M.OPENAI,vector_size:300},labelCol:{span:6},wrapperCol:{span:18},children:[(0,F.jsx)(g.Z.Item,{name:"name",label:"名称",rules:[{required:!0,message:"请输入名称"}],children:(0,F.jsx)(C.Z,{})}),(0,F.jsx)(g.Z.Item,{name:"description",label:"描述",rules:[{required:!0,message:"请输入模型描述"}],children:(0,F.jsx)(C.Z,{})}),(0,F.jsx)(g.Z.Item,{name:"m_name",label:"模型名称",rules:[{required:!0,message:"请输入模型名称"}],children:(0,F.jsx)(C.Z,{})}),(0,F.jsx)(g.Z.Item,{name:"provider",label:"接口标准",rules:[{required:!0,message:"请选择接口调用标准"}],children:(0,F.jsxs)(O.default,{onChange:function(e){var r="";switch(e){case R.M.DEEPSEEK:r="https://api.deepseek.com";break;case R.M.DOUBAO:r="https://ark.cn-beijing.volces.com/api/v3";break;case R.M.OPENAI:r="https://api.openai.com";break;case R.M.LOACL:case R.M.OLLAMA:case R.M.JIUTIAN:case R.M.TELEAI:r="http://"}J.setFieldsValue({service_url:r})},children:[(0,F.jsx)(O.default.Option,{value:R.M.LOACL,children:"本地化"}),(0,F.jsx)(O.default.Option,{value:R.M.OLLAMA,children:"Ollama"}),(0,F.jsx)(O.default.Option,{value:R.M.JIUTIAN,children:"移动九天"}),(0,F.jsx)(O.default.Option,{value:R.M.DEEPSEEK,children:"DeepSeek"}),(0,F.jsx)(O.default.Option,{value:R.M.DOUBAO,children:"豆包"}),(0,F.jsx)(O.default.Option,{value:R.M.TELEAI,children:"电信星辰"}),(0,F.jsx)(O.default.Option,{value:R.M.OPENAI,children:"OpenAI"})]})}),(0,F.jsx)(g.Z.Item,{name:"service_url",label:"服务地址",extra:"服务地址必须以 /v1 结尾",rules:[{required:!0,message:"请输入服务地址"},{pattern:/^(http|https):\/\/[^ "]+$/,message:"请输入有效的 URL，必须以 http 或 https 开头"},{pattern:/\/v1$/,message:"服务地址必须以 /v1 结尾"}],children:(0,F.jsx)(C.Z,{})}),(0,F.jsx)(g.Z.Item,{name:"api_key",label:"API Key",rules:[{required:!0,message:"请输入 API Key"}],children:(0,F.jsx)(C.Z,{})}),(0,F.jsx)(g.Z.Item,{name:"temperature",label:"默认温度",rules:[{required:!0,message:"请输入温度"}],children:(0,F.jsx)(C.Z,{type:"number"})}),(0,F.jsx)(g.Z.Item,{name:"max_tokens",label:"最大令牌数",rules:[{required:!0,message:"请输入最大令牌数"}],children:(0,F.jsx)(C.Z,{type:"number"})}),(0,F.jsx)(g.Z.Item,{name:"top_p",label:"Top P",rules:[{required:!0,message:"请输入 Top P"}],children:(0,F.jsx)(C.Z,{type:"number"})})]})}),D&&(0,F.jsx)(b.Z,{visible:c,title:"更新模型",onCancel:function(){d(!1),J.resetFields()},onOk:function(){return J.submit()},destroyOnClose:!0,forceRender:!0,children:(0,F.jsxs)(g.Z,{form:J,layout:"horizontal",initialValues:D,onFinish:ee,labelCol:{span:6},wrapperCol:{span:18},children:[(0,F.jsx)(g.Z.Item,{name:"id",hidden:!0,children:(0,F.jsx)(C.Z,{})}),(0,F.jsx)(g.Z.Item,{name:"name",label:"名称",rules:[{required:!0,message:"请输入名称"}],children:(0,F.jsx)(C.Z,{})}),(0,F.jsx)(g.Z.Item,{name:"description",label:"描述",rules:[{required:!1,message:"请输入模型描述"}],children:(0,F.jsx)(C.Z,{})}),(0,F.jsx)(g.Z.Item,{name:"m_name",label:"模型名称",rules:[{required:!0,message:"请输入模型名称"}],children:(0,F.jsx)(C.Z,{})}),(0,F.jsx)(g.Z.Item,{name:"provider",label:"接口标准",rules:[{required:!0,message:"请选择接口调用标准"}],children:(0,F.jsxs)(O.default,{children:[(0,F.jsx)(O.default.Option,{value:R.M.LOACL,children:"本地化"}),(0,F.jsx)(O.default.Option,{value:R.M.OLLAMA,children:"Ollama"}),(0,F.jsx)(O.default.Option,{value:R.M.JIUTIAN,children:"移动九天"}),(0,F.jsx)(O.default.Option,{value:R.M.DEEPSEEK,children:"DeepSeek"}),(0,F.jsx)(O.default.Option,{value:R.M.DOUBAO,children:"豆包"}),(0,F.jsx)(O.default.Option,{value:R.M.OPENAI,children:"OpenAI"}),(0,F.jsx)(O.default.Option,{value:R.M.TELEAI,children:"电信星辰"})]})}),(0,F.jsx)(g.Z.Item,{name:"service_url",label:"服务地址",extra:"服务地址必须以 /v1 结尾",rules:[{required:!0,message:"请输入服务地址"},{pattern:/^(http|https):\/\/[^ "]+$/,message:"请输入有效的 URL，必须以 http 或 https 开头"},{pattern:/\/v1$/,message:"服务地址必须以 /v1 结尾"}],children:(0,F.jsx)(C.Z,{})}),(0,F.jsx)(g.Z.Item,{name:"api_key",label:"API Key",rules:[{required:!0,message:"请输入 API Key"}],children:(0,F.jsx)(C.Z,{})}),(0,F.jsx)(g.Z.Item,{name:"temperature",label:"默认温度",rules:[{required:!0,message:"请输入温度"}],children:(0,F.jsx)(C.Z,{type:"number"})}),(0,F.jsx)(g.Z.Item,{name:"max_tokens",label:"最大令牌数",rules:[{required:!0,message:"请输入最大令牌数"}],children:(0,F.jsx)(C.Z,{type:"number"})}),(0,F.jsx)(g.Z.Item,{name:"top_p",label:"Top P",rules:[{required:!0,message:"请输入 Top P"}],children:(0,F.jsx)(C.Z,{type:"number"})})]},D.id)}),D&&(0,F.jsx)(b.Z,{visible:$,title:"模型信息",width:800,onCancel:function(){return M(!1)},footer:null,children:(0,F.jsxs)(P.Z,{bordered:!0,column:1,children:[(0,F.jsx)(P.Z.Item,{label:"名称",children:D.name}),(0,F.jsx)(P.Z.Item,{label:"模型名称",children:D.m_name}),(0,F.jsx)(P.Z.Item,{label:"描述",children:D.description}),(0,F.jsx)(P.Z.Item,{label:"服务地址",children:D.service_url}),(0,F.jsx)(P.Z.Item,{label:"接口标准",children:D.provider}),(0,F.jsx)(P.Z.Item,{label:"状态",children:(0,F.jsx)(j.Z,{color:D.is_active?"success":"default",icon:D.is_active?(0,F.jsx)(f.Z,{}):(0,F.jsx)(h.Z,{}),style:{fontWeight:"bold",padding:"0 8px",borderRadius:"12px",display:"flex",alignItems:"center",gap:"4px"},children:D.is_active?"已上线":"已下线"})}),(0,F.jsx)(P.Z.Item,{label:"最大令牌数",children:D.max_tokens}),(0,F.jsx)(P.Z.Item,{label:"创建时间",children:D.created_at}),(0,F.jsx)(P.Z.Item,{label:"API Key",children:D.api_key.slice(0,4)+"****"+D.api_key.slice(-4)}),(0,F.jsx)(P.Z.Item,{label:"默认温度",children:D.temperature}),(0,F.jsx)(P.Z.Item,{label:"Top P",children:D.top_p}),(0,F.jsx)(P.Z.Item,{label:"频率惩罚",children:D.frequency_penalty}),(0,F.jsx)(P.Z.Item,{label:"存在惩罚",children:D.presence_penalty}),(0,F.jsx)(P.Z.Item,{label:"价格",children:D.price})]})})]})}},77885:function(e,r,t){t.d(r,{M:function(){return n}});var n={LOACL:"local",OPENAI:"openai",DEEPSEEK:"deepseek",DOUBAO:"doubao",TELEAI:"teleai",JIUTIAN:"jiutian",OLLAMA:"ollama"}},66309:function(e,r,t){t.d(r,{Z:function(){return _}});var n=t(67294),a=t(93967),l=t.n(a),s=t(98423),o=t(98787),i=t(69760),c=t(96159),u=t(45353),d=t(53124),p=t(11568),m=t(15063),f=t(14747),h=t(83262),x=t(83559);const v=e=>{const{lineWidth:r,fontSizeIcon:t,calc:n}=e,a=e.fontSizeSM;return(0,h.IX)(e,{tagFontSize:a,tagLineHeight:(0,p.bf)(n(e.lineHeightSM).mul(a).equal()),tagIconSize:n(t).sub(n(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},b=e=>({defaultBg:new m.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var g=(0,x.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:t,tagPaddingHorizontal:n,componentCls:a,calc:l}=e,s=l(n).sub(t).equal(),o=l(r).sub(t).equal();return{[a]:Object.assign(Object.assign({},(0,f.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:s,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${a}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${a}-close-icon`]:{marginInlineStart:o,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${a}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${a}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:s}}),[`${a}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(v(e))),b),Z=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)r.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(t[n[a]]=e[n[a]])}return t};const y=n.forwardRef(((e,r)=>{const{prefixCls:t,style:a,className:s,checked:o,onChange:i,onClick:c}=e,u=Z(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:m}=n.useContext(d.E_),f=p("tag",t),[h,x,v]=g(f),b=l()(f,`${f}-checkable`,{[`${f}-checkable-checked`]:o},null==m?void 0:m.className,s,x,v);return h(n.createElement("span",Object.assign({},u,{ref:r,style:Object.assign(Object.assign({},a),null==m?void 0:m.style),className:b,onClick:e=>{null==i||i(!o),null==c||c(e)}})))}));var j=y,k=t(98719);var I=(0,x.bk)(["Tag","preset"],(e=>(e=>(0,k.Z)(e,((r,t)=>{let{textColor:n,lightBorderColor:a,lightColor:l,darkColor:s}=t;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:n,background:l,borderColor:a,"&-inverse":{color:e.colorTextLightSolid,background:s,borderColor:s},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(v(e))),b);const C=(e,r,t)=>{const n="string"!=typeof(a=t)?a:a.charAt(0).toUpperCase()+a.slice(1);var a;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${t}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var O=(0,x.bk)(["Tag","status"],(e=>{const r=v(e);return[C(r,"success","Success"),C(r,"processing","Info"),C(r,"error","Error"),C(r,"warning","Warning")]}),b),P=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)r.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(t[n[a]]=e[n[a]])}return t};const w=n.forwardRef(((e,r)=>{const{prefixCls:t,className:a,rootClassName:p,style:m,children:f,icon:h,color:x,onClose:v,bordered:b=!0,visible:Z}=e,y=P(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:j,direction:k,tag:C}=n.useContext(d.E_),[w,E]=n.useState(!0),_=(0,s.Z)(y,["closeIcon","closable"]);n.useEffect((()=>{void 0!==Z&&E(Z)}),[Z]);const S=(0,o.o2)(x),T=(0,o.yT)(x),$=S||T,A=Object.assign(Object.assign({backgroundColor:x&&!$?x:void 0},null==C?void 0:C.style),m),M=j("tag",t),[L,N,q]=g(M),z=l()(M,null==C?void 0:C.className,{[`${M}-${x}`]:$,[`${M}-has-color`]:x&&!$,[`${M}-hidden`]:!w,[`${M}-rtl`]:"rtl"===k,[`${M}-borderless`]:!b},a,p,N,q),B=e=>{e.stopPropagation(),null==v||v(e),e.defaultPrevented||E(!1)},[,D]=(0,i.Z)((0,i.w)(e),(0,i.w)(C),{closable:!1,closeIconRender:e=>{const r=n.createElement("span",{className:`${M}-close-icon`,onClick:B},e);return(0,c.wm)(e,r,(e=>({onClick:r=>{var t;null===(t=null==e?void 0:e.onClick)||void 0===t||t.call(e,r),B(r)},className:l()(null==e?void 0:e.className,`${M}-close-icon`)})))}}),R="function"==typeof y.onClick||f&&"a"===f.type,F=h||null,U=F?n.createElement(n.Fragment,null,F,f&&n.createElement("span",null,f)):f,K=n.createElement("span",Object.assign({},_,{ref:r,className:z,style:A}),U,D,S&&n.createElement(I,{key:"preset",prefixCls:M}),T&&n.createElement(O,{key:"status",prefixCls:M}));return L(R?n.createElement(u.Z,{component:"Tag"},K):K)})),E=w;E.CheckableTag=j;var _=E}}]);