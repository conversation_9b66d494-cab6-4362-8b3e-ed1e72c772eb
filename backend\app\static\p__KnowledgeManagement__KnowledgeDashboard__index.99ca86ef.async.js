"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3325],{266:function(e,t,r){r.r(t),r.d(t,{default:function(){return L}});var a=r(15009),s=r.n(a),n=r(99289),i=r.n(n),c=r(5574),l=r.n(c),o=r(67294),d=r(97131),u=r(98137),h=r(74330),p=r(71230),f=r(15746),x=r(4393),m=r(14409),b=r(77171),v=r(21377),g=r(78158);function j(){return y.apply(this,arguments)}function y(){return(y=i()(s()().mark((function e(){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,g.N)("/api/knowledge_base/statistics/overall",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function w(){return N.apply(this,arguments)}function N(){return(N=i()(s()().mark((function e(){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,g.N)("/api/knowledge_base/statistics/files",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function S(e){return k.apply(this,arguments)}function k(){return(k=i()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,g.N)("/api/knowledge_base/statistics/access",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var Z=r(85893),L=(u.default.RangePicker,function(){var e=(0,o.useState)(!0),t=l()(e,2),r=t[0],a=t[1],n=(0,o.useState)({knowledge_bases:0,files:0,chunks:0}),c=l()(n,2),u=c[0],g=c[1],y=(0,o.useState)([]),N=l()(y,2),k=N[0],L=N[1],_=(0,o.useState)([]),z=l()(_,2),W=z[0],C=z[1],E=(0,o.useState)([]),G=l()(E,2),R=G[0],T=G[1];(0,o.useEffect)((function(){var e=function(){var e=i()(s()().mark((function e(){var t,r,n,i,c;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,a(!0),e.next=4,Promise.all([j(),w(),S({days:30})]);case 4:t=e.sent,r=l()(t,3),n=r[0],i=r[1],c=r[2],n.success&&g(n.data),i.success&&(L(i.data.by_status.map((function(e){return{name:e.status||"未知",value:e.count}}))),C(i.data.by_type.map((function(e){return{name:e.type||"未知",value:e.count}})))),c.success&&T(c.data),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(0),console.error("获取仪表盘数据失败:",e.t0);case 17:return e.prev=17,a(!1),e.finish(17);case 20:case"end":return e.stop()}}),e,null,[[0,14,17,20]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);var A=[{title:"知识库总数",value:u.knowledge_bases.toLocaleString()},{title:"文件总数",value:u.files.toLocaleString()},{title:"Chunk总量",value:u.chunks.toLocaleString()}],H={grid:{left:"8%",right:"3%",bottom:"15%",top:"3%",containLabel:!1},xAxis:{type:"category",data:R.map((function(e){return e.date})),boundaryGap:!0,axisLabel:{fontSize:12,color:"#666"},axisLine:{lineStyle:{color:"#e8e8e8"}}},yAxis:{type:"value",splitNumber:7,axisLabel:{fontSize:12,color:"#666"},splitLine:{lineStyle:{color:"#f0f0f0"}}},series:[{data:R.map((function(e){return e.count})),type:"bar",barWidth:"50%",itemStyle:{color:"#1890ff",borderRadius:[2,2,0,0]}}],tooltip:{trigger:"axis",backgroundColor:"rgba(0, 0, 0, 0.8)",textStyle:{color:"#fff"}}},O={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:20,top:"center",itemWidth:10,itemHeight:10,textStyle:{fontSize:12}},series:[{name:"处理状态",type:"pie",radius:["40%","65%"],center:["35%","50%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:4,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:14,fontWeight:"bold"}},labelLine:{show:!1},data:k}]},P={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:20,top:"center",itemWidth:10,itemHeight:10,textStyle:{fontSize:12}},series:[{name:"文件类型",type:"pie",radius:["40%","65%"],center:["35%","50%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:4,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:14,fontWeight:"bold"}},labelLine:{show:!1},data:W}]};return(0,Z.jsx)(d._z,{children:(0,Z.jsx)(h.Z,{spinning:r,size:"large",children:(0,Z.jsxs)("div",{className:"dashboard-container",children:[(0,Z.jsx)(p.Z,{gutter:16,className:"stat-cards",children:A.map((function(e,t){return(0,Z.jsx)(f.Z,{xs:24,sm:12,md:12,lg:8,children:(0,Z.jsxs)(x.Z,{bordered:!1,className:"stat-card",children:[(0,Z.jsx)("div",{className:"card-header",children:(0,Z.jsx)("span",{children:e.title})}),(0,Z.jsx)("div",{className:"card-content",children:(0,Z.jsx)("div",{className:"card-main",children:(0,Z.jsxs)("div",{className:"card-left",children:[(0,Z.jsx)("div",{className:"card-value",children:e.value}),e.rate&&(0,Z.jsxs)("div",{className:"card-rates",children:[(0,Z.jsxs)("span",{children:[e.rate.text,(0,Z.jsxs)("span",{className:e.rate.up?"rate-up":"rate-down",children:[e.rate.value," ",e.rate.up?(0,Z.jsx)(m.Z,{}):(0,Z.jsx)(b.Z,{})]})]}),e.rate2&&(0,Z.jsxs)("span",{children:[e.rate2.text,(0,Z.jsxs)("span",{className:e.rate2.up?"rate-up":"rate-down",children:[e.rate2.value," ",e.rate2.up?(0,Z.jsx)(m.Z,{}):(0,Z.jsx)(b.Z,{})]})]})]})]})})}),(0,Z.jsx)("div",{className:"card-footer",children:e.footer&&(0,Z.jsxs)(Z.Fragment,{children:[(0,Z.jsx)("span",{children:e.footer.text}),(0,Z.jsx)("span",{children:e.footer.value})]})})]})},t)}))}),(0,Z.jsx)(p.Z,{gutter:16,className:"chart-section",children:(0,Z.jsx)(f.Z,{xs:24,md:24,children:(0,Z.jsx)(x.Z,{bordered:!1,className:"chart-card",title:"知识库访问量 (最近30天)",children:(0,Z.jsx)("div",{className:"chart-content",children:(0,Z.jsx)(v.Z,{option:H,style:{height:"100%",width:"100%"}})})})})}),(0,Z.jsxs)(p.Z,{gutter:16,className:"bottom-section",children:[(0,Z.jsx)(f.Z,{xs:24,md:12,children:(0,Z.jsx)(x.Z,{title:"文件处理状态",bordered:!1,className:"category-card",children:(0,Z.jsx)("div",{className:"pie-chart",children:(0,Z.jsx)(v.Z,{option:O,style:{height:320,width:"100%"}})})})}),(0,Z.jsx)(f.Z,{xs:24,md:12,children:(0,Z.jsx)(x.Z,{title:"文件类型分布",bordered:!1,className:"category-card",children:(0,Z.jsx)("div",{className:"pie-chart",children:(0,Z.jsx)(v.Z,{option:P,style:{height:320,width:"100%"}})})})})]})]})})})})}}]);