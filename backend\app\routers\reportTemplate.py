from fastapi import APIRouter, HTTPException, status, Query, Depends, Body
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime
from bson import ObjectId
from ..db.mongodb import db
from ..utils.auth import verify_token
# 导入 Pydantic 模型而不是 MongoEngine 模型
from ..models.reportTemplate import ReportTemplateModel, TemplateSectionModel, ReportTaskModel
# 仍然需要 MongoEngine 模型用于数据库操作
from ..models.reportTemplate import ReportTemplate, TemplateSection, ReportTask

from app.utils.logging_config import setup_logging, get_logger
import traceback
setup_logging()
logger = get_logger(__name__)


router = APIRouter(
    prefix="/api",
    tags=["reportTemplate"]
)

# Pydantic模型，用于接口请求和响应
class PyObjectId(str):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("无效的ObjectId")
        return str(v)


# 报告模板API
@router.get("/reportTemplate", response_model=Dict[str, Any])
async def get_templates(
    current: int = Query(1, description="当前页码", ge=1),
    pageSize: int = Query(10, description="每页数量", ge=1, le=100),
    search: Optional[str] = None,
    template_type: Optional[str] = None,
    is_active: Optional[bool] = None,
    current_user: dict = Depends(verify_token)
):
    """
    获取报告模板列表

    Args:
        current: 当前页码
        pageSize: 每页数量
        search: 模板标题搜索关键词
        template_type: 模板类型筛选
        is_active: 是否激活状态筛选
        current_user: 当前登录用户

    Returns:
        包含模板列表的统一响应格式
    """
    # 构建查询条件
    query = {}
    if search:
        query["title"] = {"$regex": search, "$options": "i"}
    if template_type:
        query["template_type"] = template_type
    if is_active is not None:
        query["is_active"] = is_active
    
    # 计算总数
    total = await db.report_templates.count_documents(query)
    
    # 获取分页数据
    skip = (current - 1) * pageSize
    templates = await db.report_templates.find(query).skip(skip).limit(pageSize).to_list(length=pageSize)
    for template in templates:
        template["id"] = str(template["_id"])
        del template["_id"]

    
    return {
        "success": True,
        "data": templates,
        "message": "success",
        "total": total,
        "current": current,
        "pageSize": pageSize
    }
@router.get("/reportTemplate/detail", response_model=Dict[str, Any])
async def get_template(template_id: str = Query(..., description="模板ID"), current_user: dict = Depends(verify_token)):
    """
    获取特定报告模板详情

    Args:
        template_id: 模板ID
        current_user: 当前登录用户

    Returns:
        包含模板详情的统一响应格式
    """
    try:
        template = await db.report_templates.find_one({"_id": ObjectId(template_id)})
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模板不存在"
            )
        return {
            "success": True,
            "data": template,
            "message": "success"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"获取模板失败: {str(e)}"
        )

# 修改这里，使用 ReportTemplateModel 作为请求体模型
@router.post("/reportTemplate", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
async def create_template(template_data: ReportTemplateModel, current_user: dict = Depends(verify_token)):
    """
    创建新的报告模板

    Args:
        template: 模板创建数据
        current_user: 当前登录用户

    Returns:
        包含新创建模板的统一响应格式
    """
    try:
        # 将 Pydantic 模型转换为 dict 后创建 MongoEngine 文档
        new_template = {
            "title": template_data.title,
            "description": template_data.description,
            "template_type": template_data.template_type,
            "tags": template_data.tags or [],
            "file_path": template_data.file_path,
            "file_type": template_data.file_type,
            "content_template": template_data.content_template,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "user_id": current_user["id"],
            "user_name": current_user["name"],
            "is_published": False,
            "is_active": True,
            "template_sections": []
        }
        
        result = await db.report_templates.insert_one(new_template)
        
        return {
            "success": True,
            "id": str(result.inserted_id),
            "message": "模板创建成功"
        }
    except Exception as e:
        logger.error(f"创建模板失败: {str(e)}")
        traceback.print_exc()
        # raise HTTPException(
        #     status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        #     detail=f"创建模板失败: {str(e)}"
        # )
        return {
            "success": False,
            "message": "模板创建成功"
        }
    

# 修改这里，使用 ReportTemplateModel 作为请求体模型
@router.put("/reportTemplate/{template_id}", response_model=Dict[str, Any])
async def update_template(
    template_id: str, 
    template_data: ReportTemplateModel, 
    current_user: dict = Depends(verify_token)
):
    """
    更新报告模板

    Args:
        template_id: 模板ID
        template: 模板更新数据
        current_user: 当前登录用户

    Returns:
        包含更新后模板的统一响应格式
    """
    try:
        # 检查模板是否存在
        existing_template = await db.report_templates.find_one({"_id": ObjectId(template_id)})
        if not existing_template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模板不存在"
            )
        
        # 构建更新数据，从 Pydantic 模型转换为 dict
        update_data = template_data.dict(exclude_unset=True)
        update_data["updated_at"] = datetime.now()
        
        await db.report_templates.update_one(
            {"_id": ObjectId(template_id)},
            {"$set": update_data}
        )
        
        # 获取更新后的数据
        updated_template = await db.report_templates.find_one({"_id": ObjectId(template_id)})
        
        return {
            "success": True,
            "data": updated_template,
            "message": "模板更新成功"
        }
    
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新模板失败: {str(e)}"
        )

@router.delete("/reportTemplate/{template_id}", response_model=Dict[str, Any])
async def delete_template(template_id: str, current_user: dict = Depends(verify_token)):
    """
    删除报告模板

    Args:
        template_id: 模板ID
        current_user: 当前登录用户

    Returns:
        包含删除结果的统一响应格式
    """
    try:
        result = await db.report_templates.delete_one({"_id": ObjectId(template_id)})
        if result.deleted_count == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模板不存在"
            )
        return {
            "success": True,
            "message": "模板删除成功"
        }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除模板失败: {str(e)}"
        )

# 模板章节API
@router.get("/reportTemplate/sections", response_model=Dict[str, Any])
async def get_sections(
    current: int = Query(1, description="当前页码", ge=1),
    pageSize: int = Query(10, description="每页数量", ge=1, le=100),
    search: Optional[str] = None,
    section_type: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    """
    获取模板章节列表

    Args:
        current: 当前页码
        pageSize: 每页数量
        search: 章节标题搜索关键词
        section_type: 章节类型筛选
        current_user: 当前登录用户

    Returns:
        包含章节列表的统一响应格式
    """
    logger.info(f"获取模板章节列表: {current}, {pageSize}, {search}, {section_type}")
    # 构建查询条件
    query = {}
    if search:
        query["title"] = {"$regex": search, "$options": "i"}
    if section_type:
        query["section_type"] = section_type
    
    # 计算总数
    total = await db.template_sections.count_documents(query)
    
    # 获取分页数据
    skip = (current - 1) * pageSize
    sections = await db.template_sections.find(query).skip(skip).limit(pageSize).to_list()
    for section in sections:
        section["id"] = str(section["_id"])
        del section["_id"]
    
    return {
        "success": True,
        "data": sections,
        "message": "success",
        "total": total,
        "current": current,
        "pageSize": pageSize
    }

@router.get("/reportTemplate/sections/{section_id}", response_model=Dict[str, Any])
async def get_section(section_id: str, current_user: dict = Depends(verify_token)):
    """
    获取特定模板章节详情

    Args:
        section_id: 章节ID
        current_user: 当前登录用户

    Returns:
        包含章节详情的统一响应格式
    """
    try:
        section = await db.template_sections.find_one({"_id": ObjectId(section_id)})

        if not section:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="章节不存在"
            )
        section['id'] = str(section['_id'])
        del section['_id']
        return {
            "success": True,
            "data": section,
            "message": "success"
        }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"获取章节失败: {str(e)}"
        )

# 修改这里，使用 TemplateSectionModel 作为请求体模型
@router.post("/reportTemplate/sections", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
async def create_section(section_data: TemplateSectionModel, current_user: dict = Depends(verify_token)):
    """
    创建新的模板章节

    Args:
        section: 章节创建数据
        current_user: 当前登录用户

    Returns:
        包含新创建章节的统一响应格式
    """
    new_section = {
        "title": section_data.title,
        "section_type": section_data.section_type,
        "tags": section_data.tags or [],
        "prompt_text": section_data.prompt_text,
        "data_requirements": section_data.data_requirements,
        "content_template": section_data.content_template,
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
        "child_sections": []
    }
    
    result = await db.template_sections.insert_one(new_section)
    new_section["id"] = str(result.inserted_id)
    
    return {
        "success": True,
        "data": new_section,
        "message": "章节创建成功"
    }

# 修改这里，使用 TemplateSectionModel 作为请求体模型
@router.put("/reportTemplate/sections/{section_id}", response_model=Dict[str, Any])
async def update_section(
    section_id: str, 
    section_data: TemplateSectionModel, 
    current_user: dict = Depends(verify_token)
):
    """
    更新模板章节

    Args:
        section_id: 章节ID
        section: 章节更新数据
        current_user: 当前登录用户

    Returns:
        包含更新后章节的统一响应格式
    """
    try:
        # 检查章节是否存在
        existing_section = await db.template_sections.find_one({"_id": ObjectId(section_id)})
        if not existing_section:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="章节不存在"
            )
        
        # 构建更新数据，从 Pydantic 模型转换为 dict
        update_data = section_data.dict(exclude_unset=True)
        update_data["updated_at"] = datetime.now()
        
        await db.template_sections.update_one(
            {"_id": ObjectId(section_id)},
            {"$set": update_data}
        )
        
        # 获取更新后的数据
        updated_section = await db.template_sections.find_one({"_id": ObjectId(section_id)})
        
        return {
            "success": True,
            "data": updated_section,
            "message": "章节更新成功"
        }
    
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新章节失败: {str(e)}"
        )

@router.delete("/reportTemplate/template_sections/{section_id}", response_model=Dict[str, Any])
async def delete_section(section_id: str, current_user: dict = Depends(verify_token)):
    """
    删除模板章节

    Args:
        section_id: 章节ID
        current_user: 当前登录用户

    Returns:
        包含删除结果的统一响应格式
    """
    try:
        result = await db.template_sections.delete_one({"_id": ObjectId(section_id)})
        if result.deleted_count == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="章节不存在"
            )
        return {
            "success": True,
            "message": "章节删除成功"
        }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除章节失败: {str(e)}"
        )

# 模板与章节关联API
@router.get("/reportTemplate/template_sections", response_model=Dict[str, Any])
async def get_template_sections(
    template_id: str = Query(..., description="模板ID"),
    current: int = Query(1, description="当前页码", ge=1),
    pageSize: int = Query(10, description="每页数量", ge=1, le=100),
    current_user: dict = Depends(verify_token)
):
    """
    获取指定模板的所有章节

    Args:
        template_id: 模板ID
        current: 当前页码
        pageSize: 每页数量
        current_user: 当前登录用户

    Returns:
        包含模板章节列表的统一响应格式
    """
    try:
        template = await db.report_templates.find_one({"_id": ObjectId(template_id)})
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模板不存在"
            )
        
        sections = []
        total = 0
        
        if template.get("template_sections"):
            section_ids = template["template_sections"]
            total = len(section_ids)
            
            # 计算分页
            start = (current - 1) * pageSize
            end = start + pageSize
            page_section_ids = section_ids[start:end] if start < total else []
            
            if page_section_ids:
                # 将字符串ID转换为ObjectId
                object_ids = [ObjectId(id) for id in page_section_ids]
                cursor = db.template_sections.find({"_id": {"$in": object_ids}})
                sections = await cursor.to_list(length=len(page_section_ids))
        
        return {
            "success": True,
            "data": sections,
            "message": "success",
            "total": total,
            "current": current,
            "pageSize": pageSize
        }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取模板章节失败: {str(e)}"
        )

@router.post("/reportTemplate/template_sections/{template_id}", response_model=Dict[str, Any])
async def link_section_to_template(
    template_id: str, 
    section_id: str = Query(..., description="章节ID"),
    current_user: dict = Depends(verify_token)
):
    """
    将章节链接到模板

    Args:
        template_id: 模板ID
        section_id: 章节ID
        current_user: 当前登录用户

    Returns:
        包含操作结果的统一响应格式
    """
    try:
        # 检查模板和章节是否存在
        template = await db.report_templates.find_one({"_id": ObjectId(template_id)})
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模板不存在"
            )
        
        section = await db.template_sections.find_one({"_id": ObjectId(section_id)})
        if not section:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="章节不存在"
            )
        
        # 确保章节ID不重复添加
        template_sections = template.get("template_sections", [])
        if section_id not in template_sections:
            await db.report_templates.update_one(
                {"_id": ObjectId(template_id)},
                {
                    "$push": {"template_sections": section_id},
                    "$set": {"updated_at": datetime.now()}
                }
            )
        
        return {
            "success": True,
            "message": "章节已链接到模板"
        }
    
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"链接章节失败: {str(e)}"
        )

@router.delete("/reportTemplate/template_sections", response_model=Dict[str, Any])
async def unlink_section_from_template(
    template_id: str, 
    section_id: str = Query(..., description="章节ID"),
    current_user: dict = Depends(verify_token)
):
    """
    从模板中解除章节链接

    Args:
        template_id: 模板ID
        section_id: 章节ID
        current_user: 当前登录用户

    Returns:
        包含操作结果的统一响应格式
    """
    try:
        # 检查模板是否存在
        template = await db.report_templates.find_one({"_id": ObjectId(template_id)})
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模板不存在"
            )
        
        # 移除章节ID
        await db.report_templates.update_one(
            {"_id": ObjectId(template_id)},
            {
                "$pull": {"template_sections": section_id},
                "$set": {"updated_at": datetime.now()}
            }
        )
        
        return {
            "success": True,
            "message": "章节已从模板解除链接"
        }
    
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"解除章节链接失败: {str(e)}"
        )

@router.post("/reportTemplate/template_sections/child", response_model=Dict[str, Any])
async def add_child_section(
    section_id: str, 
    child_id: str = Query(..., description="子章节ID"),
    current_user: dict = Depends(verify_token)
):
    """
    为章节添加子章节

    Args:
        section_id: 父章节ID
        child_id: 子章节ID
        current_user: 当前登录用户

    Returns:
        包含操作结果的统一响应格式
    """
    try:
        # 检查章节是否存在
        parent_section = await db.template_sections.find_one({"_id": ObjectId(section_id)})
        if not parent_section:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="父章节不存在"
            )
        
        child_section = await db.template_sections.find_one({"_id": ObjectId(child_id)})
        if not child_section:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="子章节不存在"
            )
        
        # 确保子章节ID不重复添加
        child_sections = parent_section.get("child_sections", [])
        if child_id not in child_sections:
            await db.template_sections.update_one(
                {"_id": ObjectId(section_id)},
                {
                    "$push": {"child_sections": child_id},
                    "$set": {"updated_at": datetime.now()}
                }
            )
        
        return {
            "success": True,
            "message": "子章节添加成功"
        }
    
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"添加子章节失败: {str(e)}"
        )

@router.delete("/reportTemplate/template_sections/child", response_model=Dict[str, Any])
async def remove_child_section(
    section_id: str, 
    child_id: str = Query(..., description="子章节ID"),
    current_user: dict = Depends(verify_token)
):
    """
    移除章节的子章节

    Args:
        section_id: 父章节ID
        child_id: 子章节ID
        current_user: 当前登录用户

    Returns:
        包含操作结果的统一响应格式
    """
    try:
        # 检查章节是否存在
        parent_section = await db.template_sections.find_one({"_id": ObjectId(section_id)})
        if not parent_section:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="父章节不存在"
            )
        
        # 移除子章节ID
        await db.template_sections.update_one(
            {"_id": ObjectId(section_id)},
            {
                "$pull": {"child_sections": child_id},
                "$set": {"updated_at": datetime.now()}
            }
        )
        
        return {
            "success": True,
            "message": "子章节移除成功"
        }
    
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"移除子章节失败: {str(e)}"
        )
