(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[552],{92287:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"}},34804:function(e,t,r){"use strict";var n=r(1413),o=r(67294),a=r(66023),l=r(91146),i=function(e,t){return o.createElement(l.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},c=o.forwardRef(i);t.Z=c},64029:function(e,t,r){"use strict";var n=r(1413),o=r(67294),a=r(92287),l=r(91146),i=function(e,t){return o.createElement(l.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},c=o.forwardRef(i);t.Z=c},34843:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return J}});var n=r(15009),o=r.n(n),a=r(99289),l=r.n(a),i=r(5574),c=r.n(i),s=r(47019),d=r(71471),u=r(2487),p=r(4393),g=r(55102),m=r(27484),h=r.n(m),f=r(84110),x=r.n(f),v=r(97857),b=r.n(v),y=r(9783),C=r.n(y),k=r(13769),S=r.n(k),j=r(93967),w=r.n(j),T=r(67294),$=r(28846),O=(0,$.kc)((function(e){var t=e.token;return{standardFormRow:{display:"flex",width:"100%",marginBottom:"16px",paddingBottom:"16px",borderBottom:"1px dashed ".concat(t.colorSplit),".ant-form-item, .ant-legacy-form-item":{marginRight:"24px"},".ant-form-item-label, .ant-legacy-form-item-label":{label:{marginRight:"0",color:t.colorText}},".ant-form-item-label, .ant-legacy-form-item-label, .ant-form-item-control, .ant-legacy-form-item-control":{padding:"0",lineHeight:"32px"}},label:{flex:"0 0 auto",marginRight:"24px",color:t.colorTextHeading,fontSize:t.fontSize,textAlign:"right","& > span":{display:"inline-block",height:"32px",lineHeight:"32px","&::after":{content:"'：'"}}},content:{flex:"1 1 0",".ant-form-item, .ant-legacy-form-item":{"&:last-child":{display:"block",marginRight:"0"}}},standardFormRowLast:{marginBottom:"0",paddingBottom:"0",border:"none"},standardFormRowBlock:{".ant-form-item, .ant-legacy-form-item, div.ant-form-item-control-wrapper, div.ant-legacy-form-item-control-wrapper":{display:"block"}},standardFormRowGrid:{".ant-form-item, .ant-legacy-form-item, div.ant-form-item-control-wrapper, div.ant-legacy-form-item-control-wrapper":{display:"block"},".ant-form-item-label, .ant-legacy-form-item-label":{float:"left"}}}})),B=r(85893),N=["title","children","last","block","grid"],Z=function(e){var t=e.title,r=e.children,n=e.last,o=e.block,a=e.grid,l=S()(e,N),i=O().styles,c=w()(i.standardFormRow,C()(C()(C()({},i.standardFormRowBlock,o),i.standardFormRowLast,n),i.standardFormRowGrid,a));return(0,B.jsxs)("div",b()(b()({className:c},l),{},{children:[t&&(0,B.jsx)("div",{className:i.label,children:(0,B.jsx)("span",{children:t})}),(0,B.jsx)("div",{className:i.content,children:r})]}))},z=r(19632),R=r.n(z),I=r(64029),E=r(34804),F=r(66309),H=r(56790),P=(0,$.kc)((function(e){return{tagSelect:{position:"relative",maxHeight:"32px",marginLeft:"-8px",overflow:"hidden",lineHeight:"32px",transition:"all 0.3s",userSelect:"none",".ant-tag":{marginRight:"24px",padding:"0 8px",fontSize:e.token.fontSize}},trigger:{position:"absolute",top:"0",right:"0","span.anticon":{fontSize:"12px"}},expanded:{maxHeight:"200px",transition:"all 0.3s"},hasExpandTag:{paddingRight:"50px"}}})),L=F.Z.CheckableTag,M=function(e){var t=e.children,r=e.checked,n=e.onChange,o=e.value;return(0,B.jsx)(L,{checked:!!r,onChange:function(e){return n&&n(o,e)},children:t},o)};M.isTagSelectOption=!0;var _=function(e){var t=P().styles,r=e.children,n=e.hideCheckAll,o=void 0!==n&&n,a=e.className,l=e.style,i=e.expandable,s=e.actionsText,d=void 0===s?{}:s,u=(0,T.useState)(!1),p=c()(u,2),g=p[0],m=p[1],h=(0,H.C8)(e.defaultValue||[],{value:e.value,defaultValue:e.defaultValue,onChange:e.onChange}),f=c()(h,2),x=f[0],v=f[1],b=function(e){return e&&e.type&&(e.type.isTagSelectOption||"TagSelectOption"===e.type.displayName)},y=function(){return T.Children.toArray(r).filter((function(e){return b(e)})).map((function(e){return e.props.value}))||[]},k=function(e,t){var r=R()(x||[]),n=r.indexOf(e);t&&-1===n?r.push(e):!t&&n>-1&&r.splice(n,1),v(r)},S=y().length===(null==x?void 0:x.length),j=d.expandText,$=void 0===j?"展开":j,O=d.collapseText,N=void 0===O?"收起":O,Z=d.selectAllText,z=void 0===Z?"全部":Z,F=w()(t.tagSelect,a,C()(C()({},t.hasExpandTag,i),t.expanded,g));return(0,B.jsxs)("div",{className:F,style:l,children:[o?null:(0,B.jsx)(L,{checked:S,onChange:function(e){var t=[];e&&(t=y()),v(t)},children:z},"tag-select-__all__"),r&&T.Children.map(r,(function(e){return b(e)?T.cloneElement(e,{key:"tag-select-".concat(e.props.value),value:e.props.value,checked:x&&x.indexOf(e.props.value)>-1,onChange:k}):e})),i&&(0,B.jsx)("a",{className:t.trigger,onClick:function(){m(!g)},children:g?(0,B.jsxs)(B.Fragment,{children:[N," ",(0,B.jsx)(I.Z,{})]}):(0,B.jsxs)(B.Fragment,{children:[$,(0,B.jsx)(E.Z,{})]})})]})};_.Option=M;var A=_,W=r(78158);function V(e,t){return q.apply(this,arguments)}function q(){return(q=l()(o()().mark((function e(t,r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,W.N)("/api/useActiveCases",b()({method:"GET",params:t},r||{})));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var G=(0,$.kc)((function(e){var t=e.token;return{card:{".ant-card-meta-title":{marginBottom:"4px","& > a":{display:"inline-block",maxWidth:"100%",color:t.colorTextHeading}},".ant-card-meta-description":{height:"44px",overflow:"hidden",lineHeight:"22px"},"&:hover":{".ant-card-meta-title > a":{color:t.colorPrimary}}},cardItemContent:{display:"flex",height:"20px",marginTop:"16px",marginBottom:"-4px",lineHeight:"20px","& > span":{flex:"1",color:t.colorTextSecondary,fontSize:"12px"}},avatarList:{flex:"0 1 auto"},cardList:{marginTop:"24px"},coverCardList:{".ant-list .ant-list-item-content-single":{maxWidth:"100%"}},tags:{display:"flex",flexWrap:"wrap",gap:"8px"},tag:{padding:"2px 8px",backgroundColor:"#f5f5f5",borderRadius:"4px",fontSize:"12px"}}})),X=r(97131),D=r(76772);h().extend(x());var K=s.Z.Item,Q=d.Z.Paragraph,U=[{value:"合规",label:"合规"},{value:"消保",label:"消保"},{value:"金融",label:"金融"},{value:"科技",label:"科技"},{value:"RAG",label:"RAG"},{value:"知识库",label:"知识库"},{value:"智能问答",label:"智能问答"},{value:"BI",label:"BI"},{value:"数据分析",label:"数据分析"},{value:"可视化",label:"可视化"},{value:"文件分析",label:"文件分析"},{value:"智能分类",label:"智能分类"}],J=function(){var e=G().styles,t=(0,T.useState)(""),r=c()(t,2),n=r[0],a=r[1],i=(0,T.useState)(1),d=c()(i,2),m=d[0],h=d[1],f=s.Z.useForm(),x=c()(f,1)[0],v=(0,T.useState)([]),b=c()(v,2),y=b[0],C=b[1],k=(0,T.useState)(0),S=c()(k,2),j=S[0],w=S[1],$=(0,D.useRequest)(function(){var e=l()(o()().mark((function e(t){var r;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,V({current:t.current||1,pageSize:t.pageSize||8,category:t.category,searchText:t.searchText});case 2:return r=e.sent,C(r.data),w(r.total),e.abrupt("return",r);case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),{defaultParams:[{current:1,pageSize:8}],manual:!1,onSuccess:function(e){console.log("Fetched Data:",e)}}),O=$.loading,N=$.run,z=function(e){var t;h(1),N({current:1,pageSize:8,category:null===(t=e.category)||void 0===t?void 0:t.join(","),searchText:n})},R=y&&(0,B.jsx)(u.Z,{rowKey:"id",loading:O,grid:{gutter:16,xs:1,sm:2,md:3,lg:3,xl:4,xxl:4},pagination:{current:m,pageSize:8,total:j,onChange:function(e){h(e),N({current:e,pageSize:8,category:x.getFieldValue("category"),searchText:n})}},dataSource:y,renderItem:function(t){var r;return(0,B.jsx)(u.Z.Item,{children:(0,B.jsxs)(p.Z,{className:e.card,hoverable:!0,cover:(0,B.jsx)("div",{style:{height:"200px",overflow:"hidden"},children:(0,B.jsx)("img",{alt:t.name,src:t.cover_image,style:{width:"100%",height:"100%",objectFit:"cover"}})}),onClick:function(){return window.open(t.redirect_url,"_blank")},children:[(0,B.jsx)(p.Z.Meta,{title:(0,B.jsx)("a",{children:t.name}),description:(0,B.jsx)(Q,{ellipsis:{rows:2},children:t.description})}),(0,B.jsx)("div",{className:e.cardItemContent,children:(0,B.jsx)("span",{children:(null===(r=t.tags)||void 0===r?void 0:r.join(", "))||""})})]})})}});return(0,B.jsx)(X._z,{fixedHeader:!0,header:{title:"用户案例",breadcrumb:{routes:[{path:"/",breadcrumbName:"首页"},{path:"/use-cases",breadcrumbName:"用户案例"}]}},content:(0,B.jsx)("div",{style:{textAlign:"center"},children:(0,B.jsx)(g.Z.Search,{placeholder:"请输入关键词搜索",enterButton:"搜索",size:"large",style:{maxWidth:522,width:"100%"},onSearch:function(e){a(e),h(1),N({current:1,pageSize:8,searchText:e})}})}),children:(0,B.jsxs)("div",{className:e.coverCardList,children:[(0,B.jsx)(p.Z,{bordered:!0,children:(0,B.jsx)(s.Z,{form:x,layout:"inline",onValuesChange:function(e,t){z(t)},children:(0,B.jsx)(Z,{title:"所属类目",block:!0,style:{paddingBottom:11},children:(0,B.jsx)(K,{name:"category",children:(0,B.jsx)(A,{expandable:!0,onChange:function(e){x.setFieldsValue({category:e}),z({category:e})},children:U.map((function(e){return(0,B.jsx)(A.Option,{value:e.value,children:e.label},e.value)}))})})})})}),(0,B.jsx)("div",{className:e.cardList,children:R})]})})}},66309:function(e,t,r){"use strict";r.d(t,{Z:function(){return N}});var n=r(67294),o=r(93967),a=r.n(o),l=r(98423),i=r(98787),c=r(69760),s=r(96159),d=r(45353),u=r(53124),p=r(11568),g=r(15063),m=r(14747),h=r(83262),f=r(83559);const x=e=>{const{lineWidth:t,fontSizeIcon:r,calc:n}=e,o=e.fontSizeSM;return(0,h.IX)(e,{tagFontSize:o,tagLineHeight:(0,p.bf)(n(e.lineHeightSM).mul(o).equal()),tagIconSize:n(r).sub(n(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},v=e=>({defaultBg:new g.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var b=(0,f.I$)("Tag",(e=>(e=>{const{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:n,componentCls:o,calc:a}=e,l=a(n).sub(r).equal(),i=a(t).sub(r).equal();return{[o]:Object.assign(Object.assign({},(0,m.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:i,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:l}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(x(e))),v),y=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r};const C=n.forwardRef(((e,t)=>{const{prefixCls:r,style:o,className:l,checked:i,onChange:c,onClick:s}=e,d=y(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:g}=n.useContext(u.E_),m=p("tag",r),[h,f,x]=b(m),v=a()(m,`${m}-checkable`,{[`${m}-checkable-checked`]:i},null==g?void 0:g.className,l,f,x);return h(n.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},o),null==g?void 0:g.style),className:v,onClick:e=>{null==c||c(!i),null==s||s(e)}})))}));var k=C,S=r(98719);var j=(0,f.bk)(["Tag","preset"],(e=>(e=>(0,S.Z)(e,((t,r)=>{let{textColor:n,lightBorderColor:o,lightColor:a,darkColor:l}=r;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:n,background:a,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:l,borderColor:l},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(x(e))),v);const w=(e,t,r)=>{const n="string"!=typeof(o=r)?o:o.charAt(0).toUpperCase()+o.slice(1);var o;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var T=(0,f.bk)(["Tag","status"],(e=>{const t=x(e);return[w(t,"success","Success"),w(t,"processing","Info"),w(t,"error","Error"),w(t,"warning","Warning")]}),v),$=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r};const O=n.forwardRef(((e,t)=>{const{prefixCls:r,className:o,rootClassName:p,style:g,children:m,icon:h,color:f,onClose:x,bordered:v=!0,visible:y}=e,C=$(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:k,direction:S,tag:w}=n.useContext(u.E_),[O,B]=n.useState(!0),N=(0,l.Z)(C,["closeIcon","closable"]);n.useEffect((()=>{void 0!==y&&B(y)}),[y]);const Z=(0,i.o2)(f),z=(0,i.yT)(f),R=Z||z,I=Object.assign(Object.assign({backgroundColor:f&&!R?f:void 0},null==w?void 0:w.style),g),E=k("tag",r),[F,H,P]=b(E),L=a()(E,null==w?void 0:w.className,{[`${E}-${f}`]:R,[`${E}-has-color`]:f&&!R,[`${E}-hidden`]:!O,[`${E}-rtl`]:"rtl"===S,[`${E}-borderless`]:!v},o,p,H,P),M=e=>{e.stopPropagation(),null==x||x(e),e.defaultPrevented||B(!1)},[,_]=(0,c.Z)((0,c.w)(e),(0,c.w)(w),{closable:!1,closeIconRender:e=>{const t=n.createElement("span",{className:`${E}-close-icon`,onClick:M},e);return(0,s.wm)(e,t,(e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),M(t)},className:a()(null==e?void 0:e.className,`${E}-close-icon`)})))}}),A="function"==typeof C.onClick||m&&"a"===m.type,W=h||null,V=W?n.createElement(n.Fragment,null,W,m&&n.createElement("span",null,m)):m,q=n.createElement("span",Object.assign({},N,{ref:t,className:L,style:I}),V,_,Z&&n.createElement(j,{key:"preset",prefixCls:E}),z&&n.createElement(T,{key:"status",prefixCls:E}));return F(A?n.createElement(d.Z,{component:"Tag"},q):q)})),B=O;B.CheckableTag=k;var N=B},84110:function(e){e.exports=function(){"use strict";return function(e,t,r){e=e||{};var n=t.prototype,o={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function a(e,t,r,o){return n.fromToBase(e,t,r,o)}r.en.relativeTime=o,n.fromToBase=function(t,n,a,l,i){for(var c,s,d,u=a.$locale().relativeTime||o,p=e.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],g=p.length,m=0;m<g;m+=1){var h=p[m];h.d&&(c=l?r(t).diff(a,h.d,!0):a.diff(t,h.d,!0));var f=(e.rounding||Math.round)(Math.abs(c));if(d=c>0,f<=h.r||!h.r){f<=1&&m>0&&(h=p[m-1]);var x=u[h.l];i&&(f=i(""+f)),s="string"==typeof x?x.replace("%d",f):x(f,n,h.l,d);break}}if(n)return s;var v=d?u.future:u.past;return"function"==typeof v?v(s):v.replace("%s",s)},n.to=function(e,t){return a(e,t,this,!0)},n.from=function(e,t){return a(e,t,this)};var l=function(e){return e.$u?r.utc():r()};n.toNow=function(e){return this.to(l(this),e)},n.fromNow=function(e){return this.from(l(this),e)}}}()}}]);