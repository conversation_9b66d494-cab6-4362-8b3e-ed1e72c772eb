"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6412],{26412:function(e,t,n){n.d(t,{Z:function(){return k}});var l=n(67294),o=n(93967),a=n.n(o),i=n(74443),s=n(53124),r=n(98675),c=n(25378);var d={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1};var b=l.createContext({}),m=n(50344),p=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&t.indexOf(l)<0&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(l=Object.getOwnPropertySymbols(e);o<l.length;o++)t.indexOf(l[o])<0&&Object.prototype.propertyIsEnumerable.call(e,l[o])&&(n[l[o]]=e[l[o]])}return n};function g(e,t,n){const o=l.useMemo((()=>{return t||(e=n,(0,m.Z)(e).map((e=>Object.assign(Object.assign({},null==e?void 0:e.props),{key:e.key}))));var e}),[t,n]);return l.useMemo((()=>o.map((t=>{var{span:n}=t,l=p(t,["span"]);return"filled"===n?Object.assign(Object.assign({},l),{filled:!0}):Object.assign(Object.assign({},l),{span:"number"==typeof n?n:(0,i.m9)(e,n)})}))),[o,e])}var u=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&t.indexOf(l)<0&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(l=Object.getOwnPropertySymbols(e);o<l.length;o++)t.indexOf(l[o])<0&&Object.prototype.propertyIsEnumerable.call(e,l[o])&&(n[l[o]]=e[l[o]])}return n};var y=(e,t)=>{const[n,o]=(0,l.useMemo)((()=>function(e,t){let n=[],l=[],o=!1,a=0;return e.filter((e=>e)).forEach((e=>{const{filled:i}=e,s=u(e,["filled"]);if(i)return l.push(s),n.push(l),l=[],void(a=0);const r=t-a;a+=e.span||1,a>=t?(a>t?(o=!0,l.push(Object.assign(Object.assign({},s),{span:r}))):l.push(s),n.push(l),l=[],a=0):l.push(s)})),l.length>0&&n.push(l),n=n.map((e=>{const n=e.reduce(((e,t)=>e+(t.span||1)),0);if(n<t){const l=e[e.length-1];return l.span=t-(n-(l.span||1)),e}return e})),[n,o]}(t,e)),[t,e]);return n};var f=e=>{let{children:t}=e;return t};function O(e){return null!=e}var h=e=>{const{itemPrefixCls:t,component:n,span:o,className:i,style:s,labelStyle:r,contentStyle:c,bordered:d,label:m,content:p,colon:g,type:u,styles:y}=e,f=n,h=l.useContext(b),{classNames:j}=h;return d?l.createElement(f,{className:a()({[`${t}-item-label`]:"label"===u,[`${t}-item-content`]:"content"===u,[`${null==j?void 0:j.label}`]:"label"===u,[`${null==j?void 0:j.content}`]:"content"===u},i),style:s,colSpan:o},O(m)&&l.createElement("span",{style:Object.assign(Object.assign({},r),null==y?void 0:y.label)},m),O(p)&&l.createElement("span",{style:Object.assign(Object.assign({},r),null==y?void 0:y.content)},p)):l.createElement(f,{className:a()(`${t}-item`,i),style:s,colSpan:o},l.createElement("div",{className:`${t}-item-container`},(m||0===m)&&l.createElement("span",{className:a()(`${t}-item-label`,null==j?void 0:j.label,{[`${t}-item-no-colon`]:!g}),style:Object.assign(Object.assign({},r),null==y?void 0:y.label)},m),(p||0===p)&&l.createElement("span",{className:a()(`${t}-item-content`,null==j?void 0:j.content),style:Object.assign(Object.assign({},c),null==y?void 0:y.content)},p)))};function j(e,t,n){let{colon:o,prefixCls:a,bordered:i}=t,{component:s,type:r,showLabel:c,showContent:d,labelStyle:b,contentStyle:m,styles:p}=n;return e.map(((e,t)=>{let{label:n,children:g,prefixCls:u=a,className:y,style:f,labelStyle:O,contentStyle:j,span:$=1,key:v,styles:x}=e;return"string"==typeof s?l.createElement(h,{key:`${r}-${v||t}`,className:y,style:f,styles:{label:Object.assign(Object.assign(Object.assign(Object.assign({},b),null==p?void 0:p.label),O),null==x?void 0:x.label),content:Object.assign(Object.assign(Object.assign(Object.assign({},m),null==p?void 0:p.content),j),null==x?void 0:x.content)},span:$,colon:o,component:s,itemPrefixCls:u,bordered:i,label:c?n:null,content:d?g:null,type:r}):[l.createElement(h,{key:`label-${v||t}`,className:y,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},b),null==p?void 0:p.label),f),O),null==x?void 0:x.label),span:1,colon:o,component:s[0],itemPrefixCls:u,bordered:i,label:n,type:"label"}),l.createElement(h,{key:`content-${v||t}`,className:y,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},m),null==p?void 0:p.content),f),j),null==x?void 0:x.content),span:2*$-1,component:s[1],itemPrefixCls:u,bordered:i,content:g,type:"content"})]}))}var $=e=>{const t=l.useContext(b),{prefixCls:n,vertical:o,row:a,index:i,bordered:s}=e;return o?l.createElement(l.Fragment,null,l.createElement("tr",{key:`label-${i}`,className:`${n}-row`},j(a,e,Object.assign({component:"th",type:"label",showLabel:!0},t))),l.createElement("tr",{key:`content-${i}`,className:`${n}-row`},j(a,e,Object.assign({component:"td",type:"content",showContent:!0},t)))):l.createElement("tr",{key:i,className:`${n}-row`},j(a,e,Object.assign({component:s?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},t)))},v=n(11568),x=n(14747),S=n(83559),w=n(83262);const E=e=>{const{componentCls:t,labelBg:n}=e;return{[`&${t}-bordered`]:{[`> ${t}-view`]:{border:`${(0,v.bf)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"> table":{tableLayout:"auto"},[`${t}-row`]:{borderBottom:`${(0,v.bf)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:first-child":{"> th:first-child, > td:first-child":{borderStartStartRadius:e.borderRadiusLG}},"&:last-child":{borderBottom:"none","> th:first-child, > td:first-child":{borderEndStartRadius:e.borderRadiusLG}},[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,v.bf)(e.padding)} ${(0,v.bf)(e.paddingLG)}`,borderInlineEnd:`${(0,v.bf)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`> ${t}-item-label`]:{color:e.colorTextSecondary,backgroundColor:n,"&::after":{display:"none"}}}},[`&${t}-middle`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,v.bf)(e.paddingSM)} ${(0,v.bf)(e.paddingLG)}`}}},[`&${t}-small`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,v.bf)(e.paddingXS)} ${(0,v.bf)(e.padding)}`}}}}}};var C=(0,S.I$)("Descriptions",(e=>(e=>{const{componentCls:t,extraColor:n,itemPaddingBottom:l,itemPaddingEnd:o,colonMarginRight:a,colonMarginLeft:i,titleMarginBottom:s}=e;return{[t]:Object.assign(Object.assign(Object.assign({},(0,x.Wf)(e)),E(e)),{"&-rtl":{direction:"rtl"},[`${t}-header`]:{display:"flex",alignItems:"center",marginBottom:s},[`${t}-title`]:Object.assign(Object.assign({},x.vS),{flex:"auto",color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),[`${t}-extra`]:{marginInlineStart:"auto",color:n,fontSize:e.fontSize},[`${t}-view`]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},[`${t}-row`]:{"> th, > td":{paddingBottom:l,paddingInlineEnd:o},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},[`${t}-item-label`]:{color:e.labelColor,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${(0,v.bf)(i)} ${(0,v.bf)(a)}`},[`&${t}-item-no-colon::after`]:{content:'""'}},[`${t}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${t}-item-content`]:{display:"table-cell",flex:1,color:e.contentColor,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${t}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${t}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${t}-item-content`]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}})((0,w.IX)(e,{}))),(e=>({labelBg:e.colorFillAlter,labelColor:e.colorTextTertiary,titleColor:e.colorText,titleMarginBottom:e.fontSizeSM*e.lineHeightSM,itemPaddingBottom:e.padding,itemPaddingEnd:e.padding,colonMarginRight:e.marginXS,colonMarginLeft:e.marginXXS/2,contentColor:e.colorText,extraColor:e.colorText}))),N=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&t.indexOf(l)<0&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(l=Object.getOwnPropertySymbols(e);o<l.length;o++)t.indexOf(l[o])<0&&Object.prototype.propertyIsEnumerable.call(e,l[o])&&(n[l[o]]=e[l[o]])}return n};const P=e=>{const{prefixCls:t,title:n,extra:o,column:m,colon:p=!0,bordered:u,layout:f,children:O,className:h,rootClassName:j,style:v,size:x,labelStyle:S,contentStyle:w,styles:E,items:P,classNames:k}=e,B=N(e,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","styles","items","classNames"]),{getPrefixCls:I,direction:M,className:L,style:z,classNames:T,styles:W}=(0,s.dj)("descriptions"),R=I("descriptions",t),G=(0,c.Z)();const H=l.useMemo((()=>{var e;return"number"==typeof m?m:null!==(e=(0,i.m9)(G,Object.assign(Object.assign({},d),m)))&&void 0!==e?e:3}),[G,m]),X=g(G,P,O),Z=(0,r.Z)(x),_=y(H,X),[A,F,D]=C(R),q=l.useMemo((()=>({labelStyle:S,contentStyle:w,styles:{content:Object.assign(Object.assign({},W.content),null==E?void 0:E.content),label:Object.assign(Object.assign({},W.label),null==E?void 0:E.label)},classNames:{label:a()(T.label,null==k?void 0:k.label),content:a()(T.content,null==k?void 0:k.content)}})),[S,w,E,k,T,W]);return A(l.createElement(b.Provider,{value:q},l.createElement("div",Object.assign({className:a()(R,L,T.root,null==k?void 0:k.root,{[`${R}-${Z}`]:Z&&"default"!==Z,[`${R}-bordered`]:!!u,[`${R}-rtl`]:"rtl"===M},h,j,F,D),style:Object.assign(Object.assign(Object.assign(Object.assign({},z),W.root),null==E?void 0:E.root),v)},B),(n||o)&&l.createElement("div",{className:a()(`${R}-header`,T.header,null==k?void 0:k.header),style:Object.assign(Object.assign({},W.header),null==E?void 0:E.header)},n&&l.createElement("div",{className:a()(`${R}-title`,T.title,null==k?void 0:k.title),style:Object.assign(Object.assign({},W.title),null==E?void 0:E.title)},n),o&&l.createElement("div",{className:a()(`${R}-extra`,T.extra,null==k?void 0:k.extra),style:Object.assign(Object.assign({},W.extra),null==E?void 0:E.extra)},o)),l.createElement("div",{className:`${R}-view`},l.createElement("table",null,l.createElement("tbody",null,_.map(((e,t)=>l.createElement($,{key:t,index:t,colon:p,prefixCls:R,vertical:"vertical"===f,bordered:u,row:e})))))))))};P.Item=f;var k=P}}]);