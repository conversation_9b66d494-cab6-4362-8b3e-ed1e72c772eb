(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6297],{85175:function(e,t,r){"use strict";var n=r(1413),o=r(67294),a=r(48820),s=r(91146),c=function(e,t){return o.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},i=o.forwardRef(c);t.Z=i},69753:function(e,t,r){"use strict";var n=r(1413),o=r(67294),a=r(49495),s=r(91146),c=function(e,t){return o.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},i=o.forwardRef(c);t.Z=i},51042:function(e,t,r){"use strict";var n=r(1413),o=r(67294),a=r(42110),s=r(91146),c=function(e,t){return o.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},i=o.forwardRef(c);t.Z=i},88484:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var n=r(1413),o=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"upload",theme:"outlined"},s=r(91146),c=function(e,t){return o.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a}))};var i=o.forwardRef(c)},17424:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return ce}});var n=r(13769),o=r.n(n),a=r(9783),s=r.n(a),c=r(64599),i=r.n(c),l=r(97857),u=r.n(l),d=r(15009),p=r.n(d),f=r(99289),h=r.n(f),m=r(5574),y=r.n(m),x=r(67294),g=r(97131),v=r(12453),b=r(71471),k=r(55102),j=r(17788),Z=r(8232),C=r(2453),w=r(42075),S=r(66309),P=r(83622),_=r(67839),T=r(11550),N=r(34041),O=r(72269),z=r(96074),I=r(51042),E=r(88484),$=r(69753),B=r(85175),A=r(78158);function R(e){return L.apply(this,arguments)}function L(){return(L=h()(p()().mark((function e(t){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,A.N)("/api/prompts",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function F(e){return H.apply(this,arguments)}function H(){return(H=h()(p()().mark((function e(t){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,A.N)("/api/prompts/".concat(t),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function q(e){return W.apply(this,arguments)}function W(){return(W=h()(p()().mark((function e(t){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,A.N)("/api/prompts",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function D(e){return J.apply(this,arguments)}function J(){return(J=h()(p()().mark((function e(t){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,A.N)("/api/system_prompts",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function M(e,t){return V.apply(this,arguments)}function V(){return(V=h()(p()().mark((function e(t,r){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,A.N)("/api/prompts/".concat(t),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function U(e){return X.apply(this,arguments)}function X(){return(X=h()(p()().mark((function e(t){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,A.N)("/api/prompts/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function G(e){return K.apply(this,arguments)}function K(){return(K=h()(p()().mark((function e(t){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,A.N)("/api/prompts/".concat(t,"/copy"),{method:"POST"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Q(){return Y.apply(this,arguments)}function Y(){return(Y=h()(p()().mark((function e(){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,A.N)("/api/prompts/categories",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var ee=r(85893),te=["current","pageSize"],re=b.Z.Title,ne=b.Z.Text,oe=b.Z.Paragraph,ae=k.Z.TextArea,se=j.Z.confirm,ce=function(){var e,t=(0,x.useRef)(),r=Z.Z.useForm(),n=y()(r,1)[0],a=(0,x.useState)(!1),c=y()(a,2),l=c[0],d=c[1],f=(0,x.useState)(null),m=y()(f,2),b=m[0],A=m[1],L=(0,x.useState)(!1),H=y()(L,2),W=H[0],J=H[1],V=(0,x.useState)(null),X=y()(V,2),K=X[0],Y=X[1],ce=(0,x.useState)([]),ie=y()(ce,2),le=ie[0],ue=ie[1],de=(0,x.useState)(!1),pe=y()(de,2),fe=pe[0],he=pe[1],me=(0,x.useState)(!1),ye=y()(me,2),xe=ye[0],ge=ye[1],ve=(0,x.useState)([]),be=y()(ve,2),ke=be[0],je=be[1],Ze=(0,x.useState)([]),Ce=y()(Ze,2),we=Ce[0],Se=Ce[1],Pe=(0,x.useState)(!1),_e=y()(Pe,2),Te=_e[0],Ne=_e[1],Oe=[{label:"全部",desc:"所有模型",key:"all"},{label:"语言模型",desc:"文本处理",key:"language_model"},{label:"推理模型",desc:"推理模型",key:"reasoning_model"},{label:"多模态模型",desc:"多种数据类型处理",key:"multimodal_model"}];(0,x.useEffect)((function(){var e=function(){var e=h()(p()().mark((function e(){var t;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Q();case 3:(t=e.sent).success&&t.data&&ue(t.data),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取分类失败",e.t0),C.ZP.error("获取分类列表失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);var ze=function(e){A(e||null),e?n.setFieldsValue(u()(u()({},e),{},{title:e.title,content:e.content,category:e.category,positions:e.positions,models:e.models,language:e.language||"zh-CN",is_system:e.is_system,is_active:e.is_active})):(n.resetFields(),n.setFieldsValue({models:"language_model",category:[],positions:["analyst"],language:"zh-CN",is_system:!1,is_active:!0})),d(!0)},Ie=function(){var e=h()(p()().mark((function e(t){var r;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,F(t.id);case 3:(r=e.sent).success&&r.data&&(Y(r.data),J(!0)),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取提示词详情失败",e.t0),C.ZP.error("获取提示词详情失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}(),Ee=function(){var e=h()(p()().mark((function e(){var r,o,a,s;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,n.validateFields();case 3:if(r=e.sent,he(!0),o=u()(u()({},r),{},{category:"string"==typeof r.category?[r.category]:Array.isArray(r.category)?r.category:[]}),!b){e.next=12;break}return e.next=9,M(b.id,o);case 9:a=e.sent,e.next=15;break;case 12:return e.next=14,q(o);case 14:a=e.sent;case 15:a.success?(C.ZP.success("".concat(b?"更新":"创建","提示词成功")),d(!1),n.resetFields(),null===(s=t.current)||void 0===s||s.reload()):C.ZP.error(a.message||"".concat(b?"更新":"创建","提示词失败")),e.next=22;break;case 18:e.prev=18,e.t0=e.catch(0),console.error("表单验证或提交失败:",e.t0),C.ZP.error("".concat(b?"更新":"创建","提示词失败"));case 22:return e.prev=22,he(!1),e.finish(22);case 25:case"end":return e.stop()}}),e,null,[[0,18,22,25]])})));return function(){return e.apply(this,arguments)}}(),$e=function(){var e=h()(p()().mark((function e(r){var n,o;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,he(!0),e.next=4,G(r);case 4:(n=e.sent).success?(C.ZP.success("复制提示词成功"),null===(o=t.current)||void 0===o||o.reload()):C.ZP.error(n.message||"复制提示词失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("复制失败:",e.t0),C.ZP.error("复制提示词失败");case 12:return e.prev=12,he(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})));return function(t){return e.apply(this,arguments)}}(),Be=function(){var e=h()(p()().mark((function e(r,n){var o,a;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,he(!0),e.next=4,M(r,{is_active:!n});case 4:(o=e.sent).success?(C.ZP.success("".concat(n?"禁用":"启用","提示词成功")),null===(a=t.current)||void 0===a||a.reload()):C.ZP.error(o.message||"".concat(n?"禁用":"启用","提示词失败")),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("状态更新失败:",e.t0),C.ZP.error("".concat(n?"禁用":"启用","提示词失败"));case 12:return e.prev=12,he(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})));return function(t,r){return e.apply(this,arguments)}}(),Ae=function(){var e=h()(p()().mark((function e(){var r,n,o,a,s,c,l,u;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==we.length){e.next=3;break}return C.ZP.warning("请至少选择一条数据进行导入"),e.abrupt("return");case 3:Ne(!0),r=ke.filter((function(e){return we.includes(e.key)})),n=0,o=0,a=i()(r),e.prev=8,a.s();case 10:if((s=a.n()).done){e.next=28;break}if(c=s.value,e.prev=12,(l={title:c.title,content:c.content,category:c.category||[],positions:c.positions||[],is_system:!0,is_active:!0,models:c.models||["gpt4"],language:c.language||"zh-CN"}).title&&l.content){e.next=17;break}return o++,e.abrupt("continue",26);case 17:return e.next=19,D(l);case 19:n++,e.next=26;break;case 22:e.prev=22,e.t0=e.catch(12),o++,console.error("导入失败:",c,e.t0);case 26:e.next=10;break;case 28:e.next=33;break;case 30:e.prev=30,e.t1=e.catch(8),a.e(e.t1);case 33:return e.prev=33,a.f(),e.finish(33);case 36:Ne(!1),C.ZP.success("导入完成！成功 ".concat(n," 条，失败 ").concat(o," 条。")),n>0&&(null===(u=t.current)||void 0===u||u.reload()),ge(!1),je([]),Se([]);case 42:case"end":return e.stop()}}),e,null,[[8,30,33,36],[12,22]])})));return function(){return e.apply(this,arguments)}}(),Re=[{title:"提示词名称",dataIndex:"title",width:300,valueType:"text",render:function(e,t){return(0,ee.jsx)("a",{onClick:function(){return Ie(t)},children:t.title})}},{title:"分类",dataIndex:"category",valueType:"text",render:function(e,t){return(0,ee.jsx)(w.Z,{children:Array.isArray(t.category)?t.category.map((function(e){return(0,ee.jsx)(S.Z,{color:"blue",children:e},e)})):t.category&&(0,ee.jsx)(S.Z,{color:"blue",children:t.category})})},valueEnum:le.reduce((function(e,t){return u()(u()({},e),{},s()({},t,{text:t}))}),{})},{title:"类型",dataIndex:"is_system",valueType:"select",width:100,render:function(e,t){return(0,ee.jsx)(S.Z,{color:t.is_system?"success":"default",children:t.is_system?"系统提示词":"个人提示词"})},valueEnum:{true:{text:"系统提示词",status:"Success"},false:{text:"个人提示词",status:"Default"}}},{title:"创建者",dataIndex:"user",valueType:"text",width:100,fieldProps:{placeholder:"请输入创建者"}},{title:"状态",dataIndex:"is_active",valueType:"select",width:80,render:function(e,t){return(0,ee.jsx)(S.Z,{color:t.is_active?"success":"error",children:t.is_active?"已启用":"已禁用"})},valueEnum:{true:{text:"已启用",status:"Success"},false:{text:"已禁用",status:"Error"}}},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",sorter:!0,width:160,hideInSearch:!0},{title:"操作",dataIndex:"option",valueType:"option",fixed:"right",width:220,render:function(e,r){return[(0,ee.jsx)(P.ZP,{size:"small",type:"link",onClick:function(){return ze(r)},children:"编辑"},"edit"),(0,ee.jsx)(P.ZP,{size:"small",type:"link",onClick:function(){return $e(r.id)},children:"复制"},"copy"),(0,ee.jsx)(P.ZP,{size:"small",type:"link",danger:r.is_active,onClick:function(){return Be(r.id,r.is_active)},children:r.is_active?"禁用":"启用"},"status"),(0,ee.jsx)(P.ZP,{size:"small",type:"link",danger:!0,onClick:function(){return e=r.id,void se({title:"确认删除提示词",content:"您确定要删除这个提示词吗？此操作无法撤销。",okText:"确定删除",cancelText:"取消",okButtonProps:{danger:!0},onOk:(n=h()(p()().mark((function r(){var n,o;return p()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,he(!0),r.next=4,U(e);case 4:(n=r.sent).success?(C.ZP.success("删除提示词成功"),null===(o=t.current)||void 0===o||o.reload()):C.ZP.error(n.message||"删除提示词失败"),r.next=12;break;case 8:r.prev=8,r.t0=r.catch(0),console.error("删除失败:",r.t0),C.ZP.error("删除提示词失败");case 12:return r.prev=12,he(!1),r.finish(12);case 15:case"end":return r.stop()}}),r,null,[[0,8,12,15]])}))),function(){return n.apply(this,arguments)})});var e,n},children:"删除"},"delete")]}}];return(0,ee.jsxs)(g._z,{children:[(0,ee.jsx)(v.Z,{headerTitle:"提示词管理",actionRef:t,scroll:{x:1200},rowKey:"id",search:{labelWidth:"auto",defaultCollapsed:!1},toolBarRender:function(){return[(0,ee.jsx)(P.ZP,{type:"primary",onClick:function(){return ze()},icon:(0,ee.jsx)(I.Z,{}),children:"新建提示词"},"create"),(0,ee.jsx)(P.ZP,{onClick:function(){return ge(!0)},icon:(0,ee.jsx)(E.Z,{}),children:"批量导入系统提示词"},"import"),(0,ee.jsx)(P.ZP,{onClick:function(){var e=document.createElement("a");e.href="/static/templates/提示词导入模版.json",e.download="提示词导入模版.json",document.body.appendChild(e),e.click(),document.body.removeChild(e)},icon:(0,ee.jsx)($.Z,{}),children:"下载导入模板"},"downloadTemplate")]},request:h()(p()().mark((function e(){var t,r,n,a,s,c=arguments;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},r=t.current,n=t.pageSize,a=o()(t,te),e.prev=2,e.next=5,R(u()({current:r,pageSize:n},a));case 5:return s=e.sent,e.abrupt("return",{data:s.data||[],success:s.success,total:s.total||0});case 9:return e.prev=9,e.t0=e.catch(2),console.error("获取提示词失败:",e.t0),C.ZP.error("获取提示词列表失败"),e.abrupt("return",{data:[],success:!1,total:0});case 14:case"end":return e.stop()}}),e,null,[[2,9]])}))),columns:Re,rowSelection:{selections:[_.Z.SELECTION_ALL,_.Z.SELECTION_INVERT]},pagination:{showQuickJumper:!0,showSizeChanger:!0}}),(0,ee.jsxs)(j.Z,{title:"批量导入系统提示词",open:xe,onCancel:function(){ge(!1),je([]),Se([])},width:1e3,destroyOnClose:!0,footer:[(0,ee.jsx)(P.ZP,{onClick:function(){return ge(!1)},children:"取消"},"back"),(0,ee.jsxs)(P.ZP,{type:"primary",loading:Te,onClick:Ae,children:["保存选中的 ",we.length," 条"]},"submit")],children:[(0,ee.jsxs)(T.Z.Dragger,{name:"file",multiple:!1,beforeUpload:function(e){var t=new FileReader;return t.onload=function(t){try{var r;if(!e.name.endsWith(".json"))return void C.ZP.error("请上传JSON格式的文件");var n=null===(r=t.target)||void 0===r?void 0:r.result,o=JSON.parse(n);if(!Array.isArray(o))return void C.ZP.error("JSON文件内容必须是数组格式");var a=o.map((function(e,t){var r=e.positions;if("string"==typeof r)if(r.startsWith("[")&&r.endsWith("]"))try{r=JSON.parse(r)}catch(e){r=r.split(",").map((function(e){return e.trim()}))}else r=r.split(",").map((function(e){return e.trim()}));return Array.isArray(r)||(r=["analyst"]),u()(u()({},e),{},{positions:r,key:t})}));je(a),Se(a.map((function(e){return e.key})))}catch(e){C.ZP.error("文件解析失败，请检查是否为合法的JSON格式")}},t.readAsText(e,"UTF-8"),!1},onRemove:function(){return je([])},accept:".json",style:{marginBottom:20},children:[(0,ee.jsx)("p",{className:"ant-upload-drag-icon",children:(0,ee.jsx)(E.Z,{})}),(0,ee.jsx)("p",{className:"ant-upload-text",children:"点击或拖拽 JSON 文件到此区域进行上传"}),(0,ee.jsx)("p",{className:"ant-upload-hint",children:"仅支持JSON格式。文件内容应为数组，数组项包含 'title', 'content', 'category', 'positions' 等字段。"})]}),ke.length>0&&(0,ee.jsx)(_.Z,{rowSelection:{selectedRowKeys:we,onChange:Se},dataSource:ke,columns:[{title:"提示词名称",dataIndex:"title",key:"title",width:"20%"},{title:"提示词内容",dataIndex:"content",key:"content",width:"40%",ellipsis:!0},{title:"分类",dataIndex:"category",key:"category",width:"20%",render:function(e){return Array.isArray(e)?e.join(", "):e}},{title:"适用岗位",dataIndex:"positions",key:"positions",width:"20%",render:function(e){return Array.isArray(e)?e.join(", "):e}}],pagination:{pageSize:5}})]}),(0,ee.jsx)(j.Z,{title:b?"编辑提示词":"新建提示词",open:l,onCancel:function(){return d(!1)},destroyOnClose:!0,footer:[(0,ee.jsx)(P.ZP,{onClick:function(){return d(!1)},children:"取消"},"cancel"),(0,ee.jsx)(P.ZP,{type:"primary",onClick:Ee,loading:fe,children:"确定"},"submit")],width:600,children:(0,ee.jsxs)(Z.Z,{form:n,layout:"vertical",initialValues:{models:["gpt4"],category:[],positions:["analyst"],language:"zh-CN",is_system:!1,is_active:!0},children:[(0,ee.jsx)(Z.Z.Item,{name:"title",label:"提示词名称",rules:[{required:!0,message:"请输入提示词名称"}],children:(0,ee.jsx)(k.Z,{placeholder:"请输入提示词名称"})}),(0,ee.jsx)(Z.Z.Item,{name:"content",label:"提示词内容",rules:[{required:!0,message:"请输入提示词内容"}],children:(0,ee.jsx)(ae,{placeholder:"请输入提示词内容",rows:6,style:{resize:"none"}})}),(0,ee.jsx)(Z.Z.Item,{name:"category",label:"分类",rules:[{required:!0,message:"请选择分类"}],children:(0,ee.jsx)(N.default,{mode:"tags",options:le.map((function(e){return{label:e,value:e}})),placeholder:"请选择分类"})}),(0,ee.jsx)(Z.Z.Item,{name:"positions",label:"适用岗位",rules:[{required:!0,message:"请选择适用岗位"}],children:(0,ee.jsx)(N.default,{mode:"multiple",options:[{label:"金融分析师",value:"analyst"},{label:"风险管理师",value:"risk_manager"},{label:"投资经理",value:"investment_manager"}],placeholder:"请选择适用岗位"})}),(0,ee.jsx)(Z.Z.Item,{name:"models",label:"使用模型",rules:[{required:!0,message:"请选择使用模型"}],children:(0,ee.jsx)(N.default,{options:Oe.filter((function(e){return"all"!==e.key})).map((function(e){return{label:e.label,value:e.key}})),placeholder:"请选择使用模型"})}),(0,ee.jsx)(Z.Z.Item,{name:"language",label:"语言",rules:[{required:!0,message:"请选择语言"}],children:(0,ee.jsx)(N.default,{options:[{label:"中文",value:"zh-CN"},{label:"英文",value:"en-US"}],placeholder:"请选择语言"})}),(0,ee.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[(0,ee.jsx)(Z.Z.Item,{name:"is_system",label:"是否为系统提示词",valuePropName:"checked",children:(0,ee.jsx)(O.Z,{checkedChildren:"是",unCheckedChildren:"否"})}),(0,ee.jsx)(Z.Z.Item,{name:"is_active",label:"是否启用",valuePropName:"checked",children:(0,ee.jsx)(O.Z,{checkedChildren:"启用",unCheckedChildren:"禁用",defaultChecked:!0})})]})]})}),K&&(0,ee.jsxs)(j.Z,{title:(0,ee.jsx)(re,{level:5,style:{margin:0},children:K.title}),open:W,onCancel:function(){J(!1),Y(null)},width:700,footer:[(0,ee.jsx)(P.ZP,{icon:(0,ee.jsx)(B.Z,{}),onClick:function(){navigator.clipboard.writeText(K.content),C.ZP.success("提示词内容已复制到剪贴板")},children:"复制内容"},"copy"),(0,ee.jsx)(P.ZP,{type:"primary",onClick:function(){J(!1),Y(null)},children:"关闭"},"close")],children:[(0,ee.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,ee.jsxs)("div",{style:{display:"flex",flexWrap:"wrap",gap:"16px",marginBottom:"16px"},children:[(0,ee.jsxs)("div",{children:[(0,ee.jsx)(ne,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"分类"}),(0,ee.jsx)("div",{children:Array.isArray(K.category)?K.category.map((function(e){return(0,ee.jsx)(S.Z,{color:"blue",children:e},e)})):K.category&&(0,ee.jsx)(S.Z,{color:"blue",children:K.category})})]}),(0,ee.jsxs)("div",{children:[(0,ee.jsx)(ne,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"适用模型"}),(0,ee.jsx)("div",{children:(0,ee.jsx)(S.Z,{color:"green",children:(null===(e=Oe.find((function(e){return e.key===K.models})))||void 0===e?void 0:e.label)||K.models})})]}),(0,ee.jsxs)("div",{children:[(0,ee.jsx)(ne,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"适用岗位"}),(0,ee.jsx)("div",{children:K.positions&&K.positions.map((function(e){return(0,ee.jsx)(S.Z,{color:"gold",children:"analyst"===e?"金融分析师":"risk_manager"===e?"风险管理师":"investment_manager"===e?"投资经理":e},e)}))})]}),(0,ee.jsxs)("div",{children:[(0,ee.jsx)(ne,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"语言"}),(0,ee.jsx)("div",{children:(0,ee.jsx)(S.Z,{color:"purple",children:"zh-CN"===K.language?"中文":"en-US"===K.language?"英文":K.language})})]})]}),(0,ee.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[(0,ee.jsxs)("div",{children:[(0,ee.jsx)(ne,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"类型"}),(0,ee.jsx)(S.Z,{color:K.is_system?"success":"default",children:K.is_system?"系统提示词":"个人提示词"})]}),(0,ee.jsxs)("div",{children:[(0,ee.jsx)(ne,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"状态"}),(0,ee.jsx)(S.Z,{color:K.is_active?"success":"error",children:K.is_active?"已启用":"已禁用"})]})]})]}),(0,ee.jsx)(z.Z,{orientation:"left",children:"提示词内容"}),(0,ee.jsx)(oe,{style:{whiteSpace:"pre-wrap",maxHeight:"40vh",overflowY:"auto",fontSize:"14px",lineHeight:"1.8",padding:"16px",backgroundColor:"#f9f9f9",borderRadius:"8px",border:"1px solid #f0f0f0"},children:K.content}),(0,ee.jsxs)("div",{style:{marginTop:20,paddingTop:16,borderTop:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between"},children:[(0,ee.jsxs)("div",{children:[(0,ee.jsxs)(ne,{type:"secondary",style:{fontSize:"12px"},children:["创建时间: ",new Date(K.created_at).toLocaleString()]}),K.updated_at&&K.updated_at!==K.created_at&&(0,ee.jsxs)(ne,{type:"secondary",style:{fontSize:"12px",marginLeft:"16px"},children:["更新时间: ",new Date(K.updated_at).toLocaleString()]})]}),(0,ee.jsxs)(ne,{type:"secondary",style:{fontSize:"12px"},children:["创建者: ",K.user]})]})]})]})}},66309:function(e,t,r){"use strict";r.d(t,{Z:function(){return N}});var n=r(67294),o=r(93967),a=r.n(o),s=r(98423),c=r(98787),i=r(69760),l=r(96159),u=r(45353),d=r(53124),p=r(11568),f=r(15063),h=r(14747),m=r(83262),y=r(83559);const x=e=>{const{lineWidth:t,fontSizeIcon:r,calc:n}=e,o=e.fontSizeSM;return(0,m.IX)(e,{tagFontSize:o,tagLineHeight:(0,p.bf)(n(e.lineHeightSM).mul(o).equal()),tagIconSize:n(r).sub(n(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},g=e=>({defaultBg:new f.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var v=(0,y.I$)("Tag",(e=>(e=>{const{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:n,componentCls:o,calc:a}=e,s=a(n).sub(r).equal(),c=a(t).sub(r).equal();return{[o]:Object.assign(Object.assign({},(0,h.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:s,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:c,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:s}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(x(e))),g),b=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r};const k=n.forwardRef(((e,t)=>{const{prefixCls:r,style:o,className:s,checked:c,onChange:i,onClick:l}=e,u=b(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:f}=n.useContext(d.E_),h=p("tag",r),[m,y,x]=v(h),g=a()(h,`${h}-checkable`,{[`${h}-checkable-checked`]:c},null==f?void 0:f.className,s,y,x);return m(n.createElement("span",Object.assign({},u,{ref:t,style:Object.assign(Object.assign({},o),null==f?void 0:f.style),className:g,onClick:e=>{null==i||i(!c),null==l||l(e)}})))}));var j=k,Z=r(98719);var C=(0,y.bk)(["Tag","preset"],(e=>(e=>(0,Z.Z)(e,((t,r)=>{let{textColor:n,lightBorderColor:o,lightColor:a,darkColor:s}=r;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:n,background:a,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:s,borderColor:s},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(x(e))),g);const w=(e,t,r)=>{const n="string"!=typeof(o=r)?o:o.charAt(0).toUpperCase()+o.slice(1);var o;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var S=(0,y.bk)(["Tag","status"],(e=>{const t=x(e);return[w(t,"success","Success"),w(t,"processing","Info"),w(t,"error","Error"),w(t,"warning","Warning")]}),g),P=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r};const _=n.forwardRef(((e,t)=>{const{prefixCls:r,className:o,rootClassName:p,style:f,children:h,icon:m,color:y,onClose:x,bordered:g=!0,visible:b}=e,k=P(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:j,direction:Z,tag:w}=n.useContext(d.E_),[_,T]=n.useState(!0),N=(0,s.Z)(k,["closeIcon","closable"]);n.useEffect((()=>{void 0!==b&&T(b)}),[b]);const O=(0,c.o2)(y),z=(0,c.yT)(y),I=O||z,E=Object.assign(Object.assign({backgroundColor:y&&!I?y:void 0},null==w?void 0:w.style),f),$=j("tag",r),[B,A,R]=v($),L=a()($,null==w?void 0:w.className,{[`${$}-${y}`]:I,[`${$}-has-color`]:y&&!I,[`${$}-hidden`]:!_,[`${$}-rtl`]:"rtl"===Z,[`${$}-borderless`]:!g},o,p,A,R),F=e=>{e.stopPropagation(),null==x||x(e),e.defaultPrevented||T(!1)},[,H]=(0,i.Z)((0,i.w)(e),(0,i.w)(w),{closable:!1,closeIconRender:e=>{const t=n.createElement("span",{className:`${$}-close-icon`,onClick:F},e);return(0,l.wm)(e,t,(e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),F(t)},className:a()(null==e?void 0:e.className,`${$}-close-icon`)})))}}),q="function"==typeof k.onClick||h&&"a"===h.type,W=m||null,D=W?n.createElement(n.Fragment,null,W,h&&n.createElement("span",null,h)):h,J=n.createElement("span",Object.assign({},N,{ref:t,className:L,style:E}),D,H,O&&n.createElement(C,{key:"preset",prefixCls:$}),z&&n.createElement(S,{key:"status",prefixCls:$}));return B(q?n.createElement(u.Z,{component:"Tag"},J):J)})),T=_;T.CheckableTag=j;var N=T},64599:function(e,t,r){var n=r(96263);e.exports=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=n(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var o=0,a=function(){};return{s:a,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,c=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return c=e.done,e},e:function(e){i=!0,s=e},f:function(){try{c||null==r.return||r.return()}finally{if(i)throw s}}}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);