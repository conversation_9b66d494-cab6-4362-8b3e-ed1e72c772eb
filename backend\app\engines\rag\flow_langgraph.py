from typing import List, Dict, Any, Optional, TypedDict, Annotated
from langgraph.graph import StateGraph, END
from langgraph.graph.message import  add_messages
import asyncio
import json
from concurrent.futures import ThreadPoolExecutor
from app.engines.rerank.rerank_utils import rerank_texts, RerankResult
from app.engines.retrieval.retriever_utils import parallel_knowledge_search
from app.models.system_app_setting import KnowledgeQAParams
import traceback
from app.db.mongodb import db
from datetime import datetime
from app.utils.enums import RAGSearchType
from app.utils.logging_config import setup_logging, get_logger
from app.utils.promptStr import Rag<PERSON>ueryPrompt,StandardKnowledgeQAPrompt,RagQueryPromptWithMCP,MCPQueryPrompt
# MCP导入将在使用时动态导入
setup_logging()
logger = get_logger(__name__)


# 新增检索类型枚举


class SearchResult(TypedDict):
    role: str
    content: str # index_content
    # index_content: str
    score: float
    rerank_score: float
    id: str
    file_id: str
    knowledge_base_id: str
    chunk_index: int
    answer: str
    question: str
    created_at: datetime
    search_type: RAGSearchType  # 新增检索类型字段

class RAGState(TypedDict):
    query: str
    config: Dict[str, Any]
    # vector_results: Annotated[List[Dict], add_messages]
    # graph_results: Annotated[List[Dict], add_messages]
    merged_results: Annotated[List[Dict], add_messages]
    rerank_results: Annotated[List[Dict], add_messages]
    mcp_results: Annotated[List[Dict], add_messages]  # 新增：MCP工具调用结果
    error: Optional[str]
    output: Optional[Dict]

class FlowRAG:
    def __init__(self, config: KnowledgeQAParams,user_id: str):
        logger.info(f"config==》{config}")
        self.config = config
        self.user_id = user_id
        self.thread_pool_executor = ThreadPoolExecutor(max_workers=5)
        self.workflow = StateGraph(RAGState)
        self._setup_workflow()

    def _setup_workflow(self):
        # 定义节点
        self.workflow.add_node("search", self.search_node)
        # self.workflow.add_node("merge", self.merge_node)
        self.workflow.add_node("rerank", self.rerank_node)
        self.workflow.add_node("build_answer", self.build_prompt)

        # 构建流程
        self.workflow.set_entry_point("search")
        self.workflow.add_edge("search", "rerank")
        # self.workflow.add_edge("merge", "rerank")
        self.workflow.add_edge("rerank", "build_answer")
        self.workflow.add_edge("build_answer", END)

    async def search_node(self, state: RAGState) -> RAGState:
        """并行执行搜索任务"""
        logger.info('执行搜索任务')
        logger.info(state)
        query = state["query"]
        config = state["config"]
        # loop = asyncio.get_running_loop()

        try:
            results = await parallel_knowledge_search(
                query,
                config.knowledge_base_ids or [],
                config.config or {}
            )
            
            return {**state, "merged_results": results}  # 直接返回列表格式
        except Exception as e:
            traceback.print_exc()
            logger.error(f"搜索失败: {str(e)}")
            return {**state, "error": str(e)}

    async def rerank_node(self, state: RAGState) -> RAGState:
        """重排序结果"""
        logger.info('执行重排序==state')
        logger.info(state)
        logger.info("================")

        if state.get("error") or not state.get("merged_results"):
            return state
        
        rerank_id = self.config.rerank_id
        rerank_config = await db["reranks"].find_one({"id": int(rerank_id)})
        logger.info(f"rerank_config==》{rerank_config}")
        if not rerank_config:
            raise ValueError("Rerank model not found")
        try:
            # 修改调用方式：使用同步执行上下文
            merged_results = state["merged_results"]
            loop = asyncio.get_running_loop()
            final_top_k = self.config.config.get("final_top_k", 5) if self.config.config else 5

            rerank_results: List[RerankResult] = await loop.run_in_executor(
                self.thread_pool_executor,
                lambda: rerank_texts(
                    query=state["query"],
                    texts=[{'id': res.id, 'text': f"{res.additional_kwargs['q']} {res.additional_kwargs['a']}"} for res in merged_results],
                    config=rerank_config,
                    top_k=final_top_k
                )
            )
            # logger.info(f"==》重拍结果: {rerank_results}")

            # 创建ID到原始结果的映射
            id_to_res = {res.id: res for res in merged_results}
            final_results = []
            
            # 按照rerank_results的顺序过滤和排序
            for rank in rerank_results:
                if res := id_to_res.get(rank.id):
                    # 仅保留必要字段
                    final_results.append(res)

            logger.info(f"final_results==》重拍结果: {final_results}")
            return {**state, "rerank_results": final_results}
        except Exception as e:
            traceback.print_exc()
            logger.error(f"重排序失败: {str(e)}")
            return {**state, "error": str(e)}
    async def build_prompt(self, state: RAGState) -> RAGState:
        """构建答案"""
        logger.info('构建答案')
        logger.info(state)
        
        # 从配置中获取提示词模板
        logger.info(f"config==》{state['config']}")
        config:KnowledgeQAParams = state["config"]
        logger.info(f"config==》{config}")
        prompt = "{{quote}}\n请根据以上参考内容回答：{{question}}"
        retrieval_template = "【参考资料{{index}}】{{a}}"
        # logger.info(f"prompt==》{prompt}")
        # logger.info(f"retrieval_template==》{retrieval_template}")

        
        final_top_k = 5
        if config.prompt:
            prompt = config.prompt
        if config.prompt_retrieval:
            retrieval_template = config.prompt_retrieval
        if hasattr(config, 'final_top_k'):
            final_top_k = config.final_top_k

        # logger.info(f"prompt==》{prompt}")
        # logger.info(f"retrieval_template==》{retrieval_template}")
        
        # 处理重排序结果
        quote_strs = []
        for idx, res in enumerate(state["rerank_results"][:final_top_k], 1):  # 取前5个结果
            # 安全获取字段
            logger.info(f"res==》{res}")
            metadata = res.additional_kwargs or {}
            source_name = metadata.get("source_name", "未知来源")
            sourceId = metadata.get("sourceId", "无")
            
            # 格式化单个检索结果（修改此处）
            quote_strs.append(retrieval_template.replace("{{a}}", metadata.get('a', ''))
                                             .replace("{{q}}", metadata.get("q", ""))
                                             .replace("{{source}}", source_name)
                                             .replace("{{sourceId}}", sourceId)
                                             .replace("{{index}}", str(idx))
                                             .replace("{{time}}", datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
        # logger.info(f"quote_strs==》{quote_strs}")
        
        # 合并所有检索结果（新增quote参数处理）
        quote_block = "\n\n".join(quote_strs)
        logger.info(f"quote_block=============》{quote_block}")
        # 生成最终提示词（修改模板参数）
        final_prompt = prompt.replace("{{quote}}", quote_block)\
                             .replace("{{question}}", state["query"])  # 确保使用原始问题
        # logger.info(f"final_prompt==》{final_prompt}")
        # 更新输出结果
        return {**state, "output": {"role": "user", "content": final_prompt}}
    async def run(self, query: str) -> List[Dict[str, Any]]:
        """执行流程"""
        try:
            logger.info('启动流程')
            initial_state = RAGState(
                query=query,
                config=self.config,
                user_id=self.user_id,
                error=None,
                output=None
            )
            app = self.workflow.compile()
            result = await app.ainvoke(initial_state)
            return result
        except Exception as e:
            traceback.print_exc()
            logger.error(f"流程执行失败: {str(e)}")
            return []
        

class FlowRAGforchat2kb:  
    def __init__(self, config: KnowledgeQAParams,user_id: str):
        logger.info(f"config==》{config}")
        self.config = config
        self.user_id = user_id
        self.thread_pool_executor = ThreadPoolExecutor(max_workers=5)
        self.workflow = StateGraph(RAGState)
        self._setup_workflow()

    def _setup_workflow(self):
        # 定义节点
        self.workflow.add_node("search", self.search_node)
        # self.workflow.add_node("merge", self.merge_node)
        self.workflow.add_node("rerank", self.rerank_node)
        self.workflow.add_node("mcp_tools", self.mcp_tools_node)  # 新增：MCP工具节点
        self.workflow.add_node("build_answer", self.build_prompt)

        # 构建流程
        self.workflow.set_entry_point("search")
        self.workflow.add_edge("search", "rerank")
        # self.workflow.add_edge("merge", "rerank")
        self.workflow.add_edge("rerank", "mcp_tools")  # rerank -> mcp_tools
        self.workflow.add_edge("mcp_tools", "build_answer")  # mcp_tools -> build_answer
        self.workflow.add_edge("build_answer", END)

    async def search_node(self, state: RAGState) -> RAGState:
        """并行执行搜索任务"""
        logger.info('执行搜索任务')
        logger.info(state)
        query = state["query"]
        config = state["config"]
        # loop = asyncio.get_running_loop()

        try:
            results = await parallel_knowledge_search(
                query,
                config.knowledge_base_ids or [],
                config.config or {},
                user_id=self.user_id
            )
            
            return {**state, "merged_results": results}  # 直接返回列表格式
        except Exception as e:
            traceback.print_exc()
            logger.error(f"搜索失败: {str(e)}")
            return {**state, "error": str(e)}

    async def rerank_node(self, state: RAGState) -> RAGState:
        """重排序结果"""
        logger.info('执行重排序==state')
        logger.info(state)
        logger.info("================")

        if state.get("error") or not state.get("merged_results"):
            return state
        
        rerank_id = self.config.rerank_id
        rerank_config = await db["reranks"].find_one({"id": int(rerank_id)})
        logger.info(f"rerank_config==》{rerank_config}")
        if not rerank_config:
            raise ValueError("Rerank model not found")
        try:
            # 修改调用方式：使用同步执行上下文
            merged_results = state["merged_results"]
            loop = asyncio.get_running_loop()
            final_top_k = self.config.config.get("final_top_k", 5) if self.config.config else 5

            rerank_results: List[RerankResult] = await loop.run_in_executor(
                self.thread_pool_executor,
                lambda: rerank_texts(
                    query=state["query"],
                    texts=[{'id': res.id, 'text': f"{res.additional_kwargs['q']} {res.additional_kwargs['a']}"} for res in merged_results],
                    config=rerank_config,
                    top_k=final_top_k
                )
            )
            # logger.info(f"==》重拍结果: {rerank_results}")

            # 创建ID到原始结果的映射
            id_to_res = {res.id: res for res in merged_results}
            final_results = []
            
            # 按照rerank_results的顺序过滤和排序
            for rank in rerank_results:
                if res := id_to_res.get(rank.id):
                    # 仅保留必要字段
                    final_results.append(res)

            logger.info(f"final_results==》重拍结果: {final_results}")
            return {**state, "rerank_results": final_results}
        except Exception as e:
            traceback.print_exc()
            logger.error(f"重排序失败: {str(e)}")
            return {**state, "error": str(e)}

    async def mcp_tools_node(self, state: RAGState) -> RAGState:
        """MCP工具调用节点"""
        import asyncio  # 确保在函数开头导入

        # 检查是否启用MCP功能
        config = state.get("config", {})
        mcp_enabled = getattr(config, 'mcp_enabled', False)

        if not mcp_enabled:
            logger.info("MCP功能未启用")
            return {**state, "mcp_results": []}
        logger.info("开始执行mcp节点检索")
        try:
            # 导入配置
            from app.utils.config import settings

            # 配置MCP客户端 - 使用SSE连接匹配FastMCP服务
            # FastMCP 通常使用 /sse 端点
            mcp_config = {
                "websearch": {
                    "url": f"{settings.MCP_WEBSEARCH_URL}/sse",
                    "transport": settings.MCP_WEBSEARCH_TRANSPORT
                }
            }

            # 获取MCP配置参数
            mcp_params = getattr(config, 'mcp_config', {})

            # 调用MCP工具
            try:
                from langchain_mcp_adapters.client import MultiServerMCPClient
            except ImportError:
                logger.error("langchain-mcp-adapters包导入失败，请检查安装")
                return {**state, "mcp_results": []}

            # 使用推荐的方式1：不使用上下文管理器
            client = MultiServerMCPClient(mcp_config)

            # 获取可用工具
            try:
                tools = await client.get_tools()
            except Exception as e:
                logger.error(f"获取MCP工具失败: {e}")
                return {**state, "mcp_results": []}

            web_search_tool = None

            # 找到web_search工具
            for tool in tools:
                if tool.name == "web_search":
                    web_search_tool = tool
                    break

            if web_search_tool:
                # 提取查询字符串
                query_str = state["query"]
                if isinstance(query_str, dict):
                    query_str = query_str.get("query", "")

                # 构建工具调用参数
                search_params = {
                    "query": query_str,
                    "freshness": mcp_params.get("freshness", "noLimit"),
                    "summary": mcp_params.get("summary", "false"),
                    "count": mcp_params.get("count", "5")
                }

                # 添加可选参数
                if mcp_params.get("include"):
                    search_params["include"] = mcp_params["include"]
                if mcp_params.get("exclude"):
                    search_params["exclude"] = mcp_params["exclude"]

                # 调用工具（设置超时时间为1分钟）
                tool_result = await asyncio.wait_for(
                    web_search_tool.ainvoke(search_params),
                    timeout=60.0
                )

                # 解析结果
                mcp_results = self.parse_websearch_results(tool_result)
                logger.info(f"MCP工具调用成功，获取到 {len(mcp_results)} 条结果")

                return {**state, "mcp_results": mcp_results}
            else:
                logger.warning("未找到web_search工具")
                return {**state, "mcp_results": []}

        except asyncio.TimeoutError:
            logger.error("MCP工具调用超时（60秒）")
            return {**state, "mcp_results": []}
        except Exception as e:
            import traceback
            logger.error(f"MCP工具调用失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            # 冷处理：失败不影响主流程
            return {**state, "mcp_results": []}

    def parse_websearch_results(self, tool_result) -> List[Dict[str, Any]]:
        """解析WebSearch工具返回结果"""
        try:
            # 处理不同格式的返回结果
            if isinstance(tool_result, str):
                result_data = json.loads(tool_result)
            elif hasattr(tool_result, 'content'):
                # 如果是消息对象，提取content
                result_data = json.loads(tool_result.content)
            else:
                result_data = tool_result

            # 提取webPages.value中的关键信息
            web_pages = result_data.get("data", {}).get("webPages", {}).get("value", [])

            parsed_results = []
            for page in web_pages:
                parsed_results.append({
                    "title": page.get("name", ""),
                    "content": page.get("snippet", ""),
                    "url": page.get("url", ""),
                    "source": page.get("siteName", ""),
                    "publish_time": page.get("datePublished", ""),
                    "role":"system"
                })

            return parsed_results

        except Exception as e:
            logger.error(f"解析WebSearch结果失败: {e}")
            return []

    async def build_prompt(self, state: RAGState) -> RAGState:
        """构建答案"""
        logger.info('构建答案')
        logger.info(state)
        
        # 从配置中获取提示词模板
        logger.info(f"config==》{state['config']}")
        config:KnowledgeQAParams = state["config"]
        logger.info(f"config==》{config}")
        # prompt = "{{quote}}\n"+RagQueryPrompt+"\n{{question}}"
        prompt = "{{quote}}\n"+"\n{{RagQueryPrompt}}"+"\n{{question}}"
        retrieval_template = "【参考资料{{index}}】{{a}}"
        logger.info(f"prompt==》{prompt}")
        logger.info(f"retrieval_template==》{retrieval_template}")

        
        final_top_k = 5
        if config.prompt:
            prompt = config.prompt
        if config.prompt_retrieval:
            retrieval_template = config.prompt_retrieval
        if hasattr(config, 'final_top_k'):
            final_top_k = config.final_top_k

        logger.info(f"prompt==》{prompt}")
        logger.info(f"retrieval_template==》{retrieval_template}")
        
        # 处理重排序结果
        quote_strs = []
        context_strs = []
        query = state["query"]
        for idx, res in enumerate(state["rerank_results"][:final_top_k], 1):  # 取前5个结果
            # 安全获取字段
            logger.info(f"res==》{res}")
            metadata = res.additional_kwargs or {}
            source_name = metadata.get("source_name", "未知来源")
            sourceId = metadata.get("sourceId", "无")
            citation = f"[[citation:{idx}]]"
            content = metadata.get('a', '')
            formatted_text = f"{citation} {content}"
            # 格式化单个检索结果（修改此处）
            quote_strs.append(retrieval_template.replace("{{a}}", formatted_text)
                                             .replace("{{q}}", metadata.get("q", ""))
                                             .replace("{{source}}", source_name)
                                             .replace("{{sourceId}}", sourceId)
                                             .replace("{{index}}", str(idx))
                                             .replace("{{time}}", datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
        # logger.info(f"quote_strs==》{quote_strs}")
            #把citation、content以及source_name、sourceId、index、time以字典形式添加到context_strs中
            context_strs.append({"citation":citation,"content":content,"source_name":source_name,"sourceId":sourceId,"index":idx,"time":datetime.now().strftime("%Y-%m-%d %H:%M:%S")})
        # print(f"context_strs==》{context_strs}")
        # 合并所有检索结果（新增quote参数处理）
        quote_block = "\n\n".join(quote_strs)

        # 提取查询字符串
        query = state["query"]
        if isinstance(query, dict):
            query = query.get("query", "")

        # 构建MCP上下文字符串
        mcp_context_strs = []
        mcp_results = state.get("mcp_results", [])

        for idx, result in enumerate(mcp_results, 1):
            # 处理SystemMessage对象或字典
            if hasattr(result, 'content') and hasattr(result, 'additional_kwargs'):
                # 这是SystemMessage对象
                content = result.content
                kwargs = result.additional_kwargs
                title = kwargs.get('title', '未知标题')
                source = kwargs.get('source', '未知来源')
                url = kwargs.get('url', '')
                publish_time = kwargs.get('publish_time', '未知时间')
            else:
                # 这是普通字典
                content = result.get('content', '')
                title = result.get('title', '未知标题')
                source = result.get('source', '未知来源')
                url = result.get('url', '')
                publish_time = result.get('publish_time', '未知时间')

            # 截取内容，避免过长
            if len(content) > 200:
                content = content[:200] + "..."

            mcp_context_strs.append({
                "citation": f"[[网络引用:{idx}]]",
                "content": content,
                "source_name": source,
                "sourceId": url,  # 使用URL作为sourceId
                "index": f"网络{idx}",
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "url": url,
                "title": title,
                "publish_time": publish_time
            })

        # 构建MCP结果块用于提示词
        mcp_block = self.build_mcp_block_for_prompt(mcp_context_strs)
        logger.info(f"联网搜索结果mcp_context_strs==》{mcp_context_strs}")

        # 生成最终提示词（根据不同情况选择模板）
        if context_strs and mcp_context_strs:
            logger.info(f"既有知识库结果，又有MCP结果 - 使用混合提示词")
            # 情况1：既有知识库结果，又有MCP结果 - 使用混合提示词
            final_prompt = prompt.replace("{{quote}}", quote_block + "\n\n" + mcp_block)\
                             .replace("{{RagQueryPrompt}}", RagQueryPromptWithMCP)\
                             .replace("{{question}}", query)
        elif context_strs and not mcp_context_strs:
            logger.info(f"只有知识库结果，无MCP结果 - 使用传统RAG提示词")
            # 情况2：只有知识库结果，无MCP结果 - 使用传统RAG提示词
            final_prompt = prompt.replace("{{quote}}", quote_block)\
                             .replace("{{RagQueryPrompt}}", RagQueryPrompt)\
                             .replace("{{question}}", query)
        elif mcp_context_strs and not context_strs:
            logger.info(f"只有MCP结果，无知识库结果 - 使用MCP专用提示词")
            # 情况3：只有MCP结果，无知识库结果 - 使用MCP专用提示词
            final_prompt = prompt.replace("{{quote}}", mcp_block)\
                             .replace("{{RagQueryPrompt}}", MCPQueryPrompt)\
                             .replace("{{question}}", query)
        else:
            # 情况4：都没有结果 - 使用标准提示词
            logger.info(f"无知识库结果，无MCP结果 - 使用标准提示词")
            final_prompt = prompt.replace("{{quote}}", "")\
                             .replace("{{RagQueryPrompt}}", StandardKnowledgeQAPrompt)\
                             .replace("{{question}}", query)

        logger.info(f"最终上下文==》final_prompt==》{final_prompt}")

        # 更新输出结果，返回两套引用数据
        return {
            **state,
            "output": {"role": "user", "content": final_prompt,"context": context_strs,"mcp_context": mcp_context_strs,"query": query}
        }

    def build_mcp_block_for_prompt(self, mcp_context_strs: List[Dict[str, Any]]) -> str:
        """构建MCP结果块用于提示词"""
        if not mcp_context_strs:
            return ""

        mcp_parts = []
        for mcp_ctx in mcp_context_strs:
            citation = mcp_ctx.get('citation', '')
            content = mcp_ctx.get('content', '')
            title = mcp_ctx.get('title', '未知标题')
            source_name = mcp_ctx.get('source_name', '未知来源')

            # 格式化MCP结果，与知识库格式保持一致
            formatted_text = f"{citation} 标题：{title}\n内容：{content}\n来源：{source_name}"
            mcp_parts.append(formatted_text)

        return "\n\n".join(mcp_parts)

    async def run(self, query: str) -> List[Dict[str, Any]]:
        """执行流程"""
        try:
            logger.info('启动流程')
            initial_state = RAGState(
                query=query,
                config=self.config,
                user_id=self.user_id,
                error=None,
                output=None
            )
            app = self.workflow.compile()
            result = await app.ainvoke(initial_state)
            return result
        except Exception as e:
            traceback.print_exc()
            logger.error(f"流程执行失败: {str(e)}")
            return []
        

class FlowRAGforgeContext:  
    def __init__(self, config: KnowledgeQAParams):
        logger.info(f"config==》{config}")
        self.config = config
        self.thread_pool_executor = ThreadPoolExecutor(max_workers=5)
        self.workflow = StateGraph(RAGState)
        self._setup_workflow()

    def _setup_workflow(self):
        # 定义节点
        self.workflow.add_node("search", self.search_node)
        # self.workflow.add_node("merge", self.merge_node)
        self.workflow.add_node("rerank", self.rerank_node)
        # self.workflow.add_node("build_answer", self.build_prompt)
        
        # 构建流程
        self.workflow.set_entry_point("search")
        self.workflow.add_edge("search", "rerank")
        # self.workflow.add_edge("merge", "rerank")
        # self.workflow.add_edge("rerank", "build_answer")
        self.workflow.add_edge("rerank", END)

    async def search_node(self, state: RAGState) -> RAGState:
        """并行执行搜索任务"""
        logger.info('执行搜索任务')
        logger.info(state)
        query = state["query"]
        config = state["config"]
        # loop = asyncio.get_running_loop()

        try:
            results = await parallel_knowledge_search(
                query,
                config.knowledge_base_ids or [],
                config.config or {}
            )
            
            return {**state, "merged_results": results}  # 直接返回列表格式
        except Exception as e:
            traceback.print_exc()
            logger.error(f"搜索失败: {str(e)}")
            return {**state, "error": str(e)}

    async def rerank_node(self, state: RAGState) -> RAGState:
        """重排序结果"""
        logger.info('执行重排序==state')
        logger.info(state)
        logger.info("================")

        if state.get("error") or not state.get("merged_results"):
            return state
        
        rerank_id = self.config.rerank_id
        rerank_config = await db["reranks"].find_one({"id": int(rerank_id)})
        logger.info(f"rerank_config==》{rerank_config}")
        if not rerank_config:
            raise ValueError("Rerank model not found")
        try:
            # 修改调用方式：使用同步执行上下文
            merged_results = state["merged_results"]
            loop = asyncio.get_running_loop()
            final_top_k = self.config.config.get("final_top_k", 5) if self.config.config else 5

            rerank_results: List[RerankResult] = await loop.run_in_executor(
                self.thread_pool_executor,
                lambda: rerank_texts(
                    query=state["query"],
                    texts=[{'id': res.id, 'text': f"{res.additional_kwargs['q']} {res.additional_kwargs['a']}"} for res in merged_results],
                    config=rerank_config,
                    top_k=final_top_k
                )
            )
            # logger.info(f"==》重拍结果: {rerank_results}")

            # 创建ID到原始结果的映射
            id_to_res = {res.id: res for res in merged_results}
            final_results = []
            
            # 按照rerank_results的顺序过滤和排序
            for rank in rerank_results:
                if res := id_to_res.get(rank.id):
                    # 仅保留必要字段
                    final_results.append(res)

            logger.info(f"final_results==》重拍结果: {final_results}")
            return {**state, "rerank_results": final_results}
        except Exception as e:
            traceback.print_exc()
            logger.error(f"重排序失败: {str(e)}")
            return {**state, "error": str(e)}
    
        # """构建答案"""
        # logger.info('构建答案')
        # logger.info(state)
        
        # # 从配置中获取提示词模板
        # logger.info(f"config==》{state['config']}")
        # config:KnowledgeQAParams = state["config"]
        # logger.info(f"config==》{config}")
        # prompt = "{{quote}}\n"+RagQueryPrompt+"\n{{question}}"
        # retrieval_template = "【参考资料{{index}}】{{a}}"
        # logger.info(f"prompt==》{prompt}")
        # logger.info(f"retrieval_template==》{retrieval_template}")

        
        # final_top_k = 5
        # if config.prompt:
        #     prompt = config.prompt
        # if config.prompt_retrieval:
        #     retrieval_template = config.prompt_retrieval
        # if hasattr(config, 'final_top_k'):
        #     final_top_k = config.final_top_k

        # logger.info(f"prompt==》{prompt}")
        # logger.info(f"retrieval_template==》{retrieval_template}")
        
        # # 处理重排序结果
        # quote_strs = []
        # context_strs = []
        # for idx, res in enumerate(state["rerank_results"][:final_top_k], 1):  # 取前5个结果
        #     # 安全获取字段
        #     logger.info(f"res==》{res}")
        #     metadata = res.additional_kwargs or {}
        #     source_name = metadata.get("source_name", "未知来源")
        #     sourceId = metadata.get("sourceId", "无")
        #     citation = f"[[citation:{idx}]]"
        #     content = metadata.get('a', '')
        #     formatted_text = f"{citation} {content}"
        #     # 格式化单个检索结果（修改此处）
        #     quote_strs.append(retrieval_template.replace("{{a}}", formatted_text)
        #                                      .replace("{{q}}", metadata.get("q", ""))
        #                                      .replace("{{source}}", source_name)
        #                                      .replace("{{sourceId}}", sourceId)
        #                                      .replace("{{index}}", str(idx))
        #                                      .replace("{{time}}", datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
        # # logger.info(f"quote_strs==》{quote_strs}")
        #     #把citation、content以及source_name、sourceId、index、time以字典形式添加到context_strs中
        #     context_strs.append({"citation":citation,"content":content,"source_name":source_name,"sourceId":sourceId,"index":idx,"time":datetime.now().strftime("%Y-%m-%d %H:%M:%S")})
        # print(f"context_strs==》{context_strs}")
        # # 合并所有检索结果（新增quote参数处理）
        # quote_block = "\n\n".join(quote_strs)
        # logger.info(f"quote_block==》{quote_block}")
        # # 生成最终提示词（修改模板参数）
        # final_prompt = prompt.replace("{{quote}}", quote_block)\
        #                      .replace("{{question}}", state["query"])  # 确保使用原始问题
        # # logger.info(f"final_prompt==》{final_prompt}")
        # # 更新输出结果
        # return {**state, "output": {"role": "user", "content": final_prompt,"context": context_strs}}
    async def run(self, query: str) -> List[Dict[str, Any]]:
        """执行流程"""
        try:
            logger.info('启动流程')
            initial_state = RAGState(
                query=query,
                config=self.config,
                error=None,
                output=None
            )
            app = self.workflow.compile()
            result = await app.ainvoke(initial_state)
            return result
        except Exception as e:
            traceback.print_exc()
            logger.error(f"流程执行失败: {str(e)}")
            return []