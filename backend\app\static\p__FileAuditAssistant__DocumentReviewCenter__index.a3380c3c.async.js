"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6605],{46687:function(e,t,n){n.r(t),n.d(t,{default:function(){return ve}});var r=n(64599),a=n.n(r),s=n(19632),i=n.n(s),c=n(15009),u=n.n(c),o=n(97857),l=n.n(o),d=n(99289),p=n.n(d),f=n(5574),h=n.n(f),x=n(67294),v=n(11941),m=n(55102),Z=n(8232),k=n(2453),j=n(17788),y=n(83062),w=n(66309),g=n(42075),b=n(83622),S=n(86738),P=n(4393),T=n(71230),_=n(15746),I=n(55054),C=n(67839),z=n(11550),F=n(34041),N=n(71471),E=n(97131),O=n(47389),A=n(82061),J=n(29158),U=n(13520),V=n(90389),L=n(51042),q=n(88484),K=n(69753),B=n(63783),D=n(78158);function G(){return M.apply(this,arguments)}function M(){return M=p()(u()().mark((function e(){var t,n=arguments;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=n.length>0&&void 0!==n[0]?n[0]:{},e.abrupt("return",(0,D.N)("/api/auditTask/audit-rules",{method:"GET",params:l()(l()({},t),{},{current:t.current||1,pageSize:t.pageSize||10})}));case 2:case"end":return e.stop()}}),e)}))),M.apply(this,arguments)}function Q(e){return R.apply(this,arguments)}function R(){return(R=p()(u()().mark((function e(t){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/auditTask/audit-rules",{method:"POST",data:{name:t.name,type:t.type,content:t.content,is_active:void 0===t.is_active||t.is_active}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function W(e,t){return H.apply(this,arguments)}function H(){return(H=p()(u()().mark((function e(t,n){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/auditTask/audit-rules/".concat(t),{method:"PUT",data:{name:n.name,content:n.content}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function X(e){return Y.apply(this,arguments)}function Y(){return(Y=p()(u()().mark((function e(t){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/auditTask/audit-rules/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function $(){return ee.apply(this,arguments)}function ee(){return ee=p()(u()().mark((function e(){var t,n=arguments;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=n.length>0&&void 0!==n[0]?n[0]:{},e.abrupt("return",(0,D.N)("/api/auditTask/audit-task-types",{method:"GET",params:t}));case 2:case"end":return e.stop()}}),e)}))),ee.apply(this,arguments)}function te(e){return ne.apply(this,arguments)}function ne(){return(ne=p()(u()().mark((function e(t){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/auditTask/audit-task-types",{method:"POST",data:{name:t.name,code:t.code,description:t.description||"",rules:t.rules||[],is_active:!0}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function re(e,t){return ae.apply(this,arguments)}function ae(){return(ae=p()(u()().mark((function e(t,n){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/auditTask/audit-task-types/".concat(t),{method:"PUT",data:{name:n.name,description:n.description||"",rules:n.rules,is_active:n.is_active}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function se(e){return ie.apply(this,arguments)}function ie(){return(ie=p()(u()().mark((function e(t){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/auditTask/audit-task-types/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function ce(e){return ue.apply(this,arguments)}function ue(){return(ue=p()(u()().mark((function e(t){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/auditTask/audit-task-types/".concat(t,"/rules"),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function oe(e,t){return le.apply(this,arguments)}function le(){return(le=p()(u()().mark((function e(t,n){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/auditTask/audit-task-types/".concat(t,"/rules"),{method:"PUT",data:{rule_id:n}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function de(e,t){return pe.apply(this,arguments)}function pe(){return(pe=p()(u()().mark((function e(t,n){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/auditTask/audit-task-types/".concat(t,"/rules/").concat(n),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var fe=n(85893),he=v.Z.TabPane,xe=m.Z.TextArea,ve=function(){var e=(0,x.useState)([]),t=h()(e,2),n=t[0],r=t[1],s=(0,x.useState)(!1),c=h()(s,2),o=c[0],d=c[1],f=(0,x.useState)(0),D=h()(f,2),M=D[0],R=D[1],H=(0,x.useState)({current:1,pageSize:10}),Y=h()(H,2),ee=Y[0],ne=Y[1],ae=(0,x.useState)("all"),ie=h()(ae,2),ue=ie[0],le=(ie[1],(0,x.useState)([])),pe=h()(le,2),ve=pe[0],me=pe[1],Ze=(0,x.useState)(!1),ke=h()(Ze,2),je=ke[0],ye=ke[1],we=(0,x.useState)(0),ge=h()(we,2),be=ge[0],Se=ge[1],Pe=(0,x.useState)({current:1,pageSize:10}),Te=h()(Pe,2),_e=Te[0],Ie=Te[1],Ce=(0,x.useState)(!1),ze=h()(Ce,2),Fe=ze[0],Ne=ze[1],Ee=(0,x.useState)(null),Oe=h()(Ee,2),Ae=Oe[0],Je=Oe[1],Ue=Z.Z.useForm(),Ve=h()(Ue,1)[0],Le=(0,x.useState)(!1),qe=h()(Le,2),Ke=qe[0],Be=qe[1],De=(0,x.useState)(""),Ge=h()(De,2),Me=Ge[0],Qe=Ge[1],Re=(0,x.useState)(""),We=h()(Re,2),He=We[0],Xe=We[1],Ye=(0,x.useState)([]),$e=h()(Ye,2),et=$e[0],tt=$e[1],nt=(0,x.useState)([]),rt=h()(nt,2),at=rt[0],st=rt[1],it=(0,x.useState)(!1),ct=h()(it,2),ut=ct[0],ot=ct[1],lt=(0,x.useState)(""),dt=h()(lt,2),pt=dt[0],ft=dt[1],ht=(0,x.useState)(!1),xt=h()(ht,2),vt=xt[0],mt=xt[1],Zt=Z.Z.useForm(),kt=h()(Zt,1)[0],jt=(0,x.useState)([]),yt=h()(jt,2),wt=(yt[0],yt[1]),gt=(0,x.useState)(""),bt=h()(gt,2),St=(bt[0],bt[1],(0,x.useState)(null)),Pt=h()(St,2),Tt=Pt[0],_t=Pt[1],It=(0,x.useState)(!1),Ct=h()(It,2),zt=Ct[0],Ft=Ct[1],Nt=(0,x.useState)([]),Et=h()(Nt,2),Ot=Et[0],At=Et[1],Jt=(0,x.useState)([]),Ut=h()(Jt,2),Vt=Ut[0],Lt=Ut[1],qt=(0,x.useState)(!1),Kt=h()(qt,2),Bt=Kt[0],Dt=Kt[1],Gt=((0,x.useMemo)((function(){return{total:n.length,auto:n.filter((function(e){return"auto"===e.source})).length,manual:n.filter((function(e){return"manual"===e.source})).length}}),[n]),(0,x.useMemo)((function(){return{total:ve.length,active:ve.filter((function(e){return e.is_active})).length,inactive:ve.filter((function(e){return!e.is_active})).length}}),[ve])),Mt=((0,x.useMemo)((function(){return ve.map((function(e){return{value:e.id,label:e.name}}))}),[ve]),function(){var e=p()(u()().mark((function e(){var t,n,a,s;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return d(!0),e.prev=1,t=ee.current,n=ee.pageSize,a=l()({current:t,pageSize:n},"all"!==ue?{source:ue}:{}),e.next=6,G(a);case 6:(s=e.sent).success?(r(s.data),R(s.total)):k.ZP.error("获取规则列表失败"),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(1),console.error("获取规则列表出错:",e.t0),k.ZP.error("获取规则列表出错");case 14:return e.prev=14,d(!1),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[1,10,14,17]])})));return function(){return e.apply(this,arguments)}}()),Qt=function(){var e=p()(u()().mark((function e(){var t,n,r,a;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ye(!0),e.prev=1,t=_e.current,n=_e.pageSize,r={skip:(t-1)*n,limit:n},e.next=6,$(r);case 6:(a=e.sent).success?(me(a.data),Se(a.total)):k.ZP.error("获取类型列表失败"),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(1),console.error("获取类型列表出错:",e.t0),k.ZP.error("获取类型列表出错");case 14:return e.prev=14,ye(!1),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[1,10,14,17]])})));return function(){return e.apply(this,arguments)}}(),Rt=function(){var e=p()(u()().mark((function e(t,n){var r,a,s,i;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ot(!0),e.prev=1,e.next=4,ce(t);case 4:if(r=e.sent,console.log("关联规则响应:",r),!r||!r.success){e.next=18;break}return tt(r.data||[]),Qe(t),Xe(n),e.next=12,G({pageSize:1e3});case 12:a=e.sent,console.log("所有规则响应:",a),a&&a.success?(s=r.data?r.data.map((function(e){return e.id})):[],i=a.data.filter((function(e){return!s.includes(e.id)})),console.log("过滤后的可用规则:",i),st(i),ft("")):(st([]),k.ZP.error("获取可用规则失败")),Be(!0),e.next=19;break;case 18:k.ZP.error("获取关联规则失败");case 19:e.next=26;break;case 21:e.prev=21,e.t0=e.catch(1),console.error("获取关联规则出错:",e.t0),k.ZP.error("获取关联规则出错"),st([]);case 26:return e.prev=26,ot(!1),e.finish(26);case 29:case"end":return e.stop()}}),e,null,[[1,21,26,29]])})));return function(t,n){return e.apply(this,arguments)}}(),Wt=function(){var e=p()(u()().mark((function e(){var t;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,$({limit:1e3});case 3:(t=e.sent).success&&me(t.data),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取所有类型失败:",e.t0);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();(0,x.useEffect)((function(){Mt()}),[ee.current,ee.pageSize,ue]),(0,x.useEffect)((function(){Qt()}),[_e.current,_e.pageSize]),(0,x.useEffect)((function(){Wt()}),[]);var Ht=function(){var e=p()(u()().mark((function e(){var t,n;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Ve.validateFields();case 3:if(t=e.sent,console.log("表单提交的值:",t),!Ae){e.next=12;break}return e.next=8,re(Ae.id,{name:t.name,description:t.description,is_active:t.is_active});case 8:e.sent?(k.ZP.success("更新类型成功"),Ne(!1),Ve.resetFields(),Qt(),Wt()):k.ZP.error("更新类型失败"),e.next=18;break;case 12:return n={name:t.name,code:t.code,description:t.description||"",is_active:void 0===t.is_active||t.is_active},console.log("创建类型数据:",n),e.next=16,te(n);case 16:e.sent?(k.ZP.success("创建类型成功"),Ne(!1),Ve.resetFields(),Qt(),Wt()):k.ZP.error("创建类型失败");case 18:e.next=24;break;case 20:e.prev=20,e.t0=e.catch(0),console.error("表单提交出错:",e.t0),k.ZP.error("表单验证失败，请检查必填字段");case 24:case"end":return e.stop()}}),e,null,[[0,20]])})));return function(){return e.apply(this,arguments)}}(),Xt=function(){var e=p()(u()().mark((function e(){var t,n;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(pt&&Me){e.next=3;break}return k.ZP.warning("请选择要添加的规则"),e.abrupt("return");case 3:return e.prev=3,e.next=6,oe(Me,pt);case 6:if(!(t=e.sent).success){e.next=15;break}return k.ZP.success("规则添加成功"),e.next=11,ce(Me);case 11:(n=e.sent).success&&(tt(n.data),st(at.filter((function(e){return e.id!==pt}))),ft("")),e.next=16;break;case 15:k.ZP.error(t.message||"规则添加失败");case 16:e.next=22;break;case 18:e.prev=18,e.t0=e.catch(3),console.error("添加规则出错:",e.t0),k.ZP.error("添加规则出错");case 22:case"end":return e.stop()}}),e,null,[[3,18]])})));return function(){return e.apply(this,arguments)}}(),Yt=function(){var e=p()(u()().mark((function e(t){var n,r;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,de(Me,t);case 3:(n=e.sent).success?(k.ZP.success("规则移除成功"),tt(et.filter((function(e){return e.id!==t}))),(r=et.find((function(e){return e.id===t})))&&st([].concat(i()(at),[r]))):k.ZP.error(n.message||"规则移除失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("移除规则出错:",e.t0),k.ZP.error("移除规则出错");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}(),$t=function(){var e=p()(u()().mark((function e(t){var r;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:try{(r=n.find((function(e){return e.id===t})))&&(_t(r),kt.setFieldsValue({name:r.name,description:r.description,content:r.content}),wt(r.result_definition||[]),mt(!0))}catch(e){k.ZP.error("加载规则详情失败")}case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),en=function(){var e=p()(u()().mark((function e(){var t,n;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,kt.validateFields();case 3:if(t=e.sent,n={name:t.name,content:t.content,is_active:void 0===t.is_active||t.is_active},!Tt){e.next=12;break}return e.next=8,W(Tt.id,n);case 8:e.sent?(k.ZP.success("编辑规则成功"),mt(!1),_t(null),kt.resetFields(),wt([]),Mt()):k.ZP.error("编辑规则失败"),e.next=16;break;case 12:return e.next=14,Q(n);case 14:e.sent?(k.ZP.success("创建规则成功"),mt(!1),kt.resetFields(),wt([]),Mt()):k.ZP.error("创建规则失败");case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(0),k.ZP.error("表单验证失败，请检查必填字段");case 21:case"end":return e.stop()}}),e,null,[[0,18]])})));return function(){return e.apply(this,arguments)}}(),tn=function(e){var t=new FileReader;return t.onload=function(t){try{var n;if(!e.name.endsWith(".json"))return k.ZP.error("不支持的文件格式，请上传JSON文件"),!1;var r=null===(n=t.target)||void 0===n?void 0:n.result,a=JSON.parse(r);if(!Array.isArray(a))return k.ZP.error("JSON文件格式不正确，应为数组格式"),!1;var s=a.map((function(e){return{name:e.name||"",content:e.content||"",description:e.description||"",type:e.type||"",is_active:!0}})).filter((function(e){return e.name&&e.content}));if(0===s.length)return k.ZP.error("没有找到有效的规则数据"),!1;At(s),Lt(s.map((function(e,t){return t.toString()}))),Ft(!0)}catch(e){console.error("解析文件出错:",e),k.ZP.error("解析文件失败，请检查文件格式")}},e.name.endsWith(".json")?(t.readAsText(e),!1):(k.ZP.error("不支持的文件格式，请上传JSON文件"),!1)},nn=function(){var e=p()(u()().mark((function e(){var t,n,r,s,i,c;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==Vt.length){e.next=3;break}return k.ZP.warning("请选择要导入的规则"),e.abrupt("return");case 3:Dt(!0),e.prev=4,t=Vt.map((function(e){return Ot[parseInt(e)]})),n=0,r=0,s=a()(t),e.prev=9,s.s();case 11:if((i=s.n()).done){e.next=26;break}return c=i.value,e.prev=13,e.next=16,Q(c);case 16:e.sent?n++:r++,e.next=24;break;case 20:e.prev=20,e.t0=e.catch(13),console.error("导入规则失败:",e.t0),r++;case 24:e.next=11;break;case 26:e.next=31;break;case 28:e.prev=28,e.t1=e.catch(9),s.e(e.t1);case 31:return e.prev=31,s.f(),e.finish(31);case 34:n>0?(k.ZP.success("成功导入".concat(n,"条规则").concat(r>0?"，".concat(r,"条失败"):"")),Ft(!1),Mt()):k.ZP.error("导入失败，请检查数据格式"),e.next=41;break;case 37:e.prev=37,e.t2=e.catch(4),console.error("批量导入出错:",e.t2),k.ZP.error("批量导入出错");case 41:return e.prev=41,Dt(!1),e.finish(41);case 44:case"end":return e.stop()}}),e,null,[[4,37,41,44],[9,28,31,34],[13,20]])})));return function(){return e.apply(this,arguments)}}(),rn=[{title:"规则名称",dataIndex:"name",key:"name",width:250,ellipsis:!0,render:function(e,t){return(0,fe.jsx)(y.Z,{title:t.description,children:(0,fe.jsx)("span",{children:e})})}},{title:"规则类型",dataIndex:"code",key:"code",width:100},{title:"来源",dataIndex:"source",key:"source",width:100,render:function(e){return(0,fe.jsx)(w.Z,{color:"auto"===e?"blue":"green",children:"auto"===e?"自动生成":"手动创建"})}},{title:"创建时间",dataIndex:"created_at",key:"created_at",width:180},{title:"操作",key:"action",width:150,render:function(e,t){return(0,fe.jsxs)(g.Z,{size:"small",children:[(0,fe.jsx)(b.ZP,{type:"link",icon:(0,fe.jsx)(O.Z,{}),onClick:function(){return $t(t.id)},children:"编辑"}),(0,fe.jsx)(S.Z,{title:"确定删除此规则?",onConfirm:function(){return e=t.id,void j.Z.confirm({title:"确认删除规则？",content:"此操作不可撤销",onOk:(n=p()(u()().mark((function t(){var n;return u()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,X(e);case 3:(n=t.sent).success?(k.ZP.success("删除成功"),Mt()):k.ZP.error(n.message||"删除失败"),t.next=11;break;case 7:t.prev=7,t.t0=t.catch(0),console.error("删除规则出错:",t.t0),k.ZP.error("删除规则出错");case 11:case"end":return t.stop()}}),t,null,[[0,7]])}))),function(){return n.apply(this,arguments)})});var e,n},okText:"确定",cancelText:"取消",children:(0,fe.jsx)(b.ZP,{type:"link",danger:!0,icon:(0,fe.jsx)(A.Z,{}),children:"删除"})})]})}}],an=[{title:"名称",dataIndex:"name",key:"name",width:200,render:function(e,t){return(0,fe.jsx)(y.Z,{title:t.description,children:(0,fe.jsx)("span",{children:e})})}},{title:"规则数量",dataIndex:"rules_count",key:"rules_count",width:100},{title:"状态",dataIndex:"is_active",key:"is_active",width:100,render:function(e){return(0,fe.jsx)(w.Z,{color:e?"green":"red",children:e?"启用":"禁用"})}},{title:"创建时间",dataIndex:"created_at",key:"created_at",width:180},{title:"操作",key:"action",width:220,render:function(e,t){return(0,fe.jsxs)(g.Z,{size:"small",children:[(0,fe.jsx)(b.ZP,{type:"link",icon:(0,fe.jsx)(J.Z,{}),onClick:function(){return Rt(t.id,t.name)},children:"关联规则"}),(0,fe.jsx)(b.ZP,{type:"link",icon:(0,fe.jsx)(O.Z,{}),onClick:function(){return Je(e=t),Ve.setFieldsValue({name:e.name,code:e.code,description:e.description,is_active:e.is_active}),void Ne(!0);var e},children:"编辑"}),(0,fe.jsx)(S.Z,{title:"确定删除此类型?",onConfirm:function(){return e=t.id,void j.Z.confirm({title:"确认删除类型？",content:"此操作不可撤销，且会解除与所有规则的关联",onOk:(n=p()(u()().mark((function t(){var n;return u()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,se(e);case 3:(n=t.sent).success?(k.ZP.success("删除成功"),Qt()):k.ZP.error(n.message||"删除失败"),t.next=11;break;case 7:t.prev=7,t.t0=t.catch(0),console.error("删除类型出错:",t.t0),k.ZP.error("删除类型出错");case 11:case"end":return t.stop()}}),t,null,[[0,7]])}))),function(){return n.apply(this,arguments)})});var e,n},okText:"确定",cancelText:"取消",children:(0,fe.jsx)(b.ZP,{type:"link",danger:!0,icon:(0,fe.jsx)(A.Z,{}),children:"删除"})})]})}}],sn=[{title:"规则名称",dataIndex:"name",key:"name",width:250,ellipsis:!0},{title:"来源",dataIndex:"source",key:"source",width:100,render:function(e){return(0,fe.jsx)(w.Z,{color:"auto"===e?"blue":"green",children:"auto"===e?"自动生成":"手动创建"})}},{title:"操作",key:"action",width:100,render:function(e,t){return(0,fe.jsx)(S.Z,{title:"确定移除此规则?",onConfirm:function(){return Yt(t.id)},okText:"确定",cancelText:"取消",children:(0,fe.jsx)(b.ZP,{type:"link",danger:!0,children:"移除"})})}}],cn=[{title:"规则名称",dataIndex:"name",key:"name",width:180,ellipsis:!0,render:function(e,t){return(0,fe.jsx)(y.Z,{title:t.description,children:(0,fe.jsx)("span",{children:e})})}},{title:"规则类型",dataIndex:"type",key:"type",width:120,ellipsis:!0},{title:"规则描述",dataIndex:"description",key:"description",width:200,ellipsis:!0},{title:"规则内容",dataIndex:"content",key:"content",width:200,ellipsis:!0}];return(0,fe.jsxs)(E._z,{children:[(0,fe.jsxs)(v.Z,{defaultActiveKey:"types",children:[(0,fe.jsx)(he,{tab:(0,fe.jsxs)("span",{children:[(0,fe.jsx)(U.Z,{}),"场景管理"]}),children:(0,fe.jsxs)(P.Z,{children:[(0,fe.jsxs)(T.Z,{gutter:16,style:{marginBottom:16},children:[(0,fe.jsx)(_.Z,{span:6,children:(0,fe.jsx)(I.Z,{title:"类型总数",value:Gt.total,prefix:(0,fe.jsx)(U.Z,{})})}),(0,fe.jsx)(_.Z,{span:6,children:(0,fe.jsx)(I.Z,{title:"启用状态",value:Gt.active,prefix:(0,fe.jsx)(V.Z,{})})}),(0,fe.jsx)(_.Z,{span:6,children:(0,fe.jsx)(I.Z,{title:"禁用状态",value:Gt.inactive,prefix:(0,fe.jsx)(V.Z,{})})}),(0,fe.jsx)(_.Z,{span:6,style:{textAlign:"right"},children:(0,fe.jsx)(b.ZP,{type:"primary",icon:(0,fe.jsx)(L.Z,{}),onClick:function(){Je(null),Ve.resetFields(),Ve.setFieldsValue({is_active:!0}),Ne(!0)},children:"新建场景"})})]}),(0,fe.jsx)(C.Z,{columns:an,dataSource:ve,rowKey:"id",loading:je,pagination:{current:_e.current,pageSize:_e.pageSize,total:be,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(e){return"共 ".concat(e," 条类型")},onChange:function(e,t){Ie({current:e,pageSize:t})}}})]})},"types"),(0,fe.jsx)(he,{tab:(0,fe.jsxs)("span",{children:[(0,fe.jsx)(V.Z,{}),"规则管理"]}),children:(0,fe.jsxs)(P.Z,{children:[(0,fe.jsx)(T.Z,{gutter:16,style:{marginBottom:16},children:(0,fe.jsx)(_.Z,{span:12,children:(0,fe.jsxs)(g.Z,{children:[(0,fe.jsx)(b.ZP,{type:"primary",icon:(0,fe.jsx)(L.Z,{}),onClick:function(){_t(null),kt.resetFields(),wt([]),mt(!0)},children:"新建规则"}),(0,fe.jsx)(z.Z,{beforeUpload:tn,showUploadList:!1,accept:".json",children:(0,fe.jsx)(b.ZP,{icon:(0,fe.jsx)(q.Z,{}),children:"批量导入"})}),(0,fe.jsx)(b.ZP,{icon:(0,fe.jsx)(K.Z,{}),onClick:function(){return window.open("/static/样例.json")},children:"导入文件样例"})]})})}),(0,fe.jsx)(C.Z,{columns:rn,dataSource:n,rowKey:"id",loading:o,scroll:{x:800},pagination:{current:ee.current,pageSize:ee.pageSize,total:M,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(e){return"共 ".concat(e," 条规则")},onChange:function(e,t){ne({current:e,pageSize:t})}}})]})},"rules")]}),(0,fe.jsx)(j.Z,{title:Tt?"编辑规则":"新建规则",open:vt,onOk:en,onCancel:function(){mt(!1),_t(null),kt.resetFields(),wt([])},width:800,children:(0,fe.jsxs)(Z.Z,{form:kt,layout:"vertical",initialValues:{source:"manual"},children:[(0,fe.jsx)(Z.Z.Item,{name:"name",label:"规则名称",rules:[{required:!0,message:"请输入规则名称"}],children:(0,fe.jsx)(m.Z,{placeholder:"请输入规则名称"})}),(0,fe.jsx)(Z.Z.Item,{name:"description",label:"规则描述",rules:[{required:!0,message:"请输入规则描述"}],children:(0,fe.jsx)(xe,{rows:4,placeholder:"请输入规则描述"})}),(0,fe.jsx)(Z.Z.Item,{name:"content",label:(0,fe.jsxs)("span",{children:["规则内容"," ",(0,fe.jsx)(y.Z,{title:"这个就是给大模型的提示词，用于指导AI如何分析内容",children:(0,fe.jsx)(B.Z,{style:{color:"#1890ff"}})})]}),rules:[{required:!0,message:"请输入规则内容"}],children:(0,fe.jsx)(xe,{rows:6,placeholder:"请输入规则内容（提示词），用于指导AI如何分析内容"})})]})}),(0,fe.jsx)(j.Z,{title:Ae?"编辑场景":"新建场景",open:Fe,onOk:Ht,onCancel:function(){Ne(!1),Ve.resetFields()},width:600,children:(0,fe.jsxs)(Z.Z,{form:Ve,layout:"vertical",initialValues:{is_active:!0},children:[(0,fe.jsx)(Z.Z.Item,{name:"name",label:"场景名称",rules:[{required:!0,message:"请输入场景名称"}],children:(0,fe.jsx)(m.Z,{placeholder:"请输入场景名称"})}),(0,fe.jsx)(Z.Z.Item,{name:"code",label:"场景编码",rules:[{required:!0,message:"请输入场景编码"}],children:(0,fe.jsx)(m.Z,{placeholder:"请输入场景编码"})}),(0,fe.jsx)(Z.Z.Item,{name:"description",label:"场景描述",children:(0,fe.jsx)(xe,{rows:4,placeholder:"请输入场景描述"})}),(0,fe.jsx)(Z.Z.Item,{name:"is_active",label:"启用状态",initialValue:!0,children:(0,fe.jsx)(F.default,{defaultValue:!0,options:[{value:!0,label:"启用"},{value:!1,label:"禁用"}]})})]})}),(0,fe.jsxs)(j.Z,{title:"".concat(He||"未知类型"," - 关联规则管理"),open:Ke,onCancel:function(){return Be(!1)},footer:null,width:900,children:[(0,fe.jsx)("div",{style:{marginBottom:16},children:(0,fe.jsxs)(T.Z,{gutter:16,children:[(0,fe.jsx)(_.Z,{span:16,children:(0,fe.jsx)(F.default,{style:{width:"100%"},placeholder:"选择要添加的规则",value:pt,onChange:function(e){return ft(e)},options:at.map((function(e){return console.log("规则详情:",e),{value:e.id,label:e.code+"-"+(e.name||"未命名规则")}})),notFoundContent:0===at.length?"没有可添加的规则":void 0})}),(0,fe.jsx)(_.Z,{span:8,children:(0,fe.jsx)(b.ZP,{type:"primary",onClick:Xt,disabled:!pt,children:"添加规则"})})]})}),(0,fe.jsx)(C.Z,{columns:sn,dataSource:et,rowKey:"id",loading:ut,scroll:{x:600},pagination:!1})]}),(0,fe.jsxs)(j.Z,{title:"批量导入规则",open:zt,onOk:nn,onCancel:function(){return Ft(!1)},width:900,confirmLoading:Bt,okText:"导入选中规则",cancelText:"取消",children:[(0,fe.jsx)("div",{style:{marginBottom:16},children:(0,fe.jsxs)("span",{children:["共解析到 ",Ot.length," 条规则，已选择 ",Vt.length," 条"]})}),(0,fe.jsx)(C.Z,{rowSelection:{selectedRowKeys:Vt,onChange:function(e){Lt(e.map((function(e){return e.toString()})))}},columns:cn,dataSource:Ot.map((function(e,t){return l()(l()({},e),{},{key:t.toString()})})),scroll:{y:400},pagination:!1}),(0,fe.jsxs)("div",{style:{marginTop:16},children:[(0,fe.jsx)(z.Z,{beforeUpload:tn,showUploadList:!1,accept:".json",children:(0,fe.jsx)(b.ZP,{icon:(0,fe.jsx)(q.Z,{}),children:"重新上传"})}),(0,fe.jsx)("div",{style:{marginTop:8},children:(0,fe.jsx)(N.Z.Text,{type:"secondary",children:"支持的文件格式：JSON(.json)"})})]})]})]})}}}]);