"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[456],{47046:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"}},85175:function(e,t,r){var n=r(1413),o=r(67294),a=r(48820),s=r(91146),l=function(e,t){return o.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},i=o.forwardRef(l);t.Z=i},82061:function(e,t,r){var n=r(1413),o=r(67294),a=r(47046),s=r(91146),l=function(e,t){return o.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},i=o.forwardRef(l);t.Z=i},47389:function(e,t,r){var n=r(1413),o=r(67294),a=r(27363),s=r(91146),l=function(e,t){return o.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},i=o.forwardRef(l);t.Z=i},49354:function(e,t,r){r.d(t,{Z:function(){return i}});var n=r(1413),o=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M531.3 574.4l.3-1.4c5.8-23.9 13.1-53.7 7.4-80.7-3.8-21.3-19.5-29.6-32.9-30.2-15.8-.7-29.9 8.3-33.4 21.4-6.6 24-.7 56.8 10.1 98.6-13.6 32.4-35.3 79.5-51.2 107.5-29.6 15.3-69.3 38.9-75.2 68.7-1.2 5.5.2 12.5 3.5 18.8 3.7 7 9.6 12.4 16.5 15 3 1.1 6.6 2 10.8 2 17.6 0 46.1-14.2 84.1-79.4 5.8-1.9 11.8-3.9 17.6-5.9 27.2-9.2 55.4-18.8 80.9-23.1 28.2 15.1 60.3 24.8 82.1 24.8 21.6 0 30.1-12.8 33.3-20.5 5.6-13.5 2.9-30.5-6.2-39.6-13.2-13-45.3-16.4-95.3-10.2-24.6-15-40.7-35.4-52.4-65.8zM421.6 726.3c-13.9 20.2-24.4 30.3-30.1 34.7 6.7-12.3 19.8-25.3 30.1-34.7zm87.6-235.5c5.2 8.9 4.5 35.8.5 49.4-4.9-19.9-5.6-48.1-2.7-51.4.8.1 1.5.7 2.2 2zm-1.6 120.5c10.7 18.5 24.2 34.4 39.1 46.2-21.6 4.9-41.3 13-58.9 20.2-4.2 1.7-8.3 3.4-12.3 5 13.3-24.1 24.4-51.4 32.1-71.4zm155.6 65.5c.1.2.2.5-.4.9h-.2l-.2.3c-.8.5-9 5.3-44.3-8.6 40.6-1.9 45 7.3 45.1 7.4zm191.4-388.2L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file-pdf",theme:"outlined"},s=r(91146),l=function(e,t){return o.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a}))};var i=o.forwardRef(l)},37446:function(e,t,r){r.d(t,{Z:function(){return i}});var n=r(1413),o=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"},s=r(91146),l=function(e,t){return o.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a}))};var i=o.forwardRef(l)},51042:function(e,t,r){var n=r(1413),o=r(67294),a=r(42110),s=r(91146),l=function(e,t){return o.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},i=o.forwardRef(l);t.Z=i},82867:function(e,t,r){r.r(t),r.d(t,{default:function(){return G}});var n=r(97857),o=r.n(n),a=r(15009),s=r.n(a),l=r(19632),i=r.n(l),c=r(99289),u=r.n(c),d=r(5574),p=r.n(d),f=r(67294),h=r(71471),g=r(26058),m=r(55102),x=r(17788),y=r(47019),v=r(2453),b=r(4393),j=r(85418),C=r(83062),k=r(83622),Z=r(42075),w=r(34041),S=r(50136),z=r(11941),P=r(74330),N=r(66309),O=r(96074),T=r(41156),$=r(85175),E=r(47389),B=r(82061),I=r(37446),H=r(49354),_=r(87547),R=r(51042),F=r(77880),A=r(85893),L=h.Z.Title,M=h.Z.Text,W=g.Z.Sider,q=g.Z.Content,V=m.Z.TextArea,D=x.Z.confirm,U=m.Z.Search,G=function(){var e=(0,f.useState)(!1),t=p()(e,2),r=t[0],n=t[1],a=y.Z.useForm(),l=p()(a,1)[0],c=(0,f.useState)(null),d=p()(c,2),G=d[0],X=d[1],Y=(0,f.useState)("all"),J=p()(Y,2),K=J[0],Q=J[1],ee=(0,f.useState)(""),te=p()(ee,2),re=te[0],ne=te[1],oe=(0,f.useState)("all"),ae=p()(oe,2),se=ae[0],le=ae[1],ie=(0,f.useState)(!1),ce=p()(ie,2),ue=ce[0],de=ce[1],pe=(0,f.useState)(null),fe=p()(pe,2),he=fe[0],ge=fe[1],me=(0,f.useState)("system"),xe=p()(me,2),ye=xe[0],ve=xe[1],be=(0,f.useState)({user:!1,system:!1}),je=p()(be,2),Ce=je[0],ke=je[1],Ze=(0,f.useState)([]),we=p()(Ze,2),Se=we[0],ze=we[1],Pe=(0,f.useState)([]),Ne=p()(Pe,2),Oe=Ne[0],Te=Ne[1],$e=(0,f.useState)([]),Ee=p()($e,2),Be=Ee[0],Ie=Ee[1],He=(0,f.useState)({current:1,pageSize:9,total:0}),_e=p()(He,2),Re=_e[0],Fe=_e[1],Ae=(0,f.useState)({current:1,pageSize:9,total:0}),Le=p()(Ae,2),Me=Le[0],We=Le[1],qe=[{label:"全部",desc:"所有模型",key:"all"},{label:"语言模型",desc:"语言模型",key:"language_model"},{label:"推理模型",desc:"推理模型",key:"reasoning_model"},{label:"多模态模型",desc:"多模态模型",key:"multimodal_model"}],Ve=(0,f.useState)([{label:"全部提示词",key:"all",icon:(0,A.jsx)(T.Z,{})}]),De=p()(Ve,2),Ue=De[0],Ge=De[1];(0,f.useEffect)((function(){var e=function(){var e=u()(s()().mark((function e(){var t,r;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,F.qB)();case 3:(t=e.sent).success&&t.data&&(r=t.data.map((function(e){return{label:e,key:e}})),Ie(t.data),Ge([{label:"全部提示词",key:"all",icon:(0,A.jsx)(T.Z,{})}].concat(i()(r)))),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取分类失败",e.t0),v.ZP.error("获取分类列表失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);var Xe=function(){var e=u()(s()().mark((function e(t){var r,n,a,l=arguments;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=l.length>1&&void 0!==l[1]&&l[1],ke((function(e){return o()(o()({},e),{},{user:!0})})),e.prev=2,n={current:t,pageSize:Re.pageSize,title:re||void 0,category:"all"!==K?K:void 0,model:"all"!==se?se:void 0},e.next=6,(0,F.He)(n);case 6:(a=e.sent).success&&a.data&&(ze(r?function(e){return[].concat(i()(e),i()(a.data))}:a.data),Fe(o()(o()({},Re),{},{current:t,total:a.total||0}))),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(2),console.error("获取个人提示词失败",e.t0),v.ZP.error("获取个人提示词失败");case 14:return e.prev=14,ke((function(e){return o()(o()({},e),{},{user:!1})})),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[2,10,14,17]])})));return function(t){return e.apply(this,arguments)}}(),Ye=function(){var e=u()(s()().mark((function e(t){var r,n,a,l=arguments;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=l.length>1&&void 0!==l[1]&&l[1],ke((function(e){return o()(o()({},e),{},{system:!0})})),e.prev=2,n={current:t,pageSize:Me.pageSize,title:re||void 0,category:"all"!==K?K:void 0,model:"all"!==se?se:void 0},e.next=6,(0,F.V7)(n);case 6:(a=e.sent).success&&a.data&&(Te(r?function(e){return[].concat(i()(e),i()(a.data))}:a.data),We(o()(o()({},Me),{},{current:t,total:a.total||0}))),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(2),console.error("获取系统提示词失败",e.t0),v.ZP.error("获取系统提示词失败");case 14:return e.prev=14,ke((function(e){return o()(o()({},e),{},{system:!1})})),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[2,10,14,17]])})));return function(t){return e.apply(this,arguments)}}();(0,f.useEffect)((function(){Fe((function(e){return o()(o()({},e),{},{current:1})})),We((function(e){return o()(o()({},e),{},{current:1})})),Xe(1),Ye(1)}),[K,re,se]);var Je=function(e){X(e||null),e?l.setFieldsValue(o()(o()({},e),{},{title:e.title,content:e.content,category:Array.isArray(e.category)?e.category:[e.category],positions:e.positions,models:e.models,language:e.language||"zh-CN"})):(l.resetFields(),l.setFieldsValue({models:"all"!==se?[se]:["gpt4"],category:"all"!==K?[K]:Be.length>0?[Be[0]]:[],positions:["analyst"],language:"zh-CN"})),n(!0)},Ke=function(){var e=u()(s()().mark((function e(t){var r;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,F.WJ)(t.id);case 3:(r=e.sent).success&&r.data&&(ge(r.data),de(!0)),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取提示词详情失败",e.t0),v.ZP.error("获取提示词详情失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}(),Qe=function(){var e=u()(s()().mark((function e(){var t,r,a;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,l.validateFields();case 3:if(t=e.sent,ke((function(e){return o()(o()({},e),{},{user:!0})})),r=o()({},t),!G){e.next=12;break}return e.next=9,(0,F.Fu)(G.id,r);case 9:a=e.sent,e.next=15;break;case 12:return e.next=14,(0,F.Fh)(r);case 14:a=e.sent;case 15:a.success?(v.ZP.success("".concat(G?"更新":"创建","提示词成功")),n(!1),l.resetFields(),G||Fe((function(e){return o()(o()({},e),{},{current:1})})),Xe(1)):v.ZP.error(a.message||"".concat(G?"更新":"创建","提示词失败")),e.next=22;break;case 18:e.prev=18,e.t0=e.catch(0),console.error("表单验证或提交失败:",e.t0),v.ZP.error("".concat(G?"更新":"创建","提示词失败"));case 22:return e.prev=22,ke((function(e){return o()(o()({},e),{},{user:!1})})),e.finish(22);case 25:case"end":return e.stop()}}),e,null,[[0,18,22,25]])})));return function(){return e.apply(this,arguments)}}(),et=function(){var e=u()(s()().mark((function e(t){var r;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,ke((function(e){return o()(o()({},e),{},{user:!0})})),e.next=4,(0,F.Yb)(t);case 4:(r=e.sent).success?(v.ZP.success("复制提示词成功"),Fe((function(e){return o()(o()({},e),{},{current:1})})),Xe(1)):v.ZP.error(r.message||"复制提示词失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("复制失败:",e.t0),v.ZP.error("复制提示词失败");case 12:return e.prev=12,ke((function(e){return o()(o()({},e),{},{user:!1})})),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})));return function(t){return e.apply(this,arguments)}}(),tt=function(e,t){var r=e.is_system,a=[{key:"copy",icon:(0,A.jsx)($.Z,{}),label:"复制",onClick:function(t){t.domEvent.stopPropagation(),et(e.id)}}];return r||t||a.unshift({key:"edit",icon:(0,A.jsx)(E.Z,{}),label:"编辑",onClick:function(t){t.domEvent.stopPropagation(),Je(e)}},{key:"delete",icon:(0,A.jsx)(B.Z,{}),label:"删除",danger:!0,onClick:function(t){var r,n;t.domEvent.stopPropagation(),r=e.id,D({title:"确认删除提示词",content:"您确定要删除这个提示词吗？此操作无法撤销。",okText:"确定删除",cancelText:"取消",okButtonProps:{danger:!0},onOk:(n=u()(s()().mark((function e(){var t;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,ke((function(e){return o()(o()({},e),{},{user:!0})})),e.next=4,(0,F.$j)(r);case 4:(t=e.sent).success?(v.ZP.success("删除提示词成功"),1===Se.length&&Re.current>1?Fe((function(e){return o()(o()({},e),{},{current:e.current-1})})):Xe(1)):v.ZP.error(t.message||"删除提示词失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除失败:",e.t0),v.ZP.error("删除提示词失败");case 12:return e.prev=12,ke((function(e){return o()(o()({},e),{},{user:!1})})),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])}))),function(){return n.apply(this,arguments)})})}}),(0,A.jsxs)(b.Z,{hoverable:!0,style:{height:"100%",position:"relative",borderRadius:"8px",boxShadow:"0 1px 3px rgba(0,0,0,0.06)",background:t?"linear-gradient(135deg, #f6ffed, #e6f7ff)":"linear-gradient(135deg, #f0f5ff, #e6f7ff)",transition:"all 0.3s ease"},bodyStyle:{padding:"16px",display:"flex",flexDirection:"column",height:"100%",paddingRight:"40px"},onClick:function(){return Ke(e)},className:t?"system-prompt-card":"user-prompt-card",children:[(0,A.jsx)("div",{style:{position:"absolute",top:"12px",right:"12px",zIndex:10},children:(0,A.jsx)(j.Z,{menu:{items:a},trigger:["click"],placement:"bottomRight",children:(0,A.jsx)(C.Z,{title:"更多操作",children:(0,A.jsx)(k.ZP,{type:"text",shape:"circle",icon:(0,A.jsx)(I.Z,{style:{color:"#8c8c8c"}}),onClick:function(e){return e.stopPropagation()}})})})}),(0,A.jsxs)("div",{style:{marginBottom:"12px",display:"flex",alignItems:"center"},children:[(0,A.jsx)("div",{style:{width:"40px",height:"40px",borderRadius:"8px",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:t?"#b7eb8f":"#adc6ff",color:t?"#52c41a":"#1890ff",fontSize:"20px",marginRight:"10px"},children:(0,A.jsx)(H.Z,{})}),(0,A.jsx)(L,{level:5,style:{margin:0,fontWeight:500},children:e.title})]}),(0,A.jsx)(M,{type:"secondary",style:{display:"-webkit-box",marginBottom:"12px",flex:1,overflow:"hidden",textOverflow:"ellipsis",WebkitLineClamp:2,WebkitBoxOrient:"vertical",fontSize:"13px",lineHeight:"1.5",maxHeight:"40px"},children:e.content}),(0,A.jsxs)("div",{style:{marginTop:"auto",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,A.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,A.jsx)(_.Z,{style:{color:"#8c8c8c",marginRight:"4px",fontSize:"12px"}}),(0,A.jsx)(M,{style:{fontSize:"12px",color:"#8c8c8c"},children:e.user})]}),(0,A.jsxs)(Z.Z,{size:"small",children:[t&&(0,A.jsx)(C.Z,{title:"添加到我的提示词",children:(0,A.jsx)(k.ZP,{type:"text",icon:(0,A.jsx)(R.Z,{}),size:"small",onClick:function(t){t.stopPropagation(),l.resetFields(),X(null),l.setFieldsValue({title:"".concat(e.title," - 副本"),content:e.content,category:Array.isArray(e.category)?e.category:[],positions:e.positions||["analyst"],models:e.models||("all"!==se?[se]:["gpt4"]),language:e.language||"zh-CN"}),n(!0)}})}),(0,A.jsx)(C.Z,{title:"复制提示词内容",children:(0,A.jsx)(k.ZP,{type:"text",icon:(0,A.jsx)($.Z,{}),size:"small",onClick:function(t){t.stopPropagation(),navigator.clipboard.writeText(e.content),v.ZP.success("提示词内容已复制到剪贴板")}})})]})]})]},e.id)};return(0,A.jsxs)(g.Z,{style:{height:"100vh",overflow:"hidden",background:"#fff"},children:[(0,A.jsxs)("div",{style:{padding:"12px 24px",borderBottom:"1px solid #e8e8e8",backgroundColor:"#fff",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,A.jsx)(L,{level:4,style:{margin:0},children:"提示词模版"}),(0,A.jsxs)(Z.Z,{size:"middle",children:[(0,A.jsx)(M,{type:"secondary",children:"当前模型:"}),(0,A.jsx)(w.default,{value:se,onChange:le,bordered:!1,style:{width:150},dropdownMatchSelectWidth:!1,children:qe.map((function(e){return(0,A.jsx)(w.default.Option,{value:e.key,children:(0,A.jsxs)(Z.Z,{children:[e.label,(0,A.jsxs)(M,{type:"secondary",style:{fontSize:"12px"},children:["(",e.desc,")"]})]})},e.key)}))}),(0,A.jsx)(M,{type:"secondary",children:"|"}),(0,A.jsxs)(M,{type:"secondary",children:[Se.length+Oe.length," 个提示词"]})]})]}),(0,A.jsxs)(g.Z,{style:{height:"calc(100vh - 65px)",background:"#fff"},children:[(0,A.jsx)(W,{width:200,style:{background:"#fff",overflow:"auto",height:"100%",paddingTop:"16px"},children:(0,A.jsx)(S.Z,{mode:"inline",selectedKeys:[K],style:{height:"calc(100% - 16px)",borderRight:0,background:"transparent"},items:Ue.map((function(e){return o()(o()({},e),{},{label:(0,A.jsx)("span",{style:{fontWeight:K===e.key?500:void 0},children:e.label})})})),onClick:function(e){var t=e.key;Q(t)}})}),(0,A.jsx)(q,{style:{padding:"20px",overflow:"auto",height:"100%",background:"#fff"},children:(0,A.jsxs)(z.Z,{activeKey:ye,onChange:ve,tabBarExtraContent:(0,A.jsxs)(Z.Z,{children:[(0,A.jsx)(U,{placeholder:"搜索提示词名称或描述",onSearch:function(e){ne(e)},onChange:function(e){return ne(e.target.value)},style:{width:300},allowClear:!0,enterButton:!0}),(0,A.jsx)(w.default,{defaultValue:"zh-CN",style:{width:100},options:[{label:"中文",value:"zh-CN"},{label:"英文",value:"en-US"}]}),"user"===ye&&(0,A.jsx)(k.ZP,{type:"primary",icon:(0,A.jsx)(R.Z,{}),onClick:function(){return Je()},children:"添加提示词"})]}),children:[(0,A.jsxs)(z.Z.TabPane,{tab:(0,A.jsxs)("span",{style:{fontSize:"16px",color:"#52c41a"},children:["系统提示词 (",Me.total,")"]}),children:[(0,A.jsx)("div",{style:{marginBottom:"16px"},children:(0,A.jsx)(M,{type:"secondary",style:{fontSize:"12px",display:"block",marginTop:"4px"},children:"系统提供的标准提示词模板，您可以查看和复制这些提示词作为参考"})}),(0,A.jsx)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(300px, 1fr))",gap:"16px",marginBottom:"16px"},children:Oe.map((function(e){return tt(e,!0)}))}),Ce.system?(0,A.jsx)("div",{style:{textAlign:"center",marginTop:"20px"},children:(0,A.jsx)(P.Z,{})}):Me.current*Me.pageSize<Me.total&&(0,A.jsx)("div",{style:{textAlign:"center",marginTop:"20px",marginBottom:"40px"},children:(0,A.jsx)(k.ZP,{onClick:function(){var e=Me.current+1;Ye(e,!0)},type:"default",size:"middle",children:"加载更多"})})]},"system"),(0,A.jsxs)(z.Z.TabPane,{tab:(0,A.jsxs)("span",{style:{fontSize:"16px",color:"#1890ff"},children:["我的提示词 (",Re.total,")"]}),children:[(0,A.jsx)("div",{style:{marginBottom:"16px"},children:(0,A.jsx)(M,{type:"secondary",style:{fontSize:"12px",display:"block",marginTop:"4px"},children:"您可以创建、编辑和管理个人提示词，根据业务需求自定义提示词内容"})}),(0,A.jsx)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(300px, 1fr))",gap:"16px",marginBottom:"16px"},children:Se.map((function(e){return tt(e,!1)}))}),Ce.user?(0,A.jsx)("div",{style:{textAlign:"center",marginTop:"20px"},children:(0,A.jsx)(P.Z,{})}):Re.current*Re.pageSize<Re.total&&(0,A.jsx)("div",{style:{textAlign:"center",marginTop:"20px",marginBottom:"40px"},children:(0,A.jsx)(k.ZP,{onClick:function(){var e=Re.current+1;Xe(e,!0)},type:"default",size:"middle",children:"加载更多"})})]},"user")]})})]}),(0,A.jsx)(x.Z,{title:G?"编辑提示词":"新建提示词",open:r,onCancel:function(){return n(!1)},destroyOnClose:!0,footer:[(0,A.jsx)(k.ZP,{onClick:function(){return n(!1)},children:"取消"},"cancel"),(0,A.jsx)(k.ZP,{type:"primary",onClick:Qe,loading:Ce.user,children:"确定"},"submit")],width:600,children:(0,A.jsxs)(y.Z,{form:l,layout:"vertical",initialValues:{models:"all"!==se?[se]:["gpt4"],category:"all"!==K?[K]:Be.length>0?[Be[0]]:[],positions:["analyst"],language:"zh-CN"},children:[(0,A.jsx)(y.Z.Item,{name:"title",label:"提示词名称",rules:[{required:!0,message:"请输入提示词名称"}],children:(0,A.jsx)(m.Z,{placeholder:"请输入提示词名称"})}),(0,A.jsx)(y.Z.Item,{name:"content",label:"提示词内容",rules:[{required:!0,message:"请输入提示词内容"}],children:(0,A.jsx)(V,{placeholder:"请输入提示词内容",rows:6,style:{resize:"none"}})}),(0,A.jsx)(y.Z.Item,{name:"category",label:"分类",rules:[{required:!0,message:"请选择分类"}],children:(0,A.jsx)(w.default,{mode:"tags",options:Be.map((function(e){return{label:e,value:e}})),placeholder:"请选择分类"})}),(0,A.jsx)(y.Z.Item,{name:"positions",label:"适用岗位",rules:[{required:!0,message:"请选择适用岗位"}],children:(0,A.jsx)(w.default,{mode:"multiple",options:[{label:"金融分析师",value:"analyst"},{label:"风险管理师",value:"risk_manager"},{label:"投资经理",value:"investment_manager"}],placeholder:"请选择适用岗位"})}),(0,A.jsx)(y.Z.Item,{name:"models",label:"使用模型",rules:[{required:!0,message:"请选择使用模型"}],children:(0,A.jsx)(w.default,{mode:"multiple",options:qe.map((function(e){return{label:e.label,value:e.key}})),placeholder:"请选择使用模型"})}),(0,A.jsx)(y.Z.Item,{name:"language",label:"语言",rules:[{required:!0,message:"请选择语言"}],children:(0,A.jsx)(w.default,{options:[{label:"中文",value:"zh-CN"},{label:"英文",value:"en-US"}],placeholder:"请选择语言"})})]})}),he&&(0,A.jsxs)(x.Z,{title:(0,A.jsx)(L,{level:5,style:{margin:0},children:he.title}),open:ue,onCancel:function(){de(!1),ge(null)},width:700,footer:[(0,A.jsx)(k.ZP,{icon:(0,A.jsx)($.Z,{}),onClick:function(){navigator.clipboard.writeText(he.content),v.ZP.success("提示词内容已复制到剪贴板")},children:"复制内容"},"copy"),(0,A.jsx)(k.ZP,{type:"primary",onClick:function(){de(!1),ge(null)},children:"关闭"},"close")],children:[(0,A.jsx)("div",{style:{marginBottom:"20px"},children:(0,A.jsxs)("div",{style:{display:"flex",flexWrap:"wrap",gap:"16px",marginBottom:"16px"},children:[(0,A.jsxs)("div",{children:[(0,A.jsx)(M,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"分类"}),(0,A.jsx)("div",{children:(Array.isArray(he.category)?he.category:[]).map((function(e){return(0,A.jsx)(N.Z,{color:"blue",children:e.trim()},e)}))})]}),(0,A.jsxs)("div",{children:[(0,A.jsx)(M,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"适用模型"}),(0,A.jsx)("div",{children:he.models&&he.models.map((function(e){var t;return(0,A.jsx)(N.Z,{color:"green",children:(null===(t=qe.find((function(t){return t.key===e})))||void 0===t?void 0:t.label)||e},e)}))})]}),(0,A.jsxs)("div",{children:[(0,A.jsx)(M,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"适用岗位"}),(0,A.jsx)("div",{children:he.positions&&he.positions.map((function(e){return(0,A.jsx)(N.Z,{color:"gold",children:"analyst"===e?"金融分析师":"risk_manager"===e?"风险管理师":"investment_manager"===e?"投资经理":e},e)}))})]}),(0,A.jsxs)("div",{children:[(0,A.jsx)(M,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"语言"}),(0,A.jsx)("div",{children:(0,A.jsx)(N.Z,{color:"purple",children:"zh-CN"===he.language?"中文":"en-US"===he.language?"英文":he.language})})]})]})}),(0,A.jsx)(O.Z,{orientation:"left",children:"提示词内容"}),(0,A.jsx)(h.Z.Paragraph,{style:{whiteSpace:"pre-wrap",maxHeight:"40vh",overflowY:"auto",fontSize:"14px",lineHeight:"1.8",padding:"16px",backgroundColor:"#f9f9f9",borderRadius:"8px",border:"1px solid #f0f0f0"},children:he.content}),(0,A.jsxs)("div",{style:{marginTop:20,paddingTop:16,borderTop:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between"},children:[(0,A.jsxs)("div",{children:[(0,A.jsxs)(M,{type:"secondary",style:{fontSize:"12px"},children:["创建时间: ",new Date(he.created_at).toLocaleString()]}),he.updated_at&&he.updated_at!==he.created_at&&(0,A.jsxs)(M,{type:"secondary",style:{fontSize:"12px",marginLeft:"16px"},children:["更新时间: ",new Date(he.updated_at).toLocaleString()]})]}),(0,A.jsxs)(M,{type:"secondary",style:{fontSize:"12px"},children:["创建者: ",he.user]})]})]})]})}},77880:function(e,t,r){r.d(t,{$j:function(){return y},Fh:function(){return h},Fu:function(){return m},He:function(){return i},V7:function(){return u},WJ:function(){return p},Yb:function(){return b},qB:function(){return C}});var n=r(15009),o=r.n(n),a=r(99289),s=r.n(a),l=r(78158);function i(e){return c.apply(this,arguments)}function c(){return(c=s()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/user-prompts",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function u(e){return d.apply(this,arguments)}function d(){return(d=s()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/system-prompts",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function p(e){return f.apply(this,arguments)}function f(){return(f=s()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/prompts/".concat(t),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function h(e){return g.apply(this,arguments)}function g(){return(g=s()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/prompts",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function m(e,t){return x.apply(this,arguments)}function x(){return(x=s()(o()().mark((function e(t,r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/prompts/".concat(t),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function y(e){return v.apply(this,arguments)}function v(){return(v=s()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/prompts/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function b(e){return j.apply(this,arguments)}function j(){return(j=s()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/prompts/".concat(t,"/copy"),{method:"POST"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function C(){return k.apply(this,arguments)}function k(){return(k=s()(o()().mark((function e(){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/prompt_categories",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},26058:function(e,t,r){r.d(t,{Z:function(){return C}});var n=r(74902),o=r(67294),a=r(93967),s=r.n(a),l=r(98423),i=r(53124),c=r(82401),u=r(50344),d=r(70985);var p=r(24793),f=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r};function h(e){let{suffixCls:t,tagName:r,displayName:n}=e;return e=>o.forwardRef(((n,a)=>o.createElement(e,Object.assign({ref:a,suffixCls:t,tagName:r},n))))}const g=o.forwardRef(((e,t)=>{const{prefixCls:r,suffixCls:n,className:a,tagName:l}=e,c=f(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:u}=o.useContext(i.E_),d=u("layout",r),[h,g,m]=(0,p.ZP)(d),x=n?`${d}-${n}`:d;return h(o.createElement(l,Object.assign({className:s()(r||x,a,g,m),ref:t},c)))})),m=o.forwardRef(((e,t)=>{const{direction:r}=o.useContext(i.E_),[a,h]=o.useState([]),{prefixCls:g,className:m,rootClassName:x,children:y,hasSider:v,tagName:b,style:j}=e,C=f(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),k=(0,l.Z)(C,["suffixCls"]),{getPrefixCls:Z,className:w,style:S}=(0,i.dj)("layout"),z=Z("layout",g),P=function(e,t,r){return"boolean"==typeof r?r:!!e.length||(0,u.Z)(t).some((e=>e.type===d.Z))}(a,y,v),[N,O,T]=(0,p.ZP)(z),$=s()(z,{[`${z}-has-sider`]:P,[`${z}-rtl`]:"rtl"===r},w,m,x,O,T),E=o.useMemo((()=>({siderHook:{addSider:e=>{h((t=>[].concat((0,n.Z)(t),[e])))},removeSider:e=>{h((t=>t.filter((t=>t!==e))))}}})),[]);return N(o.createElement(c.V.Provider,{value:E},o.createElement(b,Object.assign({ref:t,className:$,style:Object.assign(Object.assign({},S),j)},k),y)))})),x=h({tagName:"div",displayName:"Layout"})(m),y=h({suffixCls:"header",tagName:"header",displayName:"Header"})(g),v=h({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(g),b=h({suffixCls:"content",tagName:"main",displayName:"Content"})(g);const j=x;j.Header=y,j.Footer=v,j.Content=b,j.Sider=d.Z,j._InternalSiderContext=d.D;var C=j},66309:function(e,t,r){r.d(t,{Z:function(){return O}});var n=r(67294),o=r(93967),a=r.n(o),s=r(98423),l=r(98787),i=r(69760),c=r(96159),u=r(45353),d=r(53124),p=r(11568),f=r(15063),h=r(14747),g=r(83262),m=r(83559);const x=e=>{const{lineWidth:t,fontSizeIcon:r,calc:n}=e,o=e.fontSizeSM;return(0,g.IX)(e,{tagFontSize:o,tagLineHeight:(0,p.bf)(n(e.lineHeightSM).mul(o).equal()),tagIconSize:n(r).sub(n(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},y=e=>({defaultBg:new f.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var v=(0,m.I$)("Tag",(e=>(e=>{const{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:n,componentCls:o,calc:a}=e,s=a(n).sub(r).equal(),l=a(t).sub(r).equal();return{[o]:Object.assign(Object.assign({},(0,h.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:s,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:s}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(x(e))),y),b=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r};const j=n.forwardRef(((e,t)=>{const{prefixCls:r,style:o,className:s,checked:l,onChange:i,onClick:c}=e,u=b(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:f}=n.useContext(d.E_),h=p("tag",r),[g,m,x]=v(h),y=a()(h,`${h}-checkable`,{[`${h}-checkable-checked`]:l},null==f?void 0:f.className,s,m,x);return g(n.createElement("span",Object.assign({},u,{ref:t,style:Object.assign(Object.assign({},o),null==f?void 0:f.style),className:y,onClick:e=>{null==i||i(!l),null==c||c(e)}})))}));var C=j,k=r(98719);var Z=(0,m.bk)(["Tag","preset"],(e=>(e=>(0,k.Z)(e,((t,r)=>{let{textColor:n,lightBorderColor:o,lightColor:a,darkColor:s}=r;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:n,background:a,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:s,borderColor:s},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(x(e))),y);const w=(e,t,r)=>{const n="string"!=typeof(o=r)?o:o.charAt(0).toUpperCase()+o.slice(1);var o;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var S=(0,m.bk)(["Tag","status"],(e=>{const t=x(e);return[w(t,"success","Success"),w(t,"processing","Info"),w(t,"error","Error"),w(t,"warning","Warning")]}),y),z=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r};const P=n.forwardRef(((e,t)=>{const{prefixCls:r,className:o,rootClassName:p,style:f,children:h,icon:g,color:m,onClose:x,bordered:y=!0,visible:b}=e,j=z(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:C,direction:k,tag:w}=n.useContext(d.E_),[P,N]=n.useState(!0),O=(0,s.Z)(j,["closeIcon","closable"]);n.useEffect((()=>{void 0!==b&&N(b)}),[b]);const T=(0,l.o2)(m),$=(0,l.yT)(m),E=T||$,B=Object.assign(Object.assign({backgroundColor:m&&!E?m:void 0},null==w?void 0:w.style),f),I=C("tag",r),[H,_,R]=v(I),F=a()(I,null==w?void 0:w.className,{[`${I}-${m}`]:E,[`${I}-has-color`]:m&&!E,[`${I}-hidden`]:!P,[`${I}-rtl`]:"rtl"===k,[`${I}-borderless`]:!y},o,p,_,R),A=e=>{e.stopPropagation(),null==x||x(e),e.defaultPrevented||N(!1)},[,L]=(0,i.Z)((0,i.w)(e),(0,i.w)(w),{closable:!1,closeIconRender:e=>{const t=n.createElement("span",{className:`${I}-close-icon`,onClick:A},e);return(0,c.wm)(e,t,(e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),A(t)},className:a()(null==e?void 0:e.className,`${I}-close-icon`)})))}}),M="function"==typeof j.onClick||h&&"a"===h.type,W=g||null,q=W?n.createElement(n.Fragment,null,W,h&&n.createElement("span",null,h)):h,V=n.createElement("span",Object.assign({},O,{ref:t,className:F,style:B}),q,L,T&&n.createElement(Z,{key:"preset",prefixCls:I}),$&&n.createElement(S,{key:"status",prefixCls:I}));return H(M?n.createElement(u.Z,{component:"Tag"},V):V)})),N=P;N.CheckableTag=C;var O=N}}]);