"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2437],{20922:function(e,n,t){t.r(n),t.d(n,{default:function(){return G}});var r=t(97857),a=t.n(r),u=t(15009),i=t.n(u),c=t(99289),s=t.n(c),o=t(5574),p=t.n(o),l=t(67294),d=t(97131),f=t(12453),h=t(63496),m=t(2453),v=t(17788),x=t(66309),y=t(83622),b=t(42075),w=t(78045),k=t(47389),g=t(82061),j=t(51042),P=t(69044),Z=t(19632),E=t.n(Z),T=t(37476),S=t(5966),_=t(90672),N=t(64317),C=t(8232),R=t(85893),O=function(e){var n=C.Z.useForm(),t=p()(n,1)[0],r=e.modalVisible,a=e.onCancel,u=e.onSubmit,c=e.values,o=(0,l.useState)([]),d=p()(o,2),f=d[0],h=d[1],m=function(){var e=s()(i()().mark((function e(){var n,r,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return c?t.setFieldsValue({name:c.name,description:c.description,parent_id:c.parent_id}):t.resetFields(),e.prev=1,e.next=4,(0,P.jA)({pageSize:1e3,current:1});case 4:(n=e.sent).success&&(r=n.data,null!=c&&c.id&&(a=function e(t){var r=n.data.filter((function(e){return e.parent_id===t})),a=r.map((function(e){return e.id}));return r.forEach((function(n){a=[].concat(E()(a),E()(e(n.id)))})),a}(c.id),r=n.data.filter((function(e){return e.id!==c.id&&!a.includes(e.id)}))),h(r)),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(1),console.error("加载分组数据失败",e.t0);case 11:case"end":return e.stop()}}),e,null,[[1,8]])})));return function(){return e.apply(this,arguments)}}();(0,l.useEffect)((function(){r&&m()}),[r,null==c?void 0:c.id]);var v=null!=c&&c.id?"编辑组":"新建组";return(0,R.jsxs)(T.Y,{title:v,form:t,open:r,onFinish:u,onOpenChange:function(e){e||a()},modalProps:{destroyOnClose:!0,maskClosable:!1,width:500},children:[(0,R.jsx)(S.Z,{name:"name",label:"组名",rules:[{required:!0,message:"请输入组名"}],placeholder:"请输入组名"}),(0,R.jsx)(_.Z,{name:"description",label:"描述",placeholder:"请输入描述信息",fieldProps:{autoSize:{minRows:3,maxRows:6}}}),(0,R.jsx)(N.Z,{name:"parent_id",label:"上级分组",options:f.map((function(e){return{label:e.name,value:e.id}})),placeholder:"请选择上级分组",fieldProps:{showSearch:!0,allowClear:!0,optionFilterProp:"label"},extra:"选择一个上级分组，不选择则为顶级分组"})]})},D=h.Z.DirectoryTree,G=function(){var e=(0,l.useState)(!1),n=p()(e,2),t=n[0],r=n[1],u=(0,l.useState)(void 0),c=p()(u,2),o=c[0],h=c[1],Z=(0,l.useRef)(),E=(0,l.useState)([]),T=p()(E,2),S=T[0],_=T[1],N=(0,l.useState)("table"),C=p()(N,2),G=C[0],z=C[1],A=(0,l.useState)([]),F=p()(A,2),I=F[0],B=F[1],L=function(e){var n=new Map,t=[];return e.forEach((function(e){n.set(e.id,{key:e.id,title:e.name,children:[],original:e})})),e.forEach((function(e){var r=n.get(e.id);if(r)if(e.parent_id&&n.has(e.parent_id)){var a,u=n.get(e.parent_id);null==u||null===(a=u.children)||void 0===a||a.push(r)}else t.push(r)})),t},U=function(){var e=s()(i()().mark((function e(){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,P.jA)({pageSize:1e3,current:1});case 3:(n=e.sent).success&&(_(n.data),B(L(n.data))),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),m.ZP.error("获取分组列表失败");case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();(0,l.useEffect)((function(){U()}),[]);var q=function(e){r(e),e||h(void 0)},V=function(){var e=s()(i()().mark((function e(n){var t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=m.ZP.loading(null!=o&&o.id?"正在更新":"正在添加"),e.prev=1,null==o||!o.id){e.next=7;break}return e.next=5,(0,P.mD)(a()(a()({},n),{},{id:o.id}));case 5:e.next=9;break;case 7:return e.next=9,(0,P.Rp)(n);case 9:t(),m.ZP.success(null!=o&&o.id?"更新成功":"添加成功"),q(!1),null===(r=Z.current)||void 0===r||r.reload(),U(),e.next=20;break;case 16:e.prev=16,e.t0=e.catch(1),t(),m.ZP.error(null!=o&&o.id?"更新失败":"添加失败");case 20:case"end":return e.stop()}}),e,null,[[1,16]])})));return function(n){return e.apply(this,arguments)}}(),W=function(e){return S.some((function(n){return n.parent_id===e.id}))?{deletable:!1,reason:"该分组下有子分组，请先删除子分组"}:e.deletable?{deletable:!0,reason:""}:{deletable:!1,reason:"该分组不允许删除"}},Y=function(){var e=s()(i()().mark((function e(n){var t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if((t=W(n)).deletable){e.next=4;break}return m.ZP.error(t.reason),e.abrupt("return");case 4:v.Z.confirm({title:"确认删除",content:"确定要删除这个组吗？",okText:"确认",okType:"danger",cancelText:"取消",onOk:function(){var e=s()(i()().mark((function e(){var t,r,a,u,c,s;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t="delete-".concat(n.id,"-").concat(Date.now()),m.ZP.loading({content:"正在删除",key:t}),e.prev=2,e.next=5,(0,P.iE)(n.id,{skipErrorHandler:!0});case 5:(r=e.sent)&&"success"in r?r.success?(m.ZP.success({content:r.message||"删除成功",key:t}),null===(a=Z.current)||void 0===a||a.reload(),U()):m.ZP.error({content:r.message||"删除失败",key:t}):(m.ZP.success({content:"删除成功",key:t}),null===(u=Z.current)||void 0===u||u.reload(),U()),e.next=15;break;case 9:e.prev=9,e.t0=e.catch(2),console.error("删除分组时出错:",e.t0),s="删除失败，请重试",null!==(c=e.t0.response)&&void 0!==c&&c.data&&(s=e.t0.response.data.detail||e.t0.response.data.message||s),m.ZP.error({content:s,key:t});case 15:case"end":return e.stop()}}),e,null,[[2,9]])})));return function(){return e.apply(this,arguments)}}()});case 5:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),H={textAlign:"center"},K=[{title:(0,R.jsx)("div",{style:H,children:"组名"}),dataIndex:"name",align:"center"},{title:(0,R.jsx)("div",{style:H,children:"上级组"}),dataIndex:"parent_id",align:"center",render:function(e,n){var t=S.find((function(e){return e.id===n.parent_id}));return t?(0,R.jsx)(x.Z,{children:t.name}):"-"},search:!1},{title:(0,R.jsx)("div",{style:H,children:"描述"}),dataIndex:"description",align:"center"},{title:(0,R.jsx)("div",{style:H,children:"创建时间"}),dataIndex:"created_at",valueType:"dateTime",align:"center",search:!1},{title:(0,R.jsx)("div",{style:H,children:"是否可删除"}),dataIndex:"deletable",valueType:"select",align:"center",valueEnum:{true:{text:"是",status:"Success"},false:{text:"否",status:"Error"}},render:function(e,n){return(0,R.jsx)(x.Z,{color:n.deletable?"green":"red",children:n.deletable?"是":"否"})},search:!1},{title:"操作",dataIndex:"option",valueType:"option",align:"center",fixed:"right",render:function(e,n){return[(0,R.jsx)(y.ZP,{type:"link",onClick:function(){h(n),q(!0)},children:"编辑"},"edit"),(0,R.jsx)(y.ZP,{type:"link",danger:!0,disabled:!n.deletable||S.some((function(e){return e.parent_id===n.id})),onClick:function(){return Y(n)},title:n.deletable?S.some((function(e){return e.parent_id===n.id}))?"该分组下有子分组，请先删除子分组":"删除此分组":"该分组不允许删除",children:"删除"},"delete")]}}];return(0,R.jsxs)(d._z,{children:[(0,R.jsxs)("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between"},children:[(0,R.jsxs)(w.ZP.Group,{value:G,onChange:function(e){return z(e.target.value)},children:[(0,R.jsx)(w.ZP.Button,{value:"table",children:"列表视图"}),(0,R.jsx)(w.ZP.Button,{value:"tree",children:"树状视图"})]}),(0,R.jsxs)(y.ZP,{type:"primary",onClick:function(){h(void 0),q(!0)},children:[(0,R.jsx)(j.Z,{})," 新建"]})]}),"table"===G&&(0,R.jsx)(f.Z,{headerTitle:"组管理",actionRef:Z,rowKey:"id",search:{labelWidth:120},toolBarRender:!1,request:P.jA,columns:K}),"tree"===G&&(0,R.jsx)(D,{defaultExpandAll:!0,treeData:I,titleRender:function(e){var n=e.original;if(!n)return e.title;var t=S.some((function(e){return e.parent_id===n.id})),r=!n.deletable||t,a=n.deletable?t?"该分组下有子分组，请先删除子分组":"删除此分组":"该分组不允许删除";return(0,R.jsxs)(b.Z,{children:[(0,R.jsx)("span",{children:e.title}),(0,R.jsx)(y.ZP,{type:"text",size:"small",icon:(0,R.jsx)(k.Z,{}),onClick:function(e){e.stopPropagation(),h(n),q(!0)}}),(0,R.jsx)(y.ZP,{type:"text",size:"small",danger:!0,icon:(0,R.jsx)(g.Z,{}),disabled:r,title:a,onClick:function(e){e.stopPropagation(),Y(n)}})]})},style:{background:"#fff",padding:24}}),(0,R.jsx)(O,{onSubmit:V,onCancel:function(){q(!1)},modalVisible:t,values:o})]})}},69044:function(e,n,t){t.d(n,{CW:function(){return F},F3:function(){return N},Nq:function(){return v},Rd:function(){return z},Rf:function(){return d},Rp:function(){return P},_d:function(){return R},az:function(){return w},cY:function(){return B},cn:function(){return h},h8:function(){return y},iE:function(){return S},jA:function(){return g},mD:function(){return E},ul:function(){return D},w1:function(){return U},wG:function(){return V}});var r=t(5574),a=t.n(r),u=t(97857),i=t.n(u),c=t(15009),s=t.n(c),o=t(99289),p=t.n(o),l=t(78158);function d(e){return f.apply(this,arguments)}function f(){return(f=p()(s()().mark((function e(n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/users",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function h(e){return m.apply(this,arguments)}function m(){return(m=p()(s()().mark((function e(n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/users",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function v(e){return x.apply(this,arguments)}function x(){return(x=p()(s()().mark((function e(n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/users/".concat(n.id),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function y(e){return b.apply(this,arguments)}function b(){return(b=p()(s()().mark((function e(n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/users/".concat(n),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function w(e,n){return k.apply(this,arguments)}function k(){return(k=p()(s()().mark((function e(n,t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/users/changeStatus",{method:"POST",data:{id:n,status:t}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function g(e){return j.apply(this,arguments)}function j(){return(j=p()(s()().mark((function e(n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/groups",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function P(e){return Z.apply(this,arguments)}function Z(){return(Z=p()(s()().mark((function e(n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/groups",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function E(e){return T.apply(this,arguments)}function T(){return(T=p()(s()().mark((function e(n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/groups/".concat(n.id),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function S(e,n){return _.apply(this,arguments)}function _(){return(_=p()(s()().mark((function e(n,t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/groups/".concat(n),i()({method:"DELETE"},t)));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function N(e){return C.apply(this,arguments)}function C(){return(C=p()(s()().mark((function e(n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/roles",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function R(e){return O.apply(this,arguments)}function O(){return(O=p()(s()().mark((function e(n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/roles",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function D(e,n){return G.apply(this,arguments)}function G(){return(G=p()(s()().mark((function e(n,t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/roles/".concat(n),{method:"PUT",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function z(e){return A.apply(this,arguments)}function A(){return(A=p()(s()().mark((function e(n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/roles/".concat(n),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function F(){return I.apply(this,arguments)}function I(){return(I=p()(s()().mark((function e(){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/role/tree",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function B(e){return L.apply(this,arguments)}function L(){return(L=p()(s()().mark((function e(n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/roles/".concat(n),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function U(e){return q.apply(this,arguments)}function q(){return(q=p()(s()().mark((function e(n){var t;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=new URLSearchParams,Object.entries(n).forEach((function(e){var n=a()(e,2),r=n[0],u=n[1];t.append(r,String(u))})),e.abrupt("return",(0,l.N)("/api/system/config?".concat(t.toString()),{method:"POST"}));case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function V(e){return W.apply(this,arguments)}function W(){return(W=p()(s()().mark((function e(n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/useActiveCases",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}}}]);