"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3455],{47046:function(e,n){n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"}},82061:function(e,n,t){var r=t(1413),i=t(67294),a=t(47046),o=t(91146),c=function(e,n){return i.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:a.Z}))},s=i.forwardRef(c);n.Z=s},37446:function(e,n,t){t.d(n,{Z:function(){return s}});var r=t(1413),i=t(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"},o=t(91146),c=function(e,n){return i.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:a}))};var s=i.forwardRef(c)},51042:function(e,n,t){var r=t(1413),i=t(67294),a=t(42110),o=t(91146),c=function(e,n){return i.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:a.Z}))},s=i.forwardRef(c);n.Z=s},3940:function(e,n,t){t.r(n),t.d(n,{default:function(){return H}});var r=t(15009),i=t.n(r),a=t(97857),o=t.n(a),c=t(99289),s=t.n(c),d=t(5574),l=t.n(d),p=t(51042),u=t(43425),x=t(82061),f=t(37446),h=t(90389),g=t(97131),m=t(71471),v=t(34041),b=t(2453),y=t(83622),w=t(2487),j=t(4393),k=t(85418),S=t(17788),C=t(55102),Z=t(72269),R=t(67294),_=t(49185),B=t(58258),z=t(9783),I=t.n(z),P=t(28846),T=(0,P.kc)((function(e){var n=e.token;return{card:{borderRadius:"8px",background:"linear-gradient(135deg, #e8f4fe, #ede7fd)",overflow:"hidden",".ant-card-meta-title":{marginBottom:"12px",fontSize:"16px",fontWeight:"bold","& > a":{display:"inline-block",maxWidth:"100%",color:n.colorTextHeading}},".ant-card-body":{padding:"16px"},".ant-card-body:hover":{".ant-card-meta-title > a":{color:n.colorPrimary}},position:"relative"},item:{height:"40px",color:n.colorPrimary,fontSize:"14px",marginBottom:"0"},cardList:{".ant-list .ant-list-item-content-single":{maxWidth:"100%"},marginTop:"16px"},extraImg:I()({width:"155px",marginTop:"-20px",textAlign:"center",img:{width:"100%"}},"@media screen and (max-width: ".concat(n.screenMD,"px)"),{display:"none"}),newButton:{width:"100%",height:"238px",color:n.colorTextSecondary,backgroundColor:n.colorBgContainer,borderColor:n.colorBorder,borderRadius:"8px",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",fontSize:"16px","&:hover":{borderColor:n.colorPrimary,color:n.colorPrimary}},cardAvatar:{width:"48px",height:"48px",borderRadius:"48px"},cardDescription:{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis",wordBreak:"break-all"},pageHeaderContent:I()({position:"relative"},"@media screen and (max-width: ".concat(n.screenSM,"px)"),{paddingBottom:"30px"}),contentLink:I()(I()({marginTop:"16px",a:{marginRight:"32px",img:{width:"24px"}},img:{marginRight:"8px",verticalAlign:"middle"}},"@media screen and (max-width: ".concat(n.screenLG,"px)"),{a:{marginRight:"16px"}}),"@media screen and (max-width: ".concat(n.screenSM,"px)"),{position:"absolute",bottom:"-4px",left:"0",width:"1000px",a:{marginRight:"16px"},img:{marginRight:"4px"}}),cardLabel:{display:"inline-block",padding:"2px 8px",borderRadius:"4px",fontSize:"12px",marginRight:"8px",backgroundColor:n.colorBgTextHover},cardStats:{marginTop:"8px",display:"flex",justifyContent:"space-between",color:n.colorTextSecondary,fontSize:"12px"},avatarContainer:{marginBottom:"8px",display:"flex",alignItems:"center"},avatarIcon:{width:"40px",height:"40px",borderRadius:"8px",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:n.colorPrimaryBg,color:n.colorPrimary,fontSize:"20px",marginRight:"10px"},moreButton:{position:"absolute",top:"12px",right:"12px",color:n.colorTextSecondary,cursor:"pointer",width:"32px",height:"32px",display:"flex",justifyContent:"center",alignItems:"center",borderRadius:"4px",zIndex:10,"&:hover":{backgroundColor:n.colorBgTextHover}},tableListOperator:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px",padding:"16px 24px",backgroundColor:"#fff",borderRadius:"8px"},rightContent:{display:"flex",alignItems:"center"},searchInput:{".ant-input":{borderRadius:"4px"},".ant-input-search-button":{borderRadius:"0 4px 4px 0"}},contentLinks:{marginTop:"16px",a:{display:"inline-flex",alignItems:"center",img:{width:"24px",marginRight:"8px"}}}}})),N=(0,P.kc)((function(){return{".knowledge-base-dropdown":{".ant-dropdown-menu":{padding:"8px 0",borderRadius:"8px"},".ant-dropdown-menu-item":{padding:"10px 16px",margin:"0","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.04)"}}}}})),E=T,L=t(96974),M=t(85893),W=m.Z.Paragraph,D=v.default.Option,H=function(){var e=E().styles;N();var n=(0,R.useState)(!1),t=l()(n,2),r=t[0],a=t[1],c=(0,R.useState)([]),d=l()(c,2),m=d[0],z=d[1],I=(0,R.useState)(!1),P=l()(I,2),T=P[0],H=P[1],O=(0,R.useState)(""),A=l()(O,2),G=A[0],V=A[1],F=(0,R.useState)(""),K=l()(F,2),Y=K[0],q=K[1],J=(0,R.useState)(void 0),Q=l()(J,2),U=Q[0],X=Q[1],$=(0,R.useState)(void 0),ee=l()($,2),ne=ee[0],te=ee[1],re=(0,R.useState)({current:1,pageSize:12,total:0}),ie=l()(re,2),ae=ie[0],oe=ie[1],ce=(0,R.useState)([]),se=l()(ce,2),de=se[0],le=se[1],pe=(0,R.useState)(!0),ue=l()(pe,2),xe=ue[0],fe=ue[1],he=(0,R.useState)(!1),ge=l()(he,2),me=ge[0],ve=ge[1],be=(0,R.useState)(!1),ye=l()(be,2),we=ye[0],je=ye[1],ke=(0,L.s0)(),Se=function(){var e=s()(i()().mark((function e(){var n,t,r,c;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a(!0),e.prev=1,e.next=4,(0,_.ws)({current:ae.current,pageSize:ae.pageSize});case 4:(n=e.sent).success&&(t=n.data.filter((function(e){return!1===e.is_conversation_kb})),r=t.map((function(e){return{id:e.id,name:e.name,description:e.description,created_at:e.created_at,file_count:e.file_count,index_count:e.chunk_count,chunk_count:e.chunk_count}})),z(r),c=Math.ceil(t.length*n.total/n.data.length),oe(o()(o()({},ae),{},{total:c}))),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(1),b.ZP.error("获取知识库列表失败");case 11:return e.prev=11,a(!1),e.finish(11);case 14:case"end":return e.stop()}}),e,null,[[1,8,11,14]])})));return function(){return e.apply(this,arguments)}}(),Ce=function(){var e=s()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(G.trim()){e.next=3;break}return b.ZP.error("知识库名称不能为空"),e.abrupt("return");case 3:if(U){e.next=6;break}return b.ZP.error("请选择向量模型"),e.abrupt("return");case 6:return a(!0),e.prev=7,e.next=10,(0,_.D9)({name:G.trim(),description:Y.trim(),embedding_model:U,embedding_model_id:null==ne?void 0:ne.toString(),basic_index:xe,graph_index:me,semantic_index:we});case 10:b.ZP.success("知识库创建成功"),H(!1),V(""),q(""),Se(),e.next=20;break;case 17:e.prev=17,e.t0=e.catch(7),b.ZP.error((null===e.t0||void 0===e.t0?void 0:e.t0.message)||"创建失败，请稍后重试");case 20:return e.prev=20,a(!1),e.finish(20);case 23:case"end":return e.stop()}}),e,null,[[7,17,20,23]])})));return function(){return e.apply(this,arguments)}}(),Ze=function(e){ke("/knowledgeManagement/knowledgeInfo?id=".concat(e))};(0,R.useEffect)((function(){var e=function(){var e=s()(i()().mark((function e(){var n,t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,B.DY)();case 3:(n=e.sent).success&&(t=n.data.map((function(e){return{label:e.name,value:e.embedding_name,id:e.id}})),r=t.map((function(e){return o()(o()({},e),{},{id:Number(e.id)})})),le(r),r.length>0&&(X(r[0].value),te(r[0].id))),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取向量模型列表失败:",e.t0);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);(0,R.useEffect)((function(){Se()}),[ae.current,ae.pageSize]);var Re=function(){var e=s()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,_.eV)(n);case 3:e.sent&&(b.ZP.success("删除成功"),Se()),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("删除失败:",e.t0),b.ZP.error("删除失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(n){return e.apply(this,arguments)}}();return(0,M.jsxs)(M.Fragment,{children:[(0,M.jsxs)(g._z,{children:[(0,M.jsx)("div",{style:{marginBottom:"24px",display:"flex",justifyContent:"flex-end"},children:(0,M.jsx)(y.ZP,{type:"primary",icon:(0,M.jsx)(p.Z,{}),onClick:function(){return H(!0)},children:"新建知识库"})}),(0,M.jsx)("div",{className:e.cardList,children:(0,M.jsx)(w.Z,{rowKey:"id",loading:r,grid:{gutter:24,xs:1,sm:2,md:2,lg:3,xl:4,xxl:4},dataSource:m,pagination:o()(o()({},ae),{},{onChange:function(e){oe(o()(o()({},ae),{},{current:e}))}}),renderItem:function(n){var t=[{key:"settings",label:(0,M.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,M.jsx)(u.Z,{style:{marginRight:8}}),(0,M.jsx)("span",{children:"设置"})]}),onClick:function(e){e.domEvent.stopPropagation(),Ze(n.id)}},{key:"delete",label:(0,M.jsxs)("div",{style:{display:"flex",alignItems:"center",color:"#ff4d4f",cursor:"pointer"},onClick:function(){Re(n.id)},children:[(0,M.jsx)(x.Z,{style:{marginRight:8,color:"#ff4d4f"}}),(0,M.jsx)("span",{children:"删除"})]})}];return(0,M.jsx)(w.Z.Item,{children:(0,M.jsxs)(j.Z,{hoverable:!0,className:e.card,onClick:function(){return Ze(n.id)},bodyStyle:{paddingRight:"40px"},children:[(0,M.jsx)("div",{className:e.moreButton,onClick:function(e){e.stopPropagation()},children:(0,M.jsx)(k.Z,{menu:{items:t},trigger:["click"],placement:"bottomRight",getPopupContainer:function(e){return e.parentNode},overlayStyle:{minWidth:"120px",boxShadow:"0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)",borderRadius:"8px"},overlayClassName:"knowledge-base-dropdown",children:(0,M.jsx)(f.Z,{})})}),(0,M.jsxs)("div",{className:e.avatarContainer,children:[(0,M.jsx)("div",{className:e.avatarIcon,children:(0,M.jsx)(h.Z,{})}),(0,M.jsx)("span",{style:{fontWeight:"bold",fontSize:"16px"},children:n.name})]}),(0,M.jsx)(j.Z.Meta,{title:null,description:(0,M.jsx)(W,{className:e.item,ellipsis:{rows:2},children:n.description||"暂无描述"})}),(0,M.jsxs)("div",{className:e.cardStats,children:[(0,M.jsxs)("span",{children:["文件数量：",n.file_count||0]}),(0,M.jsxs)("span",{children:["知识点数量：",n.index_count||0]})]})]})},n.id)}})})]}),(0,M.jsxs)(S.Z,{title:"新建知识库",open:T,onOk:Ce,onCancel:function(){H(!1),V(""),q("")},confirmLoading:r,okButtonProps:{disabled:!G.trim()},children:[(0,M.jsxs)("div",{style:{marginBottom:16},children:[(0,M.jsx)(C.Z,{placeholder:"请输入知识库名称",value:G,onChange:function(e){return V(e.target.value)},maxLength:50,showCount:!0,status:G.trim()?"":"error"}),!G.trim()&&(0,M.jsx)("div",{style:{color:"#ff4d4f",fontSize:12,marginTop:4},children:"请输入知识库名称"})]}),(0,M.jsx)("div",{style:{marginBottom:20},children:(0,M.jsx)(C.Z.TextArea,{placeholder:"请输入知识库描述（选填）",value:Y,onChange:function(e){return q(e.target.value)},rows:4,maxLength:200,showCount:!0})}),(0,M.jsx)("div",{style:{marginBottom:20},children:(0,M.jsx)(v.default,{placeholder:"选择向量模型",value:U,onChange:function(e){var n=de.find((function(n){return n.value===e}));X(e),te(null==n?void 0:n.id)},style:{width:"100%"},allowClear:!1,showSearch:!1,children:de.map((function(e){return(0,M.jsx)(D,{value:e.value,children:e.label},e.id)}))})}),(0,M.jsx)("div",{style:{marginBottom:20},children:(0,M.jsxs)("div",{className:"index-settings-container",style:{background:"#f9f9f9",borderRadius:"8px",padding:"16px",border:"1px solid #f0f0f0"},children:[(0,M.jsxs)("div",{style:{marginBottom:16,fontWeight:"bold",fontSize:"15px",borderBottom:"1px solid #f0f0f0",paddingBottom:"8px",display:"flex",alignItems:"center"},children:[(0,M.jsx)(u.Z,{style:{marginRight:8}}),"索引设置"]}),(0,M.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:12},children:[(0,M.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 12px",background:"#fff",borderRadius:"6px",boxShadow:"0 1px 2px rgba(0,0,0,0.03)"},children:[(0,M.jsxs)("div",{children:[(0,M.jsx)("div",{style:{fontWeight:"500"},children:"基础索引"}),(0,M.jsx)("div",{style:{fontSize:"12px",color:"#888",marginTop:"4px"},children:"用于基本的知识检索"})]}),(0,M.jsx)(Z.Z,{defaultChecked:!0,checked:xe,onChange:function(e){return fe(e)}})]}),(0,M.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 12px",background:"#fff",borderRadius:"6px",boxShadow:"0 1px 2px rgba(0,0,0,0.03)"},children:[(0,M.jsxs)("div",{children:[(0,M.jsx)("div",{style:{fontWeight:"500"},children:"Graph索引"}),(0,M.jsx)("div",{style:{fontSize:"12px",color:"#888",marginTop:"4px"},children:"用于知识关联和图谱展示"})]}),(0,M.jsx)(Z.Z,{checked:me,onChange:function(e){return ve(e)}})]}),(0,M.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 12px",background:"#fff",borderRadius:"6px",boxShadow:"0 1px 2px rgba(0,0,0,0.03)"},children:[(0,M.jsxs)("div",{children:[(0,M.jsx)("div",{style:{fontWeight:"500"},children:"语义索引"}),(0,M.jsx)("div",{style:{fontSize:"12px",color:"#888",marginTop:"4px"},children:"用于增强语义理解能力"})]}),(0,M.jsx)(Z.Z,{checked:we,onChange:function(e){return je(e)}})]})]})]})})]})]})}},49185:function(e,n,t){t.d(n,{D9:function(){return s},IV:function(){return u},eV:function(){return f},ws:function(){return l}});var r=t(15009),i=t.n(r),a=t(99289),o=t.n(a),c=t(78158);function s(e){return d.apply(this,arguments)}function d(){return(d=o()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge_base",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function l(e){return p.apply(this,arguments)}function p(){return(p=o()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/my_knowledge_bases",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function u(e){return x.apply(this,arguments)}function x(){return(x=o()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge_bases/".concat(n),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function f(e){return h.apply(this,arguments)}function h(){return(h=o()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge_bases/".concat(n),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}}}]);