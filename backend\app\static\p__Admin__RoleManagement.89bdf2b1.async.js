(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7535],{51042:function(e,n,t){"use strict";var r=t(1413),a=t(67294),o=t(42110),u=t(91146),s=function(e,n){return a.createElement(u.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:o.Z}))},c=a.forwardRef(s);n.Z=c},5966:function(e,n,t){"use strict";var r=t(97685),a=t(1413),o=t(91),u=t(21770),s=t(8232),c=t(55241),i=t(98423),l=t(67294),p=t(62633),f=t(85893),d=["fieldProps","proFieldProps"],h=["fieldProps","proFieldProps"],v="text",m=function(e){var n=(0,u.Z)(e.open||!1,{value:e.open,onChange:e.onOpenChange}),t=(0,r.Z)(n,2),o=t[0],i=t[1];return(0,f.jsx)(s.Z.Item,{shouldUpdate:!0,noStyle:!0,children:function(n){var t,r=n.getFieldValue(e.name||[]);return(0,f.jsx)(c.Z,(0,a.Z)((0,a.Z)({getPopupContainer:function(e){return e&&e.parentNode?e.parentNode:e},onOpenChange:function(e){return i(e)},content:(0,f.jsxs)("div",{style:{padding:"4px 0"},children:[null===(t=e.statusRender)||void 0===t?void 0:t.call(e,r),e.strengthText?(0,f.jsx)("div",{style:{marginTop:10},children:(0,f.jsx)("span",{children:e.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},e.popoverProps),{},{open:o,children:e.children}))}})},g=function(e){var n=e.fieldProps,t=e.proFieldProps,r=(0,o.Z)(e,d);return(0,f.jsx)(p.Z,(0,a.Z)({valueType:v,fieldProps:n,filedConfig:{valueType:v},proFieldProps:t},r))};g.Password=function(e){var n=e.fieldProps,t=e.proFieldProps,u=(0,o.Z)(e,h),s=(0,l.useState)(!1),c=(0,r.Z)(s,2),d=c[0],g=c[1];return null!=n&&n.statusRender&&u.name?(0,f.jsx)(m,{name:u.name,statusRender:null==n?void 0:n.statusRender,popoverProps:null==n?void 0:n.popoverProps,strengthText:null==n?void 0:n.strengthText,open:d,onOpenChange:g,children:(0,f.jsx)("div",{children:(0,f.jsx)(p.Z,(0,a.Z)({valueType:"password",fieldProps:(0,a.Z)((0,a.Z)({},(0,i.Z)(n,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(e){var t;null==n||null===(t=n.onBlur)||void 0===t||t.call(n,e),g(!1)},onClick:function(e){var t;null==n||null===(t=n.onClick)||void 0===t||t.call(n,e),g(!0)}}),proFieldProps:t,filedConfig:{valueType:v}},u))})}):(0,f.jsx)(p.Z,(0,a.Z)({valueType:"password",fieldProps:n,proFieldProps:t,filedConfig:{valueType:v}},u))},g.displayName="ProFormComponent",n.Z=g},27504:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return A}});var r=t(9783),a=t.n(r),o=t(19632),u=t.n(o),s=t(15009),c=t.n(s),i=t(97857),l=t.n(i),p=t(64599),f=t.n(p),d=t(99289),h=t.n(d),v=t(5574),m=t.n(v),g=t(67294),y=t(97131),w=t(12453),x=t(2453),k=t(17788),M=t(83622),b=t(51042),P=t(69044),Z=t(37476),T=t(5966),C=t(63496),S=t(85893),j=function(e){var n=e.formRef,t=e.onSubmit,r=e.modalVisible,o=e.values,u=e.treeData,s=e.checkedKeys,i=e.onTreeCheck,p=e.onCancel;return(0,S.jsxs)(Z.Y,{formRef:n,title:null!=o&&o.id?"编辑角色":"新建角色",visible:r,onVisibleChange:function(e){e||p()},onFinish:function(){var e=h()(c()().mark((function e(n){var r,u;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=l()(l()({},n),{},{id:null==o?void 0:o.id,access:s.reduce((function(e,n){return l()(l()({},e),{},a()({},n,!0))}),{})}),e.next=3,t(r);case 3:return u=e.sent,e.abrupt("return",u);case 5:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),initialValues:o,children:[(0,S.jsx)(T.Z,{name:"id",hidden:!0}),(0,S.jsx)(T.Z,{name:"name",label:"角色名称",placeholder:"请输入角色名称",rules:[{required:!0,message:"角色名称为必填项"}]}),(0,S.jsx)(T.Z,{name:"description",label:"描述",placeholder:"请输入角色描述"}),(0,S.jsx)(C.Z,{checkable:!0,onCheck:function(e){return i(e)},checkedKeys:s,treeData:u})]})},E=t(35312),A=function(){var e=(0,E.useIntl)(),n=(0,g.useState)(!1),t=m()(n,2),r=t[0],o=t[1],s=(0,g.useState)(!1),i=m()(s,2),p=i[0],d=i[1],v=(0,g.useState)(void 0),Z=m()(v,2),T=Z[0],C=Z[1],A=(0,g.useState)([]),L=m()(A,2),R=L[0],N=L[1],K=(0,g.useRef)(),F=(0,g.useRef)(),O=(0,g.useState)([]),I=m()(O,2),D=I[0],_=I[1],G=(0,g.useState)({}),V=m()(G,2),W=V[0],q=V[1],U=(0,g.useState)({}),Q=m()(U,2),z=Q[0],B=Q[1],Y=function e(n){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;return n.forEach((function(n){var u=n.key,s=n.children;a&&(t[a]||(t[a]=[]),t[a].push(u));var c=void 0;if(u.startsWith("canAccess")&&!a){var i=u.replace("canAccess","").charAt(0).toLowerCase()+u.replace("canAccess","").slice(1);c="menu.".concat(i)}else if(u.startsWith("canAccess")&&a&&o){var l=u.replace("canAccess","").charAt(0).toLowerCase()+u.replace("canAccess","").slice(1);c="".concat(o,".").concat(l)}r[u]={parentKey:a,menuKey:c,children:{}},s&&s.length>0&&e(s,t,r,u,c)})),[t,r]},$=function(n,t){var r={ComplaintQuestionAnswer:"消保投诉问答",IntelligentRetrieval:"智能检索",MCPSquare:"MCP广场",canAccessComplaintQuestionAnswer:"消保投诉问答",canAccessIntelligentRetrieval:"智能检索",canAccessMCPSquare:"MCP广场","L L Mmarket Llm Chat":"LLM聊天","L L Mmarket Models":"LLM模型","L L Mmarket Llm Comparison":"LLM对比","L L Model Tuning":"模型微调","Model Evaluation":"模型评估","My Copywriting":"我的文案",Wiseflow:"流程编排",Talklist:"对话列表"};if(r[n])return console.log("使用特殊处理:",n,"->",r[n]),r[n];if(!n.startsWith("canAccess")){var a=e.formatMessage({id:n},{defaultMessage:""});if(a&&a!==n)return console.log("找到直接翻译:",n,"->",a),a;var o="menu.".concat(n),u=e.formatMessage({id:o},{defaultMessage:""});if(u&&u!==o)return console.log("找到菜单翻译:",o,"->",u),u;var s=n.charAt(0).toLowerCase()+n.slice(1),c="menu.".concat(s),i=e.formatMessage({id:c},{defaultMessage:""});return i&&i!==c?(console.log("找到小写菜单翻译:",c,"->",i),i):(console.log("未找到翻译，返回原键:",n),n)}var l=t||z,p=l[n],f="";if(p&&p.menuKey){var d;if(f=e.formatMessage({id:p.menuKey},{defaultMessage:""}),p.parentKey&&null!==(d=l[p.parentKey])&&void 0!==d&&d.menuKey){var h=l[p.parentKey].menuKey,v=e.formatMessage({id:h},{defaultMessage:""});if(v&&v!==h&&f&&f!==p.menuKey)return"".concat(v," - ").concat(f)}if(f&&f!==p.menuKey)return f}var m=n.replace("canAccess","");console.log("查找权限翻译:",m);var g=e.formatMessage({id:m},{defaultMessage:""});if(console.log("找到的权限翻译:",g),g&&g!==m)return g;var y=m.charAt(0).toLowerCase()+m.slice(1),w="menu.".concat(y);console.log("查找小写菜单键:",w);var x=e.formatMessage({id:w},{defaultMessage:""});return console.log("找到的小写菜单翻译:",x),x&&x!==w?x:m.replace(/([A-Z])/g," $1").trim()};(0,g.useEffect)((function(){var e=function(){var e=h()(c()().mark((function e(){var n,t,r,a,o,u,s,i,p;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,P.CW)();case 3:n=e.sent,console.log("权限树原始数据===>",n),t=function e(n){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=new Set,o=[],u="  ".repeat(r),s=f()(n);try{for(s.s();!(t=s.n()).done;){var c=t.value;if(a.has(c.key))console.warn("".concat(u,"⚠️ 发现重复节点，已跳过: ").concat(c.key," - ").concat(c.title));else{a.add(c.key),console.log("".concat(u,"✓ 添加节点: ").concat(c.key," - ").concat(c.title));var i=l()({},c);c.children&&c.children.length>0&&(i.children=e(c.children,r+1)),o.push(i)}}}catch(e){s.e(e)}finally{s.f()}return o},r=t(n),console.log("去重后的权限树数据===>",r),a=function e(n){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",a=f()(n);try{for(a.s();!(t=a.n()).done;){var o=t.value,u=r?"".concat(r,".").concat(o.key):o.key;o.key.includes("ComplaintQuestionAnswer")&&console.log("找到 ComplaintQuestionAnswer 节点:",{key:o.key,path:u,title:o.title,hasChildren:!!o.children}),o.children&&e(o.children,u)}}catch(e){a.e(e)}finally{a.f()}},a(r),o=Y(r),u=m()(o,2),s=u[0],i=u[1],console.log("newNodeRelations===>",s),console.log("newMenuRelations===>",i),q(s),B(i),p=function e(n){return n.map((function(n){return l()(l()({},n),{},{title:$(n.key,i),children:n.children?e(n.children):void 0})}))}(r),_(p),e.next=23;break;case 20:e.prev=20,e.t0=e.catch(0),x.ZP.error("获取权限树据失败，请重试");case 23:case"end":return e.stop()}}),e,null,[[0,20]])})));return function(){return e.apply(this,arguments)}}();e()}),[e]),(0,g.useEffect)((function(){var e;r||(null===(e=F.current)||void 0===e||e.resetFields(),N([]))}),[r]),(0,g.useEffect)((function(){p||(C(void 0),N([]))}),[p]);var H=function(e){var n=function e(n){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set;if(t.has(n))return[];t.add(n);for(var r=[],a=0,o=Object.entries(W);a<o.length;a++){var s=m()(o[a],2),c=s[0],i=s[1];i.includes(n)&&(r=[].concat(u()(r),[c],u()(e(c,t))))}return r},t=new Set;e.forEach((function(e){n(e).forEach((function(e){return t.add(e)}))}));var r=u()(new Set([].concat(u()(e),u()(Array.from(t)))));return console.log("原始选中keys:",e),console.log("包含父节点的完整keys:",r),r},J=function(){var n=h()(c()().mark((function n(t){var r,u,s;return c()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return r=x.ZP.loading(e.formatMessage({id:"adding",defaultMessage:"正在添加"})),n.prev=1,s=H(R),n.next=5,(0,P._d)(l()(l()({},t),{},{access:s.reduce((function(e,n){return l()(l()({},e),{},a()({},n,!0))}),{})}));case 5:return r(),x.ZP.success(e.formatMessage({id:"add.success",defaultMessage:"添加成功"})),o(!1),null===(u=K.current)||void 0===u||u.reload(),n.abrupt("return",!0);case 12:return n.prev=12,n.t0=n.catch(1),r(),x.ZP.error(e.formatMessage({id:"add.fail",defaultMessage:"添加失败，请重试"})),n.abrupt("return",!1);case 17:case"end":return n.stop()}}),n,null,[[1,12]])})));return function(e){return n.apply(this,arguments)}}(),X=function(){var n=h()(c()().mark((function n(t){var r,o,u,s;return c()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(r=x.ZP.loading(e.formatMessage({id:"updating",defaultMessage:"正在更新"})),n.prev=1,t.id){n.next=4;break}throw new Error("Role ID is undefined");case 4:return u=H(R),s=u.reduce((function(e,n){return l()(l()({},e),{},a()({},n,!0))}),{}),n.next=8,(0,P.ul)(t.id,l()(l()({},t),{},{access:s}));case 8:return r(),x.ZP.success(e.formatMessage({id:"update.success",defaultMessage:"更新成功"})),d(!1),C(void 0),null===(o=K.current)||void 0===o||o.reload(),n.abrupt("return",!0);case 16:return n.prev=16,n.t0=n.catch(1),r(),x.ZP.error(e.formatMessage({id:"update.fail",defaultMessage:"更新失败，请重试"})),n.abrupt("return",!1);case 21:case"end":return n.stop()}}),n,null,[[1,16]])})));return function(e){return n.apply(this,arguments)}}(),ee=function(){var e=h()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:k.Z.confirm({title:"确认删除",content:"确定要删除这个角色吗？",okText:"确认",cancelText:"取消",onOk:function(){var e=h()(c()().mark((function e(){var t,r;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=x.ZP.loading("正在删除"),e.prev=1,e.next=4,(0,P.Rd)(n.id);case 4:return t(),x.ZP.success("删除成功"),null===(r=K.current)||void 0===r||r.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),t(),x.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),ne=function(){var e=h()(c()().mark((function e(n){var t,r,a,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,P.cY)(n.id);case 3:r=e.sent,console.log("roleDetails===>",r),d(!0),C(r),a=Object.entries(r.access||{}).filter((function(e){return m()(e,2)[1]})).map((function(e){return m()(e,1)[0]})),console.log("selectedKeys===>",a),o=a.filter((function(e){return void 0!==z[e]||Object.values(W).some((function(n){return n.includes(e)}))})),console.log("编辑角色权限原始数据:",a),console.log("有效的权限keys:",o),N(o),null===(t=F.current)||void 0===t||t.setFieldsValue({name:r.name,description:r.description}),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(0),x.ZP.error("获取角色详情失败，请重试");case 19:case"end":return e.stop()}}),e,null,[[0,16]])})));return function(n){return e.apply(this,arguments)}}(),te=[{title:e.formatMessage({id:"role.name",defaultMessage:"名称"}),dataIndex:"name",valueType:"text"},{title:e.formatMessage({id:"role.description",defaultMessage:"描述"}),dataIndex:"description",valueType:"textarea"},{title:e.formatMessage({id:"role.createdAt",defaultMessage:"创建时间"}),dataIndex:"created_at",valueType:"dateTime",search:!1},{title:e.formatMessage({id:"role.deletable",defaultMessage:"可删除"}),dataIndex:"deletable",valueType:"text",search:!1,render:function(e){return e?"是":"否"}},{title:e.formatMessage({id:"role.action",defaultMessage:"操作"}),dataIndex:"option",valueType:"option",render:function(n,t){return[(0,S.jsx)(M.ZP,{type:"link",onClick:function(){return ne(t)},children:e.formatMessage({id:"edit",defaultMessage:"编辑"})},"edit-".concat(t.id)),(0,S.jsx)(M.ZP,{type:"link",danger:!0,onClick:function(){return ee(t)},disabled:!t.deletable,children:e.formatMessage({id:"delete",defaultMessage:"删除"})},"delete-".concat(t.id))]}}];return(0,S.jsxs)(y._z,{children:[(0,S.jsx)(w.Z,{headerTitle:e.formatMessage({id:"menu.roleManagement",defaultMessage:"角色管理"}),actionRef:K,rowKey:"id",search:{labelWidth:120},toolBarRender:function(){return[(0,S.jsxs)(M.ZP,{type:"primary",onClick:function(){o(!0),N([])},children:[(0,S.jsx)(b.Z,{})," ",e.formatMessage({id:"new",defaultMessage:"新建"})]},"primary")]},request:function(){var e=h()(c()().mark((function e(n){var t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,P.F3)(n);case 2:return t=e.sent,e.abrupt("return",{data:t.data||[],success:t.success||!0,total:t.total||0});case 4:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),columns:te,pagination:{pageSize:10}}),(0,S.jsx)(j,{modalVisible:r,onCancel:function(){return o(!1)},onSubmit:J,formRef:F,values:{},treeData:D,checkedKeys:R,onTreeCheck:N}),T&&p&&D.length>0&&(0,S.jsx)(j,{modalVisible:p,onCancel:function(){d(!1)},onSubmit:X,formRef:F,values:T,treeData:D,checkedKeys:R,onTreeCheck:N})]})}},69044:function(e,n,t){"use strict";t.d(n,{CW:function(){return I},F3:function(){return E},Nq:function(){return m},Rd:function(){return F},Rf:function(){return f},Rp:function(){return P},_d:function(){return L},az:function(){return x},cY:function(){return _},cn:function(){return h},h8:function(){return y},iE:function(){return S},jA:function(){return M},mD:function(){return T},ul:function(){return N},w1:function(){return V},wG:function(){return q}});var r=t(5574),a=t.n(r),o=t(97857),u=t.n(o),s=t(15009),c=t.n(s),i=t(99289),l=t.n(i),p=t(78158);function f(e){return d.apply(this,arguments)}function d(){return(d=l()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/users",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function h(e){return v.apply(this,arguments)}function v(){return(v=l()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/users",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function m(e){return g.apply(this,arguments)}function g(){return(g=l()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/users/".concat(n.id),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function y(e){return w.apply(this,arguments)}function w(){return(w=l()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/users/".concat(n),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function x(e,n){return k.apply(this,arguments)}function k(){return(k=l()(c()().mark((function e(n,t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/users/changeStatus",{method:"POST",data:{id:n,status:t}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function M(e){return b.apply(this,arguments)}function b(){return(b=l()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/groups",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function P(e){return Z.apply(this,arguments)}function Z(){return(Z=l()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/groups",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function T(e){return C.apply(this,arguments)}function C(){return(C=l()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/groups/".concat(n.id),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function S(e,n){return j.apply(this,arguments)}function j(){return(j=l()(c()().mark((function e(n,t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/groups/".concat(n),u()({method:"DELETE"},t)));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function E(e){return A.apply(this,arguments)}function A(){return(A=l()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/roles",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function L(e){return R.apply(this,arguments)}function R(){return(R=l()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/roles",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function N(e,n){return K.apply(this,arguments)}function K(){return(K=l()(c()().mark((function e(n,t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/roles/".concat(n),{method:"PUT",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function F(e){return O.apply(this,arguments)}function O(){return(O=l()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/roles/".concat(n),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function I(){return D.apply(this,arguments)}function D(){return(D=l()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/role/tree",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function _(e){return G.apply(this,arguments)}function G(){return(G=l()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/roles/".concat(n),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function V(e){return W.apply(this,arguments)}function W(){return(W=l()(c()().mark((function e(n){var t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=new URLSearchParams,Object.entries(n).forEach((function(e){var n=a()(e,2),r=n[0],o=n[1];t.append(r,String(o))})),e.abrupt("return",(0,p.N)("/api/system/config?".concat(t.toString()),{method:"POST"}));case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function q(e){return U.apply(this,arguments)}function U(){return(U=l()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/useActiveCases",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},64599:function(e,n,t){var r=t(96263);e.exports=function(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=r(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var a=0,o=function(){};return{s:o,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,s=!0,c=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return s=e.done,e},e:function(e){c=!0,u=e},f:function(){try{s||null==t.return||t.return()}finally{if(c)throw u}}}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);