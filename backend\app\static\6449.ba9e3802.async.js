"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6449],{97302:function(e,r,n){var t=n(1413),o=n(67294),a=n(49842),c=n(91146),i=function(e,r){return o.createElement(c.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:r,icon:a.Z}))},l=o.forwardRef(i);r.Z=l},26544:function(e,r,n){n.d(r,{Jr:function(){return b},Vv:function(){return d},Xf:function(){return g},ao:function(){return u},cO:function(){return v},v5:function(){return l},yB:function(){return y}});var t=n(15009),o=n.n(t),a=n(99289),c=n.n(a),i=n(78158);function l(e){return s.apply(this,arguments)}function s(){return(s=c()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/media_insights_reports",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function u(e){return p.apply(this,arguments)}function p(){return(p=c()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/media_insights_reports",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function d(e){return f.apply(this,arguments)}function f(){return(f=c()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/media_insights_reports/".concat(r),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function g(e,r){return h.apply(this,arguments)}function h(){return(h=c()(o()().mark((function e(r,n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/media_insights_reports/".concat(r),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function b(e){return m.apply(this,arguments)}function m(){return(m=c()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/media_insights_reports/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function y(e){return C.apply(this,arguments)}function C(){return(C=c()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/delete_media_insights_reports",{method:"DELETE",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function v(e,r){return k.apply(this,arguments)}function k(){return(k=c()(o()().mark((function e(r,n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/media_insights_report_download/".concat(r),{method:"GET",responseType:"blob"}).then((function(e){var r,t=null===(r=e.headers)||void 0===r?void 0:r.get("content-disposition"),o=n||"report.pdf";if(!n&&t){var a=t.match(/filename\*=UTF-8''([^;]+)/i);if(a)o=decodeURIComponent(a[1]);else{var c=t.match(/filename="([^"]+)"/i);c&&(o=c[1])}}var i=new Blob([e],{type:e.type}),l=window.URL.createObjectURL(i),s=document.createElement("a");return s.href=l,s.download=o,document.body.appendChild(s),s.click(),window.URL.revokeObjectURL(l),document.body.removeChild(s),e})));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},66309:function(e,r,n){n.d(r,{Z:function(){return N}});var t=n(67294),o=n(93967),a=n.n(o),c=n(98423),i=n(98787),l=n(69760),s=n(96159),u=n(45353),p=n(53124),d=n(11568),f=n(15063),g=n(14747),h=n(83262),b=n(83559);const m=e=>{const{lineWidth:r,fontSizeIcon:n,calc:t}=e,o=e.fontSizeSM;return(0,h.IX)(e,{tagFontSize:o,tagLineHeight:(0,d.bf)(t(e.lineHeightSM).mul(o).equal()),tagIconSize:t(n).sub(t(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},y=e=>({defaultBg:new f.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var C=(0,b.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:n,tagPaddingHorizontal:t,componentCls:o,calc:a}=e,c=a(t).sub(n).equal(),i=a(r).sub(n).equal();return{[o]:Object.assign(Object.assign({},(0,g.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:c,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,d.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:i,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:c}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(m(e))),y),v=function(e,r){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(n[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(t=Object.getOwnPropertySymbols(e);o<t.length;o++)r.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(n[t[o]]=e[t[o]])}return n};const k=t.forwardRef(((e,r)=>{const{prefixCls:n,style:o,className:c,checked:i,onChange:l,onClick:s}=e,u=v(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:d,tag:f}=t.useContext(p.E_),g=d("tag",n),[h,b,m]=C(g),y=a()(g,`${g}-checkable`,{[`${g}-checkable-checked`]:i},null==f?void 0:f.className,c,b,m);return h(t.createElement("span",Object.assign({},u,{ref:r,style:Object.assign(Object.assign({},o),null==f?void 0:f.style),className:y,onClick:e=>{null==l||l(!i),null==s||s(e)}})))}));var w=k,$=n(98719);var x=(0,b.bk)(["Tag","preset"],(e=>(e=>(0,$.Z)(e,((r,n)=>{let{textColor:t,lightBorderColor:o,lightColor:a,darkColor:c}=n;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:t,background:a,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:c,borderColor:c},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(m(e))),y);const O=(e,r,n)=>{const t="string"!=typeof(o=n)?o:o.charAt(0).toUpperCase()+o.slice(1);var o;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${n}`],background:e[`color${t}Bg`],borderColor:e[`color${t}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var S=(0,b.bk)(["Tag","status"],(e=>{const r=m(e);return[O(r,"success","Success"),O(r,"processing","Info"),O(r,"error","Error"),O(r,"warning","Warning")]}),y),E=function(e,r){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(n[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(t=Object.getOwnPropertySymbols(e);o<t.length;o++)r.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(n[t[o]]=e[t[o]])}return n};const T=t.forwardRef(((e,r)=>{const{prefixCls:n,className:o,rootClassName:d,style:f,children:g,icon:h,color:b,onClose:m,bordered:y=!0,visible:v}=e,k=E(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:w,direction:$,tag:O}=t.useContext(p.E_),[T,_]=t.useState(!0),N=(0,c.Z)(k,["closeIcon","closable"]);t.useEffect((()=>{void 0!==v&&_(v)}),[v]);const j=(0,i.o2)(b),P=(0,i.yT)(b),B=j||P,I=Object.assign(Object.assign({backgroundColor:b&&!B?b:void 0},null==O?void 0:O.style),f),L=w("tag",n),[R,z,Z]=C(L),H=a()(L,null==O?void 0:O.className,{[`${L}-${b}`]:B,[`${L}-has-color`]:b&&!B,[`${L}-hidden`]:!T,[`${L}-rtl`]:"rtl"===$,[`${L}-borderless`]:!y},o,d,z,Z),U=e=>{e.stopPropagation(),null==m||m(e),e.defaultPrevented||_(!1)},[,F]=(0,l.Z)((0,l.w)(e),(0,l.w)(O),{closable:!1,closeIconRender:e=>{const r=t.createElement("span",{className:`${L}-close-icon`,onClick:U},e);return(0,s.wm)(e,r,(e=>({onClick:r=>{var n;null===(n=null==e?void 0:e.onClick)||void 0===n||n.call(e,r),U(r)},className:a()(null==e?void 0:e.className,`${L}-close-icon`)})))}}),D="function"==typeof k.onClick||g&&"a"===g.type,M=h||null,W=M?t.createElement(t.Fragment,null,M,g&&t.createElement("span",null,g)):g,X=t.createElement("span",Object.assign({},N,{ref:r,className:H,style:I}),W,F,j&&t.createElement(x,{key:"preset",prefixCls:L}),P&&t.createElement(S,{key:"status",prefixCls:L}));return R(D?t.createElement(u.Z,{component:"Tag"},X):X)})),_=T;_.CheckableTag=w;var N=_}}]);