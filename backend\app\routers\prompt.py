from fastapi import APIRouter, HTTPException, Depends, Query, Body
from typing import List, Optional, Dict, Any
from ..models.prompt import Prompt, PromptBase, PromptCreate, PromptUpdate, PromptResponse
from ..db.mongodb import db
from ..utils.auth import verify_token
from datetime import datetime
from bson import ObjectId
import asyncio
from ..utils.llmClient import convert_messages, model_api, stream_openai_api, stream_jiutian_api
import traceback
from app.utils.logging_config import setup_logging, get_logger
from ..models.llm import LLMModel, Provider
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

setup_logging()
logger = get_logger(__name__)

router = APIRouter(
    prefix="/api",
    tags=["prompts"]
)

class GeneratePromptRequest(BaseModel):
    scene: str
    app_info: str
    stream: bool = False


# 获取个人提示词列表，支持分页和过滤
@router.get("/prompts", response_model=Dict[str, Any])
async def get_prompts(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    title: Optional[str] = None,
    category: Optional[str] = None,
    language: Optional[str] = None,
    model: Optional[str] = None,
    is_active: Optional[bool] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    # 基本查询: 只查询用户自己创建的提示词
    query = {}
    
    # 添加额外过滤条件
    if title:
        query["title"] = {"$regex": title, "$options": "i"}
    if category:
        query["category"] = {"$in": [category]}  # 在分类列表中包含指定分类
    if language:
        query["language"] = language
    if model:
        query["models"] = {"$in": [model]}  # 在模型列表中包含指定模型
    if is_active is not None:
        query["is_active"] = is_active
    
    # 查询数据库
    prompts = await db["prompts"].find(query).sort(
        "created_at", -1
    ).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 获取总数
    total = await db["prompts"].count_documents(query)
    
    # 处理ID格式
    for prompt in prompts:
        prompt["id"] = str(prompt["_id"])
        del prompt["_id"]
    
    return {
        "data": prompts,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }



# 获取个人提示词列表，支持分页和过滤
@router.get("/user-prompts", response_model=Dict[str, Any])
async def get_user_prompts(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    title: Optional[str] = None,
    category: Optional[str] = None,
    language: Optional[str] = None,
    model: Optional[str] = None,
    is_active: Optional[bool] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    # 基本查询: 只查询用户自己创建的提示词
    query = {"user_id": current_user["id"],"is_active":True,"is_system":False}
    
    # 添加额外过滤条件
    if title:
        query["title"] = {"$regex": title, "$options": "i"}
    if category:
        query["category"] = {"$in": [category]}  # 在分类列表中包含指定分类
    if language:
        query["language"] = language
    if model:
        query["models"] = {"$in": [model]}  # 在模型列表中包含指定模型
    if is_active is not None:
        query["is_active"] = is_active
    
    # 查询数据库
    prompts = await db["prompts"].find(query).sort(
        "created_at", -1
    ).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 获取总数
    total = await db["prompts"].count_documents(query)
    
    # 处理ID格式
    for prompt in prompts:
        prompt["id"] = str(prompt["_id"])
        del prompt["_id"]
    
    return {
        "data": prompts,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 获取系统提示词列表，支持分页和过滤
@router.get("/system-prompts", response_model=Dict[str, Any])
async def get_system_prompts(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    title: Optional[str] = None,
    category: Optional[str] = None,
    language: Optional[str] = None,
    model: Optional[str] = None,
    is_active: Optional[bool] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    # 基本查询: 只查询系统提示词
    query = {"is_system": True,"is_active":True}
    
    # 添加额外过滤条件
    if title:
        query["title"] = {"$regex": title, "$options": "i"}
    if category:
        query["category"] = {"$in": [category]}  # 在分类列表中包含指定分类
    if language:
        query["language"] = language
    if model:
        query["models"] = {"$in": [model]}  # 在模型列表中包含指定模型
    if is_active is not None:
        query["is_active"] = is_active
    
    # 查询数据库
    prompts = await db["prompts"].find(query).sort(
        "created_at", -1
    ).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 获取总数
    total = await db["prompts"].count_documents(query)
    
    # 处理ID格式
    for prompt in prompts:
        prompt["id"] = str(prompt["_id"])
        del prompt["_id"]
    
    return {
        "data": prompts,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 获取提示词详情
@router.get("/prompts/{prompt_id}", response_model=Dict[str, Any])
async def get_prompt(
    prompt_id: str,
    current_user: dict = Depends(verify_token)
):
    try:
        prompt = await db["prompts"].find_one({"_id": ObjectId(prompt_id)})
        if not prompt:
            raise HTTPException(status_code=404, detail="提示词不存在")
        
        # 检查访问权限: 用户只能查看自己的提示词或系统提示词
        if not prompt.get("is_system", False) and prompt.get("user_id") != current_user["id"]:
            raise HTTPException(status_code=403, detail="无权访问此提示词")
        
        # 处理ID格式
        prompt["id"] = str(prompt["_id"])
        del prompt["_id"]
        
        # 更新使用次数
        await db["prompts"].update_one(
            {"_id": ObjectId(prompt_id)},
            {"$inc": {"usage_count": 1}}
        )
        
        return {
            "data": prompt,
            "success": True
        }
    except Exception as e:
        logger.error(f"获取提示词详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取提示词详情失败: {str(e)}")


# 创建新提示词
@router.post("/system_prompts", response_model=Dict[str, Any])
async def create_prompt(
    prompt: PromptCreate,
    current_user: dict = Depends(verify_token)
):
    try:
        new_prompt = prompt.dict()
        new_prompt.update({
            "user": current_user["name"],
            "user_id": current_user["id"],
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "is_system": True,
            "is_active": True,
            "usage_count": 0
        })
        
        result = await db["prompts"].insert_one(new_prompt)
        created_prompt = await db["prompts"].find_one({"_id": result.inserted_id})
        
        # 处理ID格式
        created_prompt["id"] = str(created_prompt["_id"])
        del created_prompt["_id"]
        
        return {
            "data": created_prompt,
            "success": True
        }
    except Exception as e:
        logger.error(f"创建提示词失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建提示词失败: {str(e)}")



# 创建新提示词
@router.post("/prompts", response_model=Dict[str, Any])
async def create_prompt(
    prompt: PromptCreate,
    current_user: dict = Depends(verify_token)
):
    try:
        new_prompt = prompt.dict()
        new_prompt.update({
            "user": current_user["name"],
            "user_id": current_user["id"],
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "is_system": False,
            "is_active": True,
            "usage_count": 0
        })
        
        result = await db["prompts"].insert_one(new_prompt)
        created_prompt = await db["prompts"].find_one({"_id": result.inserted_id})
        
        # 处理ID格式
        created_prompt["id"] = str(created_prompt["_id"])
        del created_prompt["_id"]
        
        return {
            "data": created_prompt,
            "success": True
        }
    except Exception as e:
        logger.error(f"创建提示词失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建提示词失败: {str(e)}")

# 更新提示词
@router.put("/prompts/{prompt_id}", response_model=Dict[str, Any])
async def update_prompt(
    prompt_id: str,
    prompt: PromptUpdate,
    current_user: dict = Depends(verify_token)
):
    try:
        # 检查提示词是否存在
        existing_prompt = await db["prompts"].find_one({"_id": ObjectId(prompt_id)})
        if not existing_prompt:
            raise HTTPException(status_code=404, detail="提示词不存在")
        
        # 检查权限: 用户只能更新自己的提示词
        if existing_prompt.get("user_id") != current_user["id"]:
            raise HTTPException(status_code=403, detail="无权更新此提示词")
        
        # # 检查是否为系统提示词
        # if existing_prompt.get("is_system", False):
        #     raise HTTPException(status_code=403, detail="系统提示词不能被修改")
        
        # 只更新非空字段
        update_data = {k: v for k, v in prompt.dict(exclude_unset=True).items() if v is not None}
        update_data["updated_at"] = datetime.now()
        
        result = await db["prompts"].update_one(
            {"_id": ObjectId(prompt_id)},
            {"$set": update_data}
        )
        
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="提示词不存在")
        
        updated_prompt = await db["prompts"].find_one({"_id": ObjectId(prompt_id)})
        updated_prompt["id"] = str(updated_prompt["_id"])
        del updated_prompt["_id"]
        
        return {
            "data": updated_prompt,
            "success": True
        }
    except Exception as e:
        traceback.print_exc()

        logger.error(f"更新提示词失败: {str(e)}")
        return {
            "data": str(e),
            "success": False
        }
       

# 删除提示词
@router.delete("/prompts/{prompt_id}", response_model=Dict[str, Any])
async def delete_prompt(
    prompt_id: str,
    current_user: dict = Depends(verify_token)
):
    try:
        # 检查提示词是否存在
        existing_prompt = await db["prompts"].find_one({"_id": ObjectId(prompt_id)})
        if not existing_prompt:
            raise HTTPException(status_code=404, detail="提示词不存在")
        
        # 检查权限: 用户只能删除自己的提示词
        if existing_prompt.get("user_id") != current_user["id"]:
            raise HTTPException(status_code=403, detail="无权删除此提示词")
        
        # 检查是否为系统提示词
        # if existing_prompt.get("is_system", False):
        #     raise HTTPException(status_code=403, detail="系统提示词不能被删除")
        
        result = await db["prompts"].delete_one({"_id": ObjectId(prompt_id)})
        
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="提示词不存在")
        
        return {
            "id": prompt_id,
            "success": True
        }
    except Exception as e:
        logger.error(f"删除提示词失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除提示词失败: {str(e)}")

# 复制提示词（创建一个副本）
@router.post("/prompts/{prompt_id}/copy", response_model=Dict[str, Any])
async def copy_prompt(
    prompt_id: str,
    current_user: dict = Depends(verify_token)
):
    try:
        # 检查提示词是否存在
        existing_prompt = await db["prompts"].find_one({"_id": ObjectId(prompt_id)})
        if not existing_prompt:
            raise HTTPException(status_code=404, detail="提示词不存在")
        
        # 检查访问权限: 用户只能复制自己的提示词或系统提示词
        if not existing_prompt.get("is_system", False) and existing_prompt.get("user_id") != current_user["id"]:
            raise HTTPException(status_code=403, detail="无权复制此提示词")
        
        # 创建新的提示词副本
        new_prompt = dict(existing_prompt)
        del new_prompt["_id"]  # 删除原ID
        
        # 更新副本信息
        new_prompt.update({
            "title": f"{new_prompt['title']} (副本)",
            "user": current_user["name"],
            "user_id": current_user["id"],
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "usage_count": 0,
            "is_system": False  # 复制的提示词永远不是系统提示词
        })
        
        result = await db["prompts"].insert_one(new_prompt)
        created_prompt = await db["prompts"].find_one({"_id": result.inserted_id})
        
        # 处理ID格式
        created_prompt["id"] = str(created_prompt["_id"])
        del created_prompt["_id"]
        
        return {
            "data": created_prompt,
            "success": True
        }
    except Exception as e:
        logger.error(f"复制提示词失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"复制提示词失败: {str(e)}")

# 获取提示词分类列表
@router.get("/prompt_categories", response_model=Dict[str, Any])
async def get_prompt_categories(
    current_user: dict = Depends(verify_token)
):
    try:
        # 使用聚合管道获取所有唯一的分类
        pipeline = [
            {"$match": {"is_active": True}},
            {"$unwind": "$category"},  # 将分类数组展开
            {"$group": {"_id": "$category"}},  # 按分类分组
            {"$sort": {"_id": 1}}  # 按分类名称排序
        ]
        
        categories_cursor = db["prompts"].aggregate(pipeline)
        categories = []
        async for doc in categories_cursor:
            if doc["_id"]:  # 确保不包含空分类
                categories.append(doc["_id"])
        
        return {
            "data": categories,
            "success": True
        }
    except Exception as e:
        logger.error(f"获取提示词分类失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取提示词分类失败: {str(e)}")

# 生成提示词
@router.post("/generate-prompt", response_model=Dict[str, Any])
async def generate_prompt(
    request: GeneratePromptRequest,
    current_user: dict = Depends(verify_token)
):
    """
    根据场景描述生成专业提示词，支持流式响应和非流式响应
    """
    try:
        logger.info(f"生成提示词请求: 场景={request.scene}, app_info={request.app_info}")
        
        # 验证应用信息
        model_id = None
        app_info = request.app_info
        scene = request.scene
        llm = None
        
        if not app_info or app_info == "":
            logger.info(f"应用信息为空，使用默认模型")
        else:
            app_info_obj = await db["system_app_settings"].find_one({"app_info": app_info})
            if app_info_obj: 
                app_params = app_info_obj.get('params', None)
                if  app_params:  
                    model_id = app_params.get('MODEL_ID', None)
        if model_id:
            logger.info(f"使用模型ID: {model_id}")
            llm: LLMModel = await db["llms"].find_one({"id": int(model_id)})
        else:
            logger.info(f"使用默认模型")
            llm: LLMModel = await db["llms"].find_one({"is_default": True})


        
        # 构建提示词生成的系统提示
        system_prompt = """你是一个内容生成助手，在系统中辅助用户生成各种表单的内容。"""
        
        # 构建用户消息
        user_prompt = f"请为以下场景生成内容：{scene} /nothink"
        
        # 准备LLM输入
        llm_messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        if not llm:
            raise HTTPException(status_code=404, detail="LLM模型不存在")
        
        model = llm.get('m_name', None)
        provider = llm.get('provider', Provider.OPENAI)
        
        # 是否使用流式响应由请求参数控制
        use_stream = request.stream
        
        # 非流式响应
        if not use_stream:
            refined_query = await model_api(llm_messages, model_id)  
            return {
                "data": {
                    "content": refined_query
                },
                "success": True
            }
        
        # 流式响应
        else:
            # 选择合适的流式API
            if provider == Provider.DEEPSEEK:
                stream_api = stream_openai_api
            elif provider == Provider.DOUBAO:
                stream_api = stream_openai_api
            elif provider == Provider.LOCAL:
                stream_api = stream_openai_api
            elif provider == Provider.OLLAMA:
                stream_api = stream_openai_api
            elif provider == Provider.JIUTIAN:
                stream_api = stream_jiutian_api
            else:
                stream_api = stream_openai_api

            # 创建流式响应
            async def stream_response():
                async for chunk in stream_api(
                    llm["api_key"],
                    model,
                    llm_messages,
                    llm["service_url"],
                    extra=None,
                    system_prompt=None,
                    provider=provider
                ):
                    yield f"event: answer\n{chunk}"
                    await asyncio.sleep(0)
                
                # 添加结束事件
                yield "event: end\ndata: Stream has ended\n\n"

            return StreamingResponse(stream_response(), media_type='text/event-stream')
        
    except Exception as e:
        logger.error(f"生成提示词失败: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            "data": {
                "content": f"生成提示词时发生错误: {str(e)}。请尝试使用不同的描述或稍后再试。"
            },
            "success": False
        }
