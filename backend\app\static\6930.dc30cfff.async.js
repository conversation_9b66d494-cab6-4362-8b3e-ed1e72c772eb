"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6930],{52197:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"}}]},name:"star",theme:"filled"}},66597:function(e,t,n){const r=(0,n(67294).createContext)({});t.Z=r},86125:function(e,t,n){n.d(t,{Z:function(){return M}});var r=n(67294),a=n(93967),o=n.n(a),l=n(64155),i=n(75164),u=n(98866),c=n(66597),s=n(42550),d=n(83062);var f=r.forwardRef(((e,t)=>{const{open:n,draggingDelete:a,value:o}=e,l=(0,r.useRef)(null),u=n&&!a,c=(0,r.useRef)(null);function f(){i.Z.cancel(c.current),c.current=null}return r.useEffect((()=>(u?c.current=(0,i.Z)((()=>{var e;null===(e=l.current)||void 0===e||e.forceAlign(),c.current=null})):f(),f)),[u,e.title,o]),r.createElement(d.Z,Object.assign({ref:(0,s.sQ)(l,t)},e,{open:u}))})),v=n(11568),g=n(15063),m=n(14747),h=n(83559),b=n(83262);const p=e=>{const{componentCls:t,antCls:n,controlSize:r,dotSize:a,marginFull:o,marginPart:l,colorFillContentHover:i,handleColorDisabled:u,calc:c,handleSize:s,handleSizeHover:d,handleActiveColor:f,handleActiveOutlineColor:g,handleLineWidth:h,handleLineWidthHover:b,motionDurationMid:p}=e;return{[t]:Object.assign(Object.assign({},(0,m.Wf)(e)),{position:"relative",height:r,margin:`${(0,v.bf)(l)} ${(0,v.bf)(o)}`,padding:0,cursor:"pointer",touchAction:"none","&-vertical":{margin:`${(0,v.bf)(o)} ${(0,v.bf)(l)}`},[`${t}-rail`]:{position:"absolute",backgroundColor:e.railBg,borderRadius:e.borderRadiusXS,transition:`background-color ${p}`},[`${t}-track,${t}-tracks`]:{position:"absolute",transition:`background-color ${p}`},[`${t}-track`]:{backgroundColor:e.trackBg,borderRadius:e.borderRadiusXS},[`${t}-track-draggable`]:{boxSizing:"content-box",backgroundClip:"content-box",border:"solid rgba(0,0,0,0)"},"&:hover":{[`${t}-rail`]:{backgroundColor:e.railHoverBg},[`${t}-track`]:{backgroundColor:e.trackHoverBg},[`${t}-dot`]:{borderColor:i},[`${t}-handle::after`]:{boxShadow:`0 0 0 ${(0,v.bf)(h)} ${e.colorPrimaryBorderHover}`},[`${t}-dot-active`]:{borderColor:e.dotActiveBorderColor}},[`${t}-handle`]:{position:"absolute",width:s,height:s,outline:"none",userSelect:"none","&-dragging-delete":{opacity:0},"&::before":{content:'""',position:"absolute",insetInlineStart:c(h).mul(-1).equal(),insetBlockStart:c(h).mul(-1).equal(),width:c(s).add(c(h).mul(2)).equal(),height:c(s).add(c(h).mul(2)).equal(),backgroundColor:"transparent"},"&::after":{content:'""',position:"absolute",insetBlockStart:0,insetInlineStart:0,width:s,height:s,backgroundColor:e.colorBgElevated,boxShadow:`0 0 0 ${(0,v.bf)(h)} ${e.handleColor}`,outline:"0px solid transparent",borderRadius:"50%",cursor:"pointer",transition:`\n            inset-inline-start ${p},\n            inset-block-start ${p},\n            width ${p},\n            height ${p},\n            box-shadow ${p},\n            outline ${p}\n          `},"&:hover, &:active, &:focus":{"&::before":{insetInlineStart:c(d).sub(s).div(2).add(b).mul(-1).equal(),insetBlockStart:c(d).sub(s).div(2).add(b).mul(-1).equal(),width:c(d).add(c(b).mul(2)).equal(),height:c(d).add(c(b).mul(2)).equal()},"&::after":{boxShadow:`0 0 0 ${(0,v.bf)(b)} ${f}`,outline:`6px solid ${g}`,width:d,height:d,insetInlineStart:e.calc(s).sub(d).div(2).equal(),insetBlockStart:e.calc(s).sub(d).div(2).equal()}}},[`&-lock ${t}-handle`]:{"&::before, &::after":{transition:"none"}},[`${t}-mark`]:{position:"absolute",fontSize:e.fontSize},[`${t}-mark-text`]:{position:"absolute",display:"inline-block",color:e.colorTextDescription,textAlign:"center",wordBreak:"keep-all",cursor:"pointer",userSelect:"none","&-active":{color:e.colorText}},[`${t}-step`]:{position:"absolute",background:"transparent",pointerEvents:"none"},[`${t}-dot`]:{position:"absolute",width:a,height:a,backgroundColor:e.colorBgElevated,border:`${(0,v.bf)(h)} solid ${e.dotBorderColor}`,borderRadius:"50%",cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,pointerEvents:"auto","&-active":{borderColor:e.dotActiveBorderColor}},[`&${t}-disabled`]:{cursor:"not-allowed",[`${t}-rail`]:{backgroundColor:`${e.railBg} !important`},[`${t}-track`]:{backgroundColor:`${e.trackBgDisabled} !important`},[`\n          ${t}-dot\n        `]:{backgroundColor:e.colorBgElevated,borderColor:e.trackBgDisabled,boxShadow:"none",cursor:"not-allowed"},[`${t}-handle::after`]:{backgroundColor:e.colorBgElevated,cursor:"not-allowed",width:s,height:s,boxShadow:`0 0 0 ${(0,v.bf)(h)} ${u}`,insetInlineStart:0,insetBlockStart:0},[`\n          ${t}-mark-text,\n          ${t}-dot\n        `]:{cursor:"not-allowed !important"}},[`&-tooltip ${n}-tooltip-inner`]:{minWidth:"unset"}})}},C=(e,t)=>{const{componentCls:n,railSize:r,handleSize:a,dotSize:o,marginFull:l,calc:i}=e,u=t?"paddingBlock":"paddingInline",c=t?"width":"height",s=t?"height":"width",d=t?"insetBlockStart":"insetInlineStart",f=t?"top":"insetInlineStart",g=i(r).mul(3).sub(a).div(2).equal(),m=i(a).sub(r).div(2).equal(),h=t?{borderWidth:`${(0,v.bf)(m)} 0`,transform:`translateY(${(0,v.bf)(i(m).mul(-1).equal())})`}:{borderWidth:`0 ${(0,v.bf)(m)}`,transform:`translateX(${(0,v.bf)(e.calc(m).mul(-1).equal())})`};return{[u]:r,[s]:i(r).mul(3).equal(),[`${n}-rail`]:{[c]:"100%",[s]:r},[`${n}-track,${n}-tracks`]:{[s]:r},[`${n}-track-draggable`]:Object.assign({},h),[`${n}-handle`]:{[d]:g},[`${n}-mark`]:{insetInlineStart:0,top:0,[f]:i(r).mul(3).add(t?0:l).equal(),[c]:"100%"},[`${n}-step`]:{insetInlineStart:0,top:0,[f]:r,[c]:"100%",[s]:r},[`${n}-dot`]:{position:"absolute",[d]:i(r).sub(o).div(2).equal()}}},k=e=>{const{componentCls:t,marginPartWithMark:n}=e;return{[`${t}-horizontal`]:Object.assign(Object.assign({},C(e,!0)),{[`&${t}-with-marks`]:{marginBottom:n}})}},y=e=>{const{componentCls:t}=e;return{[`${t}-vertical`]:Object.assign(Object.assign({},C(e,!1)),{height:"100%"})}};var x=(0,h.I$)("Slider",(e=>{const t=(0,b.IX)(e,{marginPart:e.calc(e.controlHeight).sub(e.controlSize).div(2).equal(),marginFull:e.calc(e.controlSize).div(2).equal(),marginPartWithMark:e.calc(e.controlHeightLG).sub(e.controlSize).equal()});return[p(t),k(t),y(t)]}),(e=>{const t=e.controlHeightLG/4,n=e.controlHeightSM/2,r=e.lineWidth+1,a=e.lineWidth+1.5,o=e.colorPrimary,l=new g.t(o).setA(.2).toRgbString();return{controlSize:t,railSize:4,handleSize:t,handleSizeHover:n,dotSize:8,handleLineWidth:r,handleLineWidthHover:a,railBg:e.colorFillTertiary,railHoverBg:e.colorFillSecondary,trackBg:e.colorPrimaryBorder,trackHoverBg:e.colorPrimaryBorderHover,handleColor:e.colorPrimaryBorder,handleActiveColor:o,handleActiveOutlineColor:l,handleColorDisabled:new g.t(e.colorTextDisabled).onBackground(e.colorBgContainer).toHexString(),dotBorderColor:e.colorBorderSecondary,dotActiveBorderColor:e.colorPrimaryBorder,trackBgDisabled:e.colorBgContainerDisabled}}));function Z(){const[e,t]=r.useState(!1),n=r.useRef(null),a=()=>{i.Z.cancel(n.current)};return r.useEffect((()=>a),[]),[e,e=>{a(),e?t(e):n.current=(0,i.Z)((()=>{t(e)}))}]}var E=n(53124),S=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n};const $=r.forwardRef(((e,t)=>{const{prefixCls:n,range:a,className:s,rootClassName:d,style:v,disabled:g,tooltipPrefixCls:m,tipFormatter:h,tooltipVisible:b,getTooltipPopupContainer:p,tooltipPlacement:C,tooltip:k={},onChangeComplete:y,classNames:$,styles:M}=e,w=S(e,["prefixCls","range","className","rootClassName","style","disabled","tooltipPrefixCls","tipFormatter","tooltipVisible","getTooltipPopupContainer","tooltipPlacement","tooltip","onChangeComplete","classNames","styles"]),{vertical:O}=e,{getPrefixCls:B,direction:D,className:P,style:j,classNames:F,styles:H,getPopupContainer:R}=(0,E.dj)("slider"),N=r.useContext(u.Z),I=null!=g?g:N,{handleRender:L,direction:A}=r.useContext(c.Z),q="rtl"===(A||D),[z,T]=Z(),[W,V]=Z(),X=Object.assign({},k),{open:G,placement:Y,getPopupContainer:_,prefixCls:U,formatter:K}=X,Q=null!=G?G:b,J=(z||W)&&!1!==Q,ee=function(e,t){return e||null===e?e:t||null===t?t:e=>"number"==typeof e?e.toString():""}(K,h),[te,ne]=Z(),re=(e,t)=>e||(t?q?"left":"right":"top"),ae=B("slider",n),[oe,le,ie]=x(ae),ue=o()(s,P,F.root,null==$?void 0:$.root,d,{[`${ae}-rtl`]:q,[`${ae}-lock`]:te},le,ie);q&&!w.vertical&&(w.reverse=!w.reverse),r.useEffect((()=>{const e=()=>{(0,i.Z)((()=>{V(!1)}),1)};return document.addEventListener("mouseup",e),()=>{document.removeEventListener("mouseup",e)}}),[]);const ce=a&&!Q,se=L||((e,t)=>{const{index:n}=t,a=e.props;function o(e,t,n){var r,o,l,i;n&&(null===(o=(r=w)[e])||void 0===o||o.call(r,t)),null===(i=(l=a)[e])||void 0===i||i.call(l,t)}const l=Object.assign(Object.assign({},a),{onMouseEnter:e=>{T(!0),o("onMouseEnter",e)},onMouseLeave:e=>{T(!1),o("onMouseLeave",e)},onMouseDown:e=>{V(!0),ne(!0),o("onMouseDown",e)},onFocus:e=>{var t;V(!0),null===(t=w.onFocus)||void 0===t||t.call(w,e),o("onFocus",e,!0)},onBlur:e=>{var t;V(!1),null===(t=w.onBlur)||void 0===t||t.call(w,e),o("onBlur",e,!0)}}),i=r.cloneElement(e,l),u=(!!Q||J)&&null!==ee;return ce?i:r.createElement(f,Object.assign({},X,{prefixCls:B("tooltip",null!=U?U:m),title:ee?ee(t.value):"",value:t.value,open:u,placement:re(null!=Y?Y:C,O),key:n,classNames:{root:`${ae}-tooltip`},getPopupContainer:_||p||R}),i)}),de=ce?(e,t)=>{const n=r.cloneElement(e,{style:Object.assign(Object.assign({},e.props.style),{visibility:"hidden"})});return r.createElement(f,Object.assign({},X,{prefixCls:B("tooltip",null!=U?U:m),title:ee?ee(t.value):"",open:null!==ee&&J,placement:re(null!=Y?Y:C,O),key:"tooltip",classNames:{root:`${ae}-tooltip`},getPopupContainer:_||p||R,draggingDelete:t.draggingDelete}),n)}:void 0,fe=Object.assign(Object.assign(Object.assign(Object.assign({},H.root),j),null==M?void 0:M.root),v),ve=Object.assign(Object.assign({},H.tracks),null==M?void 0:M.tracks),ge=o()(F.tracks,null==$?void 0:$.tracks);return oe(r.createElement(l.Z,Object.assign({},w,{classNames:Object.assign({handle:o()(F.handle,null==$?void 0:$.handle),rail:o()(F.rail,null==$?void 0:$.rail),track:o()(F.track,null==$?void 0:$.track)},ge?{tracks:ge}:{}),styles:Object.assign({handle:Object.assign(Object.assign({},H.handle),null==M?void 0:M.handle),rail:Object.assign(Object.assign({},H.rail),null==M?void 0:M.rail),track:Object.assign(Object.assign({},H.track),null==M?void 0:M.track)},Object.keys(ve).length?{tracks:ve}:{}),step:w.step,range:a,className:ue,style:fe,disabled:I,ref:t,prefixCls:ae,handleRender:se,activeHandleRender:de,onChangeComplete:e=>{null==y||y(e),ne(!1)}})))}));var M=$},64155:function(e,t,n){n.d(t,{y:function(){return Z},Z:function(){return I}});var r=n(1413),a=n(4942),o=n(74902),l=n(71002),i=n(97685),u=n(93967),c=n.n(u),s=n(66680),d=n(21770),f=n(91881),v=n(80334),g=n(67294),m=n(87462),h=n(91),b=n(73935);function p(e,t,n){return(e-t)/(n-t)}function C(e,t,n,r){var a=p(t,n,r),o={};switch(e){case"rtl":o.right="".concat(100*a,"%"),o.transform="translateX(50%)";break;case"btt":o.bottom="".concat(100*a,"%"),o.transform="translateY(50%)";break;case"ttb":o.top="".concat(100*a,"%"),o.transform="translateY(-50%)";break;default:o.left="".concat(100*a,"%"),o.transform="translateX(-50%)"}return o}function k(e,t){return Array.isArray(e)?e[t]:e}var y=n(15105),x=g.createContext({min:0,max:0,direction:"ltr",step:1,includedStart:0,includedEnd:0,tabIndex:0,keyboard:!0,styles:{},classNames:{}}),Z=g.createContext({}),E=["prefixCls","value","valueIndex","onStartMove","onDelete","style","render","dragging","draggingDelete","onOffsetChange","onChangeComplete","onFocus","onMouseEnter"];var S=g.forwardRef((function(e,t){var n,o=e.prefixCls,l=e.value,i=e.valueIndex,u=e.onStartMove,s=e.onDelete,d=e.style,f=e.render,v=e.dragging,b=e.draggingDelete,p=e.onOffsetChange,Z=e.onChangeComplete,S=e.onFocus,$=e.onMouseEnter,M=(0,h.Z)(e,E),w=g.useContext(x),O=w.min,B=w.max,D=w.direction,P=w.disabled,j=w.keyboard,F=w.range,H=w.tabIndex,R=w.ariaLabelForHandle,N=w.ariaLabelledByForHandle,I=w.ariaRequired,L=w.ariaValueTextFormatterForHandle,A=w.styles,q=w.classNames,z="".concat(o,"-handle"),T=function(e){P||u(e,i)},W=C(D,l,O,B),V={};null!==i&&(V={tabIndex:P?null:k(H,i),role:"slider","aria-valuemin":O,"aria-valuemax":B,"aria-valuenow":l,"aria-disabled":P,"aria-label":k(R,i),"aria-labelledby":k(N,i),"aria-required":k(I,i),"aria-valuetext":null===(n=k(L,i))||void 0===n?void 0:n(l),"aria-orientation":"ltr"===D||"rtl"===D?"horizontal":"vertical",onMouseDown:T,onTouchStart:T,onFocus:function(e){null==S||S(e,i)},onMouseEnter:function(e){$(e,i)},onKeyDown:function(e){if(!P&&j){var t=null;switch(e.which||e.keyCode){case y.Z.LEFT:t="ltr"===D||"btt"===D?-1:1;break;case y.Z.RIGHT:t="ltr"===D||"btt"===D?1:-1;break;case y.Z.UP:t="ttb"!==D?1:-1;break;case y.Z.DOWN:t="ttb"!==D?-1:1;break;case y.Z.HOME:t="min";break;case y.Z.END:t="max";break;case y.Z.PAGE_UP:t=2;break;case y.Z.PAGE_DOWN:t=-2;break;case y.Z.BACKSPACE:case y.Z.DELETE:s(i)}null!==t&&(e.preventDefault(),p(t,i))}},onKeyUp:function(e){switch(e.which||e.keyCode){case y.Z.LEFT:case y.Z.RIGHT:case y.Z.UP:case y.Z.DOWN:case y.Z.HOME:case y.Z.END:case y.Z.PAGE_UP:case y.Z.PAGE_DOWN:null==Z||Z()}}});var X=g.createElement("div",(0,m.Z)({ref:t,className:c()(z,(0,a.Z)((0,a.Z)((0,a.Z)({},"".concat(z,"-").concat(i+1),null!==i&&F),"".concat(z,"-dragging"),v),"".concat(z,"-dragging-delete"),b),q.handle),style:(0,r.Z)((0,r.Z)((0,r.Z)({},W),d),A.handle)},V,M));return f&&(X=f(X,{index:i,prefixCls:o,value:l,dragging:v,draggingDelete:b})),X})),$=["prefixCls","style","onStartMove","onOffsetChange","values","handleRender","activeHandleRender","draggingIndex","draggingDelete","onFocus"];var M=g.forwardRef((function(e,t){var n=e.prefixCls,a=e.style,o=e.onStartMove,l=e.onOffsetChange,u=e.values,c=e.handleRender,s=e.activeHandleRender,d=e.draggingIndex,f=e.draggingDelete,v=e.onFocus,p=(0,h.Z)(e,$),C=g.useRef({}),y=g.useState(!1),x=(0,i.Z)(y,2),Z=x[0],E=x[1],M=g.useState(-1),w=(0,i.Z)(M,2),O=w[0],B=w[1],D=function(e){B(e),E(!0)};g.useImperativeHandle(t,(function(){return{focus:function(e){var t;null===(t=C.current[e])||void 0===t||t.focus()},hideHelp:function(){(0,b.flushSync)((function(){E(!1)}))}}}));var P=(0,r.Z)({prefixCls:n,onStartMove:o,onOffsetChange:l,render:c,onFocus:function(e,t){D(t),null==v||v(e)},onMouseEnter:function(e,t){D(t)}},p);return g.createElement(g.Fragment,null,u.map((function(e,t){var n=d===t;return g.createElement(S,(0,m.Z)({ref:function(e){e?C.current[t]=e:delete C.current[t]},dragging:n,draggingDelete:n&&f,style:k(a,t),key:t,value:e,valueIndex:t},P))})),s&&Z&&g.createElement(S,(0,m.Z)({key:"a11y"},P,{value:u[O],valueIndex:null,dragging:-1!==d,draggingDelete:f,render:s,style:{pointerEvents:"none"},tabIndex:null,"aria-hidden":!0})))})),w=function(e){var t=e.prefixCls,n=e.style,o=e.children,l=e.value,i=e.onClick,u=g.useContext(x),s=u.min,d=u.max,f=u.direction,v=u.includedStart,m=u.includedEnd,h=u.included,b="".concat(t,"-text"),p=C(f,l,s,d);return g.createElement("span",{className:c()(b,(0,a.Z)({},"".concat(b,"-active"),h&&v<=l&&l<=m)),style:(0,r.Z)((0,r.Z)({},p),n),onMouseDown:function(e){e.stopPropagation()},onClick:function(){i(l)}},o)},O=function(e){var t=e.prefixCls,n=e.marks,r=e.onClick,a="".concat(t,"-mark");return n.length?g.createElement("div",{className:a},n.map((function(e){var t=e.value,n=e.style,o=e.label;return g.createElement(w,{key:t,prefixCls:a,style:n,value:t,onClick:r},o)}))):null},B=function(e){var t=e.prefixCls,n=e.value,o=e.style,l=e.activeStyle,i=g.useContext(x),u=i.min,s=i.max,d=i.direction,f=i.included,v=i.includedStart,m=i.includedEnd,h="".concat(t,"-dot"),b=f&&v<=n&&n<=m,p=(0,r.Z)((0,r.Z)({},C(d,n,u,s)),"function"==typeof o?o(n):o);return b&&(p=(0,r.Z)((0,r.Z)({},p),"function"==typeof l?l(n):l)),g.createElement("span",{className:c()(h,(0,a.Z)({},"".concat(h,"-active"),b)),style:p})},D=function(e){var t=e.prefixCls,n=e.marks,r=e.dots,a=e.style,o=e.activeStyle,l=g.useContext(x),i=l.min,u=l.max,c=l.step,s=g.useMemo((function(){var e=new Set;if(n.forEach((function(t){e.add(t.value)})),r&&null!==c)for(var t=i;t<=u;)e.add(t),t+=c;return Array.from(e)}),[i,u,c,r,n]);return g.createElement("div",{className:"".concat(t,"-step")},s.map((function(e){return g.createElement(B,{prefixCls:t,key:e,value:e,style:a,activeStyle:o})})))},P=function(e){var t=e.prefixCls,n=e.style,o=e.start,l=e.end,i=e.index,u=e.onStartMove,s=e.replaceCls,d=g.useContext(x),f=d.direction,v=d.min,m=d.max,h=d.disabled,b=d.range,C=d.classNames,k="".concat(t,"-track"),y=p(o,v,m),Z=p(l,v,m),E=function(e){!h&&u&&u(e,-1)},S={};switch(f){case"rtl":S.right="".concat(100*y,"%"),S.width="".concat(100*Z-100*y,"%");break;case"btt":S.bottom="".concat(100*y,"%"),S.height="".concat(100*Z-100*y,"%");break;case"ttb":S.top="".concat(100*y,"%"),S.height="".concat(100*Z-100*y,"%");break;default:S.left="".concat(100*y,"%"),S.width="".concat(100*Z-100*y,"%")}var $=s||c()(k,(0,a.Z)((0,a.Z)({},"".concat(k,"-").concat(i+1),null!==i&&b),"".concat(t,"-track-draggable"),u),C.track);return g.createElement("div",{className:$,style:(0,r.Z)((0,r.Z)({},S),n),onMouseDown:E,onTouchStart:E})},j=function(e){var t=e.prefixCls,n=e.style,a=e.values,o=e.startPoint,l=e.onStartMove,i=g.useContext(x),u=i.included,s=i.range,d=i.min,f=i.styles,v=i.classNames,m=g.useMemo((function(){if(!s){if(0===a.length)return[];var e=null!=o?o:d,t=a[0];return[{start:Math.min(e,t),end:Math.max(e,t)}]}for(var n=[],r=0;r<a.length-1;r+=1)n.push({start:a[r],end:a[r+1]});return n}),[a,s,o,d]);if(!u)return null;var h=null!=m&&m.length&&(v.tracks||f.tracks)?g.createElement(P,{index:null,prefixCls:t,start:m[0].start,end:m[m.length-1].end,replaceCls:c()(v.tracks,"".concat(t,"-tracks")),style:f.tracks}):null;return g.createElement(g.Fragment,null,h,m.map((function(e,a){var o=e.start,i=e.end;return g.createElement(P,{index:a,prefixCls:t,style:(0,r.Z)((0,r.Z)({},k(n,a)),f.track),start:o,end:i,key:a,onStartMove:l})})))},F=n(8410);function H(e){var t="targetTouches"in e?e.targetTouches[0]:e;return{pageX:t.pageX,pageY:t.pageY}}var R=function(e,t,n,r,a,l,u,c,d,f,v){var m=g.useState(null),h=(0,i.Z)(m,2),b=h[0],p=h[1],C=g.useState(-1),k=(0,i.Z)(C,2),y=k[0],x=k[1],E=g.useState(!1),S=(0,i.Z)(E,2),$=S[0],M=S[1],w=g.useState(n),O=(0,i.Z)(w,2),B=O[0],D=O[1],P=g.useState(n),j=(0,i.Z)(P,2),R=j[0],N=j[1],I=g.useRef(null),L=g.useRef(null),A=g.useRef(null),q=g.useContext(Z),z=q.onDragStart,T=q.onDragChange;(0,F.Z)((function(){-1===y&&D(n)}),[n,y]),g.useEffect((function(){return function(){document.removeEventListener("mousemove",I.current),document.removeEventListener("mouseup",L.current),A.current&&(A.current.removeEventListener("touchmove",I.current),A.current.removeEventListener("touchend",L.current))}}),[]);var W=function(e,t,n){void 0!==t&&p(t),D(e);var r=e;n&&(r=e.filter((function(e,t){return t!==y}))),u(r),T&&T({rawValues:e,deleteIndex:n?y:-1,draggingIndex:y,draggingValue:t})},V=(0,s.Z)((function(e,t,n){if(-1===e){var i=R[0],u=R[R.length-1],c=r-i,s=a-u,f=t*(a-r);f=Math.max(f,c),f=Math.min(f,s);var v=l(i+f);f=v-i;var g=R.map((function(e){return e+f}));W(g)}else{var m=(a-r)*t,h=(0,o.Z)(B);h[e]=R[e];var b=d(h,m,e,"dist");W(b.values,b.value,n)}})),X=g.useMemo((function(){var e=(0,o.Z)(n).sort((function(e,t){return e-t})),t=(0,o.Z)(B).sort((function(e,t){return e-t})),r={};t.forEach((function(e){r[e]=(r[e]||0)+1})),e.forEach((function(e){r[e]=(r[e]||0)-1}));var a=f?1:0;return Object.values(r).reduce((function(e,t){return e+Math.abs(t)}),0)<=a?B:n}),[n,B,f]);return[y,b,$,X,function(r,a,o){r.stopPropagation();var l=o||n,i=l[a];x(a),p(i),N(l),D(l),M(!1);var u=H(r),s=u.pageX,d=u.pageY,g=!1;z&&z({rawValues:l,draggingIndex:a,draggingValue:i});var m=function(n){n.preventDefault();var r,o,l=H(n),i=l.pageX,u=l.pageY,c=i-s,m=u-d,h=e.current.getBoundingClientRect(),b=h.width,p=h.height;switch(t){case"btt":r=-m/p,o=c;break;case"ttb":r=m/p,o=c;break;case"rtl":r=-c/b,o=m;break;default:r=c/b,o=m}g=!!f&&(Math.abs(o)>130&&v<B.length),M(g),V(a,r,g)},h=function e(t){t.preventDefault(),document.removeEventListener("mouseup",e),document.removeEventListener("mousemove",m),A.current&&(A.current.removeEventListener("touchmove",I.current),A.current.removeEventListener("touchend",L.current)),I.current=null,L.current=null,A.current=null,c(g),x(-1),M(!1)};document.addEventListener("mouseup",h),document.addEventListener("mousemove",m),r.currentTarget.addEventListener("touchend",h),r.currentTarget.addEventListener("touchmove",m),I.current=m,L.current=h,A.current=r.currentTarget}]};var N=g.forwardRef((function(e,t){var n=e.prefixCls,u=void 0===n?"rc-slider":n,m=e.className,h=e.style,b=e.classNames,p=e.styles,C=e.id,k=e.disabled,y=void 0!==k&&k,Z=e.keyboard,E=void 0===Z||Z,S=e.autoFocus,$=e.onFocus,w=e.onBlur,B=e.min,P=void 0===B?0:B,F=e.max,H=void 0===F?100:F,N=e.step,I=void 0===N?1:N,L=e.value,A=e.defaultValue,q=e.range,z=e.count,T=e.onChange,W=e.onBeforeChange,V=e.onAfterChange,X=e.onChangeComplete,G=e.allowCross,Y=void 0===G||G,_=e.pushable,U=void 0!==_&&_,K=e.reverse,Q=e.vertical,J=e.included,ee=void 0===J||J,te=e.startPoint,ne=e.trackStyle,re=e.handleStyle,ae=e.railStyle,oe=e.dotStyle,le=e.activeDotStyle,ie=e.marks,ue=e.dots,ce=e.handleRender,se=e.activeHandleRender,de=e.track,fe=e.tabIndex,ve=void 0===fe?0:fe,ge=e.ariaLabelForHandle,me=e.ariaLabelledByForHandle,he=e.ariaRequired,be=e.ariaValueTextFormatterForHandle,pe=g.useRef(null),Ce=g.useRef(null),ke=g.useMemo((function(){return Q?K?"ttb":"btt":K?"rtl":"ltr"}),[K,Q]),ye=function(e){return(0,g.useMemo)((function(){if(!0===e||!e)return[!!e,!1,!1,0];var t=e.editable,n=e.draggableTrack;return[!0,t,!t&&n,e.minCount||0,e.maxCount]}),[e])}(q),xe=(0,i.Z)(ye,5),Ze=xe[0],Ee=xe[1],Se=xe[2],$e=xe[3],Me=xe[4],we=g.useMemo((function(){return isFinite(P)?P:0}),[P]),Oe=g.useMemo((function(){return isFinite(H)?H:100}),[H]),Be=g.useMemo((function(){return null!==I&&I<=0?1:I}),[I]),De=g.useMemo((function(){return"boolean"==typeof U?!!U&&Be:U>=0&&U}),[U,Be]),Pe=g.useMemo((function(){return Object.keys(ie||{}).map((function(e){var t=ie[e],n={value:Number(e)};return t&&"object"===(0,l.Z)(t)&&!g.isValidElement(t)&&("label"in t||"style"in t)?(n.style=t.style,n.label=t.label):n.label=t,n})).filter((function(e){var t=e.label;return t||"number"==typeof t})).sort((function(e,t){return e.value-t.value}))}),[ie]),je=function(e,t,n,r,a,l){var i=g.useCallback((function(n){return Math.max(e,Math.min(t,n))}),[e,t]),u=g.useCallback((function(r){if(null!==n){var a=e+Math.round((i(r)-e)/n)*n,o=function(e){return(String(e).split(".")[1]||"").length},l=Math.max(o(n),o(t),o(e)),u=Number(a.toFixed(l));return e<=u&&u<=t?u:null}return null}),[n,e,t,i]),c=g.useCallback((function(a){var o=i(a),l=r.map((function(e){return e.value}));null!==n&&l.push(u(a)),l.push(e,t);var c=l[0],s=t-e;return l.forEach((function(e){var t=Math.abs(o-e);t<=s&&(c=e,s=t)})),c}),[e,t,r,n,i,u]),s=function a(l,i,c){var s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit";if("number"==typeof i){var d,f=l[c],v=f+i,g=[];r.forEach((function(e){g.push(e.value)})),g.push(e,t),g.push(u(f));var m=i>0?1:-1;"unit"===s?g.push(u(f+m*n)):g.push(u(v)),g=g.filter((function(e){return null!==e})).filter((function(e){return i<0?e<=f:e>=f})),"unit"===s&&(g=g.filter((function(e){return e!==f})));var h="unit"===s?f:v;d=g[0];var b=Math.abs(d-h);if(g.forEach((function(e){var t=Math.abs(e-h);t<b&&(d=e,b=t)})),void 0===d)return i<0?e:t;if("dist"===s)return d;if(Math.abs(i)>1){var p=(0,o.Z)(l);return p[c]=d,a(p,i-m,c,s)}return d}return"min"===i?e:"max"===i?t:void 0},d=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit",a=e[n],o=s(e,t,n,r);return{value:o,changed:o!==a}},f=function(e){return null===l&&0===e||"number"==typeof l&&e<l};return[c,function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit",o=e.map(c),i=o[n],u=s(o,t,n,r);if(o[n]=u,!1===a){var v=l||0;n>0&&o[n-1]!==i&&(o[n]=Math.max(o[n],o[n-1]+v)),n<o.length-1&&o[n+1]!==i&&(o[n]=Math.min(o[n],o[n+1]-v))}else if("number"==typeof l||null===l){for(var g=n+1;g<o.length;g+=1)for(var m=!0;f(o[g]-o[g-1])&&m;){var h=d(o,1,g);o[g]=h.value,m=h.changed}for(var b=n;b>0;b-=1)for(var p=!0;f(o[b]-o[b-1])&&p;){var C=d(o,-1,b-1);o[b-1]=C.value,p=C.changed}for(var k=o.length-1;k>0;k-=1)for(var y=!0;f(o[k]-o[k-1])&&y;){var x=d(o,-1,k-1);o[k-1]=x.value,y=x.changed}for(var Z=0;Z<o.length-1;Z+=1)for(var E=!0;f(o[Z+1]-o[Z])&&E;){var S=d(o,1,Z+1);o[Z+1]=S.value,E=S.changed}}return{value:o[n],values:o}}]}(we,Oe,Be,Pe,Y,De),Fe=(0,i.Z)(je,2),He=Fe[0],Re=Fe[1],Ne=(0,d.Z)(A,{value:L}),Ie=(0,i.Z)(Ne,2),Le=Ie[0],Ae=Ie[1],qe=g.useMemo((function(){var e=null==Le?[]:Array.isArray(Le)?Le:[Le],t=(0,i.Z)(e,1)[0],n=null===Le?[]:[void 0===t?we:t];if(Ze){if(n=(0,o.Z)(e),z||void 0===Le){var r=z>=0?z+1:2;for(n=n.slice(0,r);n.length<r;){var a;n.push(null!==(a=n[n.length-1])&&void 0!==a?a:we)}}n.sort((function(e,t){return e-t}))}return n.forEach((function(e,t){n[t]=He(e)})),n}),[Le,Ze,we,z,He]),ze=function(e){return Ze?e:e[0]},Te=(0,s.Z)((function(e){var t=(0,o.Z)(e).sort((function(e,t){return e-t}));T&&!(0,f.Z)(t,qe,!0)&&T(ze(t)),Ae(t)})),We=(0,s.Z)((function(e){e&&pe.current.hideHelp();var t=ze(qe);null==V||V(t),(0,v.ZP)(!V,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),null==X||X(t)})),Ve=R(Ce,ke,qe,we,Oe,He,Te,We,Re,Ee,$e),Xe=(0,i.Z)(Ve,5),Ge=Xe[0],Ye=Xe[1],_e=Xe[2],Ue=Xe[3],Ke=Xe[4],Qe=function(e,t){if(!y){var n=(0,o.Z)(qe),r=0,a=0,l=Oe-we;qe.forEach((function(t,n){var o=Math.abs(e-t);o<=l&&(l=o,r=n),t<e&&(a=n)}));var i=r;Ee&&0!==l&&(!Me||qe.length<Me)?(n.splice(a+1,0,e),i=a+1):n[r]=e,Ze&&!qe.length&&void 0===z&&n.push(e);var u,c,s=ze(n);if(null==W||W(s),Te(n),t)null===(u=document.activeElement)||void 0===u||null===(c=u.blur)||void 0===c||c.call(u),pe.current.focus(i),Ke(t,i,n);else null==V||V(s),(0,v.ZP)(!V,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),null==X||X(s)}},Je=g.useState(null),et=(0,i.Z)(Je,2),tt=et[0],nt=et[1];g.useEffect((function(){if(null!==tt){var e=qe.indexOf(tt);e>=0&&pe.current.focus(e)}nt(null)}),[tt]);var rt=g.useMemo((function(){return(!Se||null!==Be)&&Se}),[Se,Be]),at=(0,s.Z)((function(e,t){Ke(e,t),null==W||W(ze(qe))})),ot=-1!==Ge;g.useEffect((function(){if(!ot){var e=qe.lastIndexOf(Ye);pe.current.focus(e)}}),[ot]);var lt=g.useMemo((function(){return(0,o.Z)(Ue).sort((function(e,t){return e-t}))}),[Ue]),it=g.useMemo((function(){return Ze?[lt[0],lt[lt.length-1]]:[we,lt[0]]}),[lt,Ze,we]),ut=(0,i.Z)(it,2),ct=ut[0],st=ut[1];g.useImperativeHandle(t,(function(){return{focus:function(){pe.current.focus(0)},blur:function(){var e,t=document.activeElement;null!==(e=Ce.current)&&void 0!==e&&e.contains(t)&&(null==t||t.blur())}}})),g.useEffect((function(){S&&pe.current.focus(0)}),[]);var dt=g.useMemo((function(){return{min:we,max:Oe,direction:ke,disabled:y,keyboard:E,step:Be,included:ee,includedStart:ct,includedEnd:st,range:Ze,tabIndex:ve,ariaLabelForHandle:ge,ariaLabelledByForHandle:me,ariaRequired:he,ariaValueTextFormatterForHandle:be,styles:p||{},classNames:b||{}}}),[we,Oe,ke,y,E,Be,ee,ct,st,Ze,ve,ge,me,he,be,p,b]);return g.createElement(x.Provider,{value:dt},g.createElement("div",{ref:Ce,className:c()(u,m,(0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)({},"".concat(u,"-disabled"),y),"".concat(u,"-vertical"),Q),"".concat(u,"-horizontal"),!Q),"".concat(u,"-with-marks"),Pe.length)),style:h,onMouseDown:function(e){e.preventDefault();var t,n=Ce.current.getBoundingClientRect(),r=n.width,a=n.height,o=n.left,l=n.top,i=n.bottom,u=n.right,c=e.clientX,s=e.clientY;switch(ke){case"btt":t=(i-s)/a;break;case"ttb":t=(s-l)/a;break;case"rtl":t=(u-c)/r;break;default:t=(c-o)/r}Qe(He(we+t*(Oe-we)),e)},id:C},g.createElement("div",{className:c()("".concat(u,"-rail"),null==b?void 0:b.rail),style:(0,r.Z)((0,r.Z)({},ae),null==p?void 0:p.rail)}),!1!==de&&g.createElement(j,{prefixCls:u,style:ne,values:qe,startPoint:te,onStartMove:rt?at:void 0}),g.createElement(D,{prefixCls:u,marks:Pe,dots:ue,style:oe,activeStyle:le}),g.createElement(M,{ref:pe,prefixCls:u,style:re,values:Ue,draggingIndex:Ge,draggingDelete:_e,onStartMove:at,onOffsetChange:function(e,t){if(!y){var n=Re(qe,e,t);null==W||W(ze(qe)),Te(n.values),nt(n.value)}},onFocus:$,onBlur:w,handleRender:ce,activeHandleRender:se,onChangeComplete:We,onDelete:Ee?function(e){if(!(y||!Ee||qe.length<=$e)){var t=(0,o.Z)(qe);t.splice(e,1),null==W||W(ze(t)),Te(t);var n=Math.max(0,e-1);pe.current.hideHelp(),pe.current.focus(n)}}:void 0}),g.createElement(O,{prefixCls:u,marks:Pe,onClick:Qe})))}));var I=N}}]);