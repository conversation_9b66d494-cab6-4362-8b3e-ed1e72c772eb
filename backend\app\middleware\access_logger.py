import time
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from ..db.mongodb import db
from app.utils.logging_config import get_logger
import json
from datetime import datetime
import traceback
import sys

logger = get_logger(__name__)

class AccessLogMiddleware(BaseHTTPMiddleware):
    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next):
        # 获取请求路径
        path = request.url.path
        
        # 过滤静态资源请求
        if path.startswith('/static/'):
            return await call_next(request)
        if path.startswith('/openapi/'):
            return await call_next(request)
        
        logger.info(f"AccessLogMiddleware======>: {request.method} {path}")
        # 记录请求开始时间
        start_time = time.time()
        
        # 提取请求信息
        method = request.method
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        referer = request.headers.get("referer")
        
        # 尝试获取用户信息
        user_id = None
        username = None
        try:
            if "authorization" in request.headers:
                # 从authorization header获取token
                auth_header = request.headers.get("authorization")
                token = auth_header.split(" ")[-1] if auth_header else None
                
                if token:
                    # 从数据库中查找用户，但不验证token有效性
                    user = await db["users"].find_one({"auth_token": token})
                    if user:
                        user_id = str(user.get("_id"))
                        username = user.get("username") or user.get("name") or user.get("phone")
        except Exception as e:
            traceback.print_exc()
            logger.error(f"获取用户信息时出错: {str(e)}")
            # 不影响请求处理，继续执行
            pass
            
        # 处理请求
        try:
            # 获取查询参数 (仅GET请求)
            query_params = dict(request.query_params) if method == "GET" else {}
            
            # 执行请求处理
            response = await call_next(request)
            
            # 计算响应时间
            end_time = time.time()
            execution_time = (end_time - start_time) * 1000  # 毫秒
            
            # 整合字段，确保与system_log模型兼容
            log_data = {
                "user_id": user_id,
                "user_name": username,
                "ip_address": client_ip,
                "method": method,
                "route": path,
                "params": query_params,  # 仅包含GET请求的参数
                "status_code": response.status_code,
                "response_time": execution_time,
                "user_agent": user_agent,
                "referer": referer,
                "created_at": datetime.utcnow()
            }
            
            # 异步写入数据库
            try:
                logger.info(f"log_data======>: {log_data}")
                await db["system_logs"].insert_one(log_data)
            except Exception as e:
                logger.error(f"写入访问日志失败: {str(e)}")
                logger.error(traceback.format_exc())
            
            # 记录慢请求
            if execution_time > 1000:  # 超过1秒的请求
                logger.warning(f"慢请求: {method} {path} - {execution_time:.2f}ms")
                
                # 将慢请求记录到专门的性能统计表
                slow_request_data = {
                    "user_id": user_id,
                    "username": username,
                    "path": path,
                    "method": method,
                    "execution_time": execution_time,
                    "status_code": response.status_code,
                    "timestamp": datetime.utcnow(),
                    "query_params": query_params if method == "GET" else {},
                    "client_ip": client_ip,
                    "user_agent": user_agent
                }
                
                try:
                    # 记录到专门的慢请求表
                    await db["slow_requests"].insert_one(slow_request_data)
                    
                    # 更新路径性能统计
                    await db["performance_stats"].update_one(
                        {"path": path, "method": method},
                        {
                            "$inc": {"total_count": 1, "slow_count": 1, "total_time": execution_time},
                            "$max": {"max_time": execution_time},
                            "$min": {"min_time": execution_time} if execution_time > 0 else {},
                            "$set": {"last_slow_request": datetime.utcnow()}
                        },
                        upsert=True
                    )
                except Exception as e:
                    logger.error(f"记录慢请求统计失败: {str(e)}")
            else:
                # 即使不是慢请求，也更新性能统计
                try:
                    await db["performance_stats"].update_one(
                        {"path": path, "method": method},
                        {
                            "$inc": {"total_count": 1, "total_time": execution_time},
                            "$max": {"max_time": execution_time},
                            "$min": {"min_time": execution_time} if execution_time > 0 else {}
                        },
                        upsert=True
                    )
                except Exception as e:
                    logger.error(f"更新性能统计失败: {str(e)}")
            
            return response
            
        except Exception as e:
            # 处理请求过程中的错误
            end_time = time.time()
            execution_time = (end_time - start_time) * 1000
            
            # 获取详细的异常信息
            exc_type, exc_value, exc_traceback = sys.exc_info()
            stack_trace = traceback.format_exception(exc_type, exc_value, exc_traceback)
            error_details = "".join(stack_trace)
            
            logger.error(f"请求处理错误: {str(e)}")
            logger.error(error_details)
            
            # 记录错误日志
            error_log = {
                "user_id": user_id,
                "user_name": username,
                "ip_address": client_ip,
                "method": method,
                "route": path,
                "params": dict(request.query_params) if method == "GET" else {},
                "status_code": 500,
                "response_time": execution_time,
                "user_agent": user_agent,
                "referer": referer,
                "error": {
                    "message": str(e),
                    "type": exc_type.__name__ if exc_type else "Unknown",
                    "stack_trace": error_details,
                    "module": getattr(e, "__module__", "unknown"),
                    "line": traceback.extract_tb(exc_traceback)[-1].lineno if exc_traceback else None
                },
                "created_at": datetime.utcnow()
            }
            
            try:
                await db["system_logs"].insert_one(error_log)
                
                # 更新错误统计
                await db["performance_stats"].update_one(
                    {"path": path, "method": method},
                    {
                        "$inc": {"total_count": 1, "error_count": 1},
                        "$set": {"last_error": datetime.utcnow()}
                    },
                    upsert=True
                )
            except Exception as db_err:
                logger.error(f"写入错误日志失败: {str(db_err)}")
            
            # 重新抛出原始异常，让 FastAPI 处理错误
            raise
