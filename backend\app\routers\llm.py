from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from ..models.llm import LLMModel, LLMCreate, LLMUpdate, LLMResponse
from ..db.mongodb import db
from datetime import datetime
from ..utils.auth import verify_token

from app.utils.logging_config import setup_logging, get_logger
setup_logging()
logger = get_logger(__name__)

router = APIRouter()

# 获取所有 LLM 模型，支持分页
@router.get("/api/llms", response_model=Dict[str, Any])
async def get_llms(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    m_name: Optional[str] = None,  # 支持按模型名称检索
    provider: Optional[str] = None,  # 支持按提供商检索
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {}
    # query = {"is_active":True}
    if m_name:
        query["m_name"] = {"$regex": m_name, "$options": "i"}
    if provider:
        query["provider"] = {"$regex": provider, "$options": "i"}
    logger.info(query)

    llms = await db["llms"].find(query,{"_id":0, 
                                        "id":1,
                                        "api_key":1, 
                                        "is_default":1,
                                        "service_url":1, 
                                        "description":1, 
                                        "name":1,
                                        "m_name":1,
                                        "provider":1,
                                        "is_active":1,
                                        "max_tokens":1,
                                        "temperature":1,
                                        "top_p":1,
                                        "price":1,
                                        "created_at":1
                                        }).sort("created_at", -1).skip(skip).limit(pageSize).to_list(pageSize)
    
    logger.info(f"llms===>{llms}")
    total = await db["llms"].count_documents(query)
    return {
        "data": llms,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

@router.get("/api/llmMarket", response_model=Dict[str, Any])
async def get_llms(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    m_name: Optional[str] = None,  # 支持按模型名称检索
    provider: Optional[str] = None,  # 支持按提供商检索
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {"is_active":True}
    if m_name:
        query["m_name"] = {"$regex": m_name, "$options": "i"}
    if provider:
        query["provider"] = {"$regex": provider, "$options": "i"}

    llms = await db["llms"].find(query,{"_id":0, 
                                        "id":1,
                                        "api_key":1, 
                                        "service_url":1, 
                                        "description":1, 
                                        "name":1,
                                        "m_name":1,
                                        "provider":1,
                                        "max_tokens":1,
                                        "temperature":1,
                                        "top_p":1,
                                        "created_at":1
                                        }).skip(skip).limit(pageSize).to_list(pageSize)
    total = await db["llms"].count_documents(query)
    return {
        "data": llms,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 添加新 LLM 模型
@router.post("/api/llms", response_model=LLMResponse)
async def add_llm(llm: LLMCreate, current_user: dict = Depends(verify_token)):
    last_llm = await db["llms"].find_one(sort=[("id", -1)])
    new_id = (last_llm["id"] + 1) if last_llm else 1
    # print(str(llm.description))

    new_llm = llm.dict()
    new_llm.update({
        "id": new_id,
        "created_at": datetime.now(),
        "created_by": current_user["id"],
        "is_active": True,
        "api_key": llm.api_key,
        "description": llm.description,
        "provider": llm.provider,
        "service_url": llm.service_url,
        "frequency_penalty": llm.frequency_penalty if llm.frequency_penalty else 0.0,
        "presence_penalty": llm.presence_penalty if llm.presence_penalty else 0.0,
        "is_active": True
    })

    # 调试输出
    # print("Inserting new LLM:", new_llm)

    await db["llms"].insert_one(new_llm)
    return LLMResponse(**new_llm)

# 更新 LLM 模型
@router.put("/api/llms/{llm_id}", response_model=LLMResponse)
async def update_llm(llm_id: int, llm: LLMUpdate, current_user: dict = Depends(verify_token)):
    # 只获取非空的字段进行更新
    update_data = {k: v for k, v in llm.dict(exclude_unset=True).items() if v is not None}
    if not update_data:
        raise HTTPException(status_code=400, detail="No valid fields to update")
        
    result = await db["llms"].update_one({"id": llm_id}, {"$set": update_data})
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="LLM not found")
    updated_llm = await db["llms"].find_one({"id": llm_id})
    return LLMResponse(**updated_llm)

# 删除 LLM 模型
@router.delete("/api/llms/{llm_id}", response_model=Dict[str, int])
async def delete_llm(llm_id: int, current_user: dict = Depends(verify_token)):
    llm = await db["llms"].find_one({"id": llm_id})
    if not llm:
        raise HTTPException(status_code=404, detail="LLM not found")
    result = await db["llms"].delete_one({"id": llm_id})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="LLM not found")
    return {"id": llm_id}

@router.post("/api/llms/{llm_id}/set_default", response_model=Dict[str, Any])
async def set_default_llm(llm_id: int, current_user: dict = Depends(verify_token)):
    # 先将所有模型的 is_default 设为 0
    await db["llms"].update_many({}, {"$set": {"is_default": False}})
    # 再将目标模型的 is_default 设为 1
    result = await db["llms"].update_one({"id": llm_id}, {"$set": {"is_default": True}})
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="LLM not found")
    return {"success": True, "id": llm_id}




@router.get("/api/llmList", response_model=Dict[str, Any])
async def get_llmList(
    current_user: dict = Depends(verify_token)
):
    llms = await db["llms"].find({"is_active": True}, {
        "_id": 0,
        "id": 1,
        "name": 1,
        "m_name": 1,
    }).to_list()
    return {
        "data": llms,
        "success": True,
    }