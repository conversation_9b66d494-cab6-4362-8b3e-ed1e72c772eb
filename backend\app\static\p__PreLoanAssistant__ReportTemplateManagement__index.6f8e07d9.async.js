"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9877],{44188:function(e,t,i){i.r(t);var n=i(5574),r=i.n(n),a=i(15009),s=i.n(a),l=i(99289),o=i.n(l),c=i(67294),d=i(2453),p=i(17788),x=i(4393),m=i(85418),u=i(66309),h=i(71471),f=i(86738),g=i(96074),v=i(83622),y=i(74330),j=i(32983),C=i(78818),Z=i(55287),b=i(47389),k=i(69753),S=i(82061),T=i(37446),N=i(15360),w=i(43008),P=i(28846),I=i(76772),L=i(85893),R=function(){var e=o()(s()().mark((function e(){var t,i,n,r=arguments;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.length>0&&void 0!==r[0]?r[0]:"all",i=[{id:"1",title:"贷前调查报告模板",fileName:"template1.docx",type:"贷前调查",status:"active",createdAt:"2023-05-15T10:00:00Z",updatedAt:"2023-06-20T14:30:00Z",isSystem:!0},{id:"2",title:"授信审批报告模板",fileName:"template2.docx",type:"授信审批",status:"active",createdAt:"2023-04-10T09:15:00Z",updatedAt:"2023-06-18T11:20:00Z",isSystem:!0},{id:"3",title:"贷款申请调查模板",fileName:"template3.docx",type:"贷款申请",status:"active",createdAt:"2023-03-05T08:15:00Z",updatedAt:"2023-05-18T10:20:00Z",isSystem:!0},{id:"4",title:"风险评估报告模板",fileName:"template4.docx",type:"风险评估",status:"draft",createdAt:"2023-02-12T14:30:00Z",updatedAt:"2023-04-25T16:45:00Z",isSystem:!1},{id:"5",title:"客户背景调查模板",fileName:"template5.docx",type:"背景调查",status:"active",createdAt:"2023-01-18T11:20:00Z",updatedAt:"2023-03-30T09:10:00Z",isSystem:!1},{id:"6",title:"企业财务分析模板",fileName:"template6.docx",type:"财务分析",status:"inactive",createdAt:"2022-12-05T09:45:00Z",updatedAt:"2023-02-15T14:20:00Z",isSystem:!1}],n="system"===t?i.filter((function(e){return e.isSystem})):"custom"===t?i.filter((function(e){return!e.isSystem})):i,e.abrupt("return",{data:n,total:n.length});case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),E=function(){var e=o()(s()().mark((function e(){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{success:!0});case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),A=[{id:"1",title:"1. 申请人基本情况",children:[{id:"1-1",title:"1.1 企业概况"},{id:"1-2",title:"1.2 企业历史沿革"},{id:"1-3",title:"1.3 组织架构及管理团队"}]},{id:"2",title:"2. 经营情况分析",children:[{id:"2-1",title:"2.1 业务模式"},{id:"2-2",title:"2.2 主要客户及供应商"},{id:"2-3",title:"2.3 市场竞争分析"}]},{id:"3",title:"3. 财务状况分析",children:[{id:"3-1",title:"3.1 资产负债情况"},{id:"3-2",title:"3.2 利润分析"},{id:"3-3",title:"3.3 现金流分析"}]},{id:"4",title:"4. 风险评估",children:[{id:"4-1",title:"4.1 主要风险点"},{id:"4-2",title:"4.2 风险缓释措施"}]},{id:"5",title:"5. 结论和建议"}],B=(0,P.kc)((function(e){var t=e.token;return{reportTemplateManager:{padding:"24px",backgroundColor:"#f5f7fa",minHeight:"100%"},header:{marginBottom:"24px",display:"flex",justifyContent:"space-between",alignItems:"center"},mainCard:{borderRadius:"8px",overflow:"hidden"},contentWrapper:{display:"flex",gap:"16px"},templateListContainer:{display:"flex",flexDirection:"column",gap:"24px",padding:"16px",flex:"6",borderRight:"1px solid ".concat(t.colorBorderSecondary)},rightPanel:{flex:"4",padding:"0 12px",maxHeight:"calc(100vh - 48px)",overflowY:"auto"},emptyRightPanel:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"400px",color:t.colorTextSecondary},sectionTitle:{fontSize:"16px",fontWeight:500,marginBottom:"16px",display:"flex",alignItems:"center",justifyContent:"space-between"},templateGrid:{display:"grid",gridTemplateColumns:"repeat(3, 1fr)",gap:"16px",marginBottom:"16px"},templateCard:{cursor:"pointer",transition:"all 0.3s",boxShadow:"0 1px 2px rgba(0, 0, 0, 0.05)",borderRadius:"6px",overflow:"hidden",height:"100%",background:"#fff",border:"1px solid #f0f0f0","&:hover":{transform:"translateY(-3px)",boxShadow:"0 3px 8px rgba(0, 0, 0, 0.09)"},position:"relative"},systemTemplateCard:{backgroundColor:"rgba(82, 196, 26, 0.06)"},customTemplateCard:{backgroundColor:"rgba(24, 144, 255, 0.06)"},selectedCard:{borderColor:t.colorPrimary,borderWidth:"1px",borderStyle:"solid"},templateCardContent:{padding:"16px",display:"flex",flexDirection:"column",height:"100%"},templateTitle:{fontSize:"16px",fontWeight:600,marginBottom:"12px",color:"rgba(0, 0, 0, 0.85)",lineHeight:"1.2"},templateDescription:{fontSize:"14px",color:"rgba(0, 0, 0, 0.65)",marginBottom:"16px",lineHeight:"1.5",overflow:"hidden",textOverflow:"ellipsis",display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical"},tagContainer:{marginTop:"auto"},moreButton:{position:"absolute",top:"12px",right:"12px",color:t.colorTextSecondary,cursor:"pointer",width:"32px",height:"32px",display:"flex",justifyContent:"center",alignItems:"center",borderRadius:"4px",zIndex:10,"&:hover":{backgroundColor:t.colorBgTextHover}},paginationWrapper:{marginTop:"16px",display:"flex",justifyContent:"flex-end"},templateDetail:{padding:"0"},templateHeader:{marginBottom:"16px",display:"flex",flexDirection:"column",gap:"8px"},titleRow:{display:"flex",alignItems:"center",justifyContent:"space-between"},titleLeft:{display:"flex",alignItems:"center",gap:"8px"},actionButtons:{display:"flex",gap:"12px"},actionLink:{fontSize:"13px",cursor:"pointer",color:t.colorPrimary,"&:hover":{color:t.colorPrimaryHover}},dangerLink:{color:t.colorError,"&:hover":{color:t.colorErrorHover}},compactDivider:{margin:"12px 0"},structureList:{marginTop:"12px",marginBottom:"16px"},structureItem:{paddingLeft:"16px",marginBottom:"6px",fontSize:"13px"},structureItemChild:{paddingLeft:"24px",color:t.colorTextSecondary},formActions:{display:"flex",justifyContent:"flex-end",gap:"12px",marginTop:"24px"},sectionDivider:{margin:"24px 0 16px"}}})),z=(0,P.kc)((function(){return{".template-dropdown":{".ant-dropdown-menu":{padding:"8px 0",borderRadius:"8px"},".ant-dropdown-menu-item":{padding:"10px 16px",margin:"0","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.04)"}}}}}));t.default=function(){var e=B().styles;z();var t=(0,c.useState)([]),i=r()(t,2),n=i[0],a=i[1],l=(0,c.useState)([]),P=r()(l,2),D=P[0],W=P[1],H=(0,c.useState)(!1),_=r()(H,2),M=_[0],G=_[1],O=(0,c.useState)(0),Y=r()(O,2),q=Y[0],F=Y[1],J=(0,c.useState)(0),K=r()(J,2),Q=K[0],U=K[1],V=(0,c.useState)(1),X=r()(V,2),$=X[0],ee=X[1],te=(0,c.useState)(1),ie=r()(te,2),ne=ie[0],re=ie[1],ae=(0,c.useState)(12),se=r()(ae,1)[0],le=(0,c.useState)(null),oe=r()(le,2),ce=oe[0],de=oe[1],pe=(0,c.useCallback)(o()(s()().mark((function e(){var t,i,n=arguments;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=n.length>0&&void 0!==n[0]?n[0]:"all",G(!0),e.prev=2,e.next=5,R(t);case 5:i=e.sent,"system"===t?(a(i.data),F(i.total)):"custom"===t&&(W(i.data),U(i.total)),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(2),d.ZP.error("获取模板列表失败");case 12:return e.prev=12,G(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[2,9,12,15]])}))),[]);(0,c.useEffect)((function(){pe("system"),pe("custom")}),[pe]);var xe=function(){var e=o()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,E();case 3:d.ZP.success("模板删除成功"),(null==ce?void 0:ce.id)===t&&de(null),pe("system"),pe("custom"),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(0),d.ZP.error("模板删除失败");case 12:case"end":return e.stop()}}),e,null,[[0,9]])})));return function(t){return e.apply(this,arguments)}}(),me=function(t){var i=[{key:"view",label:(0,L.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,L.jsx)(Z.Z,{style:{marginRight:8}}),(0,L.jsx)("span",{children:"查看"})]}),onClick:function(e){e.domEvent.stopPropagation(),de(t)}},{key:"edit",label:(0,L.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,L.jsx)(b.Z,{style:{marginRight:8}}),(0,L.jsx)("span",{children:"编辑"})]}),onClick:function(e){e.domEvent.stopPropagation(),d.ZP.success("编辑模板")}},{key:"download",label:(0,L.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,L.jsx)(k.Z,{style:{marginRight:8}}),(0,L.jsx)("span",{children:"下载"})]}),onClick:function(e){e.domEvent.stopPropagation(),d.ZP.success("下载模板")}},{key:"delete",label:(0,L.jsxs)("div",{style:{display:"flex",alignItems:"center",color:"#ff4d4f"},children:[(0,L.jsx)(S.Z,{style:{marginRight:8,color:"#ff4d4f"}}),(0,L.jsx)("span",{children:"删除"})]}),onClick:function(e){e.domEvent.stopPropagation(),p.Z.confirm({title:"确定要删除该模板吗？",onOk:function(){return xe(t.id)},okText:"确定",cancelText:"取消"})}}],n=t.isSystem?i.filter((function(e){return"delete"!==e.key})):i,r="".concat(e.templateCard," ").concat(t.isSystem?e.systemTemplateCard:e.customTemplateCard," ").concat((null==ce?void 0:ce.id)===t.id?e.selectedCard:"");return(0,L.jsxs)(x.Z,{className:r,bodyStyle:{padding:0,height:"100%"},hoverable:!0,onClick:function(){return de(t)},children:[(0,L.jsx)("div",{className:e.moreButton,onClick:function(e){e.stopPropagation()},children:(0,L.jsx)(m.Z,{menu:{items:n},trigger:["click"],placement:"bottomRight",getPopupContainer:function(e){return e.parentNode},overlayStyle:{minWidth:"120px",boxShadow:"0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)",borderRadius:"8px"},overlayClassName:"template-dropdown",children:(0,L.jsx)(T.Z,{})})}),(0,L.jsxs)("div",{className:e.templateCardContent,children:[(0,L.jsx)("div",{className:e.templateTitle,children:t.title}),(0,L.jsx)("div",{className:e.templateDescription,children:{"贷前调查":"贷前调查报告模板，用于收集和分析客户信息，评估贷款风险和可行性","授信审批":"授信审批报告模板，用于规范授信审批流程，记录审批意见和决策依据","贷款申请":"贷款申请调查模板，用于收集申请人资料，分析经营状况及还款能力","风险评估":"风险评估报告模板，用于全面评估贷款风险点，制定风险缓释措施","背景调查":"客户背景调查模板，用于全面了解客户基本情况、资信状况及声誉","财务分析":"企业财务分析模板，用于分析企业财务状况、盈利能力和还款能力"}[t.type||""]||"标准报告模板，适用于多种业务场景，内容丰富，格式规范"}),(0,L.jsx)("div",{className:e.tagContainer,children:(0,L.jsx)(u.Z,{color:t.isSystem?"green":"blue",children:t.type||"未分类"})})]})]},t.id)};return(0,L.jsxs)("div",{className:e.reportTemplateManager,children:[(0,L.jsxs)("div",{className:e.header,children:[(0,L.jsx)(h.Z.Title,{level:4,style:{margin:0},children:"报告模板管理"}),(0,L.jsx)(v.ZP,{type:"primary",icon:(0,L.jsx)(w.Z,{}),onClick:function(){I.history.push("/preLoanAssistant/createReportTemplate")},children:"新增模板"})]}),(0,L.jsx)(x.Z,{className:e.mainCard,children:(0,L.jsxs)("div",{className:e.contentWrapper,children:[(0,L.jsxs)("div",{className:e.templateListContainer,children:[(0,L.jsxs)("div",{children:[(0,L.jsxs)("div",{className:e.sectionTitle,children:[(0,L.jsx)(h.Z.Text,{strong:!0,children:"自建模板"}),(0,L.jsxs)("div",{children:[Q," 个模板"]})]}),(0,L.jsxs)(y.Z,{spinning:M,children:[0===D.length?(0,L.jsx)(j.Z,{description:"暂无自建模板",image:j.Z.PRESENTED_IMAGE_SIMPLE}):(0,L.jsx)("div",{className:e.templateGrid,children:D.map(me)}),D.length>0&&Q>se&&(0,L.jsx)("div",{className:e.paginationWrapper,children:(0,L.jsx)(C.Z,{current:ne,pageSize:se,total:Q,onChange:function(e){re(e)},showSizeChanger:!1,showTotal:function(e){return"共 ".concat(e," 条")},size:"small"})})]})]}),(0,L.jsx)(g.Z,{className:e.sectionDivider}),(0,L.jsxs)("div",{children:[(0,L.jsxs)("div",{className:e.sectionTitle,children:[(0,L.jsx)(h.Z.Text,{strong:!0,children:"系统模板"}),(0,L.jsxs)("div",{children:[q," 个模板"]})]}),(0,L.jsxs)(y.Z,{spinning:M,children:[0===n.length?(0,L.jsx)(j.Z,{description:"暂无系统模板",image:j.Z.PRESENTED_IMAGE_SIMPLE}):(0,L.jsx)("div",{className:e.templateGrid,children:n.map(me)}),n.length>0&&q>se&&(0,L.jsx)("div",{className:e.paginationWrapper,children:(0,L.jsx)(C.Z,{current:$,pageSize:se,total:q,onChange:function(e){ee(e)},showSizeChanger:!1,showTotal:function(e){return"共 ".concat(e," 条")},size:"small"})})]})]})]}),(0,L.jsx)("div",{className:e.rightPanel,children:ce?(0,L.jsxs)("div",{className:e.templateDetail,children:[(0,L.jsxs)("div",{className:e.templateHeader,children:[(0,L.jsx)("div",{className:e.titleRow,children:(0,L.jsxs)("div",{className:e.titleLeft,children:[(0,L.jsx)(h.Z.Title,{level:4,style:{margin:0},children:ce.title}),(0,L.jsx)(u.Z,{color:"blue",children:ce.type||"未分类"})]})}),(0,L.jsxs)("div",{className:e.actionButtons,children:[(0,L.jsxs)("span",{className:e.actionLink,onClick:function(){return d.ZP.success("使用模板")},children:[(0,L.jsx)(Z.Z,{})," 使用"]}),(0,L.jsxs)("span",{className:e.actionLink,onClick:function(){return d.ZP.success("编辑模板")},children:[(0,L.jsx)(b.Z,{})," 编辑"]}),(0,L.jsxs)("span",{className:e.actionLink,onClick:function(){return d.ZP.success("下载模板")},children:[(0,L.jsx)(k.Z,{})," 下载"]}),!ce.isSystem&&(0,L.jsx)(f.Z,{title:"确定要删除该模板吗？",onConfirm:function(){return xe(ce.id)},okText:"确定",cancelText:"取消",children:(0,L.jsxs)("span",{className:"".concat(e.actionLink," ").concat(e.dangerLink),children:[(0,L.jsx)(S.Z,{})," 删除"]})})]})]}),(0,L.jsx)(g.Z,{className:e.compactDivider,orientation:"left",children:(0,L.jsx)("span",{style:{fontSize:"14px"},children:"模板目录结构"})}),(0,L.jsx)("div",{className:e.structureList,children:A.map((function(t){var i;return(0,L.jsxs)("div",{children:[(0,L.jsx)("div",{className:e.structureItem,children:t.title}),null===(i=t.children)||void 0===i?void 0:i.map((function(t){return(0,L.jsx)("div",{className:"".concat(e.structureItem," ").concat(e.structureItemChild),children:t.title},t.id)}))]},t.id)}))})]}):(0,L.jsxs)("div",{className:e.emptyRightPanel,children:[(0,L.jsx)(N.Z,{style:{fontSize:"48px",marginBottom:"16px",opacity:.3}}),(0,L.jsx)(h.Z.Text,{type:"secondary",children:"请从左侧选择一个模板查看详情"})]})})]})})]})}}}]);