# 金融舆情分析智能体与数据工具

## 1. 项目介绍

本项目包含一个核心脚本，和一个测试数据，旨在实现完整金融舆情分析报告生成的分析流程。


*   `financialPublicOpinionReport_agent.py`: 这是一个基于大语言模型（LLM）的智能分析代理。它读取 `data.json` JSON文件(es提取)，利用LLM对每一篇文章进行深度分析和总结，然后进行跨文章的综合分析，最终自动生成一份结构化的金融舆情洞察报告（支持Markdown和PDF格式）。

## 2. 实现功能


### 报告生成 (`financialPublicOpinionReport_agent.py`)

- **本地文件输入**: 完全脱离数据库，从本地JSON文件读取文章数据进行分析。
- **默认文件机制**: 支持无参数运行，会自动寻找（或创建）一个默认的`data.json`文件作为输入。
- **LLM深度分析**: 基于STORM（结构化推理）思想，调用大模型进行多层次分析：
    1.  对单篇文章进行深度解读，提取核心观点、论据等。
    2.  对所有分析过的文章进行综合对比，总结市场共识、分歧、风险与机会。
- **多格式报告输出**: 自动生成三种文件：
    1.  `.md`：Markdown格式的报告，结构清晰，易于阅读和编辑。
    2.  `.pdf`：PDF格式的报告，方便归档和分享。
    3.  `report_result_....json`：包含所有分析过程和结果的JSON文件，便于进行二次开发。
- **智能字体支持**: 自动检测macOS和Linux系统下的中文字体（如苹方、文泉驿等），以解决PDF报告中的中文乱码问题。
- **健壮的命令行接口**: 使用`argparse`库，提供清晰的命令行参数，如使用`--days`指定分析天数。


## 3. 运行方式

### 生成分析报告

获取到数据文件后，运行智能分析代理来生成报告。
```python
#  运行分析代理
python financialPublicOpinionReport_agent_v2.py
```
- **说明**: 脚本会自动寻找`data.json`文件，并分析其中最近3天的数据。
- **输出**: 无论使用哪种方式，运行成功后都会在当前目录下生成`.md`报告文件、`.pdf`报告文件以及一个`report_result_....json`结果文件。

## 4. 智能体介绍

本项目的核心是一个基于大语言模型（LLM）和`LangGraph`框架构建的金融舆情分析智能体。它通过一系列精心设计的步骤，将原始的文章数据转化为一份结构清晰、洞察深刻的分析报告。

### 工作流详解

智能体的工作流程被定义为一个由两个主要节点构成的状态机（State Machine），确保了数据处理的有序和可靠。

#### 阶段一：数据提取与深度分析 (`data_fetch_and_analyze_node`)

这是智能体的分析核心，负责处理和理解输入的文章数据。

1.  **数据读取**: 从用户指定的JSON文件（默认为`data.json`）中加载文章列表。
2.  **并行单篇分析**: 为了提升效率，智能体采用`ThreadPoolExecutor`并发处理每一篇文章。对于每篇文章，会执行一个受**STORM**（一种结构化推理和知识生成模型）启发的两步分析法：
    *   **初步分析**: 调用LLM（扮演“金融专家”）对文章内容进行开放式的归纳总结。
    *   **结构化提取**: 再次调用LLM，要求它将上一步的分析结果整理成一个严格的**JSON对象**，包含`摘要`、`核心观点`、`观点依据`等关键字段。这种设计确保了后续处理的便捷性和准确性。
3.  **跨文章综合分析**: 在所有文章都完成单篇分析后，智能体会收集所有文章的“核心观点”，然后执行第二次**STORM**式分析：
    *   调用LLM（扮演“资深市场分析师”），对所有观点进行全面的、多维度的综合归纳。
    *   同样地，再次调用LLM将综合分析结果整理成结构化的**JSON对象**，包含`市场共识`、`观点分歧`、`主要风险与机会`、`宏观经济预期`等深度洞察。
4.  **数据汇总**: 最后，将单篇分析结果和综合分析结果汇总成一份完整的报告数据，并传递给下一个节点。

#### 阶段二：报告生成与输出 (`report_formatting_node`)

该节点负责将分析数据以用户友好的格式呈现出来。

1.  **Markdown报告生成**: 根据上一节点输出的结构化数据，自动生成一份格式规范的Markdown（`.md`）文件。
2.  **文件保存**: 将生成的`.md`文件保存在当前运行目录下。
