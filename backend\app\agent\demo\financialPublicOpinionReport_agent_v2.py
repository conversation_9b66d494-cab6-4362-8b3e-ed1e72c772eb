#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2025/07/14 10:00:00
# <AUTHOR> <PERSON><PERSON> Jia
# @File    : financialPublicOpinionReport_agent_v2.py

"""
金融舆情分析智能体
定期从本地JSON文件中获取微信公众号数据，进行分析并生成报告
基于STORM结构化推理方式改进报告生成过程
"""

import concurrent.futures
import functools
import json
import logging
import os
import signal
import traceback
import uuid
import argparse
from concurrent.futures import ThreadPoolExecutor
from contextlib import contextmanager
from datetime import datetime, timedelta, date
from typing import Any, Dict, List, Optional, TypedDict

import pandas as pd
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph
import re

# ==============================================================================
# 1. 类型定义与全局配置
# ==============================================================================

class AgentState(TypedDict):
    """定义工作流状态"""
    fetch_start_time: str
    fetch_end_time: str
    json_file_path: str
    report_data: Dict[str, Any]
    report_text: Optional[str]
    storage_path: Optional[str]
    error: Optional[str]

# 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 大模型参数
LLM_API_BASE = "http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen3_32b_2/v1"
OPENAI_API_KEY = "ZmYzMmQ1N2E2YWZjMzcwN2Y3NTBjNmQzYjk3Njk3ZWY4Njk5MWQ0ZQ=="
LLM_MODEL = "Qwen3-32B"

# 初始化大模型
llm = ChatOpenAI(
    temperature=0,
    model=LLM_MODEL,
    openai_api_base=LLM_API_BASE,
    api_key=OPENAI_API_KEY,
    request_timeout=120
)

# ==============================================================================
# 2. 核心分析逻辑 (STORM)
# ==============================================================================

def analyze_report_with_reasoning(title, content, account_name):
    """使用STORM结构化推理方式分析单个报告内容"""
    logger.info(f"开始分析来自 '{account_name}' 的报告: {title[:50]}...")
    system_msg = SystemMessage(content="""你是一位拥有丰富经验的金融研究报告分析专家，精通宏观经济、货币政策、债券市场和股票投资策略分析。请对以下金融公众号研究报告进行专业内容归纳。""")

    max_content_length = 10000
    content_to_analyze = content[:max_content_length] if content and len(content) > max_content_length else content
    
    human_msg = HumanMessage(content=f'请对以下来自"{account_name}"公众号的研究报告内容进行归纳总结，归纳总结的内容包括但不限于核心观点、观点依据等。\n\n标题: {title}\n内容: {content_to_analyze}')
    
    initial_analysis = llm.invoke([system_msg, human_msg])

    system_msg2 = SystemMessage(content="现在请基于你的专业金融分析，以严格的JSON格式提供最终的结构化结果。")
    human_msg2 = HumanMessage(content="""请以严格的JSON格式返回专业金融分析结果，必须包含以下字段:

1. summary: 整体摘要（200-300字）
2. core_viewpoints: 核心观点列表（数组格式）
3. supporting_evidence: 观点依据列表（数组格式）
4. key_insights: 关键洞见（150-200字）
5. market_impact: 对市场的潜在影响""")

    try:
        messages2 = [system_msg2, initial_analysis, human_msg2]
        result = llm.invoke(messages2)
        content = result.content
        start_pos = content.find('{')
        end_pos = content.rfind('}') + 1
        if start_pos >= 0 and end_pos > start_pos:
            json_str = content[start_pos:end_pos]
            analysis = json.loads(json_str)
            analysis["reasoning_process"] = initial_analysis.content
            return analysis
        else:
            logger.error("无法找到JSON内容")
            return {"summary": "分析失败，无法找到JSON内容"}
    except Exception as e:
        logger.error(f"分析报告内容时出错: {e}")
        return {"summary": f"分析失败: {e}"}

def analyze_all_reports_with_storm(reports_data):
    """使用STORM方法综合分析所有报告的观点"""
    logger.info(f"开始综合分析 {len(reports_data)} 份报告的观点...")
    all_viewpoints = []
    for biz, report in reports_data.items():
        if 'analysis' in report and report['analysis'].get('core_viewpoints'):
            for viewpoint in report['analysis']['core_viewpoints']:
                all_viewpoints.append({'site_name': report.get('biz_nickname', '未知'), 'viewpoint': viewpoint})
    
    viewpoints_text = "\n\n".join([f"{item['site_name']}: {item['viewpoint']}" for item in all_viewpoints])

    system_msg = SystemMessage(content="你是一位资深金融市场分析师，请对以下多家金融机构的研究报告进行专业、全面的多维度综合归纳。")
    human_msg = HumanMessage(content=f"""请对以下多家金融机构的研究报告观点进行归纳总结，维度包括：
1. 整体判断
2. 市场观点共识与分歧
3. 主要风险与机会
4. 宏观经济与政策预期
5. 利率走势预测
6. 投资策略建议

以下是各家机构的观点:
{viewpoints_text}""")
    
    initial_analysis = llm.invoke([system_msg, human_msg])

    system_msg2 = SystemMessage(content="现在请以严格的JSON格式提供最终的结构化结果。")
    human_msg2 = HumanMessage(content="""请以严格的JSON格式返回专业金融分析结果，必须包含以下字段:
- overall_summary
- consensus_points
- divergent_points
- market_risks
- market_opportunities
- macro_economic_outlook
- policy_expectations
- interest_rate_forecast
- investment_recommendations
""")

    try:
        messages2 = [system_msg2, initial_analysis, human_msg2]
        result = llm.invoke(messages2)
        content = result.content
        start_pos = content.find('{')
        end_pos = content.rfind('}') + 1
        if start_pos >= 0 and end_pos > start_pos:
            json_str = content[start_pos:end_pos]
            return json.loads(json_str)
        else:
            return {"overall_summary": "综合分析失败, 未找到JSON内容"}
    except Exception as e:
        logger.error(f"综合分析报告内容时出错: {e}")
        return {"overall_summary": f"综合分析失败: {e}"}

# ==============================================================================
# 3. 报告格式化函数
# ==============================================================================

def format_structured_list(items, content_key='content', source_key='sources'):
    """格式化包含来源的结构化列表"""
    lines = []
    for item in items:
        content = ""
        sources_str = ""
        if isinstance(item, dict):
            content = item.get(content_key, '')
            sources = item.get(source_key, [])
            if sources:
                sources_str = f"（来源：{', '.join(sources)}）"
        elif isinstance(item, str):
            content = item
        
        if content:
             lines.append(f"- {content}{sources_str}")
    return '\n'.join(lines) if lines else "暂无数据。"

def format_storm_report(report):
    """将STORM报告格式化为易于展示的Markdown格式"""
    logger.info("开始格式化STORM报告...")
    
    output = f"# {report.get('report_title', '金融分析报告')}\n\n"
    output += f"**生成时间**: {report.get('generation_time')}\n"
    output += f"**分析报告数**: {report.get('metadata', {}).get('reports_count', 0)}\n"
    output += f"**时间范围**: {report.get('metadata', {}).get('time_range', '未知')}\n\n---\n\n"

    overall = report.get("overall_analysis", {})
    output += "## 核心观点总结\n\n"
    output += f"### 整体判断\n{overall.get('overall_summary', '无')}\n\n"
    output += "### 市场观点共识\n"
    output += format_structured_list(overall.get('consensus_points', [])) + "\n\n"
    output += "### 市场观点分歧\n"
    output += format_structured_list(overall.get('divergent_points', [])) + "\n\n"
    output += "### 主要风险\n"
    output += format_structured_list(overall.get('market_risks', [])) + "\n\n"
    output += "### 主要机会\n"
    output += format_structured_list(overall.get('market_opportunities', [])) + "\n\n"

    output += "## 各机构观点明细\n\n"
    for name, report_item in report.get("individual_reports", {}).items():
        output += f"### {name}\n\n"
        output += f"**标题**: {report_item.get('title', 'N/A')}\n"
        analysis = report_item.get('analysis', {})
        output += f"**核心观点**: {analysis.get('summary', 'N/A')}\n"
        output += "**具体观点**:\n" + format_structured_list(analysis.get('core_viewpoints', [])) + "\n"
        output += "**观点依据**:\n" + format_structured_list(analysis.get('supporting_evidence', [])) + "\n\n"

    return output


# ==============================================================================
# 4. 工作流节点定义
# ==============================================================================

def data_fetch_and_analyze_node(state: AgentState) -> Dict[str, Any]:
    """
    从JSON文件加载数据，进行分析，并准备报告数据
    """
    logger.info("====== 节点: 开始数据获取与分析 ======")
    try:
        json_file_path = state["json_file_path"]

        with open(json_file_path, 'r', encoding='utf-8') as f:
            all_articles = json.load(f)
        
        logger.info(f"从 {json_file_path} 加载了 {len(all_articles)} 篇文章进行分析。")

        if not all_articles:
            return {"error": "JSON文件中没有找到任何文章。"}
        
        analyzed_reports = {}
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_article = {
                executor.submit(
                    analyze_report_with_reasoning, 
                    a.get('title', '无标题'), 
                    a.get('content', ''), 
                    a.get('source', '未知来源')
                ): a for a in all_articles
            }
            for future in concurrent.futures.as_completed(future_to_article):
                article = future_to_article[future]
                try:
                    analysis_result = future.result()
                    article['analysis'] = analysis_result
                    report_key = article.get('biz_nickname', article.get('title', str(uuid.uuid4())))
                    analyzed_reports[report_key] = article
                except Exception as exc:
                    traceback.print_exc()
                    logger.error(f"文章 '{article.get('title')}' 分析失败: {exc}")

        if not analyzed_reports:
             return {"error": "所有文章分析均失败。"}

        logger.info("开始进行综合分析...")
        overall_analysis = analyze_all_reports_with_storm(analyzed_reports)
        
        end_time_dt = datetime.now()
        time_range_str = f"全部数据"
        final_report_data = {
            "generation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "report_title": f"金融媒体洞察分析报告 - {end_time_dt.strftime('%Y-%m-%d')}",
            "individual_reports": analyzed_reports,
            "overall_analysis": overall_analysis,
            "metadata": {
                "reports_count": len(analyzed_reports),
                "generation_method": "STORM结构化推理",
                "time_range": time_range_str
            },
            "errors": []
        }
        
        return {"report_data": final_report_data}

    except Exception as e:
        logger.error(f"数据获取与分析节点出错: {e}")
        traceback.print_exc()
        return {"error": f"数据获取与分析节点出错: {e}"}


def report_formatting_node(state: AgentState) -> Dict[str, Any]:
    """格式化报告为Markdown，并保存到本地"""
    logger.info("====== 节点: 开始格式化与保存报告 ======")
    report_data = state.get("report_data")
    if not report_data:
        return {"error": "没有报告数据可供格式化。"}

    md_content = format_storm_report(report_data)

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    md_filename = f"financial_report_{timestamp}.md"
    try:
        with open(md_filename, 'w', encoding='utf-8') as f:
            f.write(md_content)
        logger.info(f"Markdown报告已保存: {md_filename}")
        return {"report_text": md_content, "storage_path": md_filename}
    except Exception as e:
        logger.error(f"保存Markdown文件失败: {e}")
        traceback.print_exc()
        return {"error": f"保存Markdown文件失败: {e}"}

# ==============================================================================
# 5. 工作流构建与执行
# ==============================================================================

workflow = StateGraph(AgentState)
workflow.add_node("data_fetch_and_analyze", data_fetch_and_analyze_node)
workflow.add_node("report_formatting", report_formatting_node)

workflow.set_entry_point("data_fetch_and_analyze")
workflow.add_edge("data_fetch_and_analyze", "report_formatting")
workflow.add_edge("report_formatting", END)

app = workflow.compile()

def run_agent(json_file_path: str):
    """运行金融媒体洞察分析智能体"""
    logger.info("====================== 开始运行金融舆情分析智能体 ======================")
    
    initial_state = {
        "json_file_path": json_file_path,
        "fetch_start_time": "", # 不再需要
        "fetch_end_time": "", # 不再需要
    }

    try:
        start_time = datetime.now()
        result = app.invoke(initial_state)
        
        if result.get("error"):
            logger.error(f"智能体运行出错: {result['error']}")
            return result

        end_time = datetime.now()
        logger.info(f"智能体运行成功，总耗时: {(end_time - start_time).total_seconds():.2f} 秒")
        return result
        
    except Exception as e:
        logger.error(f"运行智能体时发生严重错误: {e}")
        traceback.print_exc()
        return {"error": str(e)}

# ==============================================================================
# 6. 主程序入口
# ==============================================================================

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="金融舆情分析智能体",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument(
        "json_file", 
        nargs='?', 
        default="data.json", 
        help="包含文章数据的JSON文件路径。\n如果未提供，将使用默认的 'data.json'。\n如果 'data.json' 不存在，将会自动创建。"
    )
    
    args = parser.parse_args()

    json_file_to_use = args.json_file

    if json_file_to_use == "data.json" and not os.path.exists(json_file_to_use):
        logger.info(f"未找到默认数据文件 '{json_file_to_use}'，将为您创建一个。")
        default_data = [
            {
                "createTime": int(datetime.now().timestamp()) - 86400, # 昨天
                "title": "央行数字货币（CBDC）对商业银行的潜在影响分析",
                "content": "近期，多国央行加速推进央行数字货币（CBDC）的研发与试点工作...",
                "biz_nickname": "宏观深度观察"
            },
            {
                "createTime": int(datetime.now().timestamp()) - 172800, # 前天
                "title": "全球供应链重构下的半导体行业投资机遇",
                "content": "自2020年以来，全球供应链的脆弱性日益凸显...",
                "biz_nickname": "华尔街前沿"
            }
        ]
        with open(json_file_to_use, 'w', encoding='utf-8') as f:
            json.dump(default_data, f, ensure_ascii=False, indent=4)
        logger.info(f"已成功创建默认数据文件: '{json_file_to_use}'")
    
    logger.info(f"使用数据文件: {json_file_to_use}, 分析所有数据。")
    
    final_result = run_agent(json_file_path=json_file_to_use)
    
    if final_result and not final_result.get("error"):
        logger.info(f"报告分析完成，报告文件保存在: {final_result.get('storage_path')}")
    else:
        logger.error(f"智能体运行失败: {final_result.get('error') if final_result else '未知错误'}")

