"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5668],{16596:function(e,t,n){n.d(t,{Z:function(){return c}});var r=n(1413),a=n(67294),l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M518.3 459a8 8 0 00-12.6 0l-112 141.7a7.98 7.98 0 006.3 12.9h73.9V856c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V613.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 459z"}},{tag:"path",attrs:{d:"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4a245.6 245.6 0 0152.4-49.9c41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10C846.1 454.5 884 503.8 884 560c0 33.1-12.9 64.3-36.3 87.7a123.07 123.07 0 01-87.6 36.3H720c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C870.5 760 960 670.5 960 560c0-92.7-63.1-170.7-148.6-193.3z"}}]},name:"cloud-upload",theme:"outlined"},o=n(91146),i=function(e,t){return a.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:l}))};var c=a.forwardRef(i)},24495:function(e,t,n){n.d(t,{Z:function(){return ke}});var r=n(93967),a=n.n(r),l=n(67294),o=n(21450),i=n(36158),c=n(56790),s=n(73935);const d=l.createContext(null);function m(e){const{getDropContainer:t,className:n,prefixCls:r,children:o}=e,{disabled:i}=l.useContext(d),[c,m]=l.useState(),[f,p]=l.useState(null);l.useEffect((()=>{const e=t?.();c!==e&&m(e)}),[t]),l.useEffect((()=>{if(c){const e=()=>{p(!0)},t=e=>{e.preventDefault()},n=e=>{e.relatedTarget||p(!1)},r=e=>{p(!1),e.preventDefault()};return document.addEventListener("dragenter",e),document.addEventListener("dragover",t),document.addEventListener("dragleave",n),document.addEventListener("drop",r),()=>{document.removeEventListener("dragenter",e),document.removeEventListener("dragover",t),document.removeEventListener("dragleave",n),document.removeEventListener("drop",r)}}}),[!!c]);if(!(t&&c&&!i))return null;const u=`${r}-drop-area`;return(0,s.createPortal)(l.createElement("div",{className:a()(u,n,{[`${u}-on-body`]:"BODY"===c.tagName}),style:{display:f?"block":"none"}},o),c)}var f=n(87462),p=n(42110),u=n(51398),g=function(e,t){return l.createElement(u.Z,(0,f.Z)({},e,{ref:t,icon:p.Z}))};var h=l.forwardRef(g),v=n(26554),b=function(e,t){return l.createElement(u.Z,(0,f.Z)({},e,{ref:t,icon:v.Z}))};var w=l.forwardRef(b),E=n(50756),x=function(e,t){return l.createElement(u.Z,(0,f.Z)({},e,{ref:t,icon:E.Z}))};var y=l.forwardRef(x),C=n(83622),$=n(29372),z=n(11550);function L(e,t){const{children:n,upload:r,rootClassName:a}=e,o=l.useRef(null);return l.useImperativeHandle(t,(()=>o.current)),l.createElement(z.Z,(0,f.Z)({},r,{showUploadList:!1,rootClassName:a,ref:o}),n)}var Z=l.forwardRef(L),S={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM575.34 477.84l-61.22 102.3L452.3 477.8a12 12 0 00-10.27-5.79h-38.44a12 12 0 00-6.4 1.85 12 12 0 00-3.75 16.56l82.34 130.42-83.45 132.78a12 12 0 00-1.84 6.39 12 12 0 0012 12h34.46a12 12 0 0010.21-5.7l62.7-101.47 62.3 101.45a12 12 0 0010.23 5.72h37.48a12 12 0 006.48-1.9 12 12 0 003.62-16.58l-83.83-130.55 85.3-132.47a12 12 0 001.9-6.5 12 12 0 00-12-12h-35.7a12 12 0 00-10.29 5.84z"}}]},name:"file-excel",theme:"filled"},k=function(e,t){return l.createElement(u.Z,(0,f.Z)({},e,{ref:t,icon:S}))};var M=l.forwardRef(k),R={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7L639.4 73.4c-6-6-14.2-9.4-22.7-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.6-9.4-22.6zM400 402c22.1 0 40 17.9 40 40s-17.9 40-40 40-40-17.9-40-40 17.9-40 40-40zm296 294H328c-6.7 0-10.4-7.7-6.3-12.9l99.8-127.2a8 8 0 0112.6 0l41.1 52.4 77.8-99.2a8 8 0 0112.6 0l136.5 174c4.3 5.2.5 12.9-6.1 12.9zm-94-370V137.8L790.2 326H602z"}}]},name:"file-image",theme:"filled"},N=function(e,t){return l.createElement(u.Z,(0,f.Z)({},e,{ref:t,icon:R}))};var H=l.forwardRef(N),V={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM426.13 600.93l59.11 132.97a16 16 0 0014.62 9.5h24.06a16 16 0 0014.63-9.51l59.1-133.35V758a16 16 0 0016.01 16H641a16 16 0 0016-16V486a16 16 0 00-16-16h-34.75a16 16 0 00-14.67 9.62L512.1 662.2l-79.48-182.59a16 16 0 00-14.67-9.61H383a16 16 0 00-16 16v272a16 16 0 0016 16h27.13a16 16 0 0016-16V600.93z"}}]},name:"file-markdown",theme:"filled"},B=function(e,t){return l.createElement(u.Z,(0,f.Z)({},e,{ref:t,icon:V}))};var I=l.forwardRef(B),W={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM633.22 637.26c-15.18-.5-31.32.67-49.65 2.96-24.3-14.99-40.66-35.58-52.28-65.83l1.07-4.38 1.24-5.18c4.3-18.13 6.61-31.36 7.3-44.7.52-10.07-.04-19.36-1.83-27.97-3.3-18.59-16.45-29.46-33.02-30.13-15.45-.63-29.65 8-33.28 21.37-5.91 21.62-2.45 50.07 10.08 98.59-15.96 38.05-37.05 82.66-51.2 107.54-18.89 9.74-33.6 18.6-45.96 28.42-16.3 12.97-26.48 26.3-29.28 40.3-1.36 6.49.69 14.97 5.36 21.92 5.3 7.88 13.28 13 22.85 13.74 24.15 1.87 53.83-23.03 86.6-79.26 3.29-1.1 6.77-2.26 11.02-3.7l11.9-4.02c7.53-2.54 12.99-4.36 18.39-6.11 23.4-7.62 41.1-12.43 57.2-15.17 27.98 14.98 60.32 24.8 82.1 24.8 17.98 0 30.13-9.32 34.52-23.99 3.85-12.88.8-27.82-7.48-36.08-8.56-8.41-24.3-12.43-45.65-13.12zM385.23 765.68v-.36l.13-.34a54.86 54.86 0 015.6-10.76c4.28-6.58 10.17-13.5 17.47-20.87 3.92-3.95 8-7.8 12.79-12.12 1.07-.96 7.91-7.05 9.19-8.25l11.17-10.4-8.12 12.93c-12.32 19.64-23.46 33.78-33 43-3.51 3.4-6.6 5.9-9.1 7.51a16.43 16.43 0 01-2.61 1.42c-.41.17-.77.27-1.13.3a2.2 2.2 0 01-1.12-.15 2.07 2.07 0 01-1.27-1.91zM511.17 547.4l-2.26 4-1.4-4.38c-3.1-9.83-5.38-24.64-6.01-38-.72-15.2.49-24.32 5.29-24.32 6.74 0 9.83 10.8 10.07 27.05.22 14.28-2.03 29.14-5.7 35.65zm-5.81 58.46l1.53-4.05 2.09 3.8c11.69 21.24 26.86 38.96 43.54 51.31l3.6 2.66-4.39.9c-16.33 3.38-31.54 8.46-52.34 16.85 2.17-.88-21.62 8.86-27.64 11.17l-5.25 2.01 2.8-4.88c12.35-21.5 23.76-47.32 36.05-79.77zm157.62 76.26c-7.86 3.1-24.78.33-54.57-12.39l-7.56-3.22 8.2-.6c23.3-1.73 39.8-.45 49.42 3.07 4.1 1.5 6.83 3.39 8.04 5.55a4.64 4.64 0 01-1.36 6.31 6.7 6.7 0 01-2.17 1.28z"}}]},name:"file-pdf",theme:"filled"},F=function(e,t){return l.createElement(u.Z,(0,f.Z)({},e,{ref:t,icon:W}))};var P=l.forwardRef(F),D={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM468.53 760v-91.54h59.27c60.57 0 100.2-39.65 100.2-98.12 0-58.22-39.58-98.34-99.98-98.34H424a12 12 0 00-12 12v276a12 12 0 0012 12h32.53a12 12 0 0012-12zm0-139.33h34.9c47.82 0 67.19-12.93 67.19-50.33 0-32.05-18.12-50.12-49.87-50.12h-52.22v100.45z"}}]},name:"file-ppt",theme:"filled"},T=function(e,t){return l.createElement(u.Z,(0,f.Z)({},e,{ref:t,icon:D}))};var X=l.forwardRef(T),j={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM512 566.1l52.81 197a12 12 0 0011.6 8.9h31.77a12 12 0 0011.6-8.88l74.37-276a12 12 0 00.4-3.12 12 12 0 00-12-12h-35.57a12 12 0 00-11.7 9.31l-45.78 199.1-49.76-199.32A12 12 0 00528.1 472h-32.2a12 12 0 00-11.64 9.1L434.6 680.01 388.5 481.3a12 12 0 00-11.68-9.29h-35.39a12 12 0 00-3.11.41 12 12 0 00-8.47 14.7l74.17 276A12 12 0 00415.6 772h31.99a12 12 0 0011.59-8.9l52.81-197z"}}]},name:"file-word",theme:"filled"},q=function(e,t){return l.createElement(u.Z,(0,f.Z)({},e,{ref:t,icon:j}))};var O=l.forwardRef(q),U={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM296 136v64h64v-64h-64zm64 64v64h64v-64h-64zm-64 64v64h64v-64h-64zm64 64v64h64v-64h-64zm-64 64v64h64v-64h-64zm64 64v64h64v-64h-64zm-64 64v64h64v-64h-64zm0 64v160h128V584H296zm48 48h32v64h-32v-64z"}}]},name:"file-zip",theme:"filled"},_=function(e,t){return l.createElement(u.Z,(0,f.Z)({},e,{ref:t,icon:U}))};var A=l.forwardRef(_),Y={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM320 482a8 8 0 00-8 8v48a8 8 0 008 8h384a8 8 0 008-8v-48a8 8 0 00-8-8H320zm0 136a8 8 0 00-8 8v48a8 8 0 008 8h184a8 8 0 008-8v-48a8 8 0 00-8-8H320z"}}]},name:"file-text",theme:"filled"},G=function(e,t){return l.createElement(u.Z,(0,f.Z)({},e,{ref:t,icon:Y}))};var K=l.forwardRef(G),J=n(1085),Q=function(e,t){return l.createElement(u.Z,(0,f.Z)({},e,{ref:t,icon:J.Z}))};var ee=l.forwardRef(Q),te=n(27808),ne=n(83262),re=n(15063),ae=n(43495);var le=e=>{const{componentCls:t,antCls:n,calc:r}=e,a=`${t}-list-card`,l=r(e.fontSize).mul(e.lineHeight).mul(2).add(e.paddingSM).add(e.paddingSM).equal();return{[a]:{borderRadius:e.borderRadius,position:"relative",background:e.colorFillContent,borderWidth:e.lineWidth,borderStyle:"solid",borderColor:"transparent",flex:"none",[`${a}-name,${a}-desc`]:{display:"flex",flexWrap:"nowrap",maxWidth:"100%"},[`${a}-ellipsis-prefix`]:{flex:"0 1 auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},[`${a}-ellipsis-suffix`]:{flex:"none"},"&-type-overview":{padding:r(e.paddingSM).sub(e.lineWidth).equal(),paddingInlineStart:r(e.padding).add(e.lineWidth).equal(),display:"flex",flexWrap:"nowrap",gap:e.paddingXS,alignItems:"flex-start",width:236,[`${a}-icon`]:{fontSize:r(e.fontSizeLG).mul(2).equal(),lineHeight:1,paddingTop:r(e.paddingXXS).mul(1.5).equal(),flex:"none"},[`${a}-content`]:{flex:"auto",minWidth:0,display:"flex",flexDirection:"column",alignItems:"stretch"},[`${a}-desc`]:{color:e.colorTextTertiary}},"&-type-preview":{width:l,height:l,lineHeight:1,display:"flex",alignItems:"center",[`&:not(${a}-status-error)`]:{border:0},[`${n}-image`]:{width:"100%",height:"100%",borderRadius:"inherit",img:{height:"100%",objectFit:"cover",borderRadius:"inherit"}},[`${a}-img-mask`]:{position:"absolute",inset:0,display:"flex",justifyContent:"center",alignItems:"center",background:`rgba(0, 0, 0, ${e.opacityLoading})`,borderRadius:"inherit"},[`&${a}-status-error`]:{[`img, ${a}-img-mask`]:{borderRadius:r(e.borderRadius).sub(e.lineWidth).equal()},[`${a}-desc`]:{paddingInline:e.paddingXXS}},[`${a}-progress`]:{}},[`${a}-remove`]:{position:"absolute",top:0,insetInlineEnd:0,border:0,padding:e.paddingXXS,background:"transparent",lineHeight:1,transform:"translate(50%, -50%)",fontSize:e.fontSize,cursor:"pointer",opacity:e.opacityLoading,display:"none","&:dir(rtl)":{transform:"translate(-50%, -50%)"},"&:hover":{opacity:1},"&:active":{opacity:e.opacityLoading}},[`&:hover ${a}-remove`]:{display:"block"},"&-status-error":{borderColor:e.colorError,[`${a}-desc`]:{color:e.colorError}},"&-motion":{transition:["opacity","width","margin","padding"].map((t=>`${t} ${e.motionDurationSlow}`)).join(","),"&-appear-start":{width:0,transition:"none"},"&-leave-active":{opacity:0,width:0,paddingInline:0,borderInlineWidth:0,marginInlineEnd:r(e.paddingSM).mul(-1).equal()}}}}};const oe={"&, *":{boxSizing:"border-box"}},ie=e=>{const{componentCls:t,calc:n,antCls:r}=e,a=`${t}-placeholder`;return{[`${t}-drop-area`]:{position:"absolute",inset:0,zIndex:e.zIndexPopupBase,...oe,"&-on-body":{position:"fixed",inset:0},"&-hide-placement":{[`${a}-inner`]:{display:"none"}},[a]:{padding:0}},"&":{[a]:{height:"100%",borderRadius:e.borderRadius,borderWidth:e.lineWidthBold,borderStyle:"dashed",borderColor:"transparent",padding:e.padding,position:"relative",backdropFilter:"blur(10px)",background:e.colorBgPlaceholderHover,...oe,[`${r}-upload-wrapper ${r}-upload${r}-upload-btn`]:{padding:0},[`&${a}-drag-in`]:{borderColor:e.colorPrimaryHover},[`&${a}-disabled`]:{opacity:.25,pointerEvents:"none"},[`${a}-inner`]:{gap:n(e.paddingXXS).div(2).equal()},[`${a}-icon`]:{fontSize:e.fontSizeHeading2,lineHeight:1},[`${a}-title${a}-title`]:{margin:0,fontSize:e.fontSize,lineHeight:e.lineHeight},[`${a}-description`]:{}}}}},ce=e=>{const{componentCls:t,calc:n}=e,r=`${t}-list`,a=n(e.fontSize).mul(e.lineHeight).mul(2).add(e.paddingSM).add(e.paddingSM).equal();return{[t]:{position:"relative",width:"100%",...oe,[r]:{display:"flex",flexWrap:"wrap",gap:e.paddingSM,fontSize:e.fontSize,lineHeight:e.lineHeight,color:e.colorText,paddingBlock:e.paddingSM,paddingInline:e.padding,width:"100%",background:e.colorBgContainer,scrollbarWidth:"none","-ms-overflow-style":"none","&::-webkit-scrollbar":{display:"none"},"&-overflow-scrollX, &-overflow-scrollY":{"&:before, &:after":{content:'""',position:"absolute",opacity:0,transition:`opacity ${e.motionDurationSlow}`,zIndex:1}},"&-overflow-ping-start:before":{opacity:1},"&-overflow-ping-end:after":{opacity:1},"&-overflow-scrollX":{overflowX:"auto",overflowY:"hidden",flexWrap:"nowrap","&:before, &:after":{insetBlock:0,width:8},"&:before":{insetInlineStart:0,background:"linear-gradient(to right, rgba(0,0,0,0.06), rgba(0,0,0,0));"},"&:after":{insetInlineEnd:0,background:"linear-gradient(to left, rgba(0,0,0,0.06), rgba(0,0,0,0));"},"&:dir(rtl)":{"&:before":{background:"linear-gradient(to left, rgba(0,0,0,0.06), rgba(0,0,0,0));"},"&:after":{background:"linear-gradient(to right, rgba(0,0,0,0.06), rgba(0,0,0,0));"}}},"&-overflow-scrollY":{overflowX:"hidden",overflowY:"auto",maxHeight:n(a).mul(3).equal(),"&:before, &:after":{insetInline:0,height:8},"&:before":{insetBlockStart:0,background:"linear-gradient(to bottom, rgba(0,0,0,0.06), rgba(0,0,0,0));"},"&:after":{insetBlockEnd:0,background:"linear-gradient(to top, rgba(0,0,0,0.06), rgba(0,0,0,0));"}},"&-upload-btn":{width:a,height:a,fontSize:e.fontSizeHeading2,color:"#999"},"&-prev-btn, &-next-btn":{position:"absolute",top:"50%",transform:"translateY(-50%)",boxShadow:e.boxShadowTertiary,opacity:0,pointerEvents:"none"},"&-prev-btn":{left:{_skip_check_:!0,value:e.padding}},"&-next-btn":{right:{_skip_check_:!0,value:e.padding}},"&:dir(ltr)":{[`&${r}-overflow-ping-start ${r}-prev-btn`]:{opacity:1,pointerEvents:"auto"},[`&${r}-overflow-ping-end ${r}-next-btn`]:{opacity:1,pointerEvents:"auto"}},"&:dir(rtl)":{[`&${r}-overflow-ping-end ${r}-prev-btn`]:{opacity:1,pointerEvents:"auto"},[`&${r}-overflow-ping-start ${r}-next-btn`]:{opacity:1,pointerEvents:"auto"}}}}}};var se=(0,ae.I$)("Attachments",(e=>{const t=(0,ne.IX)(e,{});return[ie(t),ce(t),le(t)]}),(e=>{const{colorBgContainer:t}=e;return{colorBgPlaceholderHover:new re.t(t).setA(.85).toRgbString()}}));const de=200;function me(){return l.createElement("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},l.createElement("title",null,"audio"),l.createElement("g",{stroke:"none",strokeWidth:"1",fill:"none",fillRule:"evenodd"},l.createElement("path",{d:"M14.1178571,4.0125 C14.225,4.11964286 14.2857143,4.26428571 14.2857143,4.41607143 L14.2857143,15.4285714 C14.2857143,15.7446429 14.0303571,16 13.7142857,16 L2.28571429,16 C1.96964286,16 1.71428571,15.7446429 1.71428571,15.4285714 L1.71428571,0.571428571 C1.71428571,0.255357143 1.96964286,0 2.28571429,0 L9.86964286,0 C10.0214286,0 10.1678571,0.0607142857 10.275,0.167857143 L14.1178571,4.0125 Z M10.7315824,7.11216117 C10.7428131,7.15148751 10.7485063,7.19218979 10.7485063,7.23309113 L10.7485063,8.07742614 C10.7484199,8.27364959 10.6183424,8.44607275 10.4296853,8.50003683 L8.32984514,9.09986306 L8.32984514,11.7071803 C8.32986605,12.5367078 7.67249692,13.217028 6.84345686,13.2454634 L6.79068592,13.2463395 C6.12766108,13.2463395 5.53916361,12.8217001 5.33010655,12.1924966 C5.1210495,11.563293 5.33842118,10.8709227 5.86959669,10.4741173 C6.40077221,10.0773119 7.12636292,10.0652587 7.67042486,10.4442027 L7.67020842,7.74937024 L7.68449368,7.74937024 C7.72405122,7.59919041 7.83988806,7.48101083 7.98924584,7.4384546 L10.1880418,6.81004755 C10.42156,6.74340323 10.6648954,6.87865515 10.7315824,7.11216117 Z M9.60714286,1.31785714 L12.9678571,4.67857143 L9.60714286,4.67857143 L9.60714286,1.31785714 Z",fill:"currentColor"})))}var fe=n(9361),pe=n(38703);function ue(e){const{percent:t}=e,{token:n}=fe.Z.useToken();return l.createElement(pe.Z,{type:"circle",percent:t,size:2*n.fontSizeHeading2,strokeColor:"#FFF",trailColor:"rgba(255, 255, 255, 0.3)",format:e=>l.createElement("span",{style:{color:"#FFF"}},(e||0).toFixed(0),"%")})}function ge(){return l.createElement("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},l.createElement("title",null,"video"),l.createElement("g",{stroke:"none",strokeWidth:"1",fill:"none",fillRule:"evenodd"},l.createElement("path",{d:"M14.1178571,4.0125 C14.225,4.11964286 14.2857143,4.26428571 14.2857143,4.41607143 L14.2857143,15.4285714 C14.2857143,15.7446429 14.0303571,16 13.7142857,16 L2.28571429,16 C1.96964286,16 1.71428571,15.7446429 1.71428571,15.4285714 L1.71428571,0.571428571 C1.71428571,0.255357143 1.96964286,0 2.28571429,0 L9.86964286,0 C10.0214286,0 10.1678571,0.0607142857 10.275,0.167857143 L14.1178571,4.0125 Z M12.9678571,4.67857143 L9.60714286,1.31785714 L9.60714286,4.67857143 L12.9678571,4.67857143 Z M10.5379461,10.3101106 L6.68957555,13.0059749 C6.59910784,13.0693494 6.47439406,13.0473861 6.41101953,12.9569184 C6.3874624,12.9232903 6.37482581,12.8832269 6.37482581,12.8421686 L6.37482581,7.45043999 C6.37482581,7.33998304 6.46436886,7.25043999 6.57482581,7.25043999 C6.61588409,7.25043999 6.65594753,7.26307658 6.68957555,7.28663371 L10.5379461,9.98249803 C10.6284138,10.0458726 10.6503772,10.1705863 10.5870027,10.2610541 C10.5736331,10.2801392 10.5570312,10.2967411 10.5379461,10.3101106 Z",fill:"currentColor"})))}const he="#8c8c8c",ve=["png","jpg","jpeg","gif","bmp","webp","svg"],be=[{icon:l.createElement(M,null),color:"#22b35e",ext:["xlsx","xls"]},{icon:l.createElement(H,null),color:he,ext:ve},{icon:l.createElement(I,null),color:he,ext:["md","mdx"]},{icon:l.createElement(P,null),color:"#ff4d4f",ext:["pdf"]},{icon:l.createElement(X,null),color:"#ff6e31",ext:["ppt","pptx"]},{icon:l.createElement(O,null),color:"#1677ff",ext:["doc","docx"]},{icon:l.createElement(A,null),color:"#fab714",ext:["zip","rar","7z","tar","gz"]},{icon:l.createElement(ge,null),color:"#ff4d4f",ext:["mp4","avi","mov","wmv","flv","mkv"]},{icon:l.createElement(me,null),color:"#8c8c8c",ext:["mp3","wav","flac","ape","aac","ogg"]}];function we(e,t){return t.some((t=>e.toLowerCase()===`.${t}`))}function Ee(e,t){const{prefixCls:n,item:r,onRemove:o,className:c,style:s,imageProps:m}=e,p=l.useContext(d),{disabled:u}=p||{},{name:g,size:h,percent:v,status:b="done",description:w}=r,{getPrefixCls:E}=(0,i.Z)(),x=E("attachment",n),y=`${x}-list-card`,[C,$,z]=se(x),[L,Z]=l.useMemo((()=>{const e=g||"",t=e.match(/^(.*)\.[^.]+$/);return t?[t[1],e.slice(t[1].length)]:[e,""]}),[g]),S=l.useMemo((()=>we(Z,ve)),[Z]),k=l.useMemo((()=>w||("uploading"===b?`${v||0}%`:"error"===b?r.response||" ":h?function(e){let t=e;const n=["B","KB","MB","GB","TB","PB","EB"];let r=0;for(;t>=1024&&r<n.length-1;)t/=1024,r++;return`${t.toFixed(0)} ${n[r]}`}(h):" ")),[b,v]),[M,R]=l.useMemo((()=>{for(const{ext:e,icon:t,color:n}of be)if(we(Z,e))return[t,n];return[l.createElement(K,{key:"defaultIcon"}),he]}),[Z]),[N,H]=l.useState();l.useEffect((()=>{if(r.originFileObj){let t=!0;return(e=r.originFileObj,new Promise((t=>{if(!e||!e.type||0!==e.type.indexOf("image/"))return void t("");const n=new Image;if(n.onload=()=>{const{width:e,height:r}=n,a=e/r,l=a>1?de:de*a,o=a>1?de/a:de,i=document.createElement("canvas");i.width=l,i.height=o,i.style.cssText=`position: fixed; left: 0; top: 0; width: ${l}px; height: ${o}px; z-index: 9999; display: none;`,document.body.appendChild(i),i.getContext("2d").drawImage(n,0,0,l,o);const c=i.toDataURL();document.body.removeChild(i),window.URL.revokeObjectURL(n.src),t(c)},n.crossOrigin="anonymous",e.type.startsWith("image/svg+xml")){const t=new FileReader;t.onload=()=>{t.result&&"string"==typeof t.result&&(n.src=t.result)},t.readAsDataURL(e)}else if(e.type.startsWith("image/gif")){const n=new FileReader;n.onload=()=>{n.result&&t(n.result)},n.readAsDataURL(e)}else n.src=window.URL.createObjectURL(e)}))).then((e=>{t&&H(e)})),()=>{t=!1}}var e;H(void 0)}),[r.originFileObj]);let V=null;const B=r.thumbUrl||r.url||N,I=S&&(r.originFileObj||B);return V=I?l.createElement(l.Fragment,null,B&&l.createElement(te.Z,(0,f.Z)({alt:"preview",src:B},m)),"done"!==b&&l.createElement("div",{className:`${y}-img-mask`},"uploading"===b&&void 0!==v&&l.createElement(ue,{percent:v,prefixCls:y}),"error"===b&&l.createElement("div",{className:`${y}-desc`},l.createElement("div",{className:`${y}-ellipsis-prefix`},k)))):l.createElement(l.Fragment,null,l.createElement("div",{className:`${y}-icon`,style:{color:R}},M),l.createElement("div",{className:`${y}-content`},l.createElement("div",{className:`${y}-name`},l.createElement("div",{className:`${y}-ellipsis-prefix`},L??" "),l.createElement("div",{className:`${y}-ellipsis-suffix`},Z)),l.createElement("div",{className:`${y}-desc`},l.createElement("div",{className:`${y}-ellipsis-prefix`},k)))),C(l.createElement("div",{className:a()(y,{[`${y}-status-${b}`]:b,[`${y}-type-preview`]:I,[`${y}-type-overview`]:!I},c,$,z),style:s,ref:t},V,!u&&o&&l.createElement("button",{type:"button",className:`${y}-remove`,onClick:()=>{o(r)}},l.createElement(ee,null))))}var xe=l.forwardRef(Ee);function ye(e){const{prefixCls:t,items:n,onRemove:r,overflow:o,upload:i,listClassName:c,listStyle:s,itemClassName:m,itemStyle:f,imageProps:p}=e,u=`${t}-list`,g=l.useRef(null),[v,b]=l.useState(!1),{disabled:E}=l.useContext(d);l.useEffect((()=>(b(!0),()=>{b(!1)})),[]);const[x,z]=l.useState(!1),[L,S]=l.useState(!1),k=()=>{const e=g.current;e&&("scrollX"===o?(z(Math.abs(e.scrollLeft)>=1),S(e.scrollWidth-e.clientWidth-Math.abs(e.scrollLeft)>=1)):"scrollY"===o&&(z(0!==e.scrollTop),S(e.scrollHeight-e.clientHeight!==e.scrollTop)))};l.useEffect((()=>{k()}),[o,n.length]);const M=e=>{const t=g.current;t&&t.scrollTo({left:t.scrollLeft+e*t.clientWidth,behavior:"smooth"})};return l.createElement("div",{className:a()(u,{[`${u}-overflow-${e.overflow}`]:o,[`${u}-overflow-ping-start`]:x,[`${u}-overflow-ping-end`]:L},c),ref:g,onScroll:k,style:s},l.createElement($.V4,{keys:n.map((e=>({key:e.uid,item:e}))),motionName:`${u}-card-motion`,component:!1,motionAppear:v,motionLeave:!0,motionEnter:!0},(({key:e,item:n,className:o,style:i})=>l.createElement(xe,{key:e,prefixCls:t,item:n,onRemove:r,className:a()(o,m),imageProps:p,style:{...i,...f}}))),!E&&l.createElement(Z,{upload:i},l.createElement(C.ZP,{className:`${u}-upload-btn`,type:"dashed"},l.createElement(h,{className:`${u}-upload-btn-icon`}))),"scrollX"===o&&l.createElement(l.Fragment,null,l.createElement(C.ZP,{size:"small",shape:"circle",className:`${u}-prev-btn`,icon:l.createElement(w,null),onClick:()=>{M(-1)}}),l.createElement(C.ZP,{size:"small",shape:"circle",className:`${u}-next-btn`,icon:l.createElement(y,null),onClick:()=>{M(1)}})))}var Ce=n(86250),$e=n(71471);function ze(e,t){const{prefixCls:n,placeholder:r={},upload:o,className:i,style:c}=e,s=`${n}-placeholder`,m=r||{},{disabled:p}=l.useContext(d),[u,g]=l.useState(!1),h=l.isValidElement(r)?r:l.createElement(Ce.Z,{align:"center",justify:"center",vertical:!0,className:`${s}-inner`},l.createElement($e.Z.Text,{className:`${s}-icon`},m.icon),l.createElement($e.Z.Title,{className:`${s}-title`,level:5},m.title),l.createElement($e.Z.Text,{className:`${s}-description`,type:"secondary"},m.description));return l.createElement("div",{className:a()(s,{[`${s}-drag-in`]:u,[`${s}-disabled`]:p},i),onDragEnter:()=>{g(!0)},onDragLeave:e=>{e.currentTarget.contains(e.relatedTarget)||g(!1)},onDrop:()=>{g(!1)},"aria-hidden":p,style:c},l.createElement(z.Z.Dragger,(0,f.Z)({showUploadList:!1},o,{ref:t,style:{padding:0,border:0,background:"transparent"}}),h))}var Le=l.forwardRef(ze);function Ze(e,t){const{prefixCls:n,rootClassName:r,rootStyle:s,className:f,style:p,items:u,children:g,getDropContainer:h,placeholder:v,onChange:b,onRemove:w,overflow:E,imageProps:x,disabled:y,classNames:C={},styles:$={},...z}=e,{getPrefixCls:L,direction:S}=(0,i.Z)(),k=L("attachment",n),M=(0,o.Z)("attachments"),{classNames:R,styles:N}=M,H=l.useRef(null),V=l.useRef(null);l.useImperativeHandle(t,(()=>({nativeElement:H.current,upload:e=>{const t=V.current?.nativeElement?.querySelector('input[type="file"]');if(t){const n=new DataTransfer;n.items.add(e),t.files=n.files,t.dispatchEvent(new Event("change",{bubbles:!0}))}}})));const[B,I,W]=se(k),F=a()(I,W),[P,D]=(0,c.C8)([],{value:u}),T=(0,c.zX)((e=>{D(e.fileList),b?.(e)})),X={...z,fileList:P,onChange:T},j=e=>Promise.resolve("function"==typeof w?w(e):w).then((t=>{if(!1===t)return;const n=P.filter((t=>t.uid!==e.uid));T({file:{...e,status:"removed"},fileList:n})}));let q;const O=(e,t,n)=>{const r="function"==typeof v?v(e):v;return l.createElement(Le,{placeholder:r,upload:X,prefixCls:k,className:a()(R.placeholder,C.placeholder),style:{...N.placeholder,...$.placeholder,...t?.style},ref:n})};if(g)q=l.createElement(l.Fragment,null,l.createElement(Z,{upload:X,rootClassName:r,ref:V},g),l.createElement(m,{getDropContainer:h,prefixCls:k,className:a()(F,r)},O("drop")));else{const e=P.length>0;q=l.createElement("div",{className:a()(k,F,{[`${k}-rtl`]:"rtl"===S},f,r),style:{...s,...p},dir:S||"ltr",ref:H},l.createElement(ye,{prefixCls:k,items:P,onRemove:j,overflow:E,upload:X,listClassName:a()(R.list,C.list),listStyle:{...N.list,...$.list,...!e&&{display:"none"}},itemClassName:a()(R.item,C.item),itemStyle:{...N.item,...$.item},imageProps:x}),O("inline",e?{style:{display:"none"}}:{},V),l.createElement(m,{getDropContainer:h||(()=>H.current),prefixCls:k,className:F},O("drop")))}return B(l.createElement(d.Provider,{value:{disabled:y}},q))}const Se=l.forwardRef(Ze);Se.FileCard=xe;var ke=Se}}]);