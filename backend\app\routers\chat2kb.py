import sys
import os
# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
import asyncio  # 导入 asyncio 模块
from typing import Dict, Any, List
from datetime import datetime
from bson import ObjectId
from app.utils.logging_config import setup_logging, get_logger
from app.models.chat import ChatRequest, KnowledgeBaseRequest
from app.db.mongodb import db
from app.utils.auth import verify_token
from app.models.system_app_setting import SystemAppSettingModel
from app.utils.llmClient import stream_model_api
from app.models.message import Message
setup_logging()
logger = get_logger(__name__)

router = APIRouter(
    prefix="/api",
    tags=["chat2kb"]
)


# 创建新对话
@router.post("/app/chat/chat2kb", response_model=Dict[str, Any])
async def chat2kb(conversation: ChatRequest, current_user: dict = Depends(verify_token)):
    
    # 查询更新后的应用信息
    logger.info(f"conversation==》{conversation}")
    system_app_info: SystemAppSettingModel = await db["system_app_settings"].find_one({"app_info": conversation.app_info})
    if not system_app_info:
        logger.error(f"没有找到应用信息: {conversation.app_info}")
        raise HTTPException(status_code=404, detail="没有找到应用信息")
    # 覆盖system_app_info的params里的knowledge_base_ids为conversation.kb_id
    system_app_info['params']['knowledge_base_ids'] = conversation.kb_id

    # 添加MCP参数
    system_app_info['params']['mcp_enabled'] = conversation.web_search_enabled
    if conversation.web_search_config:
        system_app_info['params']['mcp_config'] = conversation.web_search_config
    else:
        # 设置默认的MCP配置
        system_app_info['params']['mcp_config'] = {
            "freshness": "noLimit",
            "summary": "false",
            "count": "5",  # 默认5条结果
            "timeout": 60  # 超时时间60秒
        }

    # 提取最后一条消息
    last_message = conversation.messages[-1]
    logger.info(f'last_message===>{last_message}')

    # 创建新的用户消息记录
    new_user_message = Message(
        _id=str(ObjectId()),
        conversation_id=conversation.conversation_id,
        message_id=last_message['id'],
        meta_data=last_message.get('meta', {}),
        extra=last_message.get('extra', {}),
        user_id=conversation.user_id,
        user_name=conversation.user_name,
        role=last_message['role'],
        content=last_message['content'],
        created_at=datetime.now(),
        app_info=conversation.app_info,
        token_count=0,  # 这里可以添加计算token的逻辑
        price=0.0,  # 这里可以添加计算价格的逻辑
        knowledge_ids = conversation.kb_id
    )


    # 将 MongoEngine 实例转换为字典
    new_user_message_dict = new_user_message.to_mongo().to_dict()
    await db["messages"].insert_one(new_user_message_dict)

    async def stream_response():
        ai_message_content = ""
        async for chunk in stream_model_api(
            chat_id=conversation.conversation_id,
            user_id=conversation.user_id,
            messages=conversation.messages,
            system_app_info=system_app_info
        ):
            ai_message_content += chunk
            # logger.info(chunk)
            yield chunk
            await asyncio.sleep(0)

        # 添加 end 事件类型
        yield "event: end\ndata: Stream has ended\n\n"

    return StreamingResponse(stream_response(), media_type='text/event-stream')


# 获取知识库
@router.post("/app/get_knowledge_bases", response_model=Dict[str, Any])
async def get_knowledge_bases(request: KnowledgeBaseRequest):
    try:
        # 从 MongoDB 查询指定用户的知识库信息
        user_id = request.user_id
        
        # 查询用户自己的知识库
        user_knowledge_bases = await db["knowledge_bases"].find({"user_id": user_id,
                                                               "is_active": True,
                                                               "$or": [{"is_conversation_kb": False},
                                                                      {"is_conversation_kb": None}]}).to_list(length=None)
        
        # 查询所有已发布的知识库
        published_knowledge_bases = await db["knowledge_bases"].find({"is_active": True,
                                                                     "is_published": True,
                                                                     "$or": [{"is_conversation_kb": False},
                                                                            {"is_conversation_kb": None}]}).to_list(length=None)
        
        # 合并并去重
        kb_dict = {}
        
        # 先添加用户自己的知识库
        for kb in user_knowledge_bases:
            if "_id" in kb:
                kb_id = str(kb["_id"])
                kb["_id"] = kb_id
                # 确保 is_published 字段存在，如果数据库中没有，则设为 False
                if "is_published" not in kb:
                    kb["is_published"] = False
                kb_dict[kb_id] = kb
        
        # 再添加已发布的知识库（如果有重复则不覆盖，保留用户自己的）
        for kb in published_knowledge_bases:
            if "_id" in kb:
                kb_id = str(kb["_id"])
                if kb_id not in kb_dict:  # 避免重复
                    kb["_id"] = kb_id
                    kb["is_published"] = True  # 确保标记为已发布
                    kb_dict[kb_id] = kb
        
        # 转换为列表
        combined_knowledge_bases = list(kb_dict.values())
        
        return {
            "status": "success",
            "data": combined_knowledge_bases,
            "message": "成功获取知识库列表"
        }
    except Exception as e:
        logger.error(f"获取知识库列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取知识库列表失败: {str(e)}")
    
# if __name__ == "__main__":
    ## 运行测试
    # dict_user = {
    #     "user_id": 10
    # }
    # conversation = KnowledgeBaseRequest(**dict_user)
    # print(asyncio.run(get_knowledge_bases(conversation)))
    #测试 chat2kb
    # kb_id = ["123124","99999"]
    # conversation_dict = {
    #     "conversation_id": "6758f09b8a57feb909f00b81",
    #     "user_id": 1,
    #     "user_name": "超级管理员",
    #     "app_info": "chat2kb",
    #     "messages": [{"id": "6758f09b8a57feb909f00b81", "role": "user", "content": "1"}],
    #     "extra": {"prompt": "请根据以上参考内容，用中文简洁明了地回答：{{question}}"},
    #     "kb_id": kb_id
    # }
    # # 将字典转换为 ChatResponse 对象
    # conversation = ChatResponse(**conversation_dict)
    # print(asyncio.run(chat2kb(conversation=conversation)))

    