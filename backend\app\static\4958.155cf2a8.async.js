"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4958],{4958:function(e,t,n){function r(e,t){const n=String(e);if("string"!=typeof t)throw new TypeError("Expected character");let r=0,i=n.indexOf(t);for(;-1!==i;)r++,i=n.indexOf(t,i+t.length);return r}n.d(t,{Z:function(){return lt}});var i=n(24345),o=n(15459);var l=n(88718),s=n(96093);function a(e,t,n){const r=n||{},i=(0,s.O)(r.ignore||[]),o=function(e){const t=[];if(!Array.isArray(e))throw new TypeError("Expected find and replace tuple or list of tuples");const n=!e[0]||Array.isArray(e[0])?e:[e];let r=-1;for(;++r<n.length;){const e=n[r];t.push([c(e[0]),u(e[1])])}return t}(t);let a=-1;for(;++a<o.length;)(0,l.S4)(e,"text",f);function f(e,t){let n,r=-1;for(;++r<t.length;){const e=t[r],o=n?n.children:void 0;if(i(e,o?o.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){const n=t[t.length-1],r=o[a][0],i=o[a][1];let l=0;const s=n.children.indexOf(e);let c=!1,u=[];r.lastIndex=0;let f=r.exec(e.value);for(;f;){const n=f.index,o={index:f.index,input:f.input,stack:[...t,e]};let s=i(...f,o);if("string"==typeof s&&(s=s.length>0?{type:"text",value:s}:void 0),!1===s?r.lastIndex=n+1:(l!==n&&u.push({type:"text",value:e.value.slice(l,n)}),Array.isArray(s)?u.push(...s):s&&u.push(s),l=n+f[0].length,c=!0),!r.global)break;f=r.exec(e.value)}c?(l<e.value.length&&u.push({type:"text",value:e.value.slice(l)}),n.children.splice(s,1,...u)):u=[e];return s+u.length}(e,t)}}function c(e){return"string"==typeof e?new RegExp(function(e){if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(e),"g"):e}function u(e){return"function"==typeof e?e:function(){return e}}const f="phrasing",h=["autolink","link","image","label"];function d(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function p(e){this.config.enter.autolinkProtocol.call(this,e)}function m(e){this.config.exit.autolinkProtocol.call(this,e)}function g(e){this.config.exit.data.call(this,e);const t=this.stack[this.stack.length-1];(0,i.ok)("link"===t.type),t.url="http://"+this.sliceSerialize(e)}function k(e){this.config.exit.autolinkEmail.call(this,e)}function b(e){this.exit(e)}function x(e){a(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,v],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,y]],{ignore:["link","linkReference"]})}function v(e,t,n,i,o){let l="";if(!C(o))return!1;if(/^w/i.test(t)&&(n=t+n,t="",l="http://"),!function(e){const t=e.split(".");if(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))return!1;return!0}(n))return!1;const s=function(e){const t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],i=n.indexOf(")");const o=r(e,"(");let l=r(e,")");for(;-1!==i&&o>l;)e+=n.slice(0,i+1),n=n.slice(i+1),i=n.indexOf(")"),l++;return[e,n]}(n+i);if(!s[0])return!1;const a={type:"link",title:null,url:l+t+s[0],children:[{type:"text",value:t+s[0]}]};return s[1]?[a,{type:"text",value:s[1]}]:a}function y(e,t,n,r){return!(!C(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function C(e,t){const n=e.input.charCodeAt(e.index-1);return(0===e.index||(0,o.B8)(n)||(0,o.Xh)(n))&&(!t||47!==n)}var w=n(11098);function F(){this.buffer()}function D(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function z(){this.buffer()}function A(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function L(e){const t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteReference"===n.type),n.identifier=(0,w.d)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function S(e){this.exit(e)}function O(e){const t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteDefinition"===n.type),n.identifier=(0,w.d)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function E(e){this.exit(e)}function M(e,t,n,r){const i=n.createTracker(r);let o=i.move("[^");const l=n.enter("footnoteReference"),s=n.enter("reference");return o+=i.move(n.safe(n.associationId(e),{after:"]",before:o})),s(),l(),o+=i.move("]"),o}function T(e){let t=!1;return e&&e.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,r,i){const o=r.createTracker(i);let l=o.move("[^");const s=r.enter("footnoteDefinition"),a=r.enter("label");l+=o.move(r.safe(r.associationId(e),{before:l,after:"]"})),a(),l+=o.move("]:"),e.children&&e.children.length>0&&(o.shift(4),l+=o.move((t?"\n":" ")+r.indentLines(r.containerFlow(e,o.current()),t?R:I)));return s(),l},footnoteReference:M},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}}function I(e,t,n){return 0===t?e:R(e,t,n)}function R(e,t,n){return(n?"":"    ")+e}M.peek=function(){return"["};const j=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function _(e){this.enter({type:"delete",children:[]},e)}function B(e){this.exit(e)}function P(e,t,n,r){const i=n.createTracker(r),o=n.enter("strikethrough");let l=i.move("~~");return l+=n.containerPhrasing(e,{...i.current(),before:l,after:"~"}),l+=i.move("~~"),o(),l}function $(e){return e.length}function H(e){const t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:82===t||114===t?114:0}function q(e,t,n){return">"+(n?"":" ")+e}function W(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}function V(e,t,n,r){let i=-1;for(;++i<n.unsafe.length;)if("\n"===n.unsafe[i].character&&(o=n.stack,l=n.unsafe[i],W(o,l.inConstruct,!0)&&!W(o,l.notInConstruct,!1)))return/[ \t]/.test(r.before)?"":" ";var o,l;return"\\\n"}function U(e,t,n){return(n?"":"    ")+e}function Q(e){const t=e.options.quote||'"';if('"'!==t&&"'"!==t)throw new Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function X(e){return"&#x"+e.toString(16).toUpperCase()+";"}P.peek=function(){return"~"};var Z=n(62987);function G(e,t,n){const r=(0,Z.r)(e),i=(0,Z.r)(t);return void 0===r?void 0===i?"_"===n?{inside:!0,outside:!0}:{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!0}:1===r?void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!1}:void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!1}:{inside:!1,outside:!1}}function J(e,t,n,r){const i=function(e){const t=e.options.emphasis||"*";if("*"!==t&&"_"!==t)throw new Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}(n),o=n.enter("emphasis"),l=n.createTracker(r),s=l.move(i);let a=l.move(n.containerPhrasing(e,{after:i,before:s,...l.current()}));const c=a.charCodeAt(0),u=G(r.before.charCodeAt(r.before.length-1),c,i);u.inside&&(a=X(c)+a.slice(1));const f=a.charCodeAt(a.length-1),h=G(r.after.charCodeAt(0),f,i);h.inside&&(a=a.slice(0,-1)+X(f));const d=l.move(i);return o(),n.attentionEncodeSurroundingInfo={after:h.outside,before:u.outside},s+a+d}J.peek=function(e,t,n){return n.options.emphasis||"*"};var K=n(21623),N=n(27962);function Y(e){return e.value||""}function ee(e,t,n,r){const i=Q(n),o='"'===i?"Quote":"Apostrophe",l=n.enter("image");let s=n.enter("label");const a=n.createTracker(r);let c=a.move("![");return c+=a.move(n.safe(e.alt,{before:c,after:"]",...a.current()})),c+=a.move("]("),s(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),c+=a.move("<"),c+=a.move(n.safe(e.url,{before:c,after:">",...a.current()})),c+=a.move(">")):(s=n.enter("destinationRaw"),c+=a.move(n.safe(e.url,{before:c,after:e.title?" ":")",...a.current()}))),s(),e.title&&(s=n.enter(`title${o}`),c+=a.move(" "+i),c+=a.move(n.safe(e.title,{before:c,after:i,...a.current()})),c+=a.move(i),s()),c+=a.move(")"),l(),c}function te(e,t,n,r){const i=e.referenceType,o=n.enter("imageReference");let l=n.enter("label");const s=n.createTracker(r);let a=s.move("![");const c=n.safe(e.alt,{before:a,after:"]",...s.current()});a+=s.move(c+"]["),l();const u=n.stack;n.stack=[],l=n.enter("reference");const f=n.safe(n.associationId(e),{before:a,after:"]",...s.current()});return l(),n.stack=u,o(),"full"!==i&&c&&c===f?"shortcut"===i?a=a.slice(0,-1):a+=s.move("]"):a+=s.move(f+"]"),a}function ne(e,t,n){let r=e.value||"",i="`",o=-1;for(;new RegExp("(^|[^`])"+i+"([^`]|$)").test(r);)i+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++o<n.unsafe.length;){const e=n.unsafe[o],t=n.compilePattern(e);let i;if(e.atBreak)for(;i=t.exec(r);){let e=i.index;10===r.charCodeAt(e)&&13===r.charCodeAt(e-1)&&e--,r=r.slice(0,e)+" "+r.slice(i.index+1)}}return i+r+i}function re(e,t){const n=(0,N.B)(e);return Boolean(!t.options.resourceLink&&e.url&&!e.title&&e.children&&1===e.children.length&&"text"===e.children[0].type&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}function ie(e,t,n,r){const i=Q(n),o='"'===i?"Quote":"Apostrophe",l=n.createTracker(r);let s,a;if(re(e,n)){const t=n.stack;n.stack=[],s=n.enter("autolink");let r=l.move("<");return r+=l.move(n.containerPhrasing(e,{before:r,after:">",...l.current()})),r+=l.move(">"),s(),n.stack=t,r}s=n.enter("link"),a=n.enter("label");let c=l.move("[");return c+=l.move(n.containerPhrasing(e,{before:c,after:"](",...l.current()})),c+=l.move("]("),a(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(a=n.enter("destinationLiteral"),c+=l.move("<"),c+=l.move(n.safe(e.url,{before:c,after:">",...l.current()})),c+=l.move(">")):(a=n.enter("destinationRaw"),c+=l.move(n.safe(e.url,{before:c,after:e.title?" ":")",...l.current()}))),a(),e.title&&(a=n.enter(`title${o}`),c+=l.move(" "+i),c+=l.move(n.safe(e.title,{before:c,after:i,...l.current()})),c+=l.move(i),a()),c+=l.move(")"),s(),c}function oe(e,t,n,r){const i=e.referenceType,o=n.enter("linkReference");let l=n.enter("label");const s=n.createTracker(r);let a=s.move("[");const c=n.containerPhrasing(e,{before:a,after:"]",...s.current()});a+=s.move(c+"]["),l();const u=n.stack;n.stack=[],l=n.enter("reference");const f=n.safe(n.associationId(e),{before:a,after:"]",...s.current()});return l(),n.stack=u,o(),"full"!==i&&c&&c===f?"shortcut"===i?a=a.slice(0,-1):a+=s.move("]"):a+=s.move(f+"]"),a}function le(e){const t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function se(e){const t=e.options.rule||"*";if("*"!==t&&"-"!==t&&"_"!==t)throw new Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}Y.peek=function(){return"<"},ee.peek=function(){return"!"},te.peek=function(){return"!"},ne.peek=function(){return"`"},ie.peek=function(e,t,n){return re(e,n)?"<":"["},oe.peek=function(){return"["};const ae=(0,s.O)(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function ce(e,t,n,r){const i=function(e){const t=e.options.strong||"*";if("*"!==t&&"_"!==t)throw new Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}(n),o=n.enter("strong"),l=n.createTracker(r),s=l.move(i+i);let a=l.move(n.containerPhrasing(e,{after:i,before:s,...l.current()}));const c=a.charCodeAt(0),u=G(r.before.charCodeAt(r.before.length-1),c,i);u.inside&&(a=X(c)+a.slice(1));const f=a.charCodeAt(a.length-1),h=G(r.after.charCodeAt(0),f,i);h.inside&&(a=a.slice(0,-1)+X(f));const d=l.move(i+i);return o(),n.attentionEncodeSurroundingInfo={after:h.outside,before:u.outside},s+a+d}ce.peek=function(e,t,n){return n.options.strong||"*"};const ue={blockquote:function(e,t,n,r){const i=n.enter("blockquote"),o=n.createTracker(r);o.move("> "),o.shift(2);const l=n.indentLines(n.containerFlow(e,o.current()),q);return i(),l},break:V,code:function(e,t,n,r){const i=function(e){const t=e.options.fence||"`";if("`"!==t&&"~"!==t)throw new Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}(n),o=e.value||"",l="`"===i?"GraveAccent":"Tilde";if(function(e,t){return Boolean(!1===t.options.fences&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}(e,n)){const e=n.enter("codeIndented"),t=n.indentLines(o,U);return e(),t}const s=n.createTracker(r),a=i.repeat(Math.max(function(e,t){const n=String(e);let r=n.indexOf(t),i=r,o=0,l=0;if("string"!=typeof t)throw new TypeError("Expected substring");for(;-1!==r;)r===i?++o>l&&(l=o):o=1,i=r+t.length,r=n.indexOf(t,i);return l}(o,i)+1,3)),c=n.enter("codeFenced");let u=s.move(a);if(e.lang){const t=n.enter(`codeFencedLang${l}`);u+=s.move(n.safe(e.lang,{before:u,after:" ",encode:["`"],...s.current()})),t()}if(e.lang&&e.meta){const t=n.enter(`codeFencedMeta${l}`);u+=s.move(" "),u+=s.move(n.safe(e.meta,{before:u,after:"\n",encode:["`"],...s.current()})),t()}return u+=s.move("\n"),o&&(u+=s.move(o+"\n")),u+=s.move(a),c(),u},definition:function(e,t,n,r){const i=Q(n),o='"'===i?"Quote":"Apostrophe",l=n.enter("definition");let s=n.enter("label");const a=n.createTracker(r);let c=a.move("[");return c+=a.move(n.safe(n.associationId(e),{before:c,after:"]",...a.current()})),c+=a.move("]: "),s(),!e.url||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),c+=a.move("<"),c+=a.move(n.safe(e.url,{before:c,after:">",...a.current()})),c+=a.move(">")):(s=n.enter("destinationRaw"),c+=a.move(n.safe(e.url,{before:c,after:e.title?" ":"\n",...a.current()}))),s(),e.title&&(s=n.enter(`title${o}`),c+=a.move(" "+i),c+=a.move(n.safe(e.title,{before:c,after:i,...a.current()})),c+=a.move(i),s()),l(),c},emphasis:J,hardBreak:V,heading:function(e,t,n,r){const i=Math.max(Math.min(6,e.depth||1),1),o=n.createTracker(r);if(function(e,t){let n=!1;return(0,K.Vn)(e,(function(e){if("value"in e&&/\r?\n|\r/.test(e.value)||"break"===e.type)return n=!0,l.BK})),Boolean((!e.depth||e.depth<3)&&(0,N.B)(e)&&(t.options.setext||n))}(e,n)){const t=n.enter("headingSetext"),r=n.enter("phrasing"),l=n.containerPhrasing(e,{...o.current(),before:"\n",after:"\n"});return r(),t(),l+"\n"+(1===i?"=":"-").repeat(l.length-(Math.max(l.lastIndexOf("\r"),l.lastIndexOf("\n"))+1))}const s="#".repeat(i),a=n.enter("headingAtx"),c=n.enter("phrasing");o.move(s+" ");let u=n.containerPhrasing(e,{before:"# ",after:"\n",...o.current()});return/^[\t ]/.test(u)&&(u=X(u.charCodeAt(0))+u.slice(1)),u=u?s+" "+u:s,n.options.closeAtx&&(u+=" "+s),c(),a(),u},html:Y,image:ee,imageReference:te,inlineCode:ne,link:ie,linkReference:oe,list:function(e,t,n,r){const i=n.enter("list"),o=n.bulletCurrent;let l=e.ordered?function(e){const t=e.options.bulletOrdered||".";if("."!==t&&")"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}(n):le(n);const s=e.ordered?"."===l?")":".":function(e){const t=le(e),n=e.options.bulletOther;if(!n)return"*"===t?"-":"*";if("*"!==n&&"+"!==n&&"-"!==n)throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===t)throw new Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+n+"`) to be different");return n}(n);let a=!(!t||!n.bulletLastUsed)&&l===n.bulletLastUsed;if(!e.ordered){const t=e.children?e.children[0]:void 0;if("*"!==l&&"-"!==l||!t||t.children&&t.children[0]||"list"!==n.stack[n.stack.length-1]||"listItem"!==n.stack[n.stack.length-2]||"list"!==n.stack[n.stack.length-3]||"listItem"!==n.stack[n.stack.length-4]||0!==n.indexStack[n.indexStack.length-1]||0!==n.indexStack[n.indexStack.length-2]||0!==n.indexStack[n.indexStack.length-3]||(a=!0),se(n)===l&&t){let t=-1;for(;++t<e.children.length;){const n=e.children[t];if(n&&"listItem"===n.type&&n.children&&n.children[0]&&"thematicBreak"===n.children[0].type){a=!0;break}}}}a&&(l=s),n.bulletCurrent=l;const c=n.containerFlow(e,r);return n.bulletLastUsed=l,n.bulletCurrent=o,i(),c},listItem:function(e,t,n,r){const i=function(e){const t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n);let o=n.bulletCurrent||le(n);t&&"list"===t.type&&t.ordered&&(o=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+o);let l=o.length+1;("tab"===i||"mixed"===i&&(t&&"list"===t.type&&t.spread||e.spread))&&(l=4*Math.ceil(l/4));const s=n.createTracker(r);s.move(o+" ".repeat(l-o.length)),s.shift(l);const a=n.enter("listItem"),c=n.indentLines(n.containerFlow(e,s.current()),(function(e,t,n){if(t)return(n?"":" ".repeat(l))+e;return(n?o:o+" ".repeat(l-o.length))+e}));return a(),c},paragraph:function(e,t,n,r){const i=n.enter("paragraph"),o=n.enter("phrasing"),l=n.containerPhrasing(e,r);return o(),i(),l},root:function(e,t,n,r){return(e.children.some((function(e){return ae(e)}))?n.containerPhrasing:n.containerFlow).call(n,e,r)},strong:ce,text:function(e,t,n,r){return n.safe(e.value,r)},thematicBreak:function(e,t,n){const r=(se(n)+(n.options.ruleSpaces?" ":"")).repeat(function(e){const t=e.options.ruleRepetition||3;if(t<3)throw new Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}(n));return n.options.ruleSpaces?r.slice(0,-1):r}};function fe(e){const t=e._align;(0,i.ok)(t,"expected `_align` on table"),this.enter({type:"table",align:t.map((function(e){return"none"===e?null:e})),children:[]},e),this.data.inTable=!0}function he(e){this.exit(e),this.data.inTable=void 0}function de(e){this.enter({type:"tableRow",children:[]},e)}function pe(e){this.exit(e)}function me(e){this.enter({type:"tableCell",children:[]},e)}function ge(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,ke));const n=this.stack[this.stack.length-1];(0,i.ok)("inlineCode"===n.type),n.value=t,this.exit(e)}function ke(e,t){return"|"===t?t:e}function be(e){const t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,i=t.stringLength,o=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[\t :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=ue.inlineCode(e,t,n);n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&"));return r},table:function(e,t,n,r){return s(function(e,t,n){const r=e.children;let i=-1;const o=[],l=t.enter("table");for(;++i<r.length;)o[i]=a(r[i],t,n);return l(),o}(e,n,r),e.align)},tableCell:l,tableRow:function(e,t,n,r){const i=s([a(e,n,r)]);return i.slice(0,i.indexOf("\n"))}}};function l(e,t,n,r){const i=n.enter("tableCell"),l=n.enter("phrasing"),s=n.containerPhrasing(e,{...r,before:o,after:o});return l(),i(),s}function s(e,t){return function(e,t){const n=t||{},r=(n.align||[]).concat(),i=n.stringLength||$,o=[],l=[],s=[],a=[];let c=0,u=-1;for(;++u<e.length;){const t=[],r=[];let o=-1;for(e[u].length>c&&(c=e[u].length);++o<e[u].length;){const l=null==(f=e[u][o])?"":String(f);if(!1!==n.alignDelimiters){const e=i(l);r[o]=e,(void 0===a[o]||e>a[o])&&(a[o]=e)}t.push(l)}l[u]=t,s[u]=r}var f;let h=-1;if("object"==typeof r&&"length"in r)for(;++h<c;)o[h]=H(r[h]);else{const e=H(r);for(;++h<c;)o[h]=e}h=-1;const d=[],p=[];for(;++h<c;){const e=o[h];let t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let i=!1===n.alignDelimiters?1:Math.max(1,a[h]-t.length-r.length);const l=t+"-".repeat(i)+r;!1!==n.alignDelimiters&&(i=t.length+i+r.length,i>a[h]&&(a[h]=i),p[h]=i),d[h]=l}l.splice(1,0,d),s.splice(1,0,p),u=-1;const m=[];for(;++u<l.length;){const e=l[u],t=s[u];h=-1;const r=[];for(;++h<c;){const i=e[h]||"";let l="",s="";if(!1!==n.alignDelimiters){const e=a[h]-(t[h]||0),n=o[h];114===n?l=" ".repeat(e):99===n?e%2?(l=" ".repeat(e/2+.5),s=" ".repeat(e/2-.5)):(l=" ".repeat(e/2),s=l):s=" ".repeat(e)}!1===n.delimiterStart||h||r.push("|"),!1===n.padding||!1===n.alignDelimiters&&""===i||!1===n.delimiterStart&&!h||r.push(" "),!1!==n.alignDelimiters&&r.push(l),r.push(i),!1!==n.alignDelimiters&&r.push(s),!1!==n.padding&&r.push(" "),!1===n.delimiterEnd&&h===c-1||r.push("|")}m.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return m.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:i})}function a(e,t,n){const r=e.children;let i=-1;const o=[],s=t.enter("tableRow");for(;++i<r.length;)o[i]=l(r[i],0,t,n);return s(),o}}function xe(e){const t=this.stack[this.stack.length-2];(0,i.ok)("listItem"===t.type),t.checked="taskListCheckValueChecked"===e.type}function ve(e){const t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){const e=this.stack[this.stack.length-1];(0,i.ok)("paragraph"===e.type);const n=e.children[0];if(n&&"text"===n.type){const r=t.children;let i,o=-1;for(;++o<r.length;){const e=r[o];if("paragraph"===e.type){i=e;break}}i===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function ye(e,t,n,r){const i=e.children[0],o="boolean"==typeof e.checked&&i&&"paragraph"===i.type,l="["+(e.checked?"x":" ")+"] ",s=n.createTracker(r);o&&s.move(l);let a=ue.listItem(e,t,n,{...r,...s.current()});return o&&(a=a.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,(function(e){return e+l}))),a}var Ce=n(4663);const we={tokenize:function(e,t,n){let r=0;return function t(o){if((87===o||119===o)&&r<3)return r++,e.consume(o),t;if(46===o&&3===r)return e.consume(o),i;return n(o)};function i(e){return null===e?n(e):t(e)}},partial:!0},Fe={tokenize:function(e,t,n){let r,i,l;return s;function s(t){return 46===t||95===t?e.check(ze,c,a)(t):null===t||(0,o.z3)(t)||(0,o.B8)(t)||45!==t&&(0,o.Xh)(t)?c(t):(l=!0,e.consume(t),s)}function a(t){return 95===t?r=!0:(i=r,r=void 0),e.consume(t),s}function c(e){return i||r||!l?n(e):t(e)}},partial:!0},De={tokenize:function(e,t){let n=0,r=0;return i;function i(s){return 40===s?(n++,e.consume(s),i):41===s&&r<n?l(s):33===s||34===s||38===s||39===s||41===s||42===s||44===s||46===s||58===s||59===s||60===s||63===s||93===s||95===s||126===s?e.check(ze,t,l)(s):null===s||(0,o.z3)(s)||(0,o.B8)(s)?t(s):(e.consume(s),i)}function l(t){return 41===t&&r++,e.consume(t),i}},partial:!0},ze={tokenize:function(e,t,n){return r;function r(s){return 33===s||34===s||39===s||41===s||42===s||44===s||46===s||58===s||59===s||63===s||95===s||126===s?(e.consume(s),r):38===s?(e.consume(s),l):93===s?(e.consume(s),i):60===s||null===s||(0,o.z3)(s)||(0,o.B8)(s)?t(s):n(s)}function i(e){return null===e||40===e||91===e||(0,o.z3)(e)||(0,o.B8)(e)?t(e):r(e)}function l(e){return(0,o.jv)(e)?s(e):n(e)}function s(t){return 59===t?(e.consume(t),r):(0,o.jv)(t)?(e.consume(t),s):n(t)}},partial:!0},Ae={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(e){return(0,o.H$)(e)?n(e):t(e)}},partial:!0},Le={name:"wwwAutolink",tokenize:function(e,t,n){const r=this;return function(t){if(87!==t&&119!==t||!Te.call(r,r.previous)||_e(r.events))return n(t);return e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(we,e.attempt(Fe,e.attempt(De,i),n),n)(t)};function i(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:Te},Se={name:"protocolAutolink",tokenize:function(e,t,n){const r=this;let i="",l=!1;return function(t){if((72===t||104===t)&&Ie.call(r,r.previous)&&!_e(r.events))return e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),i+=String.fromCodePoint(t),e.consume(t),s;return n(t)};function s(t){if((0,o.jv)(t)&&i.length<5)return i+=String.fromCodePoint(t),e.consume(t),s;if(58===t){const n=i.toLowerCase();if("http"===n||"https"===n)return e.consume(t),a}return n(t)}function a(t){return 47===t?(e.consume(t),l?c:(l=!0,a)):n(t)}function c(t){return null===t||(0,o.Av)(t)||(0,o.z3)(t)||(0,o.B8)(t)||(0,o.Xh)(t)?n(t):e.attempt(Fe,e.attempt(De,u),n)(t)}function u(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:Ie},Oe={name:"emailAutolink",tokenize:function(e,t,n){const r=this;let i,l;return function(t){if(!je(t)||!Re.call(r,r.previous)||_e(r.events))return n(t);return e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),s(t)};function s(t){return je(t)?(e.consume(t),s):64===t?(e.consume(t),a):n(t)}function a(t){return 46===t?e.check(Ae,u,c)(t):45===t||95===t||(0,o.H$)(t)?(l=!0,e.consume(t),a):u(t)}function c(t){return e.consume(t),i=!0,a}function u(s){return l&&i&&(0,o.jv)(r.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(s)):n(s)}},previous:Re},Ee={};let Me=48;for(;Me<123;)Ee[Me]=Oe,Me++,58===Me?Me=65:91===Me&&(Me=97);function Te(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||(0,o.z3)(e)}function Ie(e){return!(0,o.jv)(e)}function Re(e){return!(47===e||je(e))}function je(e){return 43===e||45===e||46===e||95===e||(0,o.H$)(e)}function _e(e){let t=e.length,n=!1;for(;t--;){const r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}Ee[43]=Oe,Ee[45]=Oe,Ee[46]=Oe,Ee[95]=Oe,Ee[72]=[Oe,Se],Ee[104]=[Oe,Se],Ee[87]=[Oe,Le],Ee[119]=[Oe,Le];var Be=n(23402),Pe=n(42761);const $e={tokenize:function(e,t,n){const r=this;return(0,Pe.f)(e,(function(e){const i=r.events[r.events.length-1];return i&&"gfmFootnoteDefinitionIndent"===i[1].type&&4===i[2].sliceSerialize(i[1],!0).length?t(e):n(e)}),"gfmFootnoteDefinitionIndent",5)},partial:!0};function He(e,t,n){const r=this;let i=r.events.length;const o=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let l;for(;i--;){const e=r.events[i][1];if("labelImage"===e.type){l=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(i){if(!l||!l._balanced)return n(i);const s=(0,w.d)(r.sliceSerialize({start:l.end,end:r.now()}));if(94!==s.codePointAt(0)||!o.includes(s.slice(1)))return n(i);return e.enter("gfmFootnoteCallLabelMarker"),e.consume(i),e.exit("gfmFootnoteCallLabelMarker"),t(i)}}function qe(e,t){let n,r=e.length;for(;r--;)if("labelImage"===e[r][1].type&&"enter"===e[r][0]){n=e[r][1];break}e[r+1][1].type="data",e[r+3][1].type="gfmFootnoteCallLabelMarker";const i={type:"gfmFootnoteCall",start:Object.assign({},e[r+3][1].start),end:Object.assign({},e[e.length-1][1].end)},o={type:"gfmFootnoteCallMarker",start:Object.assign({},e[r+3][1].end),end:Object.assign({},e[r+3][1].end)};o.end.column++,o.end.offset++,o.end._bufferIndex++;const l={type:"gfmFootnoteCallString",start:Object.assign({},o.end),end:Object.assign({},e[e.length-1][1].start)},s={type:"chunkString",contentType:"string",start:Object.assign({},l.start),end:Object.assign({},l.end)},a=[e[r+1],e[r+2],["enter",i,t],e[r+3],e[r+4],["enter",o,t],["exit",o,t],["enter",l,t],["enter",s,t],["exit",s,t],["exit",l,t],e[e.length-2],e[e.length-1],["exit",i,t]];return e.splice(r,e.length-r+1,...a),e}function We(e,t,n){const r=this,i=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let l,s=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),a};function a(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",c)}function c(a){if(s>999||93===a&&!l||null===a||91===a||(0,o.z3)(a))return n(a);if(93===a){e.exit("chunkString");const o=e.exit("gfmFootnoteCallString");return i.includes((0,w.d)(r.sliceSerialize(o)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(a),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(a)}return(0,o.z3)(a)||(l=!0),s++,e.consume(a),92===a?u:c}function u(t){return 91===t||92===t||93===t?(e.consume(t),s++,c):c(t)}}function Ve(e,t,n){const r=this,i=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let l,s,a=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),c};function c(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",u):n(t)}function u(t){if(a>999||93===t&&!s||null===t||91===t||(0,o.z3)(t))return n(t);if(93===t){e.exit("chunkString");const n=e.exit("gfmFootnoteDefinitionLabelString");return l=(0,w.d)(r.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),h}return(0,o.z3)(t)||(s=!0),a++,e.consume(t),92===t?f:u}function f(t){return 91===t||92===t||93===t?(e.consume(t),a++,u):u(t)}function h(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),i.includes(l)||i.push(l),(0,Pe.f)(e,d,"gfmFootnoteDefinitionWhitespace")):n(t)}function d(e){return t(e)}}function Ue(e,t,n){return e.check(Be.w,t,e.attempt($e,t,n))}function Qe(e){e.exit("gfmFootnoteDefinition")}var Xe=n(62888),Ze=n(63233);function Ge(e){let t=(e||{}).singleTilde;const n={name:"strikethrough",tokenize:function(e,n,r){const i=this.previous,o=this.events;let l=0;return function(t){if(126===i&&"characterEscape"!==o[o.length-1][1].type)return r(t);return e.enter("strikethroughSequenceTemporary"),s(t)};function s(o){const a=(0,Z.r)(i);if(126===o)return l>1?r(o):(e.consume(o),l++,s);if(l<2&&!t)return r(o);const c=e.exit("strikethroughSequenceTemporary"),u=(0,Z.r)(o);return c._open=!u||2===u&&Boolean(a),c._close=!a||2===a&&Boolean(u),n(o)}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let r=n;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[n][1].end.offset-e[n][1].start.offset==e[r][1].end.offset-e[r][1].start.offset){e[n][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";const i={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[n][1].end)},o={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},l=[["enter",i,t],["enter",e[r][1],t],["exit",e[r][1],t],["enter",o,t]],s=t.parser.constructs.insideSpan.null;s&&(0,Xe.d)(l,l.length,0,(0,Ze.C)(s,e.slice(r+1,n),t)),(0,Xe.d)(l,l.length,0,[["exit",o,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",i,t]]),(0,Xe.d)(e,r-1,n-r+3,l),n=r+l.length-2;break}}n=-1;for(;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}class Je{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,r){let i=0;if(0===n&&0===r.length)return;for(;i<e.map.length;){if(e.map[i][0]===t)return e.map[i][1]+=n,void e.map[i][2].push(...r);i+=1}e.map.push([t,n,r])}(this,e,t,n)}consume(e){if(this.map.sort((function(e,t){return e[0]-t[0]})),0===this.map.length)return;let t=this.map.length;const n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push(e.slice()),e.length=0;let r=n.pop();for(;r;){for(const t of r)e.push(t);r=n.pop()}this.map.length=0}}function Ke(e,t){let n=!1;const r=[];for(;t<e.length;){const i=e[t];if(n){if("enter"===i[0])"tableContent"===i[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===i[1].type){if("tableDelimiterMarker"===e[t-1][1].type){const e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===i[1].type)break}else"enter"===i[0]&&"tableDelimiterRow"===i[1].type&&(n=!0);t+=1}return r}function Ne(e,t,n){const r=this;let i,l=0,s=0;return function(e){let t=r.events.length-1;for(;t>-1;){const e=r.events[t][1].type;if("lineEnding"!==e&&"linePrefix"!==e)break;t--}const i=t>-1?r.events[t][1].type:null,o="tableHead"===i||"tableRow"===i?y:a;if(o===y&&r.parser.lazy[r.now().line])return n(e);return o(e)};function a(t){return e.enter("tableHead"),e.enter("tableRow"),function(e){if(124===e)return c(e);return i=!0,s+=1,c(e)}(t)}function c(t){return null===t?n(t):(0,o.Ch)(t)?s>1?(s=0,r.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),h):n(t):(0,o.xz)(t)?(0,Pe.f)(e,c,"whitespace")(t):(s+=1,i&&(i=!1,l+=1),124===t?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),i=!0,c):(e.enter("data"),u(t)))}function u(t){return null===t||124===t||(0,o.z3)(t)?(e.exit("data"),c(t)):(e.consume(t),92===t?f:u)}function f(t){return 92===t||124===t?(e.consume(t),u):u(t)}function h(t){return r.interrupt=!1,r.parser.lazy[r.now().line]?n(t):(e.enter("tableDelimiterRow"),i=!1,(0,o.xz)(t)?(0,Pe.f)(e,d,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):d(t))}function d(t){return 45===t||58===t?m(t):124===t?(i=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),p):v(t)}function p(t){return(0,o.xz)(t)?(0,Pe.f)(e,m,"whitespace")(t):m(t)}function m(t){return 58===t?(s+=1,i=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),g):45===t?(s+=1,g(t)):null===t||(0,o.Ch)(t)?x(t):v(t)}function g(t){return 45===t?(e.enter("tableDelimiterFiller"),k(t)):v(t)}function k(t){return 45===t?(e.consume(t),k):58===t?(i=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),b):(e.exit("tableDelimiterFiller"),b(t))}function b(t){return(0,o.xz)(t)?(0,Pe.f)(e,x,"whitespace")(t):x(t)}function x(n){return 124===n?d(n):(null===n||(0,o.Ch)(n))&&i&&l===s?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(n)):v(n)}function v(e){return n(e)}function y(t){return e.enter("tableRow"),C(t)}function C(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),C):null===n||(0,o.Ch)(n)?(e.exit("tableRow"),t(n)):(0,o.xz)(n)?(0,Pe.f)(e,C,"whitespace")(n):(e.enter("data"),w(n))}function w(t){return null===t||124===t||(0,o.z3)(t)?(e.exit("data"),C(t)):(e.consume(t),92===t?F:w)}function F(t){return 92===t||124===t?(e.consume(t),w):w(t)}}function Ye(e,t){let n,r,i,o=-1,l=!0,s=0,a=[0,0,0,0],c=[0,0,0,0],u=!1,f=0;const h=new Je;for(;++o<e.length;){const d=e[o],p=d[1];"enter"===d[0]?"tableHead"===p.type?(u=!1,0!==f&&(tt(h,t,f,n,r),r=void 0,f=0),n={type:"table",start:Object.assign({},p.start),end:Object.assign({},p.end)},h.add(o,0,[["enter",n,t]])):"tableRow"===p.type||"tableDelimiterRow"===p.type?(l=!0,i=void 0,a=[0,0,0,0],c=[0,o+1,0,0],u&&(u=!1,r={type:"tableBody",start:Object.assign({},p.start),end:Object.assign({},p.end)},h.add(o,0,[["enter",r,t]])),s="tableDelimiterRow"===p.type?2:r?3:1):!s||"data"!==p.type&&"tableDelimiterMarker"!==p.type&&"tableDelimiterFiller"!==p.type?"tableCellDivider"===p.type&&(l?l=!1:(0!==a[1]&&(c[0]=c[1],i=et(h,t,a,s,void 0,i)),a=c,c=[a[1],o,0,0])):(l=!1,0===c[2]&&(0!==a[1]&&(c[0]=c[1],i=et(h,t,a,s,void 0,i),a=[0,0,0,0]),c[2]=o)):"tableHead"===p.type?(u=!0,f=o):"tableRow"===p.type||"tableDelimiterRow"===p.type?(f=o,0!==a[1]?(c[0]=c[1],i=et(h,t,a,s,o,i)):0!==c[1]&&(i=et(h,t,c,s,o,i)),s=0):!s||"data"!==p.type&&"tableDelimiterMarker"!==p.type&&"tableDelimiterFiller"!==p.type||(c[3]=o)}for(0!==f&&tt(h,t,f,n,r),h.consume(t.events),o=-1;++o<t.events.length;){const e=t.events[o];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=Ke(t.events,o))}return e}function et(e,t,n,r,i,o){const l=1===r?"tableHeader":2===r?"tableDelimiter":"tableData";0!==n[0]&&(o.end=Object.assign({},nt(t.events,n[0])),e.add(n[0],0,[["exit",o,t]]));const s=nt(t.events,n[1]);if(o={type:l,start:Object.assign({},s),end:Object.assign({},s)},e.add(n[1],0,[["enter",o,t]]),0!==n[2]){const i=nt(t.events,n[2]),o=nt(t.events,n[3]),l={type:"tableContent",start:Object.assign({},i),end:Object.assign({},o)};if(e.add(n[2],0,[["enter",l,t]]),2!==r){const r=t.events[n[2]],i=t.events[n[3]];if(r[1].end=Object.assign({},i[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){const t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",l,t]])}return void 0!==i&&(o.end=Object.assign({},nt(t.events,i)),e.add(i,0,[["exit",o,t]]),o=void 0),o}function tt(e,t,n,r,i){const o=[],l=nt(t.events,n);i&&(i.end=Object.assign({},l),o.push(["exit",i,t])),r.end=Object.assign({},l),o.push(["exit",r,t]),e.add(n+1,0,o)}function nt(e,t){const n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}const rt={name:"tasklistCheck",tokenize:function(e,t,n){const r=this;return function(t){if(null!==r.previous||!r._gfmTasklistFirstContentOfListItem)return n(t);return e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),i};function i(t){return(0,o.z3)(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),l):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),l):n(t)}function l(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),s):n(t)}function s(r){return(0,o.Ch)(r)?t(r):(0,o.xz)(r)?e.check({tokenize:it},t,n)(r):n(r)}}};function it(e,t,n){return(0,Pe.f)(e,(function(e){return null===e?n(e):t(e)}),"whitespace")}const ot={};function lt(e){const t=e||ot,n=this.data(),r=n.micromarkExtensions||(n.micromarkExtensions=[]),i=n.fromMarkdownExtensions||(n.fromMarkdownExtensions=[]),o=n.toMarkdownExtensions||(n.toMarkdownExtensions=[]);r.push(function(e){return(0,Ce.W)([{text:Ee},{document:{91:{name:"gfmFootnoteDefinition",tokenize:Ve,continuation:{tokenize:Ue},exit:Qe}},text:{91:{name:"gfmFootnoteCall",tokenize:We},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:He,resolveTo:qe}}},Ge(e),{flow:{null:{name:"table",tokenize:Ne,resolveAll:Ye}}},{text:{91:rt}}])}(t)),i.push([{transforms:[x],enter:{literalAutolink:d,literalAutolinkEmail:p,literalAutolinkHttp:p,literalAutolinkWww:p},exit:{literalAutolink:b,literalAutolinkEmail:k,literalAutolinkHttp:m,literalAutolinkWww:g}},{enter:{gfmFootnoteCallString:F,gfmFootnoteCall:D,gfmFootnoteDefinitionLabelString:z,gfmFootnoteDefinition:A},exit:{gfmFootnoteCallString:L,gfmFootnoteCall:S,gfmFootnoteDefinitionLabelString:O,gfmFootnoteDefinition:E}},{canContainEols:["delete"],enter:{strikethrough:_},exit:{strikethrough:B}},{enter:{table:fe,tableData:me,tableHeader:me,tableRow:de},exit:{codeText:ge,table:he,tableData:pe,tableHeader:pe,tableRow:pe}},{exit:{taskListCheckValueChecked:xe,taskListCheckValueUnchecked:xe,paragraph:ve}}]),o.push(function(e){return{extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:f,notInConstruct:h},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:f,notInConstruct:h},{character:":",before:"[ps]",after:"\\/",inConstruct:f,notInConstruct:h}]},T(e),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:j}],handlers:{delete:P}},be(e),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:ye}}]}}(t))}}}]);