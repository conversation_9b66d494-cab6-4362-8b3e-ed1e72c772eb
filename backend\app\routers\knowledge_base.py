from fastapi import APIRouter, HTTPException, Depends, Query, File, UploadFile, Form
from typing import List, Optional, Dict, Any
from ..models.knowledge_base import (
    KnowledgeBaseCreate,
    KnowledgeBaseUpdate,
    KnowledgeBaseResponse,
    KnowledgeFileResponse,
    UploadFolderRequest,
    ConversationKnowledgeBaseCreate,
    ConversationKnowledgeBaseResponse

)
from ..models.source_files import SourceFileBase
from ..db.mongodb import db
from ..utils.auth import verify_token
from bson import ObjectId
from datetime import datetime, timedelta
import os
import re
from werkzeug.utils import secure_filename as _secure_filename
from pathlib import Path
import aiofiles
from ..models.source_files import SourceFileResponse
from app.utils.logging_config import setup_logging, get_logger
from ..db.miniIO import minio
import uuid
from ..utils.config import settings
from fastapi.responses import RedirectResponse, StreamingResponse
import httpx
import mimetypes
import traceback # 异常打印
from urllib.parse import quote
from app.utils.enums import FileStatus,chunk_type,FileStorageType
from app.engines.embedding.embedding_utils import get_embedding
import tiktoken
from app.engines.retrieval.base_retriever import es_data_ingester, update_by_query, es_data_deleter
from difflib import SequenceMatcher
from ..utils.llmClient import FlowRAG, KnowledgeQAParams
from pydantic import BaseModel

enc = tiktoken.encoding_for_model(settings.TOKENS_COUNT_MODEL)
setup_logging()
logger = get_logger(__name__)
router = APIRouter(
    prefix="/api",
    tags=["knowledge_bases"]
)

ALLOWED_EXTENSIONS = {'txt', 'pdf', 'doc', 'docx', 'md', 'csv', 'xls', 'xlsx', 'json', 'jsonl', 'pptx', 'html', 'htm', 'jpg', 'jpeg', 'png', 'wav', 'mp3', 'markdown'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def secure_filename(filename):
    """
    安全化文件名
    """
    filename = _secure_filename(filename)
    # 确保文件名是唯一的
    name, ext = os.path.splitext(filename)
    counter = 1
    while os.path.exists(filename):
        filename = f"{name}_{counter}{ext}"
        counter += 1
    return filename


FILE_UPLOAD_PATH = os.getenv("FILE_UPLOAD_PATH", "./uploads/knowledge_files")

def get_file_format(filename: str) -> str:
    """根据文件后缀返回文件格式"""
    extension = os.path.splitext(filename)[1].lower()
    format_map = {
        '.csv': 'CSV',
        '.xls': 'XLS',
        '.xlsx': 'XLSX',
        '.json': 'JSON',
        '.jsonl': 'JSONL',
        '.txt': 'TXT',
        '.pdf': 'PDF',
        '.doc': 'DOC',
        '.docx': 'DOCX',
        '.pptx': 'PPTX',
        '.html': 'HTML',
        '.htm': 'HTM',
        '.md': 'MD',
        '.markdown': 'MD',
        '.jpg': 'JPG',
        '.jpeg': 'JPEG',
        '.png': 'PNG',
        '.wav': 'WAV',
        '.mp3': 'MP3',
    }
    return format_map.get(extension, 'Unknown')


# 创建知识库
@router.post("/knowledge_base", response_model=KnowledgeBaseCreate)
async def create_knowledge_base(
    knowledge_base: KnowledgeBaseCreate,
    current_user: dict = Depends(verify_token)
):
    print(f"Creating knowledge base: {knowledge_base}")
    new_knowledge_base = knowledge_base.dict()
    new_knowledge_base["created_at"] = datetime.now()
    new_knowledge_base["last_updated"] = datetime.now()
    new_knowledge_base["user_id"] = current_user["id"]
    new_knowledge_base["user_name"] = current_user["name"]
    new_knowledge_base["is_active"] = True
    new_knowledge_base["is_conversation_kb"] = False


    print(f"new_knowledge_base: {new_knowledge_base}")
    
    result = await db["knowledge_bases"].insert_one(new_knowledge_base)
    created_knowledge_base = await db["knowledge_bases"].find_one({"_id": result.inserted_id})
    
    # 添加 id 字段并转换 _id
    created_knowledge_base["id"] = str(created_knowledge_base["_id"])
    
    return {
        "data": KnowledgeBaseResponse(**created_knowledge_base),
        "success": True
    }

@router.get("/knowledge_bases", response_model=Dict[str, Any])
async def get_knowledge_bases(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    name: Optional[str] = None,
    description: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {"is_active": True}
    
    if name:
        query["name"] = {"$regex": name, "$options": "i"}
    if description:
        query["description"] = {"$regex": description, "$options": "i"}

    # 获取知识库列表
    knowledge_bases = await db["knowledge_bases"].find(query).sort(
        "created_at", -1
    ).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 获取总数
    total = await db["knowledge_bases"].count_documents(query)
    
    # 计算文件总数和文本块总数
    total_files = sum(kb.get("file_count", 0) for kb in knowledge_bases)
    total_chunks = sum(kb.get("chunk_count", 0) for kb in knowledge_bases)
    
    # 处理每个知识库对象，确保有必要的字段
    for knowledge_base in knowledge_bases:
        knowledge_base["id"] = str(knowledge_base["_id"]) 
        del knowledge_base["_id"]
        
        # 确保每个知识库对象都有name字段，如果没有则设置一个默认值
        if "name" not in knowledge_base or knowledge_base["name"] is None:
            knowledge_base["name"] = "未命名知识库"
            logger.warning(f"知识库缺少name字段，已设置默认值: {knowledge_base['id']}")
            
    try:
        response_data = [KnowledgeBaseResponse(**kb) for kb in knowledge_bases]
        return {
            "data": response_data,
            "total": total,
            "total_files": total_files,
            "total_chunks": total_chunks,
            "success": True,
            "pageSize": pageSize,
            "current": current
        }
    except Exception as e:
        logger.error(f"构建知识库响应对象失败: {str(e)}")
        logger.error(f"问题数据: {knowledge_bases}")
        raise HTTPException(status_code=500, detail=f"构建知识库响应对象失败: {str(e)}")

# 获取知识库列表
@router.get("/my_knowledge_bases", response_model=Dict[str, Any])
async def get_my_knowledge_bases(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    name: Optional[str] = None,
    description: Optional[str] = None,
    file_type: Optional[str] = Query(None, description="文件类型"),  # 新增文件类型检索参数
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {"is_active": True, "user_id": current_user["id"]}
    
    if name:
        query["name"] = {"$regex": name, "$options": "i"}
    if description:
        query["description"] = {"$regex": description, "$options": "i"}
    if file_type:
        # 假设知识库表里有 file_types 字段（数组），否则请根据实际字段调整
        query["file_types"] = {"$in": [file_type]}

    logger.info(f"查询条件: {query}")
    knowledge_bases = await db["knowledge_bases"].find(query).sort(
        "created_at", -1
    ).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 获取总数
    total = await db["knowledge_bases"].count_documents(query)
    
    # 计算文件总数和文本块总数
    total_files = sum(kb.get("file_count", 0) for kb in knowledge_bases)
    total_chunks = sum(kb.get("chunk_count", 0) for kb in knowledge_bases)

    # 处理每个知识库对象，确保有必要的字段
    for knowledge_base in knowledge_bases:
        knowledge_base["id"] = str(knowledge_base["_id"]) 
        del knowledge_base["_id"]
        
        # 确保每个知识库对象都有name字段，如果没有则设置一个默认值
        if "name" not in knowledge_base or knowledge_base["name"] is None:
            knowledge_base["name"] = "未命名知识库"
            logger.warning(f"知识库缺少name字段，已设置默认值: {knowledge_base['id']}")

    logger.info(f"查询到的知识库数量: {len(knowledge_bases)}")
    
    try:
        response_data = [KnowledgeBaseResponse(**kb) for kb in knowledge_bases]
        return {
            "data": response_data,
            "total": total,
            "total_files": total_files,
            "total_chunks": total_chunks,
            "success": True,
            "pageSize": pageSize,
            "current": current
        }
    except Exception as e:
        logger.error(f"构建知识库响应对象失败: {str(e)}")
        logger.error(f"问题数据: {knowledge_bases}")
        raise HTTPException(status_code=500, detail=f"构建知识库响应对象失败: {str(e)}")

# 获取单个知识库
@router.get("/knowledge_bases/{knowledge_base_id}", response_model=Dict[str, Any])
async def get_knowledge_base(
    knowledge_base_id: str,
    current_user: dict = Depends(verify_token)
):
    knowledge_base = await db["knowledge_bases"].find_one({"_id": ObjectId(knowledge_base_id)})
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    # 添加 id 字段并转换 _id
    knowledge_base["id"] = str(knowledge_base["_id"])
    del knowledge_base["_id"]  # 删除 _id 字段，因为我们已经有了 id
    return {
            "data": knowledge_base,
            "success": True
        }
    # The line `return KnowledgeBaseResponse(**knowledge_base)` is attempting to create an instance of
    # the `KnowledgeBaseResponse` model by unpacking the `knowledge_base` dictionary as keyword
    # arguments.
    

# 更新知识库
@router.put("/knowledge_base/{knowledge_base_id}", response_model=Dict[str, Any])
async def update_knowledge_base(
    knowledge_base_id: str,
    knowledge_base: KnowledgeBaseUpdate,
    current_user: dict = Depends(verify_token)
):
    # 从请求体中获取更新数据
    update_data = knowledge_base.dict(exclude_unset=True)
    update_data["last_updated"] = datetime.now()

    print(f"update_data: {update_data}")
    
    # 确保布尔值字段的正确类型
    if "basic_index" in update_data:
        update_data["basic_index"] = bool(update_data["basic_index"])
    if "graph_index" in update_data:
        update_data["graph_index"] = bool(update_data["graph_index"])
    if "semantic_index" in update_data:
        update_data["semantic_index"] = bool(update_data["semantic_index"])
        
    # 确保embedding_model_id为整数
    if "embedding_model_id" in update_data:
        update_data["embedding_model_id"] = int(update_data["embedding_model_id"])
    if "reRank_model_id" in update_data:
        update_data["reRank_model_id"] = int(update_data["reRank_model_id"])
    if "embedding_model" in update_data:
        update_data["embedding_model"] = str(update_data["embedding_model"])
    if "reRank_model" in update_data:
        update_data["reRank_model"] = str(update_data["reRank_model"])
    
    # 处理标签字段
    if "tags" in update_data:
        # 确保标签是字符串列表
        if isinstance(update_data["tags"], str):
            update_data["tags"] = [tag.strip() for tag in update_data["tags"].split(",")]
        elif isinstance(update_data["tags"], list):
            update_data["tags"] = [str(tag) for tag in update_data["tags"]]
        else:
            update_data["tags"] = []
    
    result = await db["knowledge_bases"].update_one(
        {"_id": ObjectId(knowledge_base_id)},
        {"$set": update_data}
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
        
    updated_knowledge_base = await db["knowledge_bases"].find_one(
        {"_id": ObjectId(knowledge_base_id)}
    )
    
    # 确保返回的数据包含id字段
    updated_knowledge_base["id"] = str(updated_knowledge_base["_id"])
    del updated_knowledge_base["_id"]
    
    return {
        "data": KnowledgeBaseResponse(**updated_knowledge_base),
        "success": True
    }

# 删除知识库（软删除）
@router.delete("/knowledge_bases/{knowledge_base_id}", response_model=dict)
async def delete_knowledge_base(
    knowledge_base_id: str,
    current_user: dict = Depends(verify_token)
):
    result = await db["knowledge_bases"].update_one(
        {"_id": ObjectId(knowledge_base_id)},
        {
            "$set": {
                "is_active": False,
                "deleted_at": datetime.now(),
                "deleted_by": current_user["id"]
            }
        }
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
        
    return {"message": "Knowledge base deleted successfully"}



############file上传文件接口


@router.post("/updateKnowledgeFile", response_model=List[KnowledgeFileResponse])
async def upload_files(
    files: List[UploadFile] = File(...),
    knowledgeId: str = Form(...),
    parentId: Optional[str] = Form(None),
    ocr: str = Form(...),
    parserType: str = Form(...),
    oldfileid: Optional[str] = Form(None),
    current_user: dict = Depends(verify_token),
    files_source_type: Optional[str] = Form(None),
    chunk_method: Optional[str] = Form("semantic"),
    chunk_size: Optional[int] = Form(500),
    overlap_size: Optional[int] = Form(50)
):
    uploaded_files = []
    logger.info(f"upload_files: {files}, {knowledgeId}, {ocr}, {parserType}")
    try:
        # 如果存在oldfileid，先删除旧文件
        if oldfileid:
            await delete_file(oldfileid, current_user)
            
        for file in files:
            # 如果不存在oldfileid，才进行文件名相似度校验
            if not oldfileid:
                all_files = await db["source_files"].find({
                    "knowledge_base_id": ObjectId(knowledgeId)
                }).to_list(None)
                
                for existing in all_files:
                    similarity = SequenceMatcher(None, file.filename, existing["name"]).ratio()
                    if similarity > 0.99:
                        raise HTTPException(
                            status_code=400,
                            detail=f"与已有的文件重名。"
                        )
                    
            content = await file.read()
            file_size = len(content)
            
            # 生成唯一的文件名，避免文件名冲突
            file_ext = os.path.splitext(file.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_ext}"

            if settings.MINIO_ENABLE:
                # 上传到MinIO
                file_url = minio.upload_bytes(
                    data=content,
                    object_name=unique_filename,
                    content_type=file.content_type
                )
            else:
                logger.info(f"MinIO 未启用，将文件上传到本地: {settings.FILE_UPLOAD_PATH}/{unique_filename}")
                file_url = f"{settings.FILE_UPLOAD_PATH}/{unique_filename}"
                with open(file_url, "wb") as f:
                    f.write(content)
            
            file_format = get_file_format(file.filename)
            
            new_file = {
                "name": file.filename,
                "storage_path": file_url,  # 只存储文件名
                "storage_type": FileStorageType.MINIO if settings.MINIO_ENABLE else FileStorageType.LOCAL,
                "knowledge_base_id": ObjectId(knowledgeId),
                "data_type": file_format,
                "processing_status": "pending",
                "size": file_size,
                "flg": FileStatus.WAITING.value,
                "created_at": datetime.now(),
                "user_id": current_user["id"],
                "user_name": current_user["name"],
                "ocr": int(ocr),
                "type": "file",
                "forbidden": False,
                "parentId": ObjectId(parentId) if parentId else None,
                "parser_type": parserType,
                "files_source_type": files_source_type if files_source_type else "kb",
                "chunk_method": chunk_method,  # 切片方式
                "chunk_size": chunk_size,
                "overlap_size": overlap_size
            }
            logger.info(f"new_file: {new_file}")
            
            result = await db["source_files"].insert_one(new_file)
            await db["knowledge_bases"].update_one({"_id": ObjectId(knowledgeId)}, {"$inc": {"file_count": 1}})
            uploaded_files.append(SourceFileResponse(
                id=str(result.inserted_id),
                name=new_file["name"],
                data_type=new_file["data_type"],
                size=file_size,
                url=file_url,
                deleteUrl=file_url,
                deleteType='DELETE'
            ))

    except Exception as e:
        traceback.print_exc()
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

# 对话时上传文件

@router.post("/updateKnowledgeFileFromChat", response_model=List[KnowledgeFileResponse])
async def upload_files_from_chat(
    files: List[UploadFile] = File(...),
    knowledgeId: str = Form(...),
    parentId: Optional[str] = Form(None),
    ocr: str = Form(...),
    parserType: Optional[str] = Form(default="default"),
    oldfileid: Optional[str] = Form(None),
    current_user: dict = Depends(verify_token),
    files_source_type: Optional[str] = Form(None)
):
    uploaded_files = []
    logger.info(f"upload_files: {files}, {knowledgeId}, {ocr}, {parserType}")
    try:
        # 如果存在oldfileid，先删除旧文件
        if oldfileid:
            await delete_file(oldfileid, current_user)
            
        for file in files:
            # 如果不存在oldfileid，才进行文件名相似度校验
            if not oldfileid:
                all_files = await db["source_files"].find({
                    "knowledge_base_id": ObjectId(knowledgeId)
                }).to_list(None)
                
                for existing in all_files:
                    similarity = SequenceMatcher(None, file.filename, existing["name"]).ratio()
                    if similarity > 0.99:
                        raise HTTPException(
                            status_code=400,
                            # detail=f"文件 '{file.filename}' 与已存在文件 '{existing['name']}' 相似度过高（{similarity:.2f}），请确认是否重复上传"
                            detail=f"文件 '{file.filename}' ,与已有的文件重名。"
                        )
                    
            content = await file.read()
            file_size = len(content)
            
            # 生成唯一的文件名，避免文件名冲突
            file_ext = os.path.splitext(file.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_ext}"

            if settings.MINIO_ENABLE:
                # 上传到MinIO
                file_url = minio.upload_bytes(
                    data=content,
                    object_name=unique_filename,
                    content_type=file.content_type
                )
            else:
                logger.info(f"MinIO 未启用，将文件上传到本地: {settings.FILE_UPLOAD_PATH}/{unique_filename}")
                file_url = f"{settings.FILE_UPLOAD_PATH}/{unique_filename}"
                with open(file_url, "wb") as f:
                    f.write(content)
            
            file_format = get_file_format(file.filename)
            
            new_file = {
                "name": file.filename,
                "storage_path": file_url,  # 只存储文件名
                "storage_type": FileStorageType.MINIO if settings.MINIO_ENABLE else FileStorageType.LOCAL,
                "knowledge_base_id": ObjectId(knowledgeId),
                "data_type": file_format,
                "processing_status": "pending",
                "size": file_size,
                "flg": FileStatus.WAITING.value,
                "created_at": datetime.now(),
                "user_id": current_user["id"],
                "user_name": current_user["name"],
                "ocr": int(ocr),
                "type": "file",
                "parentId": ObjectId(parentId) if parentId else None,
                "parser_type": parserType,
                "files_source_type": files_source_type if files_source_type else "kb"
            }
            logger.info(f"new_file: {new_file}")
            # 先根据knowledgeId判断knowledge_bases表中是否有该knowledgeId的记录
            kb_info = await db["knowledge_bases"].find_one({"_id": ObjectId(knowledgeId)})
            if not kb_info:
                # 如果没有，先创建knowledge_bases记录
                new_knowledge_base = {}
                new_knowledge_base["_id"] = ObjectId(knowledgeId)
                new_knowledge_base["created_at"] = datetime.now()
                new_knowledge_base["last_updated"] = datetime.now()
                new_knowledge_base["user_id"] = current_user["id"]
                new_knowledge_base["user_name"] = current_user["name"]
                new_knowledge_base["is_active"] = True
                new_knowledge_base["embedding_model_id"] = 2
                new_knowledge_base["embedding_model"] = "bge-m3"
                new_knowledge_base["basic_index"] = True
                new_knowledge_base["graph_index"] = False
                new_knowledge_base["semantic_index"] = False
                new_knowledge_base["is_conversation_kb"] = True
                logger.info('插入对话只四库')
                await db["knowledge_bases"].insert_one(new_knowledge_base)
            result = await db["source_files"].insert_one(new_file)
            await db["knowledge_bases"].update_one({"_id": ObjectId(knowledgeId)}, {"$inc": {"file_count": 1}})
            uploaded_files.append(SourceFileResponse(
                id=str(result.inserted_id),
                name=new_file["name"],
                data_type=new_file["data_type"],
                size=file_size,
                url=file_url,
                deleteUrl=file_url,
                deleteType='DELETE'
                ))


    except Exception as e:
        traceback.print_exc()
        logger.error(f"对话文件上传失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"对话文件上传失败: {str(e)}")


# 添加目录
@router.post("/updateKnowledgeFolder", response_model=List[KnowledgeFileResponse])
async def update_folder(
    request: UploadFolderRequest,
    current_user: dict = Depends(verify_token)
):
    folder_name = request.folder_name
    knowledgeId = request.knowledgeId
    parentId = request.parentId
    action = request.action  # 新增：操作类型 add/update
    folder_id = request.folder_id
    
    try:
        # 查找现有文件夹
        existing_folder = await db["source_files"].find_one({
            "knowledge_base_id": ObjectId(knowledgeId),
            "parentId": ObjectId(parentId) if parentId else "",
            "name": folder_name,
            "type": "folder"
        })
        
        if action == "add":
            # 查找现有文件夹
            existing_folder = await db["source_files"].find_one({
                "knowledge_base_id": ObjectId(knowledgeId),
                "parentId": ObjectId(parentId) if parentId else "",
                "name": folder_name,
                "type": "folder"
            })
            if existing_folder:
                raise HTTPException(status_code=400, detail="该目录下已存在同名文件夹")
                
            # 创建新文件夹
            new_folder = {
                "name": folder_name,
                "storage_path": "",
                "storage_type": "",
                "knowledge_base_id": ObjectId(knowledgeId),
                "data_type": "folder",
                "processing_status": "completed",
                "size": 0,
                "flg": FileStatus.COMPLETED.value,
                "created_at": datetime.now(),
                "user_id": current_user["id"],
                "user_name": current_user["name"],
                "ocr": 0,
                "type": "folder",
                "parentId": ObjectId(parentId) if parentId else "",
            }
            
            result = await db["source_files"].insert_one(new_folder)
            folder_id = str(result.inserted_id)
            
        elif action == "update":
            existing_folder = await db["source_files"].find_one({
                "_id": ObjectId(folder_id),
                "type": "folder"
            })
            if not existing_folder:
                raise HTTPException(status_code=404, detail="文件夹不存在")
                
            # 更新文件夹信息
            await db["source_files"].update_one(
                {"_id": existing_folder["_id"]},
                {"$set": {"name": folder_name}}
            )
            folder_id = str(existing_folder["_id"])
            
            
        else:
            raise HTTPException(status_code=400, detail="无效的操作类型")
            
        return [SourceFileResponse(
            id=folder_id,
            name=folder_name,
            data_type="folder",
            size=0,
            url="",
            deleteUrl="",
            deleteType='DELETE'
        )]
        
    except Exception as e:
        traceback.print_exc()
        logger.error(f"文件夹操作失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件夹操作失败: {str(e)}")
    
# 修改标签
@router.post("/updateKnowledgeTags", response_model=Dict[str, Any])
async def update_tags(
    request: Dict[str, Any],
    current_user: dict = Depends(verify_token)
):
    try:
        file_id = request.get("file_id")
        tags = request.get("tags", [])
        # 更新文件标签
        result = await db["source_files"].update_one(
            {"_id": ObjectId(file_id)},
            {"$set": {"tags": tags}}
        )
        
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="文件未找到")
            
        return {
            "success": True,
            "message": "标签更新成功"
        }
        
    except Exception as e:
        traceback.print_exc()
        logger.error(f"标签更新失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"标签更新失败: {str(e)}")
    
# 移动目录
@router.post("/updateKnowledgeMove", response_model=Dict[str, Any])
async def update_Move(
    request: Dict[str, Any],
    current_user: dict = Depends(verify_token)
):
    try:
        file_id = request.get("file_id")
        parent_id = request.get("parent_id")
        # 更新文件父目录
        result = await db["source_files"].update_one(
            {"_id": ObjectId(file_id)},
            {"$set": {"parentId": ObjectId(parent_id) if parent_id else ""}}
        )
        
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="文件未找到")
            
        return {
            "success": True,
            "message": "文件目录更新成功"
        }
        
    except Exception as e:
        traceback.print_exc()
        logger.error(f"文件目录更新失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件目录更新失败: {str(e)}")
    
# 下架知识
@router.post("/updateKnowledgeForbid", response_model=Dict[str, Any])
async def update_Forbid(
    request: Dict[str, Any],
    current_user: dict = Depends(verify_token)
):
    try:
        file_id = request.get("file_id")
        is_forbidden = request.get("forbidden")

        logger.info(f"update_Forbid: {file_id}, {is_forbidden}")
        # 更新文件禁用状态
        result = await db["source_files"].update_one(
            {"_id": ObjectId(file_id)},
            {"$set": {"forbidden": is_forbidden}}
        )
        logger.info(f"update_Forbid: {result}")

        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="文件未找到")

        # 获取文件信息
        file_info = await db["source_files"].find_one({"_id": ObjectId(file_id)})
        if not file_info:
            raise HTTPException(status_code=404, detail="文件未找到")

        # 更新关联的 chunks 禁用状态
        chunks_result = await db["chunks"].update_many(
            {"file_id": ObjectId(file_id)},
            {"$set": {"is_expired": is_forbidden}}
        )

        # 更新关联的 chunks_index 禁用状态
        chunks_index_result = await db["chunks_index"].update_many(
            {"file_id": ObjectId(file_id)},
            {"$set": {"is_expired": is_forbidden}}
        )

        # 更新 Elasticsearch 中的禁用状态
        last_updated = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        re = update_by_query(file_id, last_updated, is_forbidden, settings.ES_HOST, settings.ES_INDEX)
        logger.info(re)

        return {
            "success": True,
            "message": "文件禁用状态更新成功",
            "source_files_matched": result.matched_count,
            "source_files_modified": result.modified_count,
            "chunks_matched": chunks_result.matched_count,
            "chunks_modified": chunks_result.modified_count,
            "chunks_index_matched": chunks_index_result.matched_count,
            "chunks_index_modified": chunks_index_result.modified_count,
            "es_result": re
        }
        
    except Exception as e:
        traceback.print_exc()
        logger.error(f"文件禁用状态更新失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件禁用状态更新失败: {str(e)}")

# 创建知识库
@router.get("/knowledge_files/{knowledge_base_id}", response_model=Dict[str, Any])
async def get_knowledge_files(
    knowledge_base_id: str,
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    name: Optional[str] = None,
    processing_status: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    
    logger.info(f"get_knowledge_files: {knowledge_base_id}, {current}, {pageSize}, {name}, {processing_status}")
    skip = (current - 1) * pageSize
    query = {"knowledge_base_id": ObjectId(knowledge_base_id)}
    
    if name:
        query["name"] = {"$regex": name, "$options": "i"}
    if processing_status:
        query["processing_status"] = processing_status

    # 获取知识库列表
    knowledge_files = await db["source_files"].find(query).sort(
        "created_at", -1
    ).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 获取总数
    total = await db["knowledge_bases"].count_documents(query)
    
    # 计算文件总数和文本块总数
    for f in knowledge_files:
        f["id"] = str(f["_id"]) 
        del f["_id"]
    return {
        "data": [SourceFileBase(**kb) for kb in knowledge_files],
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

@router.post("/knowledge_folder_tree/{knowledge_base_id}", response_model=Dict[str, Any])
async def get_knowledge_folder_tree(
    knowledge_base_id: str,
    request: Dict[str, Any],
    current_user: dict = Depends(verify_token)
):
    try:
        logger.error(f"获取文件目录树失败: {knowledge_base_id}")
        name = request.get("name")
        tags = request.get("tags")
        only_folder = request.get("only_folder", False)
        forbidden = request.get("forbidden", None)
        data_type = request.get("data_type", None)
        flg = request.get("flg", None)
        # 查询所有文件和文件夹
        query = {"knowledge_base_id": ObjectId(knowledge_base_id)}
        
        # 添加name正则匹配查询条件
        if name:
            query["name"] = {"$regex": name, "$options": "i"}
            
        # 添加tags列表查询条件
        if tags:
            query["tags"] = {"$all": tags}
        
        # 添加forbidden查询条件
        if forbidden is not None:
            query["forbidden"] = forbidden

        # 添加data_type查询条件
        if data_type:
            query["data_type"] = data_type

        # 添加flg查询条件
        if flg is not None:
            query["flg"] = flg

        # 如果only_folder为True，只查询文件夹
        if only_folder:
            query["type"] = "folder"


        logger.info(f"query: {query}")
        all_items = await db["source_files"].find(query).to_list(length=None)
        # logger.info(f"查询到的所有文件和文件夹: {all_items}")
        
        # 构建树形结构
        def build_tree(items, parent_id=None):
            tree = []
            for item in items:
                # 如果parentId不存在或为空，且当前parent_id为None，则作为根节点处理
                if (not item.get("parentId") and parent_id is None) or str(item.get("parentId", "")) == str(parent_id):
                    item["id"] = str(item["_id"])
                    del item["_id"]
                    # 当type不存在或为folder时，才添加children属性
                    if item.get("type", "file") == "folder":
                        item["children"] = build_tree(items, item["id"])
                    tree.append(item)
            return tree
        
        # 转换数据并构建树
        tree_data = build_tree([{**item, "knowledge_base_id": str(item["knowledge_base_id"]), "id": str(item["_id"]), "parentId": str(item.get("parentId", ""))} for item in all_items])
        
        return {
            "data": tree_data,
            "success": True
        }
        
    except Exception as e:
        traceback.print_exc()
        logger.error(f"获取文件目录树失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文件目录树失败: {str(e)}")

# 添加新的预览接口
@router.get("/knowledge_file_preview/{file_id}")
async def preview_file(
    file_id: str,
    download: bool = Query(False, description="Force download instead of preview"),
    current_user: dict = Depends(verify_token)
):
    try:
        file_info = await db["source_files"].find_one({"_id": ObjectId(file_id)})
        if not file_info:
            raise HTTPException(status_code=404, detail="File not found")
            
        storage_path = file_info.get("storage_path")
        original_filename = file_info.get("name")  # 获取原始文件名
        if not original_filename:
            raise HTTPException(status_code=400, detail="File name not found")
            
        logger.info(f"original_filename: {original_filename}")
        
        if not storage_path:
            raise HTTPException(status_code=404, detail="File path not found")

        # 获取文件MIME类型
        mime_type = mimetypes.guess_type(original_filename)[0] or "application/octet-stream"

        # 设置 Content-Disposition
        disposition_type = 'attachment' if download else 'inline'
        # 对文件名进行URL编码，解决特殊字符问题
        encoded_filename = quote(original_filename.encode('utf-8'))
        content_disposition = f'{disposition_type}; filename="{encoded_filename}"; filename*=UTF-8\'\'{encoded_filename}'
        
        logger.info(f"Content-Disposition: {content_disposition}")

        if settings.MINIO_ENABLE:
            try:
                async with httpx.AsyncClient(timeout=30.0) as client:
                    try:
                        response = await client.get(storage_path)
                        if response.status_code == 403:  # URL已过期
                            object_name = storage_path.split('/')[-1].split('?')[0]
                            storage_path = minio.client.presigned_get_object(
                                settings.MINIO_BUCKET,
                                object_name,
                                expires=3600
                            )
                            response = await client.get(storage_path)
                            
                        response.raise_for_status()
                        
                        return StreamingResponse(
                            content=response.iter_bytes(),
                            media_type=mime_type,
                            headers={
                                "Content-Disposition": content_disposition,
                                "Cache-Control": "no-cache",
                                "X-Content-Type-Options": "nosniff",
                            }
                        )
                    except httpx.HTTPError as http_err:
                        logger.error(f"HTTP error occurred: {str(http_err)}")
                        raise HTTPException(status_code=500, detail=f"Failed to download file: {str(http_err)}")
                        
            except Exception as e:
                logger.error(f"Download error: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to download file: {str(e)}")
        else:
            # 本地文件处理部分也使用原始文件名
            file_path = os.path.join(settings.FILE_UPLOAD_PATH, os.path.basename(storage_path))
            if not os.path.exists(file_path):
                raise HTTPException(status_code=404, detail="File not found on disk")
            
            if not os.path.isfile(file_path):
                raise HTTPException(status_code=400, detail="Invalid file path")
                
            async def file_iterator():
                async with aiofiles.open(file_path, 'rb') as f:
                    while chunk := await f.read(8192):
                        yield chunk
            
            return StreamingResponse(
                content=file_iterator(),
                media_type=mime_type,
                headers={
                    "Content-Disposition": content_disposition,
                    "Cache-Control": "no-cache",
                    "X-Content-Type-Options": "nosniff",
                }
            )
            
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error previewing file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error previewing file: {str(e)}")



@router.delete("/source_files/{file_id}")
async def delete_file(
    file_id: str,
    current_user: dict = Depends(verify_token)
):
    try:
        # 获取文件信息
        file_info = await db["source_files"].find_one({"_id": ObjectId(file_id)})
        if not file_info:
            raise HTTPException(status_code=404, detail="File not found")

        storage_path = file_info.get("storage_path")
        
        # 删除MinIO中的文件
        if settings.MINIO_ENABLE and storage_path:
            try:
                object_name = storage_path.split('/')[-1].split('?')[0]
                minio.client.remove_object(settings.MINIO_BUCKET, object_name)
            except Exception as e:
                logger.error(f"Error deleting file from MinIO: {str(e)}")
                # 继续执行，即使MinIO删除失败
        
        # 删除本地文件
        elif storage_path:
            file_path = os.path.join(settings.FILE_UPLOAD_PATH, os.path.basename(storage_path))
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except Exception as e:
                    logger.error(f"Error deleting local file: {str(e)}")
                    # 继续执行，即使本地文件删除失败

        # todo 删除文件切块数据

        # 从数据库中删除记录
        result = await db["source_files"].delete_one({"_id": ObjectId(file_id)})
        await db["knowledge_bases"].update_one({"_id": ObjectId(file_info.get("knowledge_base_id"))}, {"$inc": {"file_count": -1}})
        
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="File not found in database")

        return {"success": True, "message": "File deleted successfully"}

    except Exception as e:
        logger.error(f"Error deleting file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete file: {str(e)}")

################chunk



# chunk 接口
@router.get("/knowledge-base/{knowledge_base_id}/chunk", response_model=Dict[str, Any])
async def get_knowledge_chunks(
    knowledge_base_id: str,
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    title: Optional[str] = None,
    content: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {"knowledge_base_id": ObjectId(knowledge_base_id)}
    
    if title:
        query["title"] = {"$regex": title, "$options": "i"}
    if content:
        query["content"] = {"$regex": content, "$options": "i"}

    chunks = await db["knowledge_chunks"].find(query).skip(skip).limit(pageSize).to_list(length=pageSize)
    total = await db["knowledge_chunks"].count_documents(query)
    
    for chunk in chunks:
        chunk["id"] = str(chunk["_id"])
        del chunk["_id"]
    
    return {
        "data": chunks,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

@router.post("/knowledge-base/{knowledge_base_id}/chunk", response_model=Dict[str, Any])
async def create_knowledge_chunk(
    knowledge_base_id: str,
    chunk: dict,
    current_user: dict = Depends(verify_token)
):
    # 验证必要字段
    required_fields = ['answer', 'question']
    for field in required_fields:
        if field not in chunk:
            raise HTTPException(status_code=400, detail=f"Missing required field: {field}")

    # 类型转换
    chunk_data = {
        "file_id": ObjectId(chunk["file_id"]) if chunk.get("file_id") else None,
        "page_id": ObjectId(chunk["page_id"]) if chunk.get("page_id") else None,
        "knowledge_base_id": ObjectId(knowledge_base_id),
        "answer": chunk.get("answer",None),
        "question": chunk.get("question",None),
        "chunk_type": chunk.get("chunk_type",chunk_type.BASE),
        "is_expired": bool(chunk.get("is_expired", False)),
        "created_at": datetime.now()
    }
    
    await db["chunks"].insert_one(chunk_data)
    return {"success": True}

# 知识更新
@router.put("/knowledge-base/chunk/{chunk_id}", response_model=Dict[str, Any])
async def update_knowledge_chunk(
    chunk_id: str,
    data: dict,
    current_user: dict = Depends(verify_token)
):
    try:
    
    # 允许更新的字段
        updatable_fields = {"last_updated":datetime.now()}
        updated_chunk = await db["chunks"].find_one({"_id": ObjectId(chunk_id)})
        index_content = ''

        if 'answer' in data:
            updatable_fields['answer'] = data['answer']
            index_content += data['answer']
        else:
            index_content += updated_chunk.get('answer')

        if 'question' in data:
            updatable_fields['question'] = data['question']
            index_content += data['question']
        else:
            index_content += updated_chunk.get('question')
        
        tokens_count = len(enc.encode(index_content))
        updatable_fields['tokens_count'] = tokens_count

        await db["chunks"].update_one(
            {"_id": ObjectId(chunk_id)},
            {"$set": updatable_fields}
        )
        logger.info(f"updated_chunk: {updated_chunk}")

        kb_info = await db["knowledge_bases"].find_one({"_id": ObjectId(updated_chunk.get("knowledge_base_id"))})
        
        # 检查kb_info是否为None
        if kb_info is None:
            raise HTTPException(status_code=404, detail="Knowledge base not found for this chunk")
            
        embedding_id = int(kb_info.get("embedding_model_id", None)) if kb_info.get("embedding_model_id") else None
        if not embedding_id:
            raise Exception("embedding_model_id is not set")
        embedding_config = await db["embeddings"].find_one({"id": embedding_id})
        if not embedding_config:
            raise Exception("embedding_model is not exist")
        embedding = get_embedding(index_content, embedding_config)

        chunk_index_obj = {
                        "file_id": updated_chunk['file_id'] if updated_chunk['file_id'] else '',
                        "chunk_id":updated_chunk['_id'],
                        "knowledge_base_id":updated_chunk['knowledge_base_id'],
                        "index_content":index_content,
                        "chunk_type":chunk_type.BASE,
                        "embedding":embedding
        }
        insert_result = await db.chunks_index.insert_one(chunk_index_obj)
        index_id = str(insert_result.inserted_id)
        
        es_update = {
                "id": index_id,
                "chunk_id":str(chunk_id),
                "file_id": str(chunk_index_obj['file_id']),
                "knowledge_base_id": str(kb_info["_id"]),
                "index_content": chunk_index_obj.get('index_content'),
                "embedding": embedding,
                "chunk_type": chunk_type.BASE,
                "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }
        
        try:
            logger.info(f"插入ES数据: {es_update}")
            re = es_data_ingester(str(index_id), es_update, settings.ES_HOST, settings.ES_INDEX)
            logger.info(f"ES插入结果: {re}")
        except Exception as e:
            traceback.print_exc()
            logger.error(f"ES数据插入失败: {str(e)}")
            # 即使ES插入失败，也返回成功，因为MongoDB操作已成功
            return {"success": True, "message": "索引已保存，但搜索引擎同步失败，可能影响检索"}

        # 将updated_chunk中的ObjectId转换为字符串
        updated_chunk["_id"] = str(updated_chunk["_id"])
        if "knowledge_base_id" in updated_chunk:
            updated_chunk["knowledge_base_id"] = str(updated_chunk["knowledge_base_id"])
        if "file_id" in updated_chunk:
            updated_chunk["file_id"] = str(updated_chunk["file_id"])
        return {"data": updated_chunk, "success": True}
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"批量重置失败: {str(e)}")

@router.delete("/knowledge-base/chunk/{chunk_id}")
async def delete_knowledge_chunk(
    chunk_id: str,
    current_user: dict = Depends(verify_token)
):
    logger.info(f"尝试删除chunk: {chunk_id}")
    try:
        # 获取要删除的chunk信息
        chunk = await db["chunks"].find_one({"_id": ObjectId(chunk_id)})
        logger.info(f"查询到的chunk: {chunk}")
        if not chunk:
            logger.error(f"未找到chunk: {chunk_id}")
            raise HTTPException(status_code=404, detail=f"Chunk not found: {chunk_id}")
        
        # 删除chunk
        result = await db["chunks"].delete_one({"_id": ObjectId(chunk_id)})
        logger.info(f"删除chunk结果: {result.deleted_count}")
        
        # 删除关联的chunk索引
        index_result = await db["chunks_index"].delete_many({"chunk_id": ObjectId(chunk_id)})
        logger.info(f"删除chunk索引结果: {index_result.deleted_count}")

        # 删除es数据
        try:
            es_data_deleter(
                ids=[str(chunk_id)],
                chunk_type=chunk_type.BASE,
                kb_id=str(chunk["knowledge_base_id"]),
                es_host=settings.ES_HOST,
                index_name=settings.ES_INDEX
            )
            logger.info(f"ES数据删除成功")
        except Exception as e:
            logger.error(f"ES数据删除失败: {str(e)}")
        
        # 更新知识库的chunk_count和index_count
        if "knowledge_base_id" in chunk:
            kb_update = await db["knowledge_bases"].update_one(
                {"_id": chunk["knowledge_base_id"]},
                {"$inc": {"chunk_count": -1, "index_count": -index_result.deleted_count}}
            )
            logger.info(f"更新知识库计数结果: {kb_update.modified_count}")
        
        return {"success": True, "message": "Chunk deleted successfully"}
    except Exception as e:
        logger.error(f"删除chunk异常: {str(e)}")
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"Failed to delete chunk: {str(e)}")



@router.post("/knowledge-base/chunk/batchDelete")
async def batch_reset_source_files(
    ids: List[str],
    current_user: dict = Depends(verify_token)
):
    try:
        object_ids = [ObjectId(id) for id in ids]
        total_deleted_chunk = 0
        total_deleted_index = 0
        
       
        
        # 删除关联的chunks
        chunks_result = await db.chunks.delete_many(
            {"_id": {"$in": object_ids}}
        )
        total_deleted_chunk = chunks_result.deleted_count
        
        # 删除chunk_index
        chunks_index_result = await db.chunks_index.delete_many(
            {"chunk_id": {"$in": object_ids}}
        )
        total_deleted_index = chunks_index_result.deleted_count
        
        return {
            "success": True,
            "deleted_chunk": total_deleted_chunk,
            "deleted_chunk_index": total_deleted_index
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量重置失败: {str(e)}")





@router.get("/knowledge-base/chunk/{knowledgeBaseId}", response_model=Dict[str, Any])
async def get_knowledge_chunk(
    knowledgeBaseId: str,
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    file_name: Optional[str] = None,
    answer: Optional[str] = None,
    question: Optional[str] = None,
    file_id: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {"knowledge_base_id": ObjectId(knowledgeBaseId)}
    
    if file_name:
        query["file_name"] = {"$regex": file_name, "$options": "i"}
    if answer:
        query["answer"] = {"$regex": answer, "$options": "i"}
    if question:
        query["question"] = {"$regex": question, "$options": "i"}
    if file_id:
        query["file_id"] = ObjectId(file_id)

    chunks = await db["chunks"].find(query,{
        "file_name":1,
        "answer":1,
        "question":1,
        "tokens_count":1,
        "chunk_type":1,
        "created_at":1,
        "file_id":1
    }).skip(skip).limit(pageSize).to_list(length=pageSize)
    total = await db["chunks"].count_documents(query)
    
    for index in chunks:
        index["id"] = str(index["_id"])
        del index["_id"]
        if "created_at" in index:
            index["created_at"] = index["created_at"].strftime("%Y-%m-%d %H:%M:%S")  # 格式化为指定字符串格式
        if "file_id" in index:
            index["file_id"] = str(index["file_id"])
    
    return {
        "data": chunks,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 知识块索引相关接口

@router.get("/source_file/chunk/index/{chunk_id}", response_model=Dict[str, Any])
async def get_knowledge_chunk_index(
    chunk_id: str,
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(100, description="每页数量"),
    current_user: dict = Depends(verify_token)
):
    """
    获取指定chunk_id下的所有chunk索引数据
    """
    skip = (current - 1) * pageSize
    query = {"chunk_id": ObjectId(chunk_id)}
    
    # 查询索引数据
    indexes = await db["chunks_index"].find(query).skip(skip).limit(pageSize).to_list(length=pageSize)
    total = await db["chunks_index"].count_documents(query)
    
    # 格式化返回数据
    for index in indexes:
        index["id"] = str(index["_id"])
        del index["_id"]
        if "created_at" in index:
            index["created_at"] = index["created_at"].strftime("%Y-%m-%d %H:%M:%S")
        if "file_id" in index:
            index["file_id"] = str(index["file_id"])
        if "knowledge_base_id" in index:
            index["knowledge_base_id"] = str(index["knowledge_base_id"])
        if "chunk_id" in index:
            index["chunk_id"] = str(index["chunk_id"])
    
    return {
        "data": indexes,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }


@router.post("/knowledge-base/chunk/index/{chunk_id}", response_model=Dict[str, Any])
async def create_or_update_knowledge_chunk_index(
    chunk_id: str,
    data: dict,
    current_user: dict = Depends(verify_token)
):
    try:
        # 获取对应的chunk信息
        chunk = await db["chunks"].find_one({"_id": ObjectId(chunk_id)})
        if not chunk:
            raise HTTPException(status_code=404, detail="Chunk not found")

        # 获取知识库信息
        kb_info = await db["knowledge_bases"].find_one({"_id": ObjectId(chunk.get("knowledge_base_id"))})
        if not kb_info:
            raise HTTPException(status_code=404, detail="Knowledge base not found")

        # 验证必要字段
        required_fields = ['index_content']
        for field in required_fields:
            if field not in data:
                raise HTTPException(status_code=400, detail=f"Missing required field: {field}")

        # 获取embedding配置
        embedding_id = int(kb_info.get("embedding_model_id")) if kb_info.get("embedding_model_id") else None
        if not embedding_id:
            raise Exception("embedding_model_id is not set")
        embedding_config = await db.embeddings.find_one({"id":embedding_id})
        if not embedding_config:
            raise Exception("embedding_model is not exist")

        # 生成embedding
        embedding = get_embedding(data['index_content'], embedding_config)

        # 构建索引对象
        chunk_index_obj = {
            "file_id": chunk['file_id'] if chunk['file_id'] else '',
            "chunk_id": ObjectId(chunk_id),
            "knowledge_base_id": chunk['knowledge_base_id'],
            "index_content": data['index_content'],
            "chunk_type": chunk_type.BASE,
            "embedding": embedding,
            "last_updated": datetime.now()
        }

        # 判断是更新还是新增
        if data.get("index_id"):
            # 更新操作
            await db["chunks_index"].update_one(
                {"_id": ObjectId(data["index_id"])},
                {"$set": chunk_index_obj}
            )
            index_id = data["index_id"]
            message = "Chunk index updated successfully"
        else:
            # 新增操作
            chunk_index_obj["created_at"] = datetime.now()
            result = await db["chunks_index"].insert_one(chunk_index_obj)
            index_id = str(result.inserted_id)
            message = "Chunk index created successfully"
            
            # 更新知识库的index_count
            await db["knowledge_bases"].update_one(
                {"_id": chunk['knowledge_base_id']},
                {"$inc": {"index_count": 1}}
            )

        # 同步到ES
        es_update = {
            "id": index_id,
            "chunk_id": chunk_id,
            "file_id": str(chunk_index_obj['file_id']),
            "knowledge_base_id": str(kb_info["_id"]),
            "index_content": chunk_index_obj['index_content'],
            "embedding": embedding,
            "chunk_type": chunk_type.BASE,
            "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }
        if not data.get("index_id"):
            es_update["created_at"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        try:
            logger.info(f"插入ES数据: {es_update}")
            re = es_data_ingester(index_id, es_update, settings.ES_HOST, settings.ES_INDEX)
            logger.info(f"ES插入结果: {re}")
        except Exception as e:
            logger.error(f"ES数据插入失败: {str(e)}")
            # 即使ES插入失败，也返回成功，因为MongoDB操作已成功
            return {"success": True, "message": message, "warning": "索引已保存，但搜索引擎同步失败，可能影响检索"}

        return {"success": True, "message": message}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"操作失败: {str(e)}")


@router.delete("/knowledge-base/chunk/index/{index_id}", response_model=Dict[str, Any])
async def delete_knowledge_chunk_index(
    index_id: str,
    current_user: dict = Depends(verify_token)
):
    # 获取要删除的索引信息
    index = await db["chunks_index"].find_one({"_id": ObjectId(index_id)})
    if not index:
        raise HTTPException(status_code=404, detail="Index not found")
    
    # 删除索引
    result = await db["chunks_index"].delete_one({"_id": ObjectId(index_id)})

    # 删除ES数据
    es_data_deleter(
        ids=[str(index_id)],
        chunk_type=chunk_type.BASE,
        kb_id=str(index["knowledge_base_id"]),
        es_host=settings.ES_HOST,
        index_name=settings.ES_INDEX
    )
    
    # 更新知识库的index_count
    if "knowledge_base_id" in index:
        await db["knowledge_bases"].update_one(
            {"_id": index["knowledge_base_id"]},
            {"$inc": {"index_count": -1}}
        )
    
    return {"success": True, "message": "Index deleted successfully"}




# @router.post("/knowledge-base/{knowledge_base_id}/chunk/{chunk_id}/index", response_model=Dict[str, Any])
# async def create_knowledge_chunk_index(
#     knowledge_base_id: str,
#     chunk_id: str,
#     index: dict,
#     current_user: dict = Depends(verify_token)
# ):
#     # 验证必要字段
#     required_fields = ['file_id', 'index_content', 'chunk_type', 'chunk_index']
#     for field in required_fields:
#         if field not in index:
#             raise HTTPException(status_code=400, detail=f"Missing required field: {field}")

#     index_data = {
#         "file_id": ObjectId(index["file_id"]),
#         "knowledge_base_id": ObjectId(knowledge_base_id),
#         "chunk_id": ObjectId(chunk_id),
#         "index_content": index["index_content"],
#         "chunk_type": index["chunk_type"],
#         "chunk_index": int(index["chunk_index"]),
#         "is_expired": bool(index.get("is_expired", False)),
#         "created_at": datetime.now()
#     }
    
#     result = await db["knowledge_chunk_indexes"].insert_one(index_data)
#     created_index = await db["knowledge_chunk_indexes"].find_one({"_id": result.inserted_id})
#     created_index["id"] = str(created_index["_id"])
#     del created_index["_id"]
#     return {"data": created_index, "success": True}

# @router.put("/knowledge-base/{knowledge_base_id}/chunk/{chunk_id}/index/{index_id}", response_model=Dict[str, Any])
# async def update_knowledge_chunk_index(
#     knowledge_base_id: str,
#     chunk_id: str,
#     index_id: str,
#     data: dict,
#     current_user: dict = Depends(verify_token)
# ):
#     # 允许更新的字段
#     updatable_fields = {
#         'index_content': str,
#         'chunk_type': str,
#         'chunk_index': int,
#         'is_expired': bool
#     }
    
#     update_data = {}
#     for field, type_cast in updatable_fields.items():
#         if field in data:
#             update_data[field] = type_cast(data[field])
    
#     result = await db["knowledge_chunk_indexes"].update_one(
#         {"_id": ObjectId(index_id)},
#         {"$set": update_data}
#     )
#     if result.matched_count == 0:
#         raise HTTPException(status_code=404, detail="Index not found")
#     updated_index = await db["knowledge_chunk_indexes"].find_one({"_id": ObjectId(index_id)})
#     updated_index["id"] = str(updated_index["_id"])
#     del updated_index["_id"]
#     return {"data": updated_index, "success": True}

# @router.delete("/knowledge-base/{knowledge_base_id}/chunk/{chunk_id}/index/{index_id}")
# async def delete_knowledge_chunk_index(
#     knowledge_base_id: str,
#     chunk_id: str,
#     index_id: str,
#     current_user: dict = Depends(verify_token)
# ):
#     result = await db["knowledge_chunk_indexes"].delete_one({"_id": ObjectId(index_id)})
#     if result.deleted_count == 0:
#         raise HTTPException(status_code=404, detail="Index not found")
#     return {"success": True, "message": "Index deleted successfully"}

# 添加搜索参数模型
class SearchRequest(BaseModel):
    knowledge_base_id: str
    query: str
    search_mode: str = "semantic"  # semantic, full, mix, reRank
    max_tokens: int = 1000
    min_relevance: float = 0.3
    limit: int = 10

# 添加搜索结果模型
class SearchResponse(BaseModel):
    answer: str
    question: str
    knowledge_base_id: str
    chunk_id: str
    file_id: str
    similarity: float
    file_name: Optional[str] = None
    created_at: Optional[str] = None

# 添加搜索接口
@router.post("/knowledge_base/search", response_model=Dict[str, Any])
async def search_knowledge_base(
    search_request: SearchRequest,
    current_user: dict = Depends(verify_token)
):
    try:
        logger.info(f"知识库搜索请求: {search_request}")
        
        # 验证知识库是否存在
        knowledge_base = await db["knowledge_bases"].find_one({"_id": ObjectId(search_request.knowledge_base_id)})
        if not knowledge_base:
            raise HTTPException(status_code=404, detail="知识库不存在")
        
        # 构建搜索参数
        system_app_params = {
            "kb_id": search_request.knowledge_base_id,
            "search_mode": search_request.search_mode,
            "max_tokens": search_request.max_tokens,
            "min_similarity": search_request.min_relevance,
            "limit": search_request.limit
        }
        
        # 初始化FlowRAG搜索引擎
        kg_qa_flow_rag = FlowRAG(config=KnowledgeQAParams(**system_app_params), user_id=current_user["id"])
        
        # 执行搜索
        search_results = await kg_qa_flow_rag.search(search_request.query)
        
        # 处理搜索结果
        processed_results = []
        for result in search_results:
            # 获取文件信息
            file_info = None
            if result.get("file_id"):
                file_info = await db["source_files"].find_one({"_id": ObjectId(result["file_id"])})
            
            # 构建返回结果
            search_response = {
                "answer": result.get("answer", ""),
                "question": result.get("question", ""),
                "knowledge_base_id": search_request.knowledge_base_id,
                "chunk_id": str(result.get("chunk_id", "")),
                "file_id": str(result.get("file_id", "")),
                "similarity": float(result.get("similarity", 0)),
                "file_name": file_info.get("name") if file_info else None,
                "created_at": result.get("created_at", datetime.now()).strftime("%Y-%m-%d %H:%M:%S") if isinstance(result.get("created_at"), datetime) else None
            }
            processed_results.append(search_response)
        
        return {
            "success": True,
            "data": processed_results,
            "message": "搜索成功",
            "total": len(processed_results)
        }
        
    except Exception as e:
        logger.error(f"知识库搜索失败: {str(e)}")
        traceback.print_exc()
        return {
            "success": False,
            "message": f"搜索失败: {str(e)}",
            "data": []
        }

# ======================= 仪表盘统计接口 =======================

@router.get("/knowledge_base/statistics/overall", response_model=Dict[str, Any])
async def get_overall_statistics(current_user: dict = Depends(verify_token)):
    """
    获取全局知识库、文件和Chunk的统计信息。
    """
    try:
        knowledge_base_count = await db["knowledge_bases"].count_documents({"is_active": True})
        files_count = await db["source_files"].count_documents({})
        chunks_count = await db["chunks"].count_documents({})
        
        return {
            "success": True,
            "data": {
                "knowledge_bases": knowledge_base_count,
                "files": files_count,
                "chunks": chunks_count
            }
        }
    except Exception as e:
        logger.error(f"获取全局统计信息失败: {str(e)}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"获取全局统计信息失败: {str(e)}")

@router.get("/knowledge_base/statistics/files", response_model=Dict[str, Any])
async def get_file_statistics(current_user: dict = Depends(verify_token)):
    """
    获取文件的处理状态和类型的统计信息。
    """
    try:
        # 按处理状态分组
        status_pipeline = [
            {"$group": {"_id": "$processing_status", "count": {"$sum": 1}}}
        ]
        status_stats = await db["source_files"].aggregate(status_pipeline).to_list(length=None)

        # 按文件类型分组
        type_pipeline = [
            {"$group": {"_id": "$data_type", "count": {"$sum": 1}}}
        ]
        type_stats = await db["source_files"].aggregate(type_pipeline).to_list(length=None)

        return {
            "success": True,
            "data": {
                "by_status": [{"status": item["_id"], "count": item["count"]} for item in status_stats],
                "by_type": [{"type": item["_id"], "count": item["count"]} for item in type_stats]
            }
        }
    except Exception as e:
        logger.error(f"获取文件统计信息失败: {str(e)}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"获取文件统计信息失败: {str(e)}")

@router.get("/knowledge_base/statistics/access", response_model=Dict[str, Any])
async def get_access_statistics(
    days: int = Query(30, description="查询最近N天的数据"),
    current_user: dict = Depends(verify_token)
):
    """
    获取知识库访问量的统计信息。
    """
    try:
        # 计算起始日期
        start_date = datetime.now() - timedelta(days=days)
        
        # 聚合管道
        pipeline = [
            {"$match": {"created_at": {"$gte": start_date}}},
            {
                "$group": {
                    "_id": {
                        "$dateToString": {"format": "%Y-%m-%d", "date": "$created_at"}
                    },
                    "count": {"$sum": 1}
                }
            },
            {"$sort": {"_id": 1}}
        ]
        
        access_stats = await db["knowledge_bases_log"].aggregate(pipeline).to_list(length=None)

        # 格式化返回数据
        formatted_stats = [{"date": item["_id"], "count": item["count"]} for item in access_stats]
        
        return {
            "success": True,
            "data": formatted_stats
        }
    except Exception as e:
        logger.error(f"获取知识库访问量统计失败: {str(e)}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"获取知识库访问量统计失败: {str(e)}")

# 获取已发布的知识库列表
@router.get("/published_knowledge_bases", response_model=Dict[str, Any])
async def get_published_knowledge_bases(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    searchText: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {"is_active": True, "is_published": True}
    
    # 如果有搜索文本，同时搜索标题和描述
    if searchText:
        query["$or"] = [
            {"name": {"$regex": searchText, "$options": "i"}},
            {"description": {"$regex": searchText, "$options": "i"}}
        ]

    logger.info(f"查询已发布知识库条件: {query}")
    knowledge_bases = await db["knowledge_bases"].find(query).sort(
        "created_at", -1
    ).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 获取总数
    total = await db["knowledge_bases"].count_documents(query)
    
    # 处理每个知识库对象，确保有必要的字段
    for knowledge_base in knowledge_bases:
        knowledge_base["id"] = str(knowledge_base["_id"]) 
        del knowledge_base["_id"]
        
        # 确保每个知识库对象都有name字段，如果没有则设置一个默认值
        if "name" not in knowledge_base or knowledge_base["name"] is None:
            knowledge_base["name"] = "未命名知识库"
            logger.warning(f"知识库缺少name字段，已设置默认值: {knowledge_base['id']}")

    logger.info(f"查询到的已发布知识库数量: {len(knowledge_bases)}")
    
    try:
        response_data = [KnowledgeBaseResponse(**kb) for kb in knowledge_bases]
        return {
            "data": response_data,
            "total": total,
            "success": True,
            "pageSize": pageSize,
            "current": current
        }
    except Exception as e:
        logger.error(f"构建已发布知识库响应对象失败: {str(e)}")
        logger.error(f"问题数据: {knowledge_bases}")
        raise HTTPException(status_code=500, detail=f"构建已发布知识库响应对象失败: {str(e)}")