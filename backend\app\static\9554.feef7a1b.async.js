"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9554],{79554:function(e,t,n){n.d(t,{Z:function(){return D}});var r=n(64599),a=n.n(r),i=n(15009),s=n.n(i),c=n(19632),o=n.n(c),l=n(97857),u=n.n(l),p=n(99289),d=n.n(p),f=n(5574),x=n.n(f),h=n(67294),m=n(71471),v=n(55102),g=n(11941),y=n(2453),b=n(4393),j=n(42075),w=n(83062),k=n(83622),S=n(17788),Z=n(74330),P=n(32983),T=n(49354),z=n(87547),C=n(85175),N=n(27496),E=n(40110),B=n(77880),I=n(10981),A=n(85893),O=m.Z.Text,W=m.Z.Title,H=v.Z.Search,R=g.Z.TabPane,D=function(e){var t=e.visible,n=e.onCancel,r=e.sceneDescription,i=void 0===r?"":r,c=e.targetElementId,l=void 0===c?"":c,p=e.onSelectPrompt,f=e.app_info,m=void 0===f?"":f,D=(0,h.useState)("system"),_=x()(D,2),F=_[0],G=_[1],J=(0,h.useState)([]),L=x()(J,2),U=L[0],V=L[1],Y=(0,h.useState)([]),q=x()(Y,2),K=q[0],M=q[1],X=(0,h.useState)({system:!1,user:!1,generate:!1}),$=x()(X,2),Q=$[0],ee=$[1],te=(0,h.useState)(""),ne=x()(te,2),re=ne[0],ae=ne[1],ie=(0,h.useState)(""),se=x()(ie,2),ce=se[0],oe=se[1],le=(0,h.useState)(""),ue=x()(le,2),pe=ue[0],de=ue[1],fe=(0,h.useState)({current:1,pageSize:9,total:0}),xe=x()(fe,2),he=xe[0],me=xe[1],ve=(0,h.useState)({current:1,pageSize:9,total:0}),ge=x()(ve,2),ye=ge[0],be=ge[1];(0,h.useEffect)((function(){t&&(de(""),oe(i||""),me({current:1,pageSize:9,total:0}),be({current:1,pageSize:9,total:0}),ae(""),je(1),we(1))}),[t,i]);var je=function(){var e=d()(s()().mark((function e(t){var n,r,a,i=arguments;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=i.length>1&&void 0!==i[1]&&i[1],ee((function(e){return u()(u()({},e),{},{system:!0})})),e.prev=2,r={current:t,pageSize:he.pageSize,title:re||void 0},e.next=6,(0,B.V7)(r);case 6:(a=e.sent).success&&a.data&&(V(n?function(e){return[].concat(o()(e),o()(a.data))}:a.data),me(u()(u()({},he),{},{current:t,total:a.total||0}))),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(2),console.error("获取系统提示词失败",e.t0),y.ZP.error("获取系统提示词失败");case 14:return e.prev=14,ee((function(e){return u()(u()({},e),{},{system:!1})})),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[2,10,14,17]])})));return function(t){return e.apply(this,arguments)}}(),we=function(){var e=d()(s()().mark((function e(t){var n,r,a,i=arguments;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=i.length>1&&void 0!==i[1]&&i[1],ee((function(e){return u()(u()({},e),{},{user:!0})})),e.prev=2,r={current:t,pageSize:ye.pageSize,title:re||void 0},e.next=6,(0,B.He)(r);case 6:(a=e.sent).success&&a.data&&(M(n?function(e){return[].concat(o()(e),o()(a.data))}:a.data),be(u()(u()({},ye),{},{current:t,total:a.total||0}))),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(2),console.error("获取个人提示词失败",e.t0),y.ZP.error("获取个人提示词失败");case 14:return e.prev=14,ee((function(e){return u()(u()({},e),{},{user:!1})})),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[2,10,14,17]])})));return function(t){return e.apply(this,arguments)}}(),ke=function(){var e=d()(s()().mark((function e(){var t,n,r,i,c,o,l,p,d,f,x,h,v,g,b,j,w,k,S,Z,P,T,z,C,N,E,B,A,O,W;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(ce.trim()){e.next=3;break}return y.ZP.warning("请输入需要生成的提示词描述"),e.abrupt("return");case 3:if(ee((function(e){return u()(u()({},e),{},{generate:!0})})),de(""),e.prev=5,n={scene:ce,app_info:m||"default",stream:!0},console.log("生成提示词请求参数:",n),r=(0,I.bW)()){e.next=11;break}throw new Error("未找到认证令牌，请重新登录");case 11:return i="",c=0,o=null,e.next=16,fetch("/api/generate-prompt",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(r)},body:JSON.stringify(n)});case 16:if((l=e.sent).ok){e.next=19;break}throw new Error("HTTP 错误！状态码：".concat(l.status));case 19:if(null===(t=l.headers.get("content-type"))||void 0===t||!t.includes("text/event-stream")){e.next=82;break}if(d=null===(p=l.body)||void 0===p?void 0:p.getReader()){e.next=23;break}throw new Error("当前浏览器不支持 ReadableStream。");case 23:f=function(){if(c<i.length){var e=Math.min(5,i.length-c);c+=e,de(i.substring(0,c))}else o&&(clearInterval(o),o=null)},x=new TextDecoder("utf-8"),h="",v=!1;case 27:if(v){e.next=77;break}return e.next=30,d.read();case 30:if(g=e.sent,b=g.value,!g.done){e.next=36;break}return v=!0,e.abrupt("continue",27);case 36:h+=x.decode(b,{stream:!0}),j=h.split("\n\n"),h=j.pop()||"",w=a()(j),e.prev=40,w.s();case 42:if((k=w.n()).done){e.next=67;break}if(""!==(S=k.value).trim()){e.next=46;break}return e.abrupt("continue",65);case 46:Z=S.split("\n"),P=null,T=null,z=a()(Z);try{for(z.s();!(C=z.n()).done;)(N=C.value).startsWith("event: ")?P=N.substring(7).trim():N.startsWith("data: ")&&(T=N.substring(6))}catch(e){z.e(e)}finally{z.f()}if(!T){e.next=65;break}e.t0=P,e.next="answer"===e.t0?55:"end"===e.t0?57:"error"===e.t0?61:65;break;case 55:if("[DONE]"!==T)try{B=JSON.parse(T),(A=(null===(E=B.choices[0])||void 0===E||null===(E=E.delta)||void 0===E?void 0:E.content)||"")&&(i+=A,o||(o=setInterval(f,30)))}catch(e){console.error("解析响应数据失败:",e),i+=T}return e.abrupt("break",65);case 57:return v=!0,de(i),o&&clearInterval(o),e.abrupt("break",65);case 61:v=!0,o&&clearInterval(o);try{O=JSON.parse(T),S.error("生成提示词失败: ".concat(O.message))}catch(e){console.error("错误事件接收:",e),S.error("生成提示词失败: ".concat(T))}return e.abrupt("break",65);case 65:e.next=42;break;case 67:e.next=72;break;case 69:e.prev=69,e.t1=e.catch(40),w.e(e.t1);case 72:return e.prev=72,w.f(),e.finish(72);case 75:e.next=27;break;case 77:de(i),o&&clearInterval(o),i?y.ZP.success("提示词生成完成"):(y.ZP.warning("生成的提示词内容为空"),Se()),e.next=86;break;case 82:return e.next=84,l.json();case 84:(W=e.sent)&&W.success&&W.data?(de(W.data.content||W.data),y.ZP.success("提示词生成完成")):(console.warn("API返回格式不符合预期，使用备用提示词",W),Se());case 86:e.next=93;break;case 88:e.prev=88,e.t2=e.catch(5),console.error("生成提示词失败:",e.t2),y.ZP.error("生成提示词失败，请重试"),Se();case 93:return e.prev=93,ee((function(e){return u()(u()({},e),{},{generate:!1})})),e.finish(93);case 96:case"end":return e.stop()}}),e,null,[[5,88,93,96],[40,69,72,75]])})));return function(){return e.apply(this,arguments)}}(),Se=function(){var e='# 根据"'.concat(ce,'"生成的提示词').concat(m?"(".concat(m,")"):"","\n\n您是一位专业的").concat(ce,"领域专家，请根据用户提供的信息，给出专业的分析和建议。\n\n请考虑以下因素：\n1. 行业标准和最佳实践\n2. 潜在风险和机会\n3. 数据支持的决策建议\n\n在回答时，请保持客观、全面，并提供具体可行的建议。");de(e)},Ze=function(e){if(p)p(e),y.ZP.success("已选择提示词"),n();else if(l)try{var t=document.getElementById(l);if(!t||"TEXTAREA"!==t.tagName&&"INPUT"!==t.tagName)y.ZP.error("目标元素不是可编辑的文本区域");else{var r=t.selectionStart,a=t.selectionEnd;t.value=t.value.substring(0,r)+e+t.value.substring(a);var i=new Event("input",{bubbles:!0});t.dispatchEvent(i),y.ZP.success("提示词已插入"),n()}}catch(e){console.error("插入提示词失败",e),y.ZP.error("插入提示词失败")}else navigator.clipboard.writeText(e),y.ZP.success("提示词已复制到剪贴板"),n()},Pe=function(e,t){return(0,A.jsxs)(b.Z,{hoverable:!0,style:{position:"relative",marginBottom:"8px",borderRadius:"6px",boxShadow:"0 1px 2px rgba(0,0,0,0.05)",border:t?"1px solid #e6f7ff":"1px solid #adc6ff",backgroundColor:t?"#f0f7ff":"#f0f5ff",transition:"all 0.3s ease",height:"100%"},bodyStyle:{padding:"12px",display:"flex",flexDirection:"column",height:"100%"},children:[(0,A.jsxs)("div",{style:{marginBottom:"8px",display:"flex",alignItems:"center"},children:[(0,A.jsx)("div",{style:{width:"24px",height:"24px",borderRadius:"4px",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:t?"#bae7ff":"#adc6ff",color:"#1890ff",fontSize:"14px",marginRight:"8px"},children:(0,A.jsx)(T.Z,{})}),(0,A.jsx)(W,{level:5,style:{margin:0,fontWeight:500,flex:1,fontSize:"14px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e.title})]}),(0,A.jsx)(O,{type:"secondary",style:{display:"-webkit-box",marginBottom:"8px",flex:1,overflow:"hidden",textOverflow:"ellipsis",WebkitLineClamp:2,WebkitBoxOrient:"vertical",fontSize:"12px",lineHeight:"1.4",maxHeight:"34px"},children:e.content}),(0,A.jsxs)("div",{style:{marginTop:"auto",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,A.jsxs)("div",{style:{display:"flex",alignItems:"center",overflow:"hidden"},children:[(0,A.jsx)(z.Z,{style:{color:"#8c8c8c",marginRight:"3px",fontSize:"11px",flexShrink:0}}),(0,A.jsx)(O,{style:{fontSize:"11px",color:"#8c8c8c",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e.user})]}),(0,A.jsxs)(j.Z,{size:"small",style:{flexShrink:0},children:[(0,A.jsx)(w.Z,{title:"复制提示词内容",children:(0,A.jsx)(k.ZP,{type:"text",icon:(0,A.jsx)(C.Z,{style:{fontSize:"14px"}}),size:"small",style:{padding:"0 4px",minWidth:"20px",height:"22px"},onClick:function(t){t.stopPropagation(),navigator.clipboard.writeText(e.content),y.ZP.success("提示词内容已复制到剪贴板")}})}),(0,A.jsx)(w.Z,{title:"使用该提示词",children:(0,A.jsx)(k.ZP,{type:"text",icon:(0,A.jsx)(N.Z,{style:{fontSize:"14px"}}),size:"small",style:{padding:"0 4px",minWidth:"20px",height:"22px"},onClick:function(){return Ze(e.content)}})})]})]})]},e.id)};return(0,A.jsx)(S.Z,{title:"提示词助手",open:t,onCancel:n,width:1e3,footer:null,destroyOnClose:!0,className:"prompt-helper-modal",style:{top:20},bodyStyle:{height:"70vh",maxHeight:"600px",overflow:"hidden",padding:"0"},children:(0,A.jsxs)("div",{style:{height:"100%",display:"flex",flexDirection:"column",padding:"20px"},children:[(0,A.jsx)("div",{style:{marginBottom:"16px",flexShrink:0},children:(0,A.jsx)(H,{placeholder:"搜索提示词",onSearch:function(e){ae(e),me(u()(u()({},he),{},{current:1})),be(u()(u()({},ye),{},{current:1})),je(1),we(1)},style:{width:"100%"},allowClear:!0,enterButton:(0,A.jsx)(E.Z,{})})}),(0,A.jsxs)(g.Z,{activeKey:F,onChange:G,style:{flex:1,display:"flex",flexDirection:"column",overflow:"hidden"},tabBarStyle:{marginBottom:"16px",flexShrink:0},tabPaneStyle:{height:"100%",overflow:"hidden"},children:[(0,A.jsx)(R,{tab:"系统提示词",children:(0,A.jsx)("div",{className:"tab-content-scroll",children:Q.system&&0===U.length?(0,A.jsx)("div",{style:{textAlign:"center",padding:"20px"},children:(0,A.jsx)(Z.Z,{})}):U.length>0?(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("div",{className:"prompt-grid",children:U.map((function(e){return Pe(e,!0)}))}),Q.system?(0,A.jsx)("div",{style:{textAlign:"center",marginTop:"16px"},children:(0,A.jsx)(Z.Z,{})}):he.current*he.pageSize<he.total&&(0,A.jsx)("div",{style:{textAlign:"center",marginTop:"16px"},children:(0,A.jsx)(k.ZP,{onClick:function(){var e=he.current+1;je(e,!0)},type:"default",size:"middle",children:"加载更多"})})]}):(0,A.jsx)(P.Z,{description:"暂无系统提示词"})})},"system"),(0,A.jsx)(R,{tab:"个人提示词",children:(0,A.jsx)("div",{className:"tab-content-scroll",children:Q.user&&0===K.length?(0,A.jsx)("div",{style:{textAlign:"center",padding:"20px"},children:(0,A.jsx)(Z.Z,{})}):K.length>0?(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("div",{className:"prompt-grid",children:K.map((function(e){return Pe(e,!1)}))}),Q.user?(0,A.jsx)("div",{style:{textAlign:"center",marginTop:"16px"},children:(0,A.jsx)(Z.Z,{})}):ye.current*ye.pageSize<ye.total&&(0,A.jsx)("div",{style:{textAlign:"center",marginTop:"16px"},children:(0,A.jsx)(k.ZP,{onClick:function(){var e=ye.current+1;we(e,!0)},type:"default",size:"middle",children:"加载更多"})})]}):(0,A.jsx)(P.Z,{description:"暂无个人提示词"})})},"user"),(0,A.jsx)(R,{tab:"提示词生成",children:(0,A.jsxs)("div",{className:"tab-content-scroll",children:[(0,A.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,A.jsx)(O,{type:"secondary",style:{marginBottom:"8px",display:"block"},children:"请描述您需要的提示词场景或用途："}),(0,A.jsx)(v.Z.TextArea,{rows:4,value:ce,onChange:function(e){return oe(e.target.value)},placeholder:"例如：需要一个金融分析师角色的提示词，用于分析股票市场趋势",style:{marginBottom:"12px"}}),(0,A.jsx)(k.ZP,{type:"primary",onClick:ke,loading:Q.generate,style:{marginBottom:"20px"},children:"生成提示词"})]}),pe&&(0,A.jsxs)("div",{style:{border:"1px solid #d9d9d9",borderRadius:"8px",padding:"16px",backgroundColor:"#fafafa"},children:[(0,A.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"12px"},children:[(0,A.jsx)(W,{level:5,style:{margin:0},children:"生成的提示词"}),(0,A.jsxs)(j.Z,{children:[(0,A.jsx)(k.ZP,{icon:(0,A.jsx)(C.Z,{}),size:"small",onClick:function(){navigator.clipboard.writeText(pe),y.ZP.success("提示词已复制到剪贴板")},children:"复制"}),(0,A.jsx)(k.ZP,{type:"primary",size:"small",icon:(0,A.jsx)(N.Z,{}),onClick:function(){return Ze(pe)},children:"使用"})]})]}),(0,A.jsx)("div",{style:{whiteSpace:"pre-wrap",fontFamily:"monospace",fontSize:"14px",lineHeight:"1.6",maxHeight:"300px",overflowY:"auto"},children:pe})]})]})},"generate")]})]})})}},77880:function(e,t,n){n.d(t,{$j:function(){return g},Fh:function(){return x},Fu:function(){return m},He:function(){return o},V7:function(){return u},WJ:function(){return d},Yb:function(){return b},qB:function(){return w}});var r=n(15009),a=n.n(r),i=n(99289),s=n.n(i),c=n(78158);function o(e){return l.apply(this,arguments)}function l(){return(l=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/user-prompts",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function u(e){return p.apply(this,arguments)}function p(){return(p=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/system-prompts",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function d(e){return f.apply(this,arguments)}function f(){return(f=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompts/".concat(t),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function x(e){return h.apply(this,arguments)}function h(){return(h=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompts",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function m(e,t){return v.apply(this,arguments)}function v(){return(v=s()(a()().mark((function e(t,n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompts/".concat(t),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function g(e){return y.apply(this,arguments)}function y(){return(y=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompts/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function b(e){return j.apply(this,arguments)}function j(){return(j=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompts/".concat(t,"/copy"),{method:"POST"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function w(){return k.apply(this,arguments)}function k(){return(k=s()(a()().mark((function e(){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompt_categories",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}}}]);