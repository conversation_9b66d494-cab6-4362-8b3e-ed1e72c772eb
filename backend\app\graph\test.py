from langgraph.graph import StateGraph, END
from langchain_core.messages import HumanMessage
from typing import TypedDict, Annotated, List
from langgraph.config import get_stream_writer
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain.chat_models import init_chat_model
import json
import re

class LangGraphTool:
    """
    LangGraph 工具类，封装图构建与节点方法，供其他方法调用。
    """

    def __init__(self):
        self.llm = init_chat_model(
            "openai:DeepSeek-R1-Distill-Qwen-32B",
            base_url="http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/deepseek_r1_distill_qwen_32b_vllm/v1",
            api_key="NGZjNzgzNDk2MTNhMjBlZGRjNzI2OTEzYzdkZjI1NWYxNGM4YjM0ZQ=="
        )

    class GraphState(TypedDict):
        messages: Annotated[List[HumanMessage], ...]
        query: str  # 用户问题或查询条件
        classify: str  # 问题类型：count/list/other
        result: str  # 查询结果或大模型回复
        summary: str  # 总结内容

    def find_json_in_text(text: str):
        """
        从文本中查找第一个完整的 JSON 对象（支持多层嵌套）
        返回解析后的 dict，失败返回 None
        """
        # 找到第一个 '{' 开始的位置
        start = text.find('{')
        if start == -1:
            return None

        # 使用栈来匹配平衡的花括号
        stack = 0
        for i in range(start, len(text)):
            char = text[i]

            if char == '{':
                stack += 1
            elif char == '}':
                stack -= 1
                if stack == 0:
                    # 找到了完整的 { ... }
                    candidate = text[start:i+1]
                    try:
                        return json.loads(candidate)
                    except json.JSONDecodeError:
                        return None  # 解析失败

        # 没有找到闭合的 }
        return None

    def node_llm(self, state: "LangGraphTool.GraphState"):
        """
        大模型节点：判断用户问题类型，并生成回复或提取查询条件
        """
        messages = state["messages"]
        prompt = (
            "你是一个智能助手，请判断用户的问题是否与消费者保护相关：\n"
            "1. 如果用户问的是投诉数量、统计等问题，返回 classify: count，并提取查询的组织机构关键词。\n"
            "2. 如果用户问的是投诉内容等，返回 classify: list，并提取查询的组织机构关键词。\n"
            "3. 其他问题返回 classify: other。\n"
            "请用如下JSON格式回复：{\"classify\": \"count/list/other\", \"query\": \"...\"}\n"
            f"用户输入：{messages[-1].content}"
        )
        # 不使用流式输出，直接获取完整回复
        response = self.llm.invoke([HumanMessage(content=prompt)])
        # 尝试从回复中提取JSON
        json_obj = LangGraphTool.find_json_in_text(response.content)
        if json_obj:
            classify = json_obj.get("classify", "other")
            query = json_obj.get("query", "")
            state["classify"] = classify
            state["query"] = query
        else:
            state["classify"] = "other"
            state["query"] = ""
        return state

    async def node_count(self, state: "LangGraphTool.GraphState"):
        """
        查询投诉数量节点
        """
        client = MultiServerMCPClient({
            "essearch": {
                "transport": "sse",
                "url": "http://47.93.214.158:8005/sse",
            },
        })
        tools = await client.get_tools()
        writer = get_stream_writer()
        get_question_count_tool = next((tool for tool in tools if tool.name == "get_question_count"), None)
        if get_question_count_tool:
            result = await get_question_count_tool.ainvoke({
                "index": "pro_mcp_data_complaint_v1",
                "query": state.get("query", "")
            })
            writer({"投诉数量": result})
            state["result"] = str(result)
        else:
            writer({"投诉数量": "查询异常"})
            state["result"] = "查询异常"
        state["messages"].append(HumanMessage(content=f"投诉数量查询结果：{state['result']}"))
        return state

    async def node_list(self, state: "LangGraphTool.GraphState"):
        """
        查询投诉详情节点
        """
        client = MultiServerMCPClient({
            "essearch": {
                "transport": "sse",
                "url": "http://47.93.214.158:8005/sse",
            },
        })
        tools = await client.get_tools()
        writer = get_stream_writer()
        search_by_text_tool = next((tool for tool in tools if tool.name == "search_by_text"), None)
        if search_by_text_tool:
            result = await search_by_text_tool.ainvoke({
                "index": "pro_mcp_data_complaint_v1",
                "query": state.get("query", ""),
                "sort_field": "update_time"
            })
            writer({"投诉详情": result})
            state["result"] = str(result)
        else:
            writer({"投诉详情": "查询异常"})
            state["result"] = "查询异常"
        state["messages"].append(HumanMessage(content=f"投诉详情查询结果：{state['result']}"))
        return state

    async def node_summary(self, state: "LangGraphTool.GraphState"):
        """
        总结节点：调用大模型对查询结果进行总结，流式输出
        """
        writer = get_stream_writer()
        # 构建总结提示词
        user_question = state["messages"][0].content if state["messages"] else ""
        query_result = state.get("result", "")
        prompt = (
            "你是一个智能助手，请根据用户的问题和查询到的数据进行简明扼要的总结，直接用中文回答用户。\n"
            f"用户问题：{user_question}\n"
            f"查询结果：{query_result}\n"
            "请用简洁、专业的语言进行总结。"
        )
        summary_content = ""
        async for chunk in self.llm.astream([HumanMessage(content=prompt)]):
            content = chunk.content
            summary_content += content
            writer({"summary_chunk": content})
        state["summary"] = summary_content
        writer({"summary_content": summary_content})
        # 追加到对话上下文
        state["messages"].append(HumanMessage(content=f"总结：{summary_content}"))
        return state

    def build_graph(self):
        """
        构建LangGraph流程，根据大模型分类结果路由到不同节点，并在查询后进行总结
        """
        graph = StateGraph(self.GraphState)
        graph.add_node("node_llm", self.node_llm)
        graph.add_node("node_count", self.node_count)
        graph.add_node("node_list", self.node_list)
        graph.add_node("node_summary", self.node_summary)

        def classify_router(state):
            classify = state.get("classify", "other")
            if classify == "count":
                return "node_count"
            elif classify == "list":
                return "node_list"
            else:
                return "node_summary"

        graph.set_entry_point("node_llm")
        graph.add_conditional_edges(
            "node_llm",
            classify_router,
            {
                "node_count": "node_count",
                "node_list": "node_list",
                END: END
            }
        )
        # 查询节点后统一进入总结节点
        graph.add_edge("node_count", "node_summary")
        graph.add_edge("node_list", "node_summary")
        graph.add_edge("node_summary", END)
        return graph.compile()

    async def run_graph(self, initial_state=None):
        """
        运行图流程，流式返回每个节点的输出，用户可见内容通过writer输出
        :param initial_state: 初始状态，默认为 {"messages": []}
        """
        if initial_state is None:
            initial_state = {"messages": []}
        app = self.build_graph()
        results = []
        async for message in app.astream(initial_state, stream_mode="custom"):
            yield f"Stream message: {message}"
        #     results.append(message)
        # yield f"Stream result: {results}"
        

# 用法示例（供其他方法调用）：
# from backend.app.graph.test import LangGraphTool
# import asyncio
# tool = LangGraphTool()
# asyncio.run(tool.run_graph(initial_state = {"messages": [HumanMessage(content="你好")]}))

async def main():
    tool = LangGraphTool()
    async for chunk in tool.run_graph({"messages": [HumanMessage(content="腾讯相关投诉")]}):
        # 发送到前端、写日志、更新 UI
        print(chunk)

import asyncio
asyncio.run(main())