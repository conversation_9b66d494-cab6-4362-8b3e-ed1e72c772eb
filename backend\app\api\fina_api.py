from fastapi import APIRouter, HTTPException, Depends
from typing import AsyncGenerator,List, Dict, Any
from .auth import verify_api_token
from ..db.mongodb import db
import asyncio
from datetime import datetime
from app.engines.embedding.embedding_utils import get_embedding
from pydantic import BaseModel
from fastapi.responses import StreamingResponse
from app.utils.logging_config import setup_logging, get_logger
import traceback
from ..models.chat import ChatResponse
from ..models.system_app_setting import SystemAppSettingModel, SystemAppSetting
from ..models.llm import LLMModel, Provider
from ..utils.llmClient import stream_openai_api, stream_jiutian_api,openai_api, convert_messages
from elasticsearch import Elasticsearch
import json
from bson import ObjectId
from ..models.message import Message
# 设置日志配置
setup_logging()
logger = get_logger(__name__)


router = APIRouter(
    prefix="/openapi",
    tags=["app"]
)

# 数据查询请求模型
class DataQueryRequest(BaseModel):
    app_info:str
    query: str
    messages: List[Dict[str, Any]]
    # 加一个默认的相似度阈值
    threshold: float = 0.85
    top_k: int = 5
    # filters: Optional[Dict[str, Any]] = None
    # page: int = 1
    # page_size: int = 10
    # sort_by: Optional[str] = None
    # sort_order: Optional[str] = "asc"

# 数据查询响应模型
class DataQueryResponse(BaseModel):
    success: bool
    message: str
    data: List[Dict[str, Any]] = []
    total: int = 0


@router.post("/app/data-query", response_model=DataQueryResponse)
async def query_data(
    request: DataQueryRequest,
    token_info: dict = Depends(verify_api_token)
    
):
    """
    基于向量相似度的ES检索接口
    """
    logger.debug(f"request=====>: {request}")
    try:
        logger.info(f"数据查询: {request.query}")
        app_info = request.app_info
        query = request.query
        messages = request.messages
        top_k = request.top_k or 5


        
        if not query or query == "":
            raise HTTPException(status_code=404, detail="查询内容不能为空")
        if not app_info or app_info == "":
            raise HTTPException(status_code=404, detail="应用信息不能为空")

        app_info_obj = await db["system_app_settings"].find_one({"app_info": app_info})
        if not app_info_obj:
            raise HTTPException(status_code=404, detail="应用信息不存在")
        app_params = app_info_obj.get('params',None)
        if not app_params:  
            raise HTTPException(status_code=404, detail="应用参数不存在")
        if app_info == "financialReputationRisk":
            results = await retrieve_financial_opinion(app_info, query, messages, db, request, top_k)
        elif app_info == "ComplaintAnalysis":
            results = await retrieve_complaint_opinion(app_info, query, messages, db, request, top_k)
        else:
            raise HTTPException(status_code=404, detail="应用信息不存在")
        # results = await retrieve_financial_opinion(app_info, query, messages, db, request, logger)
        

        return DataQueryResponse(
            success=True,
            message="查询成功",
            total=len(results),
            data=results,
        )
        
    except Exception as e:
        logger.error(f"查询数据失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"查询数据失败: {str(e)}")


# 新增：封装检索逻辑为独立方法
def process_string_array(value):
        """
        测试数值是不是字符串数组，如果是则返回，如果不是则将其转成字符串数组。
        
        处理的情况包括:
        1. JSON格式的字符串: ```json\n[\n    "美国",\n    "中国"]\n```
        2. Python列表字符串: "['美国', '中国']"
        3. 已经是列表的情况: ['美国', '中国']
        4. 嵌套列表的情况: ['['特朗普', '萨尔兹曼']']
        5. 多元素嵌套列表的情况: ['['特朗普', '拜登', 'Niklas Höhne', 'Christoph Bals', 'Javier Milei']']
        
        Args:
            value: 需要处理的值
            
        Returns:
            list: 处理后的字符串数组
        """
        # 如果已经是列表，直接返回
        if isinstance(value, list):
            # 处理嵌套列表的情况，如 ['['特朗普', '萨尔兹曼']'] 或 ['['特朗普', '拜登', 'Niklas Höhne', 'Christoph Bals', 'Javier Milei']']
            if len(value) == 1 and isinstance(value[0], str) and value[0].startswith('[') and value[0].endswith(']'):
                try:
                    import ast
                    return ast.literal_eval(value[0])
                except:
                    logger.error(f"解析嵌套列表字符串失败: {value}")
                    return value
            return value
            
        # 如果是None或空字符串，返回空列表
        if not value:
            return []
            
        # 尝试处理JSON格式的字符串
        if isinstance(value, str) and '```json' in value:
            try:
                # 提取JSON部分
                json_str = value.split('```json')[1].split('```')[0].strip()
                return json.loads(json_str)
            except:
                logger.error(f"解析JSON字符串失败: {value}")
                
        # 尝试处理Python列表字符串
        if isinstance(value, str) and value.startswith('[') and value.endswith(']'):
            try:
                # 使用ast.literal_eval安全地解析Python字面量
                import ast
                return ast.literal_eval(value)
            except:
                logger.error(f"解析Python列表字符串失败: {value}")
                
        # 如果是普通字符串，将其作为单个元素的列表返回
        if isinstance(value, str):
            return [value]
            
        # 其他情况返回空列表
        return []


async def retrieve_complaint_opinion(app_info, query, messages, db, request, top_k):
    if not query or query == "":
            raise HTTPException(status_code=404, detail="查询内容不能为空")
    if not app_info or app_info == "":
        raise HTTPException(status_code=404, detail="应用信息不能为空")

    app_info_obj = await db["system_app_settings"].find_one({"app_info": app_info})
    if not app_info_obj:
        raise HTTPException(status_code=404, detail="应用信息不存在")
    app_params = app_info_obj.get('params',None)
    if not app_params:  
        raise HTTPException(status_code=404, detail="应用参数不存在")

    logger.info(f"app_params: {app_params}")
    

    embedding_model_id = app_params.get('EMBEDDING_MODEL_ID',None)
    if not embedding_model_id:
        raise HTTPException(status_code=404, detail="Embedding模型ID不存在") 
    
    embedding_model_obj = await db["embeddings"].find_one({"id": int(embedding_model_id)})
    if not embedding_model_obj:
        raise HTTPException(status_code=404, detail="Embedding模型不存在")


    # 2. 调用ES进行向量检索
    es_host = app_params.get('ES_HOST', '')
    es_port = app_params.get('ES_PORT', '9200')
    index_name = app_params.get('ES_INDEX', 'wangsu_jcg_info')
    es_user = app_params.get('ES_USER', None)
    es_password = app_params.get('ES_PASSWORD', None)
    model_id = app_params.get('MODEL_ID', None)
    logger.info(f"==============>>>>>app_params: {app_params}")
    logger.info(f"==============>>>>>index_name: {index_name}")

    # 保存原始查询
    original_query = query

    if model_id:
        logger.info(f"==============>>>>>优化查询")
        
        # 准备LLM输入
        llm_messages = [
            {"role": "system", "content": "你是一个专业的投诉分析检索助手。请根据用户的对话历史和新的提问，生成一个更精确的搜索查询语句。重点关注：投诉类型、产品服务、问题描述、用户诉求等关键信息。只需返回查询语句，不要有任何解释或额外文字。"},
        ]
        
        # 提取用户历史消息(如果有)
        history_messages = ""
        if messages and len(messages) > 0:
            user_messages = [msg for msg in messages if msg.get("role") == "user"]
            
            if len(user_messages) > 0:
                logger.info(f"有历史对话，将历史信息纳入查询优化")
                for msg in user_messages:
                    history_messages += f"用户：{msg.get('content', '')}\n"
        
        # 根据是否有历史消息使用不同的提示模板
        if history_messages:
            prompt_template = """
# 历史对话
{history_messages}

# 新的提问
{new_query}

# 请根据以上对话历史和新提问，生成一个精确的搜索查询语句，用于检索相关投诉信息。
# 提取关键的投诉特征，如产品类型、问题性质、用户诉求等。
# 只返回查询语句，不要有任何解释或额外文字。
                """
            prompt_content = prompt_template.format(history_messages=history_messages, new_query=query)
        else:
            prompt_template = """
# 用户提问
{query}

# 请将上述提问转化为更精确的搜索查询语句，用于检索相关投诉信息。
# 分析提问中隐含的投诉特征，如可能涉及的产品类型、问题性质、用户诉求等。
# 扩展模糊或笼统的描述，使查询更具体。
# 只返回查询语句，不要有任何解释或额外文字。
                """
            prompt_content = prompt_template.format(query=query)
        
        # 添加提示到消息中
        llm_messages.append({
            "role": "user", 
            "content": prompt_content
        })
        
        try:
            refined_query = await model_api(llm_messages, model_id)                    
            # 使用生成的查询替换原始查询
            if refined_query:
                logger.info(f"原始查询: {original_query}")
                logger.info(f"LLM优化后的查询: {refined_query}")
                query = refined_query
        except Exception as e:
            logger.error(f"调用LLM优化查询失败: {str(e)}")
            # 出错时继续使用原始查询
            pass


    if not es_host or es_host == "":
        raise HTTPException(status_code=404, detail="ES主机不存在")
    if not index_name or index_name == "":
        raise HTTPException(status_code=404, detail="ES索引不存在")
    
    # 执行原始查询
    logger.info(f"执行原始查询=====》: {original_query}")
    original_embedding = get_embedding(original_query, embedding_model_obj)
    original_chunks = es_vector_retriever_complaint(
        original_embedding,
        top_k=top_k,
        threshold=request.threshold or 0.65,
        es_host=es_host,
        es_port=es_port,
        index_name=index_name,
        es_user=es_user,
        es_password=es_password
    )
    
    # 如果有优化的查询，并且与原始查询不同，则执行优化查询
    optimized_chunks = []
    if query != original_query:
        logger.info(f"执行优化查询=====》: {query}")
        optimized_embedding = get_embedding(query, embedding_model_obj)
        optimized_chunks = es_vector_retriever_complaint(
            optimized_embedding,
            top_k=10,
            threshold=request.threshold or 0.65,
            es_host=es_host,
            es_port=es_port,
            index_name=index_name,
            es_user=es_user,
            es_password=es_password
        )
    
    # 合并结果并去重
    all_chunks = []
    seen_urls = set()
    
    # 先添加优化查询结果（优先级更高）
    for chunk in optimized_chunks:
        url = chunk.get("url", "")
        if url and url not in seen_urls and url != "-":
            seen_urls.add(url)
            all_chunks.append(chunk)
    
    # 再添加原始查询结果
    for chunk in original_chunks:
        url = chunk.get("url", "")
        if url and url not in seen_urls and url != "-":
            seen_urls.add(url)
            all_chunks.append(chunk)
    
    # 按相似度分数排序并取前10个
    all_chunks.sort(key=lambda x: x.get("score", 0), reverse=True)
    chunks = all_chunks[:10]
    
    logger.info(f"合并后的结果数量: {len(chunks)}")
    
    # 3. 格式化返回结果
    results = []
    for chunk in chunks:
        logger.debug(f"chunk=====>: {chunk}")
        results.append({
            "title": chunk.get("title", ""),
            "url": chunk.get("url", ""),
            "publishtime": chunk.get("publishtime", ""),
            "summary": chunk.get("summary", ""),
            "handle_depart": chunk.get("handle_depart", ""),
            "handle_result": chunk.get("handle_result", ""),
            "cotitle": chunk.get("cotitle", ""),
            "author_avatar": chunk.get("author_avatar", ""),
            "appeal": chunk.get("appeal", ""),
            "site_name": chunk.get("site_name", ""),
            "issue": chunk.get("issue", ""),
            "core_issue": chunk.get("core_issue", ""),
            "product_service_involved": chunk.get("product_service_involved", ""),
            "user_demand": chunk.get("user_demand", ""),
            "sentiment": chunk.get("sentiment", ""),
            "severity": chunk.get("severity", ""),
            "potential_risks": process_string_array(chunk.get("potential_risks", "")),
            "potential_causes": process_string_array(chunk.get("potential_causes", "")),
            "improvement_suggestions": process_string_array(chunk.get("improvement_suggestions", "")),
            "complaint_category": process_string_array(chunk.get("complaint_category", "")),
            "keywords": process_string_array(chunk.get("keywords", "")),
            "crawler_time": chunk.get("crawler_time", ""),
            "score": chunk.get("score", 0.0)
        })
    return results

    

async def retrieve_financial_opinion(app_info, query, messages, db, request, top_k):
    if not query or query == "":
        raise HTTPException(status_code=404, detail="查询内容不能为空")
    if not app_info or app_info == "":
        raise HTTPException(status_code=404, detail="应用信息不能为空")

    app_info_obj = await db["system_app_settings"].find_one({"app_info": app_info})
    if not app_info_obj:
        raise HTTPException(status_code=404, detail="应用信息不存在")
    app_params = app_info_obj.get('params',None)
    if not app_params:  
        raise HTTPException(status_code=404, detail="应用参数不存在")

    logger.info(f"app_params: {app_params}")

    embedding_model_id = app_params.get('EMBEDDING_MODEL_ID',None)
    if not embedding_model_id:
        raise HTTPException(status_code=404, detail="Embedding模型ID不存在") 

    embedding_model_obj = await db["embeddings"].find_one({"id": int(embedding_model_id)})
    if not embedding_model_obj:
        raise HTTPException(status_code=404, detail="Embedding模型不存在")

    # 2. 调用ES进行向量检索
    es_host = app_params.get('ES_HOST', '')
    es_port = app_params.get('ES_PORT', '9200')
    index_name = app_params.get('ES_INDEX', 'wangsu_jcg_info')
    es_user = app_params.get('ES_USER', None)
    es_password = app_params.get('ES_PASSWORD', None)
    model_id = app_params.get('MODEL_ID', None)
    logger.info(f"==============>>>>>app_params: {app_params}")
    logger.info(f"==============>>>>>index_name: {index_name}")

    if messages and len(messages) > 0 and model_id:
        logger.info(f"==============>>>>>优化查询")
        # 提取用户消息
        user_messages = [msg for msg in messages if msg.get("role") == "user"]

        # 如果用户消息大于一条，则调用LLM生成新的查询
        if len(user_messages) > 0:
            logger.info(f"用户消息数量大于1，调用LLM生成新的查询")
            # 准备LLM输入
            llm_messages = [
                {"role": "system", "content": "你是一个专业的金融声誉风险检索助手。请根据用户的对话历史，和新的提问，生成一个更精确的搜索查询语句，以便获取最相关的金融声誉风险信息。只需返回查询语句，不要有任何解释或额外文字。"},
            ]
            prompt_template = """
# 历史对话
{history_messages}

# 新的提问
{new_query}

# 请根据以上对话，生成一个简洁的搜索查询语句，用于检索与'{new_query}'相关的信息。只返回查询语句，不要有任何解释或额外文字。
            """ 
            history_messages = ""
            for msg in user_messages:
                history_messages += f"用户：{msg.get('content', '')}\n"
            # 添加最后的指令
            llm_messages.append({
                "role": "user", 
                "content": prompt_template.format(history_messages=history_messages, new_query=query)
            })

            try:
                refined_query = await model_api(llm_messages,model_id)                    
                # 使用生成的查询替换原始查询
                if refined_query :
                    logger.info(f"LLM生成的查询: {refined_query}")
                    query = refined_query
            except Exception as e:
                logger.error(f"调用LLM生成查询失败: {str(e)}")
                # 出错时继续使用原始查询
                pass

    if not es_host or es_host == "":
        raise HTTPException(status_code=404, detail="ES主机不存在")
    if not index_name or index_name == "":
        raise HTTPException(status_code=404, detail="ES索引不存在")
    logger.info(f"执行查询=====》: {query}")
    embedding = get_embedding(query, embedding_model_obj)
    chunks = es_vector_retriever_financial(
        embedding,
        top_k=top_k,
        threshold=request.threshold or 0.55,
        es_host=es_host,
        es_port=es_port,
        index_name=index_name,
        es_user=es_user,
        es_password=es_password
    )

    # 3. 格式化返回结果
    results = []
    for chunk in chunks:
        results.append({
            "title": chunk.get("title", ""),
            "url": chunk.get("url", ""),
            "contentSummary": chunk.get("contentSummary", ""),
            "publishtime": chunk.get("publishtime", ""),
            "summaryFacts": chunk.get("summaryFacts", ""),
            "authorViewpoint": chunk.get("authorViewpoint", ""),
            "authorAttitude": chunk.get("authorAttitude", ""),
            "characterEntity": process_string_array(chunk.get("characterEntity", "")),
            "eventInfo": process_string_array(chunk.get("eventInfo", "")),    
            "institutionalEntities": process_string_array(chunk.get("institutionalEntities", "")),
            "locationEntity": process_string_array(chunk.get("locationEntity", "")),
            "score": chunk.get("score", 0.0)
        })
    return results


# 外交部向量检索
def es_vector_retriever_financial(
    query_embedding: List[float], 
    top_k: int = 5, 
    threshold: float = 0.6,
    es_host: str = None, 
    es_port: str = "9200",
    index_name: str = "wangsu_jcg_info_trump",
    es_user: str = None,
    es_password: str = None
) -> List[Dict[str, Any]]:
    """
    基于ES客户端的向量检索
    
    Args:
        query_embedding: 查询向量
        chunk_type: 块类型
        kb_id: 知识库ID
        top_k: 返回结果数量
        threshold: 相似度阈值
        es_host: ES主机配置（可选）
        index_name: 索引名称
    
    Returns:
        List[Dict[str, Any]]: 检索结果列表
    """

    logger.info(f"=========================ip: {es_host}")
    logger.info(f"=========================port: {es_port}")
    logger.info(f"=========================user: {es_user}")
    logger.info(f"=========================password: {es_password}")
    try:
        # 构建查询DSL
        search_query = {
            "track_total_hits": True,
            "query": {
                "script_score": {
                    "query": {
                        "bool": {
                            "must": [
                                {
                                    "exists": {
                                        "field": "embedding"
                                    }
                                }
                            ]
                        }
                    },
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                        "params": {"query_vector": query_embedding}
                    }
                }
            },
            "size": top_k,
            "_source": [
                "title",
                "url", 
                "summary",
                "summaryFacts",
                "authorViewpoint",
                "authorAttitude",
                "characterEntity",
                "eventInfo",
                "institutionalEntities",
                "locationEntity",
                "publishtime"
            ],
            "sort": [
                {"_score": {"order": "desc"}}
            ]
        }

 
        es = Elasticsearch(
            [f"{es_host}:{es_port}"],
            http_auth=(es_user, es_password) if es_user else None,
            timeout=60  # 设置客户端级别的超时时间（秒）
        )
        
            
        # 检查连接
        if es.ping():
            logger.info(f"ES连接成功: {es_host}:{es_port}")
            es_info = es.info()
            logger.info(f"ES版本: {es_info['version']['number']}")
        else:
            logger.error(f"ES连接失败: {es_host}:{es_port}")
            raise Exception(f"ES连接失败: {es_host}:{es_port}")

        logger.info(f'检索=============》{index_name}')
        response = es.search(index=index_name, body=search_query)
        results = []
        hits = response.get("hits", {}).get("hits", [])
        # 在处理结果之前添加日志
        # logger.info(f"ES响应数据: {response}")
        logger.info(f"hits总数: {len(hits)}")

        for hit in hits:
            # 打印原始分数
            original_score = hit["_score"]
            logger.info(f"原始分数: {original_score}")
            
            score = (hit["_score"] - 1.0)  # 恢复原始相似度分数
            # logger.info(f"计算后的分数: {score}")
            
            if score < threshold:
                logger.info(f"分数 {score} 小于阈值 {threshold}，跳过")
                continue
        
            source = hit["_source"]
            # logger.info(f"文档源数据: {source}")
            logger.info(f"文档源数据: {source}")
            
            result = {
                "title": source.get("title", ""),
                "url": source.get("url", ""),
                "publishtime": source.get("publishtime", ""),
                "contentSummary": source.get("summary", ""),
                "summaryFacts": source.get("summaryFacts", ""),
                "authorViewpoint": source.get("authorViewpoint", ""),
                "authorAttitude": source.get("authorAttitude", ""),
                "characterEntity": source.get("characterEntity", ""),
                "eventInfo": source.get("eventInfo", ""),
                "institutionalEntities": source.get("institutionalEntities", ""),
                "locationEntity": source.get("locationEntity", ""),
                "score": score
            }
            results.append(result)
            # logger.info(f"添加结果: {result}")

        logger.info(f"最终结果数量: {len(results)}")
        return results[:top_k]
    
        
    except Exception as e:
        logger.error(f"向量检索失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise Exception(f"向量检索失败: {str(e)}")
    finally:
        if es:
            es.close()





def es_vector_retriever_complaint(
    query_embedding: List[float], 
    top_k: int = 5, 
    threshold: float = 0.6,
    es_host: str = None, 
    es_port: str = "9200",
    index_name: str = "wangsu_jcg_info_trump",
    es_user: str = None,
    es_password: str = None
) -> List[Dict[str, Any]]:
    """
    基于ES客户端的向量检索
    
    Args:
        query_embedding: 查询向量
        chunk_type: 块类型
        kb_id: 知识库ID
        top_k: 返回结果数量
        threshold: 相似度阈值
        es_host: ES主机配置（可选）
        index_name: 索引名称
    
    Returns:
        List[Dict[str, Any]]: 检索结果列表
    """

    logger.info(f"=========================ip: {es_host}")
    logger.info(f"=========================port: {es_port}")
    logger.info(f"=========================user: {es_user}")
    logger.info(f"=========================password: {es_password}")
    try:
        # 构建查询DSL
        search_query = {
            "track_total_hits": True,
            "query": {
                "script_score": {
                    "query": {
                        "bool": {
                            "must": [
                                {
                                    "exists": {
                                        "field": "embedding"
                                    }
                                }
                            ]
                        }
                    },
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                        "params": {"query_vector": query_embedding}
                    }
                }
            },
            "size": top_k,
            "_source": [
                "title",
                "url", 
                "summary",
                "handle_depart",
                "handle_result",
                "cotitle",
                "author_avatar",
                "appeal",
                "site_name",
                "issue",
                "core_issue",
                "product_service_involved",
                "user_demand",
                "sentiment",
                "severity",
                "potential_risks",
                "potential_causes",
                "improvement_suggestions",
                "complaint_category",
                "keywords",
                "publishtime",
                "crawler_time"
            ],
            "sort": [
                {"_score": {"order": "desc"}}
            ]
        }

 
        es = Elasticsearch(
            [f"{es_host}:{es_port}"],
            http_auth=(es_user, es_password) if es_user else None,
            timeout=60  # 设置客户端级别的超时时间（秒）
        )
        
            
        # 检查连接
        if es.ping():
            logger.info(f"ES连接成功: {es_host}:{es_port}")
            es_info = es.info()
            logger.info(f"ES版本: {es_info['version']['number']}")
        else:
            logger.error(f"ES连接失败: {es_host}:{es_port}")
            raise Exception(f"ES连接失败: {es_host}:{es_port}")

        logger.info(f'检索=============》{index_name}')
        response = es.search(index=index_name, body=search_query)
        results = []
        hits = response.get("hits", {}).get("hits", [])
        # 在处理结果之前添加日志
        # logger.info(f"ES响应数据: {response}")
        logger.info(f"hits总数: {len(hits)}")

        for hit in hits:
            # 打印原始分数
            original_score = hit["_score"]
            logger.info(f"原始分数: {original_score}")
            
            score = (hit["_score"] - 1.0)  # 恢复原始相似度分数
            # logger.info(f"计算后的分数: {score}")
            
            if score < threshold:
                logger.info(f"分数 {score} 小于阈值 {threshold}，跳过")
                continue
        
            source = hit["_source"]
            # logger.info(f"文档源数据: {source}")
            logger.info(f"文档源数据: {source}")
            
            result = {
                "title": source.get("title", ""),
                "url": source.get("url", ""),
                "publishtime": source.get("publishtime", ""),
                "summary": source.get("summary", ""),
                "handle_depart": source.get("handle_depart", ""),
                "handle_result": source.get("handle_result", ""),
                "cotitle": source.get("cotitle", ""),
                "author_avatar": source.get("author_avatar", ""),
                "appeal": source.get("appeal", ""),
                "site_name": source.get("site_name", ""),
                "issue": source.get("issue", ""),
                "core_issue": source.get("core_issue", ""),
                "product_service_involved": source.get("product_service_involved", ""),
                "user_demand": source.get("user_demand", ""),
                "sentiment": source.get("sentiment", ""),
                "severity": source.get("severity", ""),
                "potential_risks": process_string_array(source.get("potential_risks", "")),
                "potential_causes": process_string_array(source.get("potential_causes", "")),
                "improvement_suggestions": process_string_array(source.get("improvement_suggestions", "")),
                "complaint_category": process_string_array(source.get("complaint_category", "")),
                "keywords": process_string_array(source.get("keywords", "")),
                "crawler_time": source.get("crawler_time", ""),
                "score": score
            }
            results.append(result)
            # logger.info(f"添加结果: {result}")

        logger.info(f"最终结果数量: {len(results)}")
        return results[:top_k]
    
        
    except Exception as e:
        logger.error(f"向量检索失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise Exception(f"向量检索失败: {str(e)}")
    finally:
        if es:
            es.close()
