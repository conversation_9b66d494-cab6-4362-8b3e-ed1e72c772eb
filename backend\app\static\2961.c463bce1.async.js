"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2961],{2961:function(e,t,n){n.d(t,{Z:function(){return q}});var a,r=n(67294),o=n(93967),i=n.n(o),l=n(87462),s=n(4942),u=n(1413),c=n(74902),d=n(97685),f=n(91),p=n(67656),m=n(82234),g=n(87887),v=n(21770),x=n(71002),h=n(9220),b=n(8410),w=n(75164),y="\n  min-height:0 !important;\n  max-height:none !important;\n  height:0 !important;\n  visibility:hidden !important;\n  overflow:hidden !important;\n  position:absolute !important;\n  z-index:-1000 !important;\n  top:0 !important;\n  right:0 !important;\n  pointer-events: none !important;\n",C=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","font-variant","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing","word-break","white-space"],z={};function S(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.getAttribute("id")||e.getAttribute("data-reactid")||e.getAttribute("name");if(t&&z[n])return z[n];var a=window.getComputedStyle(e),r=a.getPropertyValue("box-sizing")||a.getPropertyValue("-moz-box-sizing")||a.getPropertyValue("-webkit-box-sizing"),o=parseFloat(a.getPropertyValue("padding-bottom"))+parseFloat(a.getPropertyValue("padding-top")),i=parseFloat(a.getPropertyValue("border-bottom-width"))+parseFloat(a.getPropertyValue("border-top-width")),l=C.map((function(e){return"".concat(e,":").concat(a.getPropertyValue(e))})).join(";"),s={sizingStyle:l,paddingSize:o,borderSize:i,boxSizing:r};return t&&n&&(z[n]=s),s}var Z=["prefixCls","defaultValue","value","autoSize","onResize","className","style","disabled","onChange","onInternalAutoSize"],E=r.forwardRef((function(e,t){var n=e,o=n.prefixCls,c=n.defaultValue,p=n.value,m=n.autoSize,g=n.onResize,C=n.className,z=n.style,E=n.disabled,$=n.onChange,A=(n.onInternalAutoSize,(0,f.Z)(n,Z)),I=(0,v.Z)(c,{value:p,postState:function(e){return null!=e?e:""}}),N=(0,d.Z)(I,2),R=N[0],O=N[1],j=r.useRef();r.useImperativeHandle(t,(function(){return{textArea:j.current}}));var F=r.useMemo((function(){return m&&"object"===(0,x.Z)(m)?[m.minRows,m.maxRows]:[]}),[m]),H=(0,d.Z)(F,2),P=H[0],T=H[1],V=!!m,k=r.useState(2),D=(0,d.Z)(k,2),M=D[0],L=D[1],W=r.useState(),B=(0,d.Z)(W,2),_=B[0],q=B[1],K=function(){L(0)};(0,b.Z)((function(){V&&K()}),[p,P,T,V]),(0,b.Z)((function(){if(0===M)L(1);else if(1===M){var e=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;a||((a=document.createElement("textarea")).setAttribute("tab-index","-1"),a.setAttribute("aria-hidden","true"),a.setAttribute("name","hiddenTextarea"),document.body.appendChild(a)),e.getAttribute("wrap")?a.setAttribute("wrap",e.getAttribute("wrap")):a.removeAttribute("wrap");var o=S(e,t),i=o.paddingSize,l=o.borderSize,s=o.boxSizing,u=o.sizingStyle;a.setAttribute("style","".concat(u,";").concat(y)),a.value=e.value||e.placeholder||"";var c,d=void 0,f=void 0,p=a.scrollHeight;if("border-box"===s?p+=l:"content-box"===s&&(p-=i),null!==n||null!==r){a.value=" ";var m=a.scrollHeight-i;null!==n&&(d=m*n,"border-box"===s&&(d=d+i+l),p=Math.max(d,p)),null!==r&&(f=m*r,"border-box"===s&&(f=f+i+l),c=p>f?"":"hidden",p=Math.min(f,p))}var g={height:p,overflowY:c,resize:"none"};return d&&(g.minHeight=d),f&&(g.maxHeight=f),g}(j.current,!1,P,T);L(2),q(e)}else!function(){try{if(document.activeElement===j.current){var e=j.current,t=e.selectionStart,n=e.selectionEnd,a=e.scrollTop;j.current.setSelectionRange(t,n),j.current.scrollTop=a}}catch(e){}}()}),[M]);var X=r.useRef(),J=function(){w.Z.cancel(X.current)};r.useEffect((function(){return J}),[]);var Y=V?_:null,G=(0,u.Z)((0,u.Z)({},z),Y);return 0!==M&&1!==M||(G.overflowY="hidden",G.overflowX="hidden"),r.createElement(h.Z,{onResize:function(e){2===M&&(null==g||g(e),m&&(J(),X.current=(0,w.Z)((function(){K()}))))},disabled:!(m||g)},r.createElement("textarea",(0,l.Z)({},A,{ref:j,style:G,className:i()(o,C,(0,s.Z)({},"".concat(o,"-disabled"),E)),disabled:E,value:R,onChange:function(e){O(e.target.value),null==$||$(e)}})))})),$=E,A=["defaultValue","value","onFocus","onBlur","onChange","allowClear","maxLength","onCompositionStart","onCompositionEnd","suffix","prefixCls","showCount","count","className","style","disabled","hidden","classNames","styles","onResize","onClear","onPressEnter","readOnly","autoSize","onKeyDown"],I=r.forwardRef((function(e,t){var n,a=e.defaultValue,o=e.value,x=e.onFocus,h=e.onBlur,b=e.onChange,w=e.allowClear,y=e.maxLength,C=e.onCompositionStart,z=e.onCompositionEnd,S=e.suffix,Z=e.prefixCls,E=void 0===Z?"rc-textarea":Z,I=e.showCount,N=e.count,R=e.className,O=e.style,j=e.disabled,F=e.hidden,H=e.classNames,P=e.styles,T=e.onResize,V=e.onClear,k=e.onPressEnter,D=e.readOnly,M=e.autoSize,L=e.onKeyDown,W=(0,f.Z)(e,A),B=(0,v.Z)(a,{value:o,defaultValue:a}),_=(0,d.Z)(B,2),q=_[0],K=_[1],X=null==q?"":String(q),J=r.useState(!1),Y=(0,d.Z)(J,2),G=Y[0],Q=Y[1],U=r.useRef(!1),ee=r.useState(null),te=(0,d.Z)(ee,2),ne=te[0],ae=te[1],re=(0,r.useRef)(null),oe=(0,r.useRef)(null),ie=function(){var e;return null===(e=oe.current)||void 0===e?void 0:e.textArea},le=function(){ie().focus()};(0,r.useImperativeHandle)(t,(function(){var e;return{resizableTextArea:oe.current,focus:le,blur:function(){ie().blur()},nativeElement:(null===(e=re.current)||void 0===e?void 0:e.nativeElement)||ie()}})),(0,r.useEffect)((function(){Q((function(e){return!j&&e}))}),[j]);var se=r.useState(null),ue=(0,d.Z)(se,2),ce=ue[0],de=ue[1];r.useEffect((function(){var e;ce&&(e=ie()).setSelectionRange.apply(e,(0,c.Z)(ce))}),[ce]);var fe,pe=(0,m.Z)(N,I),me=null!==(n=pe.max)&&void 0!==n?n:y,ge=Number(me)>0,ve=pe.strategy(X),xe=!!me&&ve>me,he=function(e,t){var n=t;!U.current&&pe.exceedFormatter&&pe.max&&pe.strategy(t)>pe.max&&t!==(n=pe.exceedFormatter(t,{max:pe.max}))&&de([ie().selectionStart||0,ie().selectionEnd||0]),K(n),(0,g.rJ)(e.currentTarget,e,b,n)},be=S;pe.show&&(fe=pe.showFormatter?pe.showFormatter({value:X,count:ve,maxLength:me}):"".concat(ve).concat(ge?" / ".concat(me):""),be=r.createElement(r.Fragment,null,be,r.createElement("span",{className:i()("".concat(E,"-data-count"),null==H?void 0:H.count),style:null==P?void 0:P.count},fe)));var we=!M&&!I&&!w;return r.createElement(p.Q,{ref:re,value:X,allowClear:w,handleReset:function(e){K(""),le(),(0,g.rJ)(ie(),e,b)},suffix:be,prefixCls:E,classNames:(0,u.Z)((0,u.Z)({},H),{},{affixWrapper:i()(null==H?void 0:H.affixWrapper,(0,s.Z)((0,s.Z)({},"".concat(E,"-show-count"),I),"".concat(E,"-textarea-allow-clear"),w))}),disabled:j,focused:G,className:i()(R,xe&&"".concat(E,"-out-of-range")),style:(0,u.Z)((0,u.Z)({},O),ne&&!we?{height:"auto"}:{}),dataAttrs:{affixWrapper:{"data-count":"string"==typeof fe?fe:void 0}},hidden:F,readOnly:D,onClear:V},r.createElement($,(0,l.Z)({},W,{autoSize:M,maxLength:y,onKeyDown:function(e){"Enter"===e.key&&k&&k(e),null==L||L(e)},onChange:function(e){he(e,e.target.value)},onFocus:function(e){Q(!0),null==x||x(e)},onBlur:function(e){Q(!1),null==h||h(e)},onCompositionStart:function(e){U.current=!0,null==C||C(e)},onCompositionEnd:function(e){U.current=!1,he(e,e.currentTarget.value),null==z||z(e)},className:i()(null==H?void 0:H.textarea),style:(0,u.Z)((0,u.Z)({},null==P?void 0:P.textarea),{},{resize:null==O?void 0:O.resize}),disabled:j,prefixCls:E,onResize:function(e){var t;null==T||T(e),null!==(t=ie())&&void 0!==t&&t.style.height&&ae(!0)},ref:oe,readOnly:D})))})),N=n(78290),R=n(9708),O=n(53124),j=n(98866),F=n(35792),H=n(98675),P=n(65223),T=n(27833),V=n(4173),k=n(47673),D=n(83559),M=n(83262),L=n(20353);const W=e=>{const{componentCls:t,paddingLG:n}=e,a=`${t}-textarea`;return{[`textarea${t}`]:{maxWidth:"100%",height:"auto",minHeight:e.controlHeight,lineHeight:e.lineHeight,verticalAlign:"bottom",transition:`all ${e.motionDurationSlow}`,resize:"vertical",[`&${t}-mouse-active`]:{transition:`all ${e.motionDurationSlow}, height 0s, width 0s`}},[`${t}-textarea-affix-wrapper-resize-dirty`]:{width:"auto"},[a]:{position:"relative","&-show-count":{[`${t}-data-count`]:{position:"absolute",bottom:e.calc(e.fontSize).mul(e.lineHeight).mul(-1).equal(),insetInlineEnd:0,color:e.colorTextDescription,whiteSpace:"nowrap",pointerEvents:"none"}},[`\n        &-allow-clear > ${t},\n        &-affix-wrapper${a}-has-feedback ${t}\n      `]:{paddingInlineEnd:n},[`&-affix-wrapper${t}-affix-wrapper`]:{padding:0,[`> textarea${t}`]:{fontSize:"inherit",border:"none",outline:"none",background:"transparent",minHeight:e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),"&:focus":{boxShadow:"none !important"}},[`${t}-suffix`]:{margin:0,"> *:not(:last-child)":{marginInline:0},[`${t}-clear-icon`]:{position:"absolute",insetInlineEnd:e.paddingInline,insetBlockStart:e.paddingXS},[`${a}-suffix`]:{position:"absolute",top:0,insetInlineEnd:e.paddingInline,bottom:0,zIndex:1,display:"inline-flex",alignItems:"center",margin:"auto",pointerEvents:"none"}}},[`&-affix-wrapper${t}-affix-wrapper-rtl`]:{[`${t}-suffix`]:{[`${t}-data-count`]:{direction:"ltr",insetInlineStart:0}}},[`&-affix-wrapper${t}-affix-wrapper-sm`]:{[`${t}-suffix`]:{[`${t}-clear-icon`]:{insetInlineEnd:e.paddingInlineSM}}}}}};var B=(0,D.I$)(["Input","TextArea"],(e=>{const t=(0,M.IX)(e,(0,L.e)(e));return[W(t)]}),L.T,{resetFont:!1}),_=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};var q=(0,r.forwardRef)(((e,t)=>{var n;const{prefixCls:a,bordered:o=!0,size:l,disabled:s,status:u,allowClear:c,classNames:d,rootClassName:f,className:p,style:m,styles:v,variant:x,showCount:h,onMouseDown:b,onResize:w}=e,y=_(e,["prefixCls","bordered","size","disabled","status","allowClear","classNames","rootClassName","className","style","styles","variant","showCount","onMouseDown","onResize"]);const{getPrefixCls:C,direction:z,allowClear:S,autoComplete:Z,className:E,style:$,classNames:A,styles:D}=(0,O.dj)("textArea"),M=r.useContext(j.Z),L=null!=s?s:M,{status:W,hasFeedback:q,feedbackIcon:K}=r.useContext(P.aM),X=(0,R.F)(W,u),J=r.useRef(null);r.useImperativeHandle(t,(()=>{var e;return{resizableTextArea:null===(e=J.current)||void 0===e?void 0:e.resizableTextArea,focus:e=>{var t,n;(0,g.nH)(null===(n=null===(t=J.current)||void 0===t?void 0:t.resizableTextArea)||void 0===n?void 0:n.textArea,e)},blur:()=>{var e;return null===(e=J.current)||void 0===e?void 0:e.blur()}}}));const Y=C("input",a),G=(0,F.Z)(Y),[Q,U,ee]=(0,k.TI)(Y,f),[te]=B(Y,G),{compactSize:ne,compactItemClassnames:ae}=(0,V.ri)(Y,z),re=(0,H.Z)((e=>{var t;return null!==(t=null!=l?l:ne)&&void 0!==t?t:e})),[oe,ie]=(0,T.Z)("textArea",x,o),le=(0,N.Z)(null!=c?c:S),[se,ue]=r.useState(!1),[ce,de]=r.useState(!1);return Q(te(r.createElement(I,Object.assign({autoComplete:Z},y,{style:Object.assign(Object.assign({},$),m),styles:Object.assign(Object.assign({},D),v),disabled:L,allowClear:le,className:i()(ee,G,p,f,ae,E,ce&&`${Y}-textarea-affix-wrapper-resize-dirty`),classNames:Object.assign(Object.assign(Object.assign({},d),A),{textarea:i()({[`${Y}-sm`]:"small"===re,[`${Y}-lg`]:"large"===re},U,null==d?void 0:d.textarea,A.textarea,se&&`${Y}-mouse-active`),variant:i()({[`${Y}-${oe}`]:ie},(0,R.Z)(Y,X)),affixWrapper:i()(`${Y}-textarea-affix-wrapper`,{[`${Y}-affix-wrapper-rtl`]:"rtl"===z,[`${Y}-affix-wrapper-sm`]:"small"===re,[`${Y}-affix-wrapper-lg`]:"large"===re,[`${Y}-textarea-show-count`]:h||(null===(n=e.count)||void 0===n?void 0:n.show)},U)}),prefixCls:Y,suffix:q&&r.createElement("span",{className:`${Y}-textarea-suffix`},K),showCount:h,ref:J,onResize:e=>{var t,n;if(null==w||w(e),se&&"function"==typeof getComputedStyle){const e=null===(n=null===(t=J.current)||void 0===t?void 0:t.nativeElement)||void 0===n?void 0:n.querySelector("textarea");e&&"both"===getComputedStyle(e).resize&&de(!0)}},onMouseDown:e=>{ue(!0),null==b||b(e);const t=()=>{ue(!1),document.removeEventListener("mouseup",t)};document.addEventListener("mouseup",t)}}))))}))}}]);