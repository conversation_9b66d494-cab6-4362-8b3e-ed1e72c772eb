"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6297],{17424:function(e,t,r){r.r(t),r.d(t,{default:function(){return de}});var n=r(13769),a=r.n(n),s=r(9783),i=r.n(s),c=r(64599),o=r.n(c),l=r(97857),u=r.n(l),d=r(15009),p=r.n(d),h=r(99289),f=r.n(h),x=r(5574),y=r.n(x),m=r(67294),v=r(97131),g=r(12453),j=r(71471),k=r(55102),Z=r(17788),b=r(47019),w=r(2453),_=r(42075),S=r(66309),C=r(83622),P=r(67839),T=r(11550),N=r(34041),z=r(72269),I=r(96074),A=r(47389),E=r(85175),O=r(87784),B=r(8751),F=r(82061),J=r(51042),L=r(88484),R=r(69753),q=r(78158);function D(e){return U.apply(this,arguments)}function U(){return(U=f()(p()().mark((function e(t){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,q.N)("/api/prompts",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function W(e){return V.apply(this,arguments)}function V(){return(V=f()(p()().mark((function e(t){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,q.N)("/api/prompts/".concat(t),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function G(e){return H.apply(this,arguments)}function H(){return(H=f()(p()().mark((function e(t){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,q.N)("/api/prompts",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function K(e){return Q.apply(this,arguments)}function Q(){return(Q=f()(p()().mark((function e(t){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,q.N)("/api/system_prompts",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Y(e,t){return M.apply(this,arguments)}function M(){return(M=f()(p()().mark((function e(t,r){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,q.N)("/api/prompts/".concat(t),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function X(e){return $.apply(this,arguments)}function $(){return($=f()(p()().mark((function e(t){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,q.N)("/api/prompts/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function ee(e){return te.apply(this,arguments)}function te(){return(te=f()(p()().mark((function e(t){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,q.N)("/api/prompts/".concat(t,"/copy"),{method:"POST"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function re(){return ne.apply(this,arguments)}function ne(){return(ne=f()(p()().mark((function e(){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,q.N)("/api/prompts/categories",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var ae=r(85893),se=["current","pageSize"],ie=j.Z.Title,ce=j.Z.Text,oe=j.Z.Paragraph,le=k.Z.TextArea,ue=Z.Z.confirm,de=function(){var e,t=(0,m.useRef)(),r=b.Z.useForm(),n=y()(r,1)[0],s=(0,m.useState)(!1),c=y()(s,2),l=c[0],d=c[1],h=(0,m.useState)(null),x=y()(h,2),j=x[0],q=x[1],U=(0,m.useState)(!1),V=y()(U,2),H=V[0],Q=V[1],M=(0,m.useState)(null),$=y()(M,2),te=$[0],ne=$[1],de=(0,m.useState)([]),pe=y()(de,2),he=pe[0],fe=pe[1],xe=(0,m.useState)(!1),ye=y()(xe,2),me=ye[0],ve=ye[1],ge=(0,m.useState)(!1),je=y()(ge,2),ke=je[0],Ze=je[1],be=(0,m.useState)([]),we=y()(be,2),_e=we[0],Se=we[1],Ce=(0,m.useState)([]),Pe=y()(Ce,2),Te=Pe[0],Ne=Pe[1],ze=(0,m.useState)(!1),Ie=y()(ze,2),Ae=Ie[0],Ee=Ie[1],Oe=[{label:"全部",desc:"所有模型",key:"all"},{label:"语言模型",desc:"文本处理",key:"language_model"},{label:"推理模型",desc:"推理模型",key:"reasoning_model"},{label:"多模态模型",desc:"多种数据类型处理",key:"multimodal_model"}];(0,m.useEffect)((function(){var e=function(){var e=f()(p()().mark((function e(){var t;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,re();case 3:(t=e.sent).success&&t.data&&fe(t.data),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取分类失败",e.t0),w.ZP.error("获取分类列表失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);var Be=function(e){q(e||null),e?n.setFieldsValue(u()(u()({},e),{},{title:e.title,content:e.content,category:e.category,positions:e.positions,models:e.models,language:e.language||"zh-CN",is_system:e.is_system,is_active:e.is_active})):(n.resetFields(),n.setFieldsValue({models:"language_model",category:[],positions:["analyst"],language:"zh-CN",is_system:!1,is_active:!0})),d(!0)},Fe=function(){var e=f()(p()().mark((function e(t){var r;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,W(t.id);case 3:(r=e.sent).success&&r.data&&(ne(r.data),Q(!0)),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取提示词详情失败",e.t0),w.ZP.error("获取提示词详情失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}(),Je=function(){var e=f()(p()().mark((function e(){var r,a,s,i;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,n.validateFields();case 3:if(r=e.sent,ve(!0),a=u()(u()({},r),{},{category:"string"==typeof r.category?[r.category]:Array.isArray(r.category)?r.category:[]}),!j){e.next=12;break}return e.next=9,Y(j.id,a);case 9:s=e.sent,e.next=15;break;case 12:return e.next=14,G(a);case 14:s=e.sent;case 15:s.success?(w.ZP.success("".concat(j?"更新":"创建","提示词成功")),d(!1),n.resetFields(),null===(i=t.current)||void 0===i||i.reload()):w.ZP.error(s.message||"".concat(j?"更新":"创建","提示词失败")),e.next=22;break;case 18:e.prev=18,e.t0=e.catch(0),console.error("表单验证或提交失败:",e.t0),w.ZP.error("".concat(j?"更新":"创建","提示词失败"));case 22:return e.prev=22,ve(!1),e.finish(22);case 25:case"end":return e.stop()}}),e,null,[[0,18,22,25]])})));return function(){return e.apply(this,arguments)}}(),Le=function(){var e=f()(p()().mark((function e(r){var n,a;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,ve(!0),e.next=4,ee(r);case 4:(n=e.sent).success?(w.ZP.success("复制提示词成功"),null===(a=t.current)||void 0===a||a.reload()):w.ZP.error(n.message||"复制提示词失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("复制失败:",e.t0),w.ZP.error("复制提示词失败");case 12:return e.prev=12,ve(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})));return function(t){return e.apply(this,arguments)}}(),Re=function(){var e=f()(p()().mark((function e(r,n){var a,s;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,ve(!0),e.next=4,Y(r,{is_active:!n});case 4:(a=e.sent).success?(w.ZP.success("".concat(n?"禁用":"启用","提示词成功")),null===(s=t.current)||void 0===s||s.reload()):w.ZP.error(a.message||"".concat(n?"禁用":"启用","提示词失败")),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("状态更新失败:",e.t0),w.ZP.error("".concat(n?"禁用":"启用","提示词失败"));case 12:return e.prev=12,ve(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})));return function(t,r){return e.apply(this,arguments)}}(),qe=function(){var e=f()(p()().mark((function e(){var r,n,a,s,i,c,l,u;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==Te.length){e.next=3;break}return w.ZP.warning("请至少选择一条数据进行导入"),e.abrupt("return");case 3:Ee(!0),r=_e.filter((function(e){return Te.includes(e.key)})),n=0,a=0,s=o()(r),e.prev=8,s.s();case 10:if((i=s.n()).done){e.next=28;break}if(c=i.value,e.prev=12,(l={title:c.title,content:c.content,category:c.category||[],positions:c.positions||[],is_system:!0,is_active:!0,models:c.models||["gpt4"],language:c.language||"zh-CN"}).title&&l.content){e.next=17;break}return a++,e.abrupt("continue",26);case 17:return e.next=19,K(l);case 19:n++,e.next=26;break;case 22:e.prev=22,e.t0=e.catch(12),a++,console.error("导入失败:",c,e.t0);case 26:e.next=10;break;case 28:e.next=33;break;case 30:e.prev=30,e.t1=e.catch(8),s.e(e.t1);case 33:return e.prev=33,s.f(),e.finish(33);case 36:Ee(!1),w.ZP.success("导入完成！成功 ".concat(n," 条，失败 ").concat(a," 条。")),n>0&&(null===(u=t.current)||void 0===u||u.reload()),Ze(!1),Se([]),Ne([]);case 42:case"end":return e.stop()}}),e,null,[[8,30,33,36],[12,22]])})));return function(){return e.apply(this,arguments)}}(),De=[{title:"提示词名称",dataIndex:"title",valueType:"text",render:function(e,t){return(0,ae.jsx)("a",{onClick:function(){return Fe(t)},children:t.title})}},{title:"分类",dataIndex:"category",valueType:"text",render:function(e,t){return(0,ae.jsx)(_.Z,{children:Array.isArray(t.category)?t.category.map((function(e){return(0,ae.jsx)(S.Z,{color:"blue",children:e},e)})):t.category&&(0,ae.jsx)(S.Z,{color:"blue",children:t.category})})},valueEnum:he.reduce((function(e,t){return u()(u()({},e),{},i()({},t,{text:t}))}),{})},{title:"类型",dataIndex:"is_system",valueType:"select",width:100,render:function(e,t){return(0,ae.jsx)(S.Z,{color:t.is_system?"success":"default",children:t.is_system?"系统提示词":"个人提示词"})},valueEnum:{true:{text:"系统提示词",status:"Success"},false:{text:"个人提示词",status:"Default"}}},{title:"创建者",dataIndex:"user",valueType:"text",width:100,fieldProps:{placeholder:"请输入创建者"}},{title:"状态",dataIndex:"is_active",valueType:"select",width:80,render:function(e,t){return(0,ae.jsx)(S.Z,{color:t.is_active?"success":"error",children:t.is_active?"已启用":"已禁用"})},valueEnum:{true:{text:"已启用",status:"Success"},false:{text:"已禁用",status:"Error"}}},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",sorter:!0,width:160,hideInSearch:!0},{title:"操作",dataIndex:"option",valueType:"option",width:240,render:function(e,r){return[(0,ae.jsx)(C.ZP,{type:"link",icon:(0,ae.jsx)(A.Z,{}),onClick:function(){return Be(r)},children:"编辑"},"edit"),(0,ae.jsx)(C.ZP,{type:"link",icon:(0,ae.jsx)(E.Z,{}),onClick:function(){return Le(r.id)},children:"复制"},"copy"),(0,ae.jsx)(C.ZP,{type:"link",icon:r.is_active?(0,ae.jsx)(O.Z,{}):(0,ae.jsx)(B.Z,{}),danger:r.is_active,onClick:function(){return Re(r.id,r.is_active)},children:r.is_active?"禁用":"启用"},"status"),(0,ae.jsx)(C.ZP,{type:"link",danger:!0,icon:(0,ae.jsx)(F.Z,{}),onClick:function(){return e=r.id,void ue({title:"确认删除提示词",content:"您确定要删除这个提示词吗？此操作无法撤销。",okText:"确定删除",cancelText:"取消",okButtonProps:{danger:!0},onOk:(n=f()(p()().mark((function r(){var n,a;return p()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,ve(!0),r.next=4,X(e);case 4:(n=r.sent).success?(w.ZP.success("删除提示词成功"),null===(a=t.current)||void 0===a||a.reload()):w.ZP.error(n.message||"删除提示词失败"),r.next=12;break;case 8:r.prev=8,r.t0=r.catch(0),console.error("删除失败:",r.t0),w.ZP.error("删除提示词失败");case 12:return r.prev=12,ve(!1),r.finish(12);case 15:case"end":return r.stop()}}),r,null,[[0,8,12,15]])}))),function(){return n.apply(this,arguments)})});var e,n},children:"删除"},"delete")]}}];return(0,ae.jsxs)(v._z,{children:[(0,ae.jsx)(g.Z,{headerTitle:"提示词管理",actionRef:t,rowKey:"id",search:{labelWidth:"auto",defaultCollapsed:!1},toolBarRender:function(){return[(0,ae.jsx)(C.ZP,{type:"primary",onClick:function(){return Be()},icon:(0,ae.jsx)(J.Z,{}),children:"新建提示词"},"create"),(0,ae.jsx)(C.ZP,{onClick:function(){return Ze(!0)},icon:(0,ae.jsx)(L.Z,{}),children:"批量导入系统提示词"},"import"),(0,ae.jsx)(C.ZP,{onClick:function(){var e=document.createElement("a");e.href="/static/templates/提示词导入模版.json",e.download="提示词导入模版.json",document.body.appendChild(e),e.click(),document.body.removeChild(e)},icon:(0,ae.jsx)(R.Z,{}),children:"下载导入模板"},"downloadTemplate")]},request:f()(p()().mark((function e(){var t,r,n,s,i,c=arguments;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},r=t.current,n=t.pageSize,s=a()(t,se),e.prev=2,e.next=5,D(u()({current:r,pageSize:n},s));case 5:return i=e.sent,e.abrupt("return",{data:i.data||[],success:i.success,total:i.total||0});case 9:return e.prev=9,e.t0=e.catch(2),console.error("获取提示词失败:",e.t0),w.ZP.error("获取提示词列表失败"),e.abrupt("return",{data:[],success:!1,total:0});case 14:case"end":return e.stop()}}),e,null,[[2,9]])}))),columns:De,rowSelection:{selections:[P.Z.SELECTION_ALL,P.Z.SELECTION_INVERT]},pagination:{showQuickJumper:!0,showSizeChanger:!0}}),(0,ae.jsxs)(Z.Z,{title:"批量导入系统提示词",open:ke,onCancel:function(){Ze(!1),Se([]),Ne([])},width:1e3,destroyOnClose:!0,footer:[(0,ae.jsx)(C.ZP,{onClick:function(){return Ze(!1)},children:"取消"},"back"),(0,ae.jsxs)(C.ZP,{type:"primary",loading:Ae,onClick:qe,children:["保存选中的 ",Te.length," 条"]},"submit")],children:[(0,ae.jsxs)(T.Z.Dragger,{name:"file",multiple:!1,beforeUpload:function(e){var t=new FileReader;return t.onload=function(t){try{var r;if(!e.name.endsWith(".json"))return void w.ZP.error("请上传JSON格式的文件");var n=null===(r=t.target)||void 0===r?void 0:r.result,a=JSON.parse(n);if(!Array.isArray(a))return void w.ZP.error("JSON文件内容必须是数组格式");var s=a.map((function(e,t){var r=e.positions;if("string"==typeof r)if(r.startsWith("[")&&r.endsWith("]"))try{r=JSON.parse(r)}catch(e){r=r.split(",").map((function(e){return e.trim()}))}else r=r.split(",").map((function(e){return e.trim()}));return Array.isArray(r)||(r=["analyst"]),u()(u()({},e),{},{positions:r,key:t})}));Se(s),Ne(s.map((function(e){return e.key})))}catch(e){w.ZP.error("文件解析失败，请检查是否为合法的JSON格式")}},t.readAsText(e,"UTF-8"),!1},onRemove:function(){return Se([])},accept:".json",style:{marginBottom:20},children:[(0,ae.jsx)("p",{className:"ant-upload-drag-icon",children:(0,ae.jsx)(L.Z,{})}),(0,ae.jsx)("p",{className:"ant-upload-text",children:"点击或拖拽 JSON 文件到此区域进行上传"}),(0,ae.jsx)("p",{className:"ant-upload-hint",children:"仅支持JSON格式。文件内容应为数组，数组项包含 'title', 'content', 'category', 'positions' 等字段。"})]}),_e.length>0&&(0,ae.jsx)(P.Z,{rowSelection:{selectedRowKeys:Te,onChange:Ne},dataSource:_e,columns:[{title:"提示词名称",dataIndex:"title",key:"title",width:"20%"},{title:"提示词内容",dataIndex:"content",key:"content",width:"40%",ellipsis:!0},{title:"分类",dataIndex:"category",key:"category",width:"20%",render:function(e){return Array.isArray(e)?e.join(", "):e}},{title:"适用岗位",dataIndex:"positions",key:"positions",width:"20%",render:function(e){return Array.isArray(e)?e.join(", "):e}}],pagination:{pageSize:5}})]}),(0,ae.jsx)(Z.Z,{title:j?"编辑提示词":"新建提示词",open:l,onCancel:function(){return d(!1)},destroyOnClose:!0,footer:[(0,ae.jsx)(C.ZP,{onClick:function(){return d(!1)},children:"取消"},"cancel"),(0,ae.jsx)(C.ZP,{type:"primary",onClick:Je,loading:me,children:"确定"},"submit")],width:600,children:(0,ae.jsxs)(b.Z,{form:n,layout:"vertical",initialValues:{models:["gpt4"],category:[],positions:["analyst"],language:"zh-CN",is_system:!1,is_active:!0},children:[(0,ae.jsx)(b.Z.Item,{name:"title",label:"提示词名称",rules:[{required:!0,message:"请输入提示词名称"}],children:(0,ae.jsx)(k.Z,{placeholder:"请输入提示词名称"})}),(0,ae.jsx)(b.Z.Item,{name:"content",label:"提示词内容",rules:[{required:!0,message:"请输入提示词内容"}],children:(0,ae.jsx)(le,{placeholder:"请输入提示词内容",rows:6,style:{resize:"none"}})}),(0,ae.jsx)(b.Z.Item,{name:"category",label:"分类",rules:[{required:!0,message:"请选择分类"}],children:(0,ae.jsx)(N.default,{mode:"tags",options:he.map((function(e){return{label:e,value:e}})),placeholder:"请选择分类"})}),(0,ae.jsx)(b.Z.Item,{name:"positions",label:"适用岗位",rules:[{required:!0,message:"请选择适用岗位"}],children:(0,ae.jsx)(N.default,{mode:"multiple",options:[{label:"金融分析师",value:"analyst"},{label:"风险管理师",value:"risk_manager"},{label:"投资经理",value:"investment_manager"}],placeholder:"请选择适用岗位"})}),(0,ae.jsx)(b.Z.Item,{name:"models",label:"使用模型",rules:[{required:!0,message:"请选择使用模型"}],children:(0,ae.jsx)(N.default,{options:Oe.filter((function(e){return"all"!==e.key})).map((function(e){return{label:e.label,value:e.key}})),placeholder:"请选择使用模型"})}),(0,ae.jsx)(b.Z.Item,{name:"language",label:"语言",rules:[{required:!0,message:"请选择语言"}],children:(0,ae.jsx)(N.default,{options:[{label:"中文",value:"zh-CN"},{label:"英文",value:"en-US"}],placeholder:"请选择语言"})}),(0,ae.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[(0,ae.jsx)(b.Z.Item,{name:"is_system",label:"是否为系统提示词",valuePropName:"checked",children:(0,ae.jsx)(z.Z,{checkedChildren:"是",unCheckedChildren:"否"})}),(0,ae.jsx)(b.Z.Item,{name:"is_active",label:"是否启用",valuePropName:"checked",children:(0,ae.jsx)(z.Z,{checkedChildren:"启用",unCheckedChildren:"禁用",defaultChecked:!0})})]})]})}),te&&(0,ae.jsxs)(Z.Z,{title:(0,ae.jsx)(ie,{level:5,style:{margin:0},children:te.title}),open:H,onCancel:function(){Q(!1),ne(null)},width:700,footer:[(0,ae.jsx)(C.ZP,{icon:(0,ae.jsx)(E.Z,{}),onClick:function(){navigator.clipboard.writeText(te.content),w.ZP.success("提示词内容已复制到剪贴板")},children:"复制内容"},"copy"),(0,ae.jsx)(C.ZP,{type:"primary",onClick:function(){Q(!1),ne(null)},children:"关闭"},"close")],children:[(0,ae.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,ae.jsxs)("div",{style:{display:"flex",flexWrap:"wrap",gap:"16px",marginBottom:"16px"},children:[(0,ae.jsxs)("div",{children:[(0,ae.jsx)(ce,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"分类"}),(0,ae.jsx)("div",{children:Array.isArray(te.category)?te.category.map((function(e){return(0,ae.jsx)(S.Z,{color:"blue",children:e},e)})):te.category&&(0,ae.jsx)(S.Z,{color:"blue",children:te.category})})]}),(0,ae.jsxs)("div",{children:[(0,ae.jsx)(ce,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"适用模型"}),(0,ae.jsx)("div",{children:(0,ae.jsx)(S.Z,{color:"green",children:(null===(e=Oe.find((function(e){return e.key===te.models})))||void 0===e?void 0:e.label)||te.models})})]}),(0,ae.jsxs)("div",{children:[(0,ae.jsx)(ce,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"适用岗位"}),(0,ae.jsx)("div",{children:te.positions&&te.positions.map((function(e){return(0,ae.jsx)(S.Z,{color:"gold",children:"analyst"===e?"金融分析师":"risk_manager"===e?"风险管理师":"investment_manager"===e?"投资经理":e},e)}))})]}),(0,ae.jsxs)("div",{children:[(0,ae.jsx)(ce,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"语言"}),(0,ae.jsx)("div",{children:(0,ae.jsx)(S.Z,{color:"purple",children:"zh-CN"===te.language?"中文":"en-US"===te.language?"英文":te.language})})]})]}),(0,ae.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[(0,ae.jsxs)("div",{children:[(0,ae.jsx)(ce,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"类型"}),(0,ae.jsx)(S.Z,{color:te.is_system?"success":"default",children:te.is_system?"系统提示词":"个人提示词"})]}),(0,ae.jsxs)("div",{children:[(0,ae.jsx)(ce,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"状态"}),(0,ae.jsx)(S.Z,{color:te.is_active?"success":"error",children:te.is_active?"已启用":"已禁用"})]})]})]}),(0,ae.jsx)(I.Z,{orientation:"left",children:"提示词内容"}),(0,ae.jsx)(oe,{style:{whiteSpace:"pre-wrap",maxHeight:"40vh",overflowY:"auto",fontSize:"14px",lineHeight:"1.8",padding:"16px",backgroundColor:"#f9f9f9",borderRadius:"8px",border:"1px solid #f0f0f0"},children:te.content}),(0,ae.jsxs)("div",{style:{marginTop:20,paddingTop:16,borderTop:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between"},children:[(0,ae.jsxs)("div",{children:[(0,ae.jsxs)(ce,{type:"secondary",style:{fontSize:"12px"},children:["创建时间: ",new Date(te.created_at).toLocaleString()]}),te.updated_at&&te.updated_at!==te.created_at&&(0,ae.jsxs)(ce,{type:"secondary",style:{fontSize:"12px",marginLeft:"16px"},children:["更新时间: ",new Date(te.updated_at).toLocaleString()]})]}),(0,ae.jsxs)(ce,{type:"secondary",style:{fontSize:"12px"},children:["创建者: ",te.user]})]})]})]})}}}]);