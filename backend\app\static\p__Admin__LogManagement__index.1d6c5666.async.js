"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4766],{82061:function(e,r,t){var n=t(1413),o=t(67294),a=t(47046),l=t(91146),c=function(e,r){return o.createElement(l.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:a.Z}))},s=o.forwardRef(c);r.Z=s},11475:function(e,r,t){t.d(r,{Z:function(){return s}});var n=t(1413),o=t(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"},l=t(91146),c=function(e,r){return o.createElement(l.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:a}))};var s=o.forwardRef(c)},57916:function(e,r,t){t.r(r),t.d(r,{default:function(){return R}});var n=t(97857),o=t.n(n),a=t(13769),l=t.n(a),c=t(15009),s=t.n(c),i=t(99289),d=t.n(i),u=t(5574),p=t.n(u),h=t(97131),f=t(12453),g=t(17788),m=t(54880),x=t(2453),v=(t(42075),t(83622)),b=t(66309),y=t(26412),C=t(67294),k=t(78158);function j(e){return w.apply(this,arguments)}function w(){return(w=d()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,k.N)("/api/access-logs",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function I(e){return S.apply(this,arguments)}function S(){return(S=d()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,k.N)("/api/access-logs/".concat(r),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Z(e){return T.apply(this,arguments)}function T(){return(T=d()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,k.N)("/api/access-logs/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var $=t(11475),P=t(55287),E=t(82061),O=t(27484),_=t.n(O),z=t(85893),N=["timeRange","current","pageSize"],B=g.Z.confirm,R=(m.default.RangePicker,function(){var e=(0,C.useState)(!1),r=p()(e,2),t=r[0],n=r[1],a=(0,C.useState)(void 0),c=p()(a,2),i=c[0],u=c[1],m=(0,C.useState)(!1),k=p()(m,2),w=k[0],S=k[1],T=(0,C.useRef)(),O=function(){var e=d()(s()().mark((function e(r){var t;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return S(!0),e.prev=1,e.next=4,I(r.id);case 4:(t=e.sent).success&&t.data?(u(t.data),n(!0)):x.ZP.error("获取日志详情失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(1),x.ZP.error("获取日志详情失败"),console.error(e.t0);case 12:return e.prev=12,S(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[1,8,12,15]])})));return function(r){return e.apply(this,arguments)}}(),R=function(){var e=d()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:B({title:"确认删除",icon:(0,z.jsx)($.Z,{}),content:"确定要删除这条日志记录吗？删除后无法恢复。",onOk:function(){var e=d()(s()().mark((function e(){var t,n,o;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=x.ZP.loading("正在删除"),e.prev=1,e.next=4,Z(r.id);case 4:n=e.sent,t(),n.success?(x.ZP.success("删除成功"),null===(o=T.current)||void 0===o||o.reload()):x.ZP.error(n.message||"删除失败"),e.next=14;break;case 9:e.prev=9,e.t0=e.catch(1),t(),x.ZP.error("删除失败，请重试"),console.error(e.t0);case 14:case"end":return e.stop()}}),e,null,[[1,9]])})));return function(){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),H=[{title:"ID",dataIndex:"id",valueType:"text",hideInTable:!0,hideInSearch:!0},{title:"路径",dataIndex:"route",valueType:"text",ellipsis:!0,width:200},{title:"方法",dataIndex:"method",valueType:"select",valueEnum:{GET:{text:"GET",status:"Default"},POST:{text:"POST",status:"Processing"},PUT:{text:"PUT",status:"Warning"},DELETE:{text:"DELETE",status:"Error"},PATCH:{text:"PATCH",status:"Success"}},width:80},{title:"状态码",dataIndex:"status_code",valueType:"digit",width:80,render:function(e,r){return(0,z.jsx)(b.Z,{color:r.status_code<400?"green":"red",children:r.status_code})}},{title:"响应时间",dataIndex:"response_time",valueType:"digit",hideInSearch:!0,width:100,render:function(e,r){return(0,z.jsxs)(b.Z,{color:r.response_time>1e3?"red":"green",children:[r.response_time.toFixed(2)," ms"]})}},{title:"用户",dataIndex:"user_name",valueType:"text",width:100},{title:"IP地址",dataIndex:"ip_address",valueType:"text",hideInSearch:!0,width:120},{title:"时间",dataIndex:"created_at",valueType:"dateTime",hideInSearch:!0,width:160},{title:"时间范围",dataIndex:"timeRange",valueType:"dateTimeRange",hideInTable:!0},{title:"操作",dataIndex:"option",valueType:"option",width:120,render:function(e,r){return[(0,z.jsx)(v.ZP,{type:"link",icon:(0,z.jsx)(P.Z,{}),onClick:function(){return O(r)},children:"详情"},"view-".concat(r.id)),(0,z.jsx)(v.ZP,{type:"link",danger:!0,icon:(0,z.jsx)(E.Z,{}),onClick:function(){return R(r)},children:"删除"},"delete-".concat(r.id))]}}];return(0,z.jsxs)(h._z,{children:[(0,z.jsx)(f.Z,{headerTitle:"访问日志",actionRef:T,rowKey:"id",search:{labelWidth:120,defaultCollapsed:!1},toolBarRender:function(){return[]},request:function(){var e=d()(s()().mark((function e(r){var t,n,a,c,i,d,u,p,h;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.timeRange,n=r.current,a=void 0===n?1:n,c=r.pageSize,i=void 0===c?10:c,d=l()(r,N),t&&2===t.length&&(u=t[0],p=t[1]),e.prev=2,e.next=5,j(o()(o()({current:a,pageSize:i},d),{},{start_time:u,end_time:p}));case 5:return h=e.sent,e.abrupt("return",{data:h.data||[],success:h.success,total:h.total||0});case 9:return e.prev=9,e.t0=e.catch(2),x.ZP.error("获取日志列表失败"),e.abrupt("return",{data:[],success:!1,total:0});case 13:case"end":return e.stop()}}),e,null,[[2,9]])})));return function(r){return e.apply(this,arguments)}}(),columns:H,pagination:{showQuickJumper:!0}}),(0,z.jsx)(g.Z,{title:"日志详情",open:t,onCancel:function(){return n(!1)},footer:null,width:800,children:w?(0,z.jsx)("div",{style:{textAlign:"center",padding:"20px"},children:"加载中..."}):i?(0,z.jsxs)(y.Z,{bordered:!0,column:1,children:[(0,z.jsx)(y.Z.Item,{label:"请求路径",children:i.route}),(0,z.jsx)(y.Z.Item,{label:"请求方法",children:i.method}),(0,z.jsx)(y.Z.Item,{label:"状态码",children:i.status_code}),(0,z.jsxs)(y.Z.Item,{label:"响应时间",children:[i.response_time.toFixed(2)," ms"]}),(0,z.jsx)(y.Z.Item,{label:"用户",children:i.user_name||"-"}),(0,z.jsx)(y.Z.Item,{label:"IP地址",children:i.ip_address||"-"}),(0,z.jsx)(y.Z.Item,{label:"User Agent",children:i.user_agent||"-"}),(0,z.jsx)(y.Z.Item,{label:"时间",children:_()(i.created_at).format("YYYY-MM-DD HH:mm:ss")}),(0,z.jsx)(y.Z.Item,{label:"查询参数",children:(0,z.jsx)("pre",{children:JSON.stringify(i.params||{},null,2)})}),(0,z.jsx)(y.Z.Item,{label:"请求体",children:(0,z.jsx)("pre",{children:JSON.stringify(i.request_body||{},null,2)})}),i.error&&(0,z.jsx)(y.Z.Item,{label:"错误信息",children:(0,z.jsxs)("div",{style:{color:"red"},children:[(0,z.jsxs)("div",{children:["错误类型: ",i.error.type]}),(0,z.jsxs)("div",{children:["错误信息: ",i.error.message]}),i.error.module&&(0,z.jsxs)("div",{children:["错误模块: ",i.error.module]}),i.error.line&&(0,z.jsxs)("div",{children:["错误行号: ",i.error.line]}),i.error.stack_trace&&(0,z.jsxs)("div",{children:[(0,z.jsx)("div",{children:"堆栈跟踪:"}),(0,z.jsx)("pre",{style:{maxHeight:"200px",overflow:"auto"},children:i.error.stack_trace})]})]})})]}):(0,z.jsx)("div",{children:"未找到日志详情"})})]})})},66309:function(e,r,t){t.d(r,{Z:function(){return P}});var n=t(67294),o=t(93967),a=t.n(o),l=t(98423),c=t(98787),s=t(69760),i=t(96159),d=t(45353),u=t(53124),p=t(11568),h=t(15063),f=t(14747),g=t(83262),m=t(83559);const x=e=>{const{lineWidth:r,fontSizeIcon:t,calc:n}=e,o=e.fontSizeSM;return(0,g.IX)(e,{tagFontSize:o,tagLineHeight:(0,p.bf)(n(e.lineHeightSM).mul(o).equal()),tagIconSize:n(t).sub(n(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},v=e=>({defaultBg:new h.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var b=(0,m.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:t,tagPaddingHorizontal:n,componentCls:o,calc:a}=e,l=a(n).sub(t).equal(),c=a(r).sub(t).equal();return{[o]:Object.assign(Object.assign({},(0,f.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:c,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:l}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(x(e))),v),y=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)r.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(t[n[o]]=e[n[o]])}return t};const C=n.forwardRef(((e,r)=>{const{prefixCls:t,style:o,className:l,checked:c,onChange:s,onClick:i}=e,d=y(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:h}=n.useContext(u.E_),f=p("tag",t),[g,m,x]=b(f),v=a()(f,`${f}-checkable`,{[`${f}-checkable-checked`]:c},null==h?void 0:h.className,l,m,x);return g(n.createElement("span",Object.assign({},d,{ref:r,style:Object.assign(Object.assign({},o),null==h?void 0:h.style),className:v,onClick:e=>{null==s||s(!c),null==i||i(e)}})))}));var k=C,j=t(98719);var w=(0,m.bk)(["Tag","preset"],(e=>(e=>(0,j.Z)(e,((r,t)=>{let{textColor:n,lightBorderColor:o,lightColor:a,darkColor:l}=t;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:n,background:a,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:l,borderColor:l},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(x(e))),v);const I=(e,r,t)=>{const n="string"!=typeof(o=t)?o:o.charAt(0).toUpperCase()+o.slice(1);var o;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${t}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var S=(0,m.bk)(["Tag","status"],(e=>{const r=x(e);return[I(r,"success","Success"),I(r,"processing","Info"),I(r,"error","Error"),I(r,"warning","Warning")]}),v),Z=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)r.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(t[n[o]]=e[n[o]])}return t};const T=n.forwardRef(((e,r)=>{const{prefixCls:t,className:o,rootClassName:p,style:h,children:f,icon:g,color:m,onClose:x,bordered:v=!0,visible:y}=e,C=Z(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:k,direction:j,tag:I}=n.useContext(u.E_),[T,$]=n.useState(!0),P=(0,l.Z)(C,["closeIcon","closable"]);n.useEffect((()=>{void 0!==y&&$(y)}),[y]);const E=(0,c.o2)(m),O=(0,c.yT)(m),_=E||O,z=Object.assign(Object.assign({backgroundColor:m&&!_?m:void 0},null==I?void 0:I.style),h),N=k("tag",t),[B,R,H]=b(N),D=a()(N,null==I?void 0:I.className,{[`${N}-${m}`]:_,[`${N}-has-color`]:m&&!_,[`${N}-hidden`]:!T,[`${N}-rtl`]:"rtl"===j,[`${N}-borderless`]:!v},o,p,R,H),M=e=>{e.stopPropagation(),null==x||x(e),e.defaultPrevented||$(!1)},[,L]=(0,s.Z)((0,s.w)(e),(0,s.w)(I),{closable:!1,closeIconRender:e=>{const r=n.createElement("span",{className:`${N}-close-icon`,onClick:M},e);return(0,i.wm)(e,r,(e=>({onClick:r=>{var t;null===(t=null==e?void 0:e.onClick)||void 0===t||t.call(e,r),M(r)},className:a()(null==e?void 0:e.className,`${N}-close-icon`)})))}}),A="function"==typeof C.onClick||f&&"a"===f.type,F=g||null,W=F?n.createElement(n.Fragment,null,F,f&&n.createElement("span",null,f)):f,q=n.createElement("span",Object.assign({},P,{ref:r,className:D,style:z}),W,L,E&&n.createElement(w,{key:"preset",prefixCls:N}),O&&n.createElement(S,{key:"status",prefixCls:N}));return B(A?n.createElement(d.Z,{component:"Tag"},q):q)})),$=T;$.CheckableTag=k;var P=$}}]);