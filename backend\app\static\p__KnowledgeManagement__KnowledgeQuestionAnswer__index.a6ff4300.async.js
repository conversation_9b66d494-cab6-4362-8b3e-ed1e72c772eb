"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9440],{82947:function(e,n){n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"}},52197:function(e,n){n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"}}]},name:"star",theme:"filled"}},92287:function(e,n){n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"}},28508:function(e,n,t){var r=t(1413),o=t(67294),s=t(89503),a=t(91146),i=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},c=o.forwardRef(i);n.Z=c},82061:function(e,n,t){var r=t(1413),o=t(67294),s=t(47046),a=t(91146),i=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},c=o.forwardRef(i);n.Z=c},34804:function(e,n,t){var r=t(1413),o=t(67294),s=t(66023),a=t(91146),i=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},c=o.forwardRef(i);n.Z=c},47389:function(e,n,t){var r=t(1413),o=t(67294),s=t(27363),a=t(91146),i=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},c=o.forwardRef(i);n.Z=c},12906:function(e,n,t){t.d(n,{Z:function(){return c}});var r=t(1413),o=t(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM512 533c-85.5 0-155.6 67.3-160 151.6a8 8 0 008 8.4h48.1c4.2 0 7.8-3.2 8.1-7.4C420 636.1 461.5 597 512 597s92.1 39.1 95.8 88.6c.3 4.2 3.9 7.4 8.1 7.4H664a8 8 0 008-8.4C667.6 600.3 597.5 533 512 533z"}}]},name:"frown",theme:"outlined"},a=t(91146),i=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s}))};var c=o.forwardRef(i)},38545:function(e,n,t){t.d(n,{Z:function(){return c}});var r=t(1413),o=t(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 512a48 48 0 1096 0 48 48 0 10-96 0zm200 0a48 48 0 1096 0 48 48 0 10-96 0zm-400 0a48 48 0 1096 0 48 48 0 10-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z"}}]},name:"message",theme:"outlined"},a=t(91146),i=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s}))};var c=o.forwardRef(i)},43471:function(e,n,t){var r=t(1413),o=t(67294),s=t(82947),a=t(91146),i=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},c=o.forwardRef(i);n.Z=c},25820:function(e,n,t){var r=t(1413),o=t(67294),s=t(52197),a=t(91146),i=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},c=o.forwardRef(i);n.Z=c},75750:function(e,n,t){t.d(n,{Z:function(){return c}});var r=t(1413),o=t(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"},a=t(91146),i=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s}))};var c=o.forwardRef(i)},87784:function(e,n,t){t.d(n,{Z:function(){return c}});var r=t(1413),o=t(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"},a=t(91146),i=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s}))};var c=o.forwardRef(i)},98165:function(e,n,t){t.d(n,{Z:function(){return c}});var r=t(1413),o=t(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"},a=t(91146),i=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s}))};var c=o.forwardRef(i)},64029:function(e,n,t){var r=t(1413),o=t(67294),s=t(92287),a=t(91146),i=function(e,n){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},c=o.forwardRef(i);n.Z=c},10964:function(e,n,t){t.r(n),t.d(n,{default:function(){return De}});var r=t(64599),o=t.n(r),s=t(19632),a=t.n(s),i=t(15009),c=t.n(i),l=t(99289),u=t.n(l),d=t(97857),f=t.n(d),p=t(9783),x=t.n(p),g=t(13769),h=t.n(g),m=t(5574),v=t.n(m),y=t(67294),b=t(93461),k=t(34114),j=t(78205),w=t(78919),Z=t(4628),S=t(24495),_=t(9502),C=t(76654),P=t(26058),z=t(83622),I=t(2453),B=t(17788),R=t(42075),T=t(86250),E=t(74330),N=t(83062),D=t(55102),W=t(85265),M=t(2487),O=t(38703),A=t(72269),H=t(10981),K=t(78404),L=t(16596),F=t(14079),Y=t(29158),q=t(38545),G=t(51042),J=t(82061),U=t(15360),V=t(64029),$=t(34804),X=t(47389),Q=t(87784),ee=t(25820),ne=t(75750),te=t(12906),re=t(85175),oe=t(43471),se=t(28508),ae=t(98165),ie=t(43425),ce=t(40110),le=t(27484),ue=t.n(le),de=(0,t(24444).kc)((function(e){var n=e.token;return{reference:{background:"".concat(n.colorBgLayout,"80"),minWidth:"400px",maxWidth:"720px",width:"50%"},layout:{width:"100%",minWidth:"800px",borderRadius:n.borderRadius,display:"flex",background:n.colorBgContainer,fontFamily:"AlibabaPuHuiTi, ".concat(n.fontFamily,", sans-serif"),".ant-prompts":{color:n.colorText},border:"3px solid",borderImage:"linear-gradient(45deg, ".concat(n.colorPrimary,", ").concat(n.colorSplit,") 1")},menu:{height:"100%",display:"flex",flexDirection:"column"},conversations:{padding:"0 12px",flex:1,overflowY:"auto"},chat:{height:"100%",width:"60%",maxWidth:"900px",margin:"0 auto",boxSizing:"border-box",display:"flex",flexDirection:"column",padding:n.paddingLG,gap:"16px"},messages:{flex:1},placeholder:{paddingTop:"32px"},sender:{boxShadow:n.boxShadow},customSender:{border:"1px solid #d9d9d9",borderRadius:"8px",padding:"12px",background:"#fff",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",".ant-input":{border:"none",boxShadow:"none",resize:"none",fontSize:"14px"},".toolbar":{display:"flex",justifyContent:"space-between",alignItems:"center",marginTop:"8px",paddingTop:"8px",borderTop:"1px solid #f0f0f0"},".tools-left":{display:"flex",alignItems:"center",gap:"8px"},".deep-thinking":{display:"flex",alignItems:"center",padding:"4px 8px",borderRadius:"4px",fontSize:"13px",fontWeight:500}},logo:{display:"flex",height:"72px",alignItems:"center",justifyContent:"start",padding:"0 24px",boxSizing:"border-box",img:{width:"24px",height:"24px",display:"inline-block"},span:{display:"inline-block",margin:"0 8px",fontWeight:"bold",color:n.colorText,fontSize:"16px"}},addBtn:{background:"#1677ff0f",border:"1px solid #1677ff34",width:"calc(100% - 24px)",margin:"0 12px 24px 12px"},wiseAnswer:{display:"block",padding:"12px",background:"#e6f7ff",borderRadius:"8px",borderLeft:"4px solid #1890ff",margin:"8px 0"}}})),fe=t(93933),pe=t(79554),xe=t(58258),ge=t(13973),he=t(45435),me=t(78158);function ve(e,n){return ye.apply(this,arguments)}function ye(){return(ye=u()(c()().mark((function e(n,t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,me.N)("/api/conversations/".concat(n,"/knowledge_ids"),{method:"PUT",data:{knowledge_ids:t}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function be(e){return ke.apply(this,arguments)}function ke(){return(ke=u()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,me.N)("/api/conversation_source_files",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var je=t(85893),we=["href","children"],Ze=["href","children"],Se=P.Z.Sider;function _e(e){return e+"-"+Date.now()}var Ce=y.createContext({expandedRefs:{},setExpandedRefs:function(){}}),Pe={key:"1",label:"热门合规主题",children:[{key:"1-1",description:"如何确保金融产品合规销售？",icon:(0,je.jsx)("span",{style:{color:"#f93a4a",fontWeight:700},children:"1"})},{key:"1-2",description:"反洗钱相关规定有哪些？",icon:(0,je.jsx)("span",{style:{color:"#ff6565",fontWeight:700},children:"2"})},{key:"1-3",description:"客户信息保护要求有哪些？",icon:(0,je.jsx)("span",{style:{color:"#ff8f1f",fontWeight:700},children:"3"})},{key:"1-4",description:"合规风险管控的主要措施有哪些？",icon:(0,je.jsx)("span",{style:{color:"#00000040",fontWeight:700},children:"4"})},{key:"1-5",description:"如何应对监管检查？",icon:(0,je.jsx)("span",{style:{color:"#00000040",fontWeight:700},children:"5"})}]},ze={key:"2",label:"知识库对话指南",children:[{key:"2-1",icon:(0,je.jsx)(L.Z,{}),label:"文件上传",description:"上传文件到知识库，支持PDF、Word、Excel等多种格式"},{key:"2-2",icon:(0,je.jsx)(F.Z,{}),label:"知识库选择",description:"选择一个或多个知识库进行精准问答"},{key:"2-3",icon:(0,je.jsx)(Y.Z,{}),label:"引用查看",description:"查看回答的知识来源，点击引用可查看原文详情"},{key:"2-4",icon:(0,je.jsx)(q.Z,{}),label:"对话交互",description:"与知识库进行多轮对话，获取专业准确的回答"}]},Ie=[{key:"historyConversation",description:"历史对话",icon:(0,je.jsx)(F.Z,{style:{color:"#FF4D4F"}})},{key:"newConversation",description:"新建对话",icon:(0,je.jsx)(G.Z,{style:{color:"#1890FF"}})},{key:"clearConversation",description:"清空对话",icon:(0,je.jsx)(J.Z,{style:{color:"#1890FF"}})},{key:"promptTemplate",description:"提示词模版",icon:(0,je.jsx)(U.Z,{style:{color:"#52C41A"}})}],Be=(0,H.bG)(),Re=(0,K.kH)(),Te="chat2kb",Ee=function(e){return"string"!=typeof e?"":e.replace(/\[\[citation:(\d+)\]\]/g,(function(e,n){return"[".concat(n,"](#citation-").concat(n,")")}))},Ne=function(e){var n=e.content,t=e.messageId,r=(0,y.useState)(!1),o=v()(r,2),s=o[0],a=o[1],i=function(e){if("string"!=typeof e)return{processedContent:"",thinkBlocks:[]};var n=[];return{processedContent:e.replace(/<think>([\s\S]*?)<\/think>/g,(function(e,t){return n.push(t.trim()),""})).trim(),thinkBlocks:n}}(n),c=i.processedContent,l=i.thinkBlocks,u=y.useContext(Ce).setExpandedRefs;return(0,je.jsxs)("div",{style:{position:"relative"},children:[function(){try{if(!c)return null;var e=Ee(c),n={a:function(e){var n=e.href,r=e.children,o=h()(e,we);if(n&&n.startsWith("#citation-")){var s=n.replace("#citation-",""),a=parseInt(s)-1;return(0,je.jsx)("span",{style:{color:"#1890ff",fontWeight:"bold",fontSize:"0.9em",cursor:"pointer",textDecoration:"underline",margin:"0 2px"},onClick:function(){var e="".concat(t,"-").concat(a);u((function(n){return n[e]?{}:x()({},e,!0)})),setTimeout((function(){var n=document.querySelector('[data-ref-key="'.concat(e,'"]'));n&&n.scrollIntoView({behavior:"smooth",block:"center"})}),100)},children:r})}return(0,je.jsx)("a",f()(f()({href:n},o),{},{children:r}))},p:function(e){var n=e.children;return(0,je.jsx)("p",{style:{marginBottom:"0.6em",marginTop:"0.6em"},children:n})}};return(0,je.jsx)("div",{className:"markdown-content",style:{lineHeight:1.5},children:(0,je.jsx)(he.UG,{components:n,children:e})})}catch(e){return console.error("渲染内容时出错:",e),(0,je.jsx)("div",{style:{color:"red"},children:"内容渲染失败"})}}(),l.length>0&&(0,je.jsxs)("div",{style:{marginTop:12},children:[(0,je.jsx)(z.ZP,{type:"text",size:"small",icon:s?(0,je.jsx)(V.Z,{}):(0,je.jsx)($.Z,{}),onClick:function(){return a(!s)},style:{color:"#666",fontSize:12,padding:0,height:"auto",background:"transparent"},children:(0,je.jsxs)("span",{style:{marginLeft:4},children:[s?"收起":"展开","思考过程 (",l.length,")"]})}),s&&(0,je.jsx)("div",{style:{marginTop:8,padding:12,backgroundColor:"#f8f9fa",border:"1px solid #e9ecef",borderRadius:6,fontSize:"13px",lineHeight:1.4,color:"#495057"},children:l.map((function(e,n){return(0,je.jsx)("div",{style:{marginBottom:n<l.length-1?8:0},children:(0,je.jsx)(he.UG,{components:{p:function(e){var n=e.children;return(0,je.jsx)("p",{style:{marginBottom:"0.6em",marginTop:"0.6em"},children:n})}},children:e})},n)}))})]})]})},De=function(){var e=(0,y.useState)({}),n=v()(e,2),t=n[0],r=n[1],s=(0,y.useState)(!1),i=v()(s,2),l=i[0],d=i[1],p=(0,y.useState)(null),g=v()(p,2),m=g[0],P=g[1],K=de().styles,q=(0,y.useState)(window.innerHeight),U=v()(q,1)[0],V=y.useRef(),$=y.useState([]),le=v()($,2),me=le[0],ye=le[1],ke=y.useRef(null),we=y.useRef(null),De=y.useState(""),We=v()(De,2),Me=We[0],Oe=We[1],Ae=y.useState([]),He=v()(Ae,2),Ke=He[0],Le=He[1],Fe=y.useState(),Ye=v()(Fe,2),qe=Ye[0],Ge=Ye[1],Je=y.useState(void 0),Ue=v()(Je,2),Ve=Ue[0],$e=Ue[1],Xe=(0,y.useState)(!1),Qe=v()(Xe,2),en=Qe[0],nn=Qe[1],tn=(0,y.useState)(!1),rn=v()(tn,2),on=rn[0],sn=rn[1],an=(0,y.useState)(!1),cn=v()(an,2),ln=cn[0],un=cn[1],dn=(0,y.useState)(!1),fn=v()(dn,2),pn=fn[0],xn=fn[1],gn=(0,y.useState)(""),hn=v()(gn,2),mn=hn[0],vn=hn[1],yn=(0,y.useState)(""),bn=v()(yn,2),kn=bn[0],jn=bn[1],wn=(0,y.useState)([]),Zn=v()(wn,2),Sn=Zn[0],_n=Zn[1],Cn=(0,y.useRef)(null),Pn=(0,y.useState)(!1),zn=v()(Pn,2),In=zn[0],Bn=zn[1],Rn=(0,y.useState)(""),Tn=v()(Rn,2),En=Tn[0],Nn=Tn[1],Dn=(0,y.useState)([]),Wn=v()(Dn,2),Mn=Wn[0],On=Wn[1],An=(0,y.useState)([]),Hn=v()(An,2),Kn=Hn[0],Ln=Hn[1],Fn=(0,y.useState)(!1),Yn=v()(Fn,2),qn=Yn[0],Gn=Yn[1],Jn=y.useState([]),Un=v()(Jn,2),Vn=Un[0],$n=Un[1],Xn=(0,y.useState)({}),Qn=v()(Xn,2),et=Qn[0],nt=Qn[1],tt=(0,y.useState)(!1),rt=v()(tt,2),ot=rt[0],st=rt[1],at=(0,y.useState)(!1),it=v()(at,2),ct=it[0],lt=it[1],ut=function(){var e=u()(c()().mark((function e(n){var t,r;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n){e.next=3;break}return I.ZP.warning("没有活跃的对话"),e.abrupt("return");case 3:return e.prev=3,e.next=6,be({knowledge_base_id:n,pageSize:100});case 6:(t=e.sent)&&t.success&&Array.isArray(t.data)&&(r=t.data.map((function(e){return{uid:e.id,name:e.filename,status:"completed"===e.processing_status?"done":"error"===e.processing_status?"error":"uploading",url:e.url,size:e.size,type:e.data_type,created_at:e.created_at,chunk_count:e.chunk_count,processing_status:e.processing_status}})),ye(r)),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(3),console.error("刷新文件状态失败:",e.t0),I.ZP.error("刷新文件状态失败");case 14:case"end":return e.stop()}}),e,null,[[3,10]])})));return function(n){return e.apply(this,arguments)}}(),dt=function(e){Nn(e),Bn(!0)},ft=function(e){var n=Sn.find((function(n){return n.message_id===e}));n&&navigator.clipboard.writeText(n.content).then((function(){I.ZP.success("复制成功")})).catch((function(){I.ZP.error("复制失败")}))},pt=(0,y.useRef)({selectedKnowledgeBaseIds:Kn,availableKnowledgeBases:Mn,activeConversationKey:qe,conversationKnowledgeBaseId:Ve});(0,y.useEffect)((function(){pt.current={selectedKnowledgeBaseIds:Kn,availableKnowledgeBases:Mn,activeConversationKey:qe,conversationKnowledgeBaseId:Ve}}),[Kn,Mn,qe,Ve]);var xt,gt=function(){var e=u()(c()().mark((function e(){var n,t,r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=(0,H.bG)(),console.log("🚀 ~ fetchKnowledgeBases ~ userInfo:",n),e.prev=2,e.next=5,(0,fe.fx)({user_id:parseInt((null==n?void 0:n.id)||"0")});case 5:return t=e.sent,console.log("🚀 ~ fetchKnowledgeBases ~ response:",t),r=t.data||[],On(r),Kn.length>0&&(o=Kn.filter((function(e){return r.some((function(n){return n._id===e}))}))).length!==Kn.length&&(console.log("🚀 ~ fetchKnowledgeBases ~ 更新有效的知识库IDs:",o),Ln(o)),e.abrupt("return",r);case 13:return e.prev=13,e.t0=e.catch(2),console.error("Error fetching knowledge bases:",e.t0),e.abrupt("return",[]);case 17:case"end":return e.stop()}}),e,null,[[2,13]])})));return function(){return e.apply(this,arguments)}}(),ht=(0,b.Z)({request:(xt=u()(c()().mark((function e(n,t){var r,s,i,l,u,d,f,p,x,g,h,m,v,y,b,k,j,w,Z,S,_,C,P,z,B,R,T,E,N,D,W,M,O,A,H,K,L,F,Y,q,G;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.messages,s=n.message,i=t.onSuccess,l=t.onUpdate,u=t.onError,e.prev=2,f=pt.current,p=f.selectedKnowledgeBaseIds,x=f.availableKnowledgeBases,g=f.conversationKnowledgeBaseId,console.log("🚀 ~ request中的最新状态:",{selectedKnowledgeBaseIds:p,availableKnowledgeBases:x,conversationKnowledgeBaseId:g}),!on){e.next=8;break}return I.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 8:if(sn(!0),h=s?s.id:_e(V.current),s){e.next=14;break}return i({content:"出现了异常: 消息为空",role:"assistant",id:h,references:[],collected:!1,query:[]}),sn(!1),e.abrupt("return");case 14:if(m=p||[],g&&(m=a()(new Set([].concat(a()(m),[g])))),console.log("🚀 ~ 最终的selectedIds:",m),v={conversation_id:V.current||"",message_id:h,meta_data:{},extra:{},role:s?s.role:"user",content:s?s.content:"",app_info:Te,user_id:null==Be?void 0:Be.id,user_name:null==Be?void 0:Be.name,references:[],token_count:null,price:null,collected:!1,created_at:ue()().format("YYYY-MM-DD HH:mm:ss"),knowledge_ids:m,contextData:[],query:[]},_n((function(e){var n=[].concat(a()(e),[v]);return console.log("更新后的消息列表:",n),n})),V.current){e.next=22;break}throw I.ZP.error("No active conversation selected"),new Error("No active conversation selected");case 22:return console.log("activeKey===>",V.current),y={conversation_id:V.current,app_info:Te,user_id:parseInt(null==Be?void 0:Be.id),user_name:null==Be?void 0:Be.name,extra:{},messages:r,kb_id:V.current?a()(new Set([].concat(a()(m),[V.current]))):m},b={id:_e(V.current),role:"assistant",content:"",references:[],collected:!1,query:[]},k=!1,j="",w=[],l(b),e.next=31,(0,fe.zl)(y);case 31:if(Z=e.sent,console.log("response===>",Z),Z.ok){e.next=35;break}throw new Error("HTTP 错误！状态码：".concat(Z.status));case 35:if(S=null===(d=Z.body)||void 0===d?void 0:d.getReader()){e.next=38;break}throw new Error("当前浏览器不支持 ReadableStream。");case 38:_=new TextDecoder("utf-8"),console.log("userInfo===>",Be),C={conversation_id:V.current||"",message_id:b.id,meta_data:{},extra:{},role:"assistant",content:"",app_info:Te,user_id:null==Be?void 0:Be.id,user_name:null==Be?void 0:Be.name,references:[],token_count:null,price:null,collected:!1,created_at:ue()().format("YYYY-MM-DD HH:mm:ss"),knowledge_ids:m};case 41:if(k){e.next=112;break}return e.next=44,S.read();case 44:P=e.sent,z=P.value,P.done&&(k=!0),j+=_.decode(z,{stream:!0}),B=j.split("\n\n"),j=B.pop()||"",R=o()(B),e.prev=52,R.s();case 54:if((T=R.n()).done){e.next=102;break}if(""!==(E=T.value).trim()){e.next=58;break}return e.abrupt("continue",100);case 58:N=E.split("\n"),D=null,W=null,M=o()(N);try{for(M.s();!(O=M.n()).done;)(A=O.value).startsWith("event: ")?D=A.substring(7).trim():A.startsWith("data: ")&&(W=A.substring(6))}catch(e){M.e(e)}finally{M.f()}if(!W){e.next=100;break}e.t0=D,e.next="answer"===e.t0?67:"moduleStatus"===e.t0?79:"appStreamResponse"===e.t0?81:"flowResponses"===e.t0?83:"end"===e.t0?85:"error"===e.t0?87:100;break;case 67:if("[DONE]"===W){e.next=78;break}e.prev=68,K=JSON.parse(W),(L=(null===(H=K.choices[0])||void 0===H||null===(H=H.delta)||void 0===H?void 0:H.content)||"")&&(b.content+=L,l(b)),e.next=78;break;case 74:return e.prev=74,e.t1=e.catch(68),console.error("Error parsing answer data:",e.t1),e.abrupt("return",i({content:"出现了异常:"+W,role:"assistant",id:_e(V.current),references:[],query:[],collected:!1}));case 78:return e.abrupt("break",100);case 79:try{F=JSON.parse(W),console.log("模块状态：",F)}catch(e){console.error("Error parsing moduleStatus data:",e)}return e.abrupt("break",100);case 81:try{Y=JSON.parse(W),console.log("appStreamData===>",Y),console.log("appStreamData[0].context===>",Y[0].context),w=Y[0].context,b.references=w}catch(e){console.error("Error parsing appStreamResponse data:",e)}return e.abrupt("break",100);case 83:try{console.log("flowResponsesData",W)}catch(e){console.error("Error parsing flowResponses data:",e)}return e.abrupt("break",100);case 85:return k=!0,e.abrupt("break",100);case 87:e.prev=87,k=!0,q=JSON.parse(W),b.content=q.message,C.role="assistant",l(b),e.next=99;break;case 95:throw e.prev=95,e.t2=e.catch(87),console.error("Error event received:",e.t2),e.t2;case 99:return e.abrupt("break",100);case 100:e.next=54;break;case 102:e.next=107;break;case 104:e.prev=104,e.t3=e.catch(52),R.e(e.t3);case 107:return e.prev=107,R.f(),e.finish(107);case 110:e.next=41;break;case 112:if(console.info(b),i(b),!b.content||""===b.content.trim()){e.next=121;break}return C.content=b.content,C.references=w,e.next=119,(0,fe.tn)(C);case 119:(G=e.sent).success?(C.message_id=G.data.message_id,console.log("创建消息成功，返回数据:",G.data),_n((function(e){var n=[].concat(a()(e),[G.data]);return console.log("更新后的消息列表:",n),n}))):I.ZP.error("消息上报失败");case 121:e.next=128;break;case 123:e.prev=123,e.t4=e.catch(2),console.log("error===>",e.t4),i({content:"出现了异常，系统正在处理其他对话。请稍后重试",role:"assistant",id:_e(V.current),references:[],collected:!1,query:[]}),u(e.t4 instanceof Error?e.t4:new Error("Unknown error"));case 128:return e.prev=128,sn(!1),e.finish(128);case 131:case"end":return e.stop()}}),e,null,[[2,123,128,131],[52,104,107,110],[68,74],[87,95]])}))),function(e,n){return xt.apply(this,arguments)})}),mt=v()(ht,1)[0],vt=(0,k.Z)({agent:mt}),yt=vt.onRequest,bt=vt.messages,kt=vt.setMessages,jt=function(e){console.log("activeKey 设置",e),V.current=e,Ge(e)},wt=function(e){var n=e.filter((function(e){return e.pinned})).sort((function(e,n){return new Date(n.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return f()(f()({},e),{},{key:e.id||"",label:e.conversation_name||e.id,group:"置顶"})})),t=e.filter((function(e){return!e.pinned})).sort((function(e,n){return new Date(n.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return f()(f()({},e),{},{key:e.id||"",label:e.conversation_name||"",group:"对话"})}));Le([].concat(a()(n),a()(t)))},Zt=function(e){return 0===e.length?null:e.reduce((function(e,n){return new Date(n.active_at)>new Date(e.active_at)?n:e}))},St=function(){var e=u()(c()().mark((function e(n){var t,r,o,s,a,i;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,sn(!0),console.info("获取对话信息",n),(t=Mn)&&0!==t.length||console.log("🚀 ~ fetchConversationMessages ~ 可用知识库列表为空，但是我们应该在页面加载时已经获取了"),r=ue()().format("YYYY-MM-DD HH:mm:ss"),e.next=8,(0,fe.$o)(n,{conversation_name:null,active_at:r,pinned_at:null,pinned:null});case 8:o=e.sent,console.log("🚀 ~ fetchConversationMessages ~ result:",o),o&&o.knowledge_ids&&console.log("🚀 ~ 获取到对话绑定的知识库IDs:",o.knowledge_ids),ye([]),o&&o.uploaded_files&&Array.isArray(o.uploaded_files)&&(console.log("🚀 ~ 获取到对话上传的文件:",o.uploaded_files),(s=o.uploaded_files.map((function(e){return{uid:e.id,name:e.filename,status:"completed"===e.processing_status?"done":"error"===e.processing_status?"error":"uploading",url:e.url,size:e.size,type:e.data_type,created_at:e.created_at,chunk_count:e.chunk_count,processing_status:e.processing_status}}))).length>0&&ye(s)),null!=o&&o.messages?(console.info("设置对话信息",o.messages),a=o.messages.map((function(e){return{id:e.message_id,message:{id:e.message_id,content:e.content,role:e.role,references:e.references||[],collected:e.collected||!1,query:e.query||[]},status:"assistant"===e.role?"success":"local",meta:{avatar:"assistant"===e.role?(null==Re?void 0:Re.logo)||"/static/logo.png":(null==Be?void 0:Be.avatar)||"/avatar/default.jpeg"}}})),_n(o.messages),kt(a),jt(n),Ln([]),$e(void 0),o.knowledge_ids&&Array.isArray(o.knowledge_ids)&&o.knowledge_ids.length>0?(console.log("🚀 ~ 获取到对话绑定的知识库IDs:",o.knowledge_ids),i=o.knowledge_ids.filter((function(e){return t.some((function(n){return n._id===e}))})),console.log("🚀 ~ 筛选后的知识库IDs:",i),i.length>0&&Ln(i)):console.log("🚀 ~ 对话没有绑定知识库IDs")):I.ZP.error("获取对话信息失败"),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(0),console.error("切换对话时出错：",e.t0);case 19:return e.prev=19,sn(!1),Ge(n),e.finish(19);case 23:case"end":return e.stop()}}),e,null,[[0,16,19,23]])})));return function(n){return e.apply(this,arguments)}}(),_t=function(){var e=u()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(V.current){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,(0,fe.Db)(V.current);case 4:e.sent.success?(_n([]),kt([]),Cn.current&&Cn.current.updateReferenceList([])):I.ZP.error("清空对话失败");case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Ct=function(){var e=u()(c()().mark((function e(){var n,t,r,o,s,i,l,u;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!on){e.next=3;break}return I.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(!(n=(0,H.bG)())){e.next=27;break}return e.prev=5,Ln([]),ye([]),nt({}),$e(void 0),t=(new Date).toLocaleString(),r="对话-".concat(t),o=[],e.next=15,(0,fe.Xw)({user_id:parseInt(n.id),user_name:n.name,conversation_name:r,app_info:Te,knowledge_ids:o});case 15:s=e.sent,i={key:s.id||"",id:s.id||"",label:s.conversation_name||"",group:"对话",conversation_name:s.conversation_name||"",app_info:Te,active_at:s.active_at||"",pinned_at:s.pinned_at,pinned:s.pinned||!1,messages:[],knowledge_ids:o,uploaded_files:[]},l=Ke.filter((function(e){return!e.pinned})),u=Ke.filter((function(e){return e.pinned})),Le([].concat(a()(u),[i],a()(l))),jt(s.id||""),_t(),e.next=27;break;case 24:e.prev=24,e.t0=e.catch(5),console.error("创建新对话时出错：",e.t0);case 27:case"end":return e.stop()}}),e,null,[[5,24]])})));return function(){return e.apply(this,arguments)}}(),Pt=function(){var e=u()(c()().mark((function e(n){var t,r,o,s,a;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=Ke.find((function(e){return e.key===n}))){e.next=3;break}return e.abrupt("return");case 3:return r=t.pinned,o=!r,e.prev=6,s=ue()().format("YYYY-MM-DD HH:mm:ss"),e.next=10,(0,fe.X1)(n,{conversation_name:null,active_at:null,pinned:o,pinned_at:s});case 10:a=Ke.map((function(e){return e.key===n?f()(f()({},e),{},{pinned:o}):e})),wt(a),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(6),console.error("更新置顶状态时出错：",e.t0);case 17:case"end":return e.stop()}}),e,null,[[6,14]])})));return function(n){return e.apply(this,arguments)}}(),zt=function(){var e=u()(c()().mark((function e(n){var t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!on){e.next=3;break}return I.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(console.info("conversationsItems===>",Ke),t=Ke.some((function(e){return e.key===n}))){e.next=10;break}return I.ZP.error("对话不存在或已被删除"),e.abrupt("return");case 10:console.info("对话存在",t);case 11:return console.log("onConversationClick===>",n),nt({}),e.next=15,St(n);case 15:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),It=function(){var e=u()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:B.Z.confirm({title:"确认删除",content:"确定要删除这个对话吗？删除后不可恢复。",okText:"确认删除",okType:"danger",cancelText:"取消",onOk:function(){return u()(c()().mark((function e(){var t,r;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,t=null,r=Ke.filter((function(e){return e.key!==n})),console.log("更新 updatedItems===>",r),r.length>0&&(t=r[0].key),console.log(" nextActiveId===>",t),console.log("删除 deleteConversation===>",n),e.next=9,(0,fe.SJ)(n);case 9:wt(r),setTimeout((function(){t?(console.log("激活 ===>",t),zt(t)):(console.log("新建 ===>",t),Ct())}),0),e.next=17;break;case 13:e.prev=13,e.t0=e.catch(0),console.error("删除对话时出错：",e.t0),I.ZP.error("删除对话失败");case 17:case"end":return e.stop()}}),e,null,[[0,13]])})))()}});case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),Bt=function(){var e=u()(c()().mark((function e(n,t){var r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,Ke.find((function(e){return e.key===n}))){e.next=4;break}return e.abrupt("return");case 4:return r={conversation_name:t,active_at:null,pinned_at:null,pinned:null},e.next=7,(0,fe.X1)(n,r);case 7:null!=(o=e.sent)&&o.success?Le((function(e){return e.map((function(e){return e.key===n?f()(f()({},e),{},{label:t}):e}))})):I.ZP.error("更新对话标题失败"),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("更新对话标题时出错：",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(n,t){return e.apply(this,arguments)}}();(0,y.useEffect)((function(){var e=function(){var e=u()(c()().mark((function e(){var n,t,r;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=(0,H.bG)(),e.next=3,gt();case 3:if(!n){e.next=32;break}return e.prev=4,sn(!0),e.next=8,(0,fe.Mw)({user_id:n.id,app_info:Te});case 8:if(!(t=e.sent).success||!Array.isArray(t.data)){e.next=24;break}if(0!==t.data.length){e.next=15;break}return e.next=13,Ct();case 13:e.next=24;break;case 15:if(r=Zt(t.data),wt(t.data),!r){e.next=22;break}return e.next=20,St(r.id);case 20:e.next=24;break;case 22:return e.next=24,St(t.data[0].id||"");case 24:e.next=29;break;case 26:e.prev=26,e.t0=e.catch(4),console.error("初始化对话时出错：",e.t0);case 29:return e.prev=29,sn(!1),e.finish(29);case 32:case"end":return e.stop()}}),e,null,[[4,26,29,32]])})));return function(){return e.apply(this,arguments)}}();e()}),[Te]);var Rt=function(){var e=u()(c()().mark((function e(n){var t,r,o,s;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("重新生成消息:",n),e.prev=1,t=Sn.findIndex((function(e){return e.message_id===n})),console.log("currentIndex===>",t),-1!==t){e.next=7;break}return I.ZP.error("未找到指定消息"),e.abrupt("return");case 7:return r=Sn[t],o=Sn.slice(t),console.log("将要删除的消息:",o),e.next=12,(0,fe.qP)(o.map((function(e){return e.message_id})));case 12:e.sent.success||I.ZP.error("删除消息失败"),_n((function(e){return e.slice(0,t)})),kt((function(e){return e.slice(0,t)})),"assistant"===r.role?(s=Sn.slice(0,t).reverse().find((function(e){return"user"===e.role})))&&yt({id:n,role:"user",content:s.content,references:[],query:[],collected:!1}):yt({id:n,role:"user",content:r.content,references:[],query:[],collected:!1}),I.ZP.success("正在重新生成回复..."),e.next=24;break;case 20:e.prev=20,e.t0=e.catch(1),console.error("重新生成消息时出错：",e.t0),I.ZP.error("重新生成消息失败");case 24:case"end":return e.stop()}}),e,null,[[1,20]])})));return function(n){return e.apply(this,arguments)}}(),Tt=function(){var e=u()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:B.Z.confirm({title:"确认删除",content:"确定要删除这条消息吗？删除后不可恢复。",okText:"确认",cancelText:"取消",onOk:function(){return u()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("delete messageId===>",n),e.next=4,(0,fe.$Z)(n);case 4:e.sent.success?(_n((function(e){return e.filter((function(e){return e.message_id!==n}))})),console.log("delete currentConversationMessages===>",Sn),kt((function(e){return e.filter((function(e){return e.message.id!==n}))})),I.ZP.success("消息及相关引用已删除")):I.ZP.error("删除消息失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除消息时出错：",e.t0),I.ZP.error("删除消息失败");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})))()}});case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),Et=function(){var e=u()(c()().mark((function e(n,t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("收藏状态切换:",n,t),e.next=3,(0,fe.bk)({message_id:n,collected:!t});case 3:e.sent.success?(I.ZP.success(t?"取消收藏成功":"收藏成功"),kt((function(e){return e.map((function(e){return e.id===n?f()(f()({},e),{},{message:f()(f()({},e.message),{},{collected:!t})}):e}))})),_n((function(e){return e.map((function(e){return e.message_id===n?f()(f()({},e),{},{collected:!t}):e}))}))):I.ZP.error(t?"取消收藏失败":"收藏失败");case 5:case"end":return e.stop()}}),e)})));return function(n,t){return e.apply(this,arguments)}}(),Nt=function(){var e=u()(c()().mark((function e(n){var t,r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n){e.next=2;break}return e.abrupt("return");case 2:if(t=Kn&&Kn.length>0,r=me&&me.length>0,o=!!Ve,t||r||o){e.next=8;break}return I.ZP.warning("您还尚未选择知识库，请先选择知识库！"),e.abrupt("return");case 8:yt({id:_e(V.current),role:"user",content:n,references:[],collected:!1,query:[]}),$n([]),Oe("");case 11:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),Dt=function(){var e=u()(c()().mark((function e(n){var t,r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!on){e.next=3;break}return I.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(t=n.data,r=t.key,o=t.description,"historyConversation"!==r){e.next=8;break}nn(!0),e.next=19;break;case 8:if("newConversation"!==r){e.next=13;break}return e.next=11,Ct();case 11:e.next=19;break;case 13:if("clearConversation"!==r){e.next=18;break}return e.next=16,_t();case 16:e.next=19;break;case 18:if("promptTemplate"===r)un(!0);else if("knowledgeBaseSetting"===r)try{sn(!0),Gn(!0)}catch(e){console.error("加载知识库数据失败:",e),I.ZP.error("加载知识库数据失败"),Gn(!1)}finally{sn(!1)}else yt({id:_e(V.current),role:"user",content:o,references:[],collected:!1,query:[]});case 19:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),Wt=function(e,n){n&&n.stopPropagation(),P(e),d(!0)},Mt=(0,je.jsxs)(R.Z,{direction:"vertical",size:16,style:{paddingInline:"calc(calc(100% - 700px) /2)"},className:K.placeholder,children:[(0,je.jsx)(j.Z,{variant:"borderless",icon:(0,je.jsx)("img",{src:(null==Re?void 0:Re.logo)||"/static/logo.png",alt:"logo"}),title:"你好，我是知识库对话助手",description:"基于您的知识库内容，为您提供精准的问答服务"}),(0,je.jsxs)(T.Z,{gap:16,children:[(0,je.jsx)(w.Z,{items:[Pe],styles:{list:{height:"100%"},item:{flex:1,backgroundImage:"linear-gradient(123deg, #e5f4ff 0%, #efe7ff 100%)",borderRadius:12,border:"none"},subItem:{padding:0,background:"transparent"}},onItemClick:Dt}),(0,je.jsx)(w.Z,{items:[ze],styles:{item:{flex:1,backgroundImage:"linear-gradient(123deg, #e5f4ff 0%, #efe7ff 100%)",borderRadius:12,border:"none"},subItem:{background:"#ffffffa6"}}})]})]}),Ot=bt.length>0?bt.map((function(e){var n=e.id,o=e.message,s=e.status;return{key:V.current+"_"+n,loadingRender:function(){return(0,je.jsxs)(R.Z,{children:[(0,je.jsx)(E.Z,{size:"small"}),"模型思考中..."]})},loading:"loading"===s&&o.content.length<1,content:o.content,messageRender:function(e){return function(e,n){if("string"!=typeof e)return String(e);if(!e.includes("<think>")){var t=Ee(e),o={a:function(e){var t=e.href,o=e.children,s=h()(e,Ze);if(t&&t.startsWith("#citation-")){var a=t.replace("#citation-",""),i=parseInt(a)-1;return(0,je.jsx)("span",{style:{color:"#1890ff",fontWeight:"bold",fontSize:"0.9em",cursor:"pointer",textDecoration:"underline",margin:"0 2px"},onClick:function(){var e="".concat(n,"-").concat(i);r((function(n){return n[e]?{}:x()({},e,!0)})),setTimeout((function(){var n=document.querySelector('[data-ref-key="'.concat(e,'"]'));n&&n.scrollIntoView({behavior:"smooth",block:"center"})}),100)},children:o})}return(0,je.jsx)("a",f()(f()({href:t},s),{},{children:o}))},p:function(e){var n=e.children;return(0,je.jsx)("p",{style:{marginBottom:"0.6em",marginTop:"0.6em"},children:n})}};return(0,je.jsx)("div",{className:"markdown-content",style:{lineHeight:1.5},children:(0,je.jsx)(he.UG,{components:o,children:t})})}return(0,je.jsx)(Ne,{content:e,messageId:n})}(e,o.id)},shape:"local"===s?"corner":"round",variant:"local"===s?"filled":"borderless",avatar:"local"===s?{src:(null==Be?void 0:Be.avatar)||"/avatar/default.jpeg"}:{src:(null==Re?void 0:Re.logo)||"/static/logo.png"},placement:"local"!==s?"start":"end",footer:"local"!==s?(0,je.jsxs)(T.Z,{children:[o.references&&o.references.length>0&&(0,je.jsxs)("div",{style:{marginTop:8,width:"100%"},children:[(0,je.jsxs)("div",{style:{fontWeight:"bold",marginBottom:8,fontSize:13},children:["引用来源 (",o.references.length,")"]}),(0,je.jsx)("div",{children:o.references.map((function(e,n){return(0,je.jsxs)("div",{style:{marginBottom:8},"data-ref-index":n,"data-ref-key":"".concat(o.id,"-").concat(n),children:[(0,je.jsxs)("div",{style:{display:"flex",alignItems:"center",cursor:"pointer",padding:"8px 13px",border:"1px solid #f0f0f0",borderRadius:t["".concat(o.id,"-").concat(n)]?"4px 4px 0 0":4,backgroundColor:"#fafafa",justifyContent:"space-between",width:"100%"},onClick:function(n){n.stopPropagation(),Wt(e,n)},children:[(0,je.jsxs)("div",{style:{fontWeight:"bold",fontSize:12,color:"#666",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:"80%"},children:[n+1,". ",e.source_name&&e.source_name.length>25?e.source_name.slice(0,25)+"...":e.source_name||"来源 ".concat(n+1)]}),(0,je.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:8,flexShrink:0},children:[(0,je.jsx)(z.ZP,{type:"text",size:"small",icon:(0,je.jsx)(F.Z,{}),onClick:function(n){return Wt(e,n)},style:{fontSize:12,color:"#1890ff",padding:"0 4px",height:20,border:"none",flexShrink:0},title:"查看详情"}),(0,je.jsx)("span",{style:{fontSize:12,color:"#999",cursor:"pointer",flexShrink:0},onClick:function(e){e.stopPropagation();var t="".concat(o.id,"-").concat(n);r((function(e){return e[t]?{}:x()({},t,!0)}))},children:t["".concat(o.id,"-").concat(n)]?"▲":"▼"})]})]}),t["".concat(o.id,"-").concat(n)]&&(0,je.jsx)("div",{style:{padding:8,border:"1px solid #f0f0f0",borderTop:"none",borderRadius:"0 0 4px 4px",backgroundColor:"#fff",fontSize:12,lineHeight:1.4,color:"#333",width:"100%",wordBreak:"break-word",overflow:"auto",maxHeight:"300px"},children:e.content})]},n)}))})]}),(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:(0,je.jsx)(J.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),Tt(o.id)}}),(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:o.collected?(0,je.jsx)(ee.Z,{style:{color:"#FFD700"}}):(0,je.jsx)(ne.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),Et(o.id,o.collected)}}),(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:(0,je.jsx)(te.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),dt(o.id)}}),(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:(0,je.jsx)(re.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),ft(o.id)}})]}):(0,je.jsxs)(T.Z,{children:[(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:(0,je.jsx)(oe.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),Rt(o.id)}}),(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:(0,je.jsx)(J.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),Tt(o.id)}}),(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:o.collected?(0,je.jsx)(ee.Z,{style:{color:"#FFD700"}}):(0,je.jsx)(ne.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),Et(o.id,o.collected)}}),(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:(0,je.jsx)(te.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),dt(o.id)}}),(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:(0,je.jsx)(re.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),ft(o.id)}})]})}})):[{content:Mt,variant:"borderless"}],At=y.useState(!1),Ht=v()(At,2),Kt=Ht[0],Lt=Ht[1],Ft=function(e){var n=e.fileList;$n(n);var t=n.filter((function(e){return e.originFileObj})).filter((function(e){return!me.some((function(n){return n.uid===e.uid}))}));if(0!==t.length){var r=t.map((function(e){return{uid:e.uid,name:e.name,status:"uploading",size:e.size,type:e.type,created_at:ue()().format("YYYY-MM-DD HH:mm:ss"),processing_status:"uploading"}}));ye((function(e){return[].concat(a()(e),a()(r))})),t.forEach(function(){var e=u()(c()().mark((function e(n){var t,r,o,s,a;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=V.current){e.next=4;break}return I.ZP.error("请先选择一个对话"),e.abrupt("return");case 4:return r=n.uid,nt((function(e){return f()(f()({},e),{},x()({},r,{percent:0,status:"uploading"}))})),console.log("开始上传文件:",r,n.name),o=setInterval((function(){nt((function(e){var n,t=(null===(n=e[r])||void 0===n?void 0:n.percent)||0;if(t<95){var o=t+Math.floor(8*Math.random())+3;return console.log("更新上传进度:",r,o),f()(f()({},e),{},x()({},r,f()(f()({},e[r]),{},{percent:o})))}return e}))}),300),1,s=[n.originFileObj].filter((function(e){return e})),"","","chat",e.prev=13,e.next=16,(0,xe.pj)(t,1,s,"","","chat");case 16:a=e.sent,clearInterval(o),console.log("🚀 ~ newFiles.forEach ~ response:",a),a.detail?(I.ZP.error(a.detail),nt((function(e){return f()(f()({},e),{},x()({},r,{percent:100,status:"error"}))})),ye((function(e){return e.map((function(e){return e.uid===r?f()(f()({},e),{},{processing_status:"error",status:"error"}):e}))})),setTimeout((function(){nt((function(e){var n=f()({},e);return delete n[r],n}))}),3e3)):(nt((function(e){return f()(f()({},e),{},x()({},r,{percent:100,status:"done"}))})),console.log("上传完成，更新状态为done:",r),ye((function(e){return e.map((function(e){return e.uid===r?f()(f()({},e),{},{processing_status:"pending",status:"done"}):e}))})),setTimeout((function(){nt((function(e){var n=f()({},e);return delete n[r],n}))}),2e3),I.ZP.success("上传文件成功"),V.current&&(ut(V.current),$e(V.current))),e.next=30;break;case 22:e.prev=22,e.t0=e.catch(13),clearInterval(o),console.error("文件上传失败:",e.t0),nt((function(e){return f()(f()({},e),{},x()({},r,{percent:100,status:"error"}))})),ye((function(e){return e.map((function(e){return e.uid===r?f()(f()({},e),{},{processing_status:"error",status:"error"}):e}))})),setTimeout((function(){nt((function(e){var n=f()({},e);return delete n[r],n}))}),3e3),I.ZP.error("文件上传失败");case 30:case"end":return e.stop()}}),e,null,[[13,22]])})));return function(n){return e.apply(this,arguments)}}()),setTimeout((function(){$n([])}),100)}},Yt=(0,je.jsx)(Z.Z.Header,{title:"附件",styles:{content:{padding:0}},open:Kt,onOpenChange:Lt,forceRender:!0,children:(0,je.jsx)(S.Z,{ref:ke,beforeUpload:function(){return!1},fileList:Vn,onChange:Ft,placeholder:function(e){return"drop"===e?{title:"将文件拖放到此处"}:{icon:(0,je.jsx)(L.Z,{}),title:"上传文件",description:"点击或将文件拖拽到此区域上传\n支持PDF、Word、Excel、CSV、PPT、HTML、Markdown、JSON、图片、音频等格式"}},getDropContainer:function(){var e;return null===(e=we.current)||void 0===e?void 0:e.nativeElement}})}),qt=(0,je.jsxs)("div",{className:K.logo,children:[(0,je.jsx)("span",{children:"对话记录"}),(0,je.jsx)(N.Z,{title:"新对话",children:(0,je.jsx)(z.ZP,{type:"text",icon:(0,je.jsx)(G.Z,{}),onClick:Ct,style:{fontSize:"16px"}})})]}),Gt=(0,je.jsx)(B.Z,{title:"修改对话标题",open:pn,onOk:function(){kn&&mn.trim()&&(Bt(kn,mn.trim()),xn(!1))},onCancel:function(){xn(!1),vn(""),jn("")},children:(0,je.jsx)(D.Z,{value:mn,onChange:function(e){return vn(e.target.value)},placeholder:"请输入新的对话标题"})}),Jt=(0,je.jsx)(W.Z,{title:"历史对话",placement:"right",width:400,onClose:function(){return nn(!1)},open:en,children:(0,je.jsxs)("div",{className:K.menu,children:[qt,(0,je.jsx)(_.Z,{items:Ke,activeKey:qe,onActiveChange:zt,menu:function(e){return{items:[{label:"重命名",key:"edit",icon:(0,je.jsx)(X.Z,{})},{label:"置顶",key:"pin",icon:(0,je.jsx)(Q.Z,{})},{label:"删除",key:"delete",icon:(0,je.jsx)(J.Z,{}),danger:!0}],onClick:function(n){switch(console.log("menuInfo","Click ".concat(e.key," - ").concat(n.key)),n.key){case"edit":jn(e.key),vn(e.label),xn(!0);break;case"pin":Pt(e.key);break;case"delete":if(on)return void I.ZP.error("系统正在处理其他对话。请稍😊");It(e.key)}}}},groupable:!0})]})}),Ut=y.memo((function(e){var n=e.isOpen,t=e.onClose,r=e.availableKnowledgeBases,o=e.selectedKnowledgeBaseIds,s=e.setSelectedKnowledgeBaseIds,i=(0,y.useState)([]),l=v()(i,2),d=l[0],f=l[1],p=(0,y.useState)(!1),x=v()(p,2),g=x[0],h=x[1],m=(0,y.useState)(!1),b=v()(m,2),k=b[0],j=b[1];(0,y.useEffect)((function(){if(n){f(a()(o));var e=setTimeout((function(){j(!0)}),100);return function(){clearTimeout(e),j(!1)}}}),[n,o]);var w=(0,y.useCallback)((function(e,n){n&&(n.stopPropagation(),n.preventDefault()),f((function(n){return n.includes(e)?n.filter((function(n){return n!==e})):[].concat(a()(n),[e])}))}),[]),Z=function(){var e=u()(c()().mark((function e(){var n;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,h(!0),!V.current){e.next=19;break}return n=d,e.next=6,ve(V.current,n);case 6:if(!e.sent.success){e.next=16;break}return s(d),I.ZP.success("知识库设置已更新"),h(!1),j(!1),t(),e.abrupt("return");case 16:I.ZP.error("更新知识库设置失败");case 17:e.next=25;break;case 19:return s(d),I.ZP.warning("未选择对话，知识库设置仅保存在本地"),h(!1),j(!1),t(),e.abrupt("return");case 25:e.next=31;break;case 27:e.prev=27,e.t0=e.catch(0),console.error("保存知识库设置时出错:",e.t0),I.ZP.error("保存知识库设置失败");case 31:h(!1);case 32:case"end":return e.stop()}}),e,null,[[0,27]])})));return function(){return e.apply(this,arguments)}}();return(0,je.jsx)(B.Z,{title:"知识库设置",open:n,onCancel:function(){j(!1),setTimeout((function(){t()}),100)},width:1200,styles:{body:{padding:"12px 16px"},mask:{backgroundColor:"rgba(0, 0, 0, 0.45)"},content:{boxShadow:"0 6px 16px rgba(0, 0, 0, 0.08), 0 3px 6px rgba(0, 0, 0, 0.12)",transition:"all 0.3s ease"}},destroyOnClose:!0,maskClosable:!1,footer:[(0,je.jsx)(z.ZP,{onClick:function(){j(!1),setTimeout((function(){t()}),100)},children:"关闭"},"close"),(0,je.jsx)(z.ZP,{type:"primary",onClick:Z,loading:g,children:"保存"},"save")],children:k?(0,je.jsxs)("div",{style:{display:"flex",height:"500px",opacity:k?1:0,transition:"opacity 0.3s ease"},children:[(0,je.jsxs)("div",{style:{width:"50%",paddingRight:"12px",overflowY:"auto"},children:[(0,je.jsx)("h3",{style:{marginBottom:"16px",fontSize:"16px",fontWeight:500},children:"可选知识库"}),r.length>0?(0,je.jsx)(M.Z,{grid:{gutter:16,column:2},dataSource:r,renderItem:function(e){var n=d.includes(e._id);return(0,je.jsx)(M.Z.Item,{onClick:function(n){return w(e._id,n)},style:{border:n?"1px solid #1890ff":"1px solid #e0e0e0",padding:"12px",backgroundColor:n?"#e6f7ff":"#fff",color:n?"#1890ff":"inherit",borderRadius:"4px",transition:"all 0.3s ease",boxShadow:n?"0 2px 8px rgba(24, 144, 255, 0.15)":"none",cursor:"pointer",marginBottom:"8px"},children:(0,je.jsx)(M.Z.Item.Meta,{title:(0,je.jsx)("span",{style:{color:n?"#1890ff":"inherit",fontWeight:n?500:400,fontSize:"14px"},children:e.name}),description:(0,je.jsx)("div",{style:{fontSize:"12px",color:"#999",marginTop:"4px"},children:e.description||"暂无描述"})})})}}):(0,je.jsx)("div",{style:{textAlign:"center",padding:"40px 0",color:"#999"},children:"暂无可用知识库"})]}),(0,je.jsxs)("div",{style:{width:"50%",paddingLeft:"12px",borderLeft:"1px solid #f0f0f0",overflowY:"auto"},children:[(0,je.jsx)("h4",{style:{marginBottom:"16px",fontSize:"16px",fontWeight:500},children:"已选知识库"}),d.length>0?(0,je.jsx)(M.Z,{grid:{gutter:16,column:2},dataSource:d,renderItem:function(e){var n=r.find((function(n){return n._id===e}));return(0,je.jsx)(M.Z.Item,{onClick:function(n){return w(e,n)},style:{border:"1px solid #1890ff",padding:"12px",backgroundColor:"#e6f7ff",color:"#1890ff",borderRadius:"4px",transition:"all 0.3s ease",boxShadow:"0 2px 8px rgba(24, 144, 255, 0.15)",cursor:"pointer",marginBottom:"8px"},children:(0,je.jsx)(M.Z.Item.Meta,{title:(0,je.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,je.jsx)("span",{style:{color:"#1890ff",fontWeight:500,fontSize:"14px"},children:(null==n?void 0:n.name)||"未知知识库"}),(0,je.jsx)(z.ZP,{type:"text",size:"small",icon:(0,je.jsx)(se.Z,{}),onClick:function(n){n.stopPropagation(),f((function(n){return n.filter((function(n){return n!==e}))}))},style:{color:"#1890ff"}})]}),description:(0,je.jsx)("div",{style:{fontSize:"12px",color:"#666",marginTop:"4px"},children:(null==n?void 0:n.description)||"暂无描述"})})})}}):(0,je.jsx)("div",{style:{textAlign:"center",padding:"40px 0",color:"#999"},children:"暂未选择知识库"})]})]}):(0,je.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"500px"},children:(0,je.jsx)(E.Z,{size:"large",tip:"正在加载知识库数据..."})})})}));(0,y.useEffect)((function(){console.log("currentConversationMessages 更新了:",Sn)}),[Sn]),(0,y.useEffect)((function(){}),[]);var Vt=(0,je.jsx)(W.Z,{title:"引用来源详情",placement:"right",width:500,onClose:function(){return d(!1)},open:l,children:m&&(0,je.jsxs)("div",{style:{padding:"16px 0"},children:[(0,je.jsxs)("div",{style:{marginBottom:24},children:[(0,je.jsx)("h3",{style:{fontSize:16,fontWeight:"bold",marginBottom:12,color:"#333"},children:"文档标题"}),(0,je.jsx)("div",{style:{padding:12,backgroundColor:"#f5f5f5",borderRadius:6,fontSize:14,color:"#666"},children:m.source_name||"未知来源"})]}),(0,je.jsxs)("div",{style:{marginBottom:24},children:[(0,je.jsx)("h3",{style:{fontSize:16,fontWeight:"bold",marginBottom:12,color:"#333"},children:"引用内容"}),(0,je.jsx)("div",{style:{padding:16,backgroundColor:"#fafafa",border:"1px solid #e8e8e8",borderRadius:6,fontSize:14,lineHeight:1.6,color:"#333",maxHeight:500,overflowY:"auto"},children:m.content||"暂无内容"})]}),m.metadata&&(0,je.jsxs)("div",{style:{marginBottom:24},children:[(0,je.jsx)("h3",{style:{fontSize:16,fontWeight:"bold",marginBottom:12,color:"#333"},children:"其他信息"}),(0,je.jsx)("div",{style:{fontSize:12,color:"#888"},children:Object.entries(m.metadata).map((function(e){var n=v()(e,2),t=n[0],r=n[1];return(0,je.jsxs)("div",{style:{marginBottom:4},children:[(0,je.jsxs)("span",{style:{fontWeight:"bold"},children:[t,":"]})," ",String(r)]},t)}))})]})]})});(0,y.useEffect)((function(){if(console.log("🚀 ~ 监听到状态变化 ~ selectedKnowledgeBaseIds:",Kn),console.log("🚀 ~ 监听到状态变化 ~ availableKnowledgeBases:",Mn),Kn.length>0&&Mn.length>0){var e=Kn.filter((function(e){return!Mn.some((function(n){return n._id===e}))}));e.length>0&&console.warn("🚀 ~ 警告：有选中的知识库ID在可用知识库列表中找不到:",e)}}),[Kn,Mn]),(0,y.useEffect)((function(){var e=function(){var e=u()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:Kn.length>0&&(console.log("🚀 ~ 已选知识库IDs:",Kn),0===Mn.length&&console.log("🚀 ~ 可用知识库列表为空，但我们期望它在初始化时已被填充"));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();e()}),[qe,Kn]),(0,y.useEffect)((function(){var e=null;return me&&me.some((function(e){return"pending"===e.processing_status||"processing"===e.processing_status}))&&qe&&(e=setInterval(u()(c()().mark((function n(){var t,r,o;return c()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(console.log("检查文件处理状态..."),n.prev=1,!qe){n.next=7;break}return n.next=5,be({knowledge_base_id:qe,pageSize:100});case 5:(t=n.sent)&&t.success&&Array.isArray(t.data)&&(r=t.data.map((function(e){return{uid:e.id,name:e.filename,status:"completed"===e.processing_status?"done":"error"===e.processing_status?"error":"uploading",url:e.url,size:e.size,type:e.data_type,created_at:e.created_at,chunk_count:e.chunk_count,processing_status:e.processing_status}})),o=r.every((function(e){return"completed"===e.processing_status||"error"===e.processing_status})),ye(r),o&&e&&(console.log("所有文件处理完成，停止检查"),clearInterval(e),e=null));case 7:n.next=12;break;case 9:n.prev=9,n.t0=n.catch(1),console.error("检查文件处理状态出错:",n.t0);case 12:case"end":return n.stop()}}),n,null,[[1,9]])}))),1e4)),function(){e&&clearInterval(e)}}),[me,qe]);var $t=function(){var e=u()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(V.current){e.next=3;break}return I.ZP.warning("没有活跃的对话"),e.abrupt("return");case 3:try{B.Z.confirm({title:"确认删除",content:"确定要删除此文件吗？删除后将无法恢复，且会影响相关的知识库索引。",okText:"确认删除",okType:"danger",cancelText:"取消",onOk:function(){var e=u()(c()().mark((function e(){var t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,xe.Rj)(n);case 3:(t=e.sent)&&t.success?(I.ZP.success("文件删除成功"),ye((function(e){return e.filter((function(e){return e.uid!==n}))})),V.current&&ut(V.current)):I.ZP.error("文件删除失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("删除文件失败:",e.t0),I.ZP.error("删除文件失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}()})}catch(e){console.error("删除文件失败:",e),I.ZP.error("删除文件失败")}case 4:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}();return(0,je.jsx)(Ce.Provider,{value:{expandedRefs:t,setExpandedRefs:r},children:(0,je.jsxs)("div",{className:K.layout,style:{height:U-56},children:[(0,je.jsxs)("div",{className:K.chat,children:[(0,je.jsx)(C.Z.List,{roles:{assistant:{placement:"start",typing:{step:5,interval:20},style:{maxWidth:600}}},items:Ot,className:K.messages}),(0,je.jsx)(w.Z,{items:Ie,onItemClick:Dt}),(0,je.jsxs)("div",{className:K.customSender,children:[(0,je.jsx)(D.Z.TextArea,{value:Me,onChange:function(e){return Oe(e.target.value)},placeholder:"请输入...",autoSize:{minRows:3,maxRows:6},style:{border:"none",boxShadow:"none",resize:"none",fontSize:"14px"},onPressEnter:function(e){e.shiftKey||(e.preventDefault(),Nt(Me))}}),(0,je.jsxs)("div",{className:"toolbar",children:[(0,je.jsxs)("div",{className:"tools-left",children:[(0,je.jsx)(z.ZP,{type:"text",icon:(0,je.jsx)(Y.Z,{}),onClick:function(){return Lt(!Kt)},style:{padding:"4px 8px"}}),(0,je.jsxs)("div",{className:"deep-thinking",children:[(0,je.jsx)("span",{style:{marginRight:8},children:"联网检索"}),(0,je.jsx)(A.Z,{size:"small",checked:ot,onChange:st})]})]}),(0,je.jsx)(z.ZP,{type:"primary",icon:on?(0,je.jsx)(ae.Z,{spin:!0}):void 0,loading:mt.isRequesting(),onClick:function(){return Nt(Me)},disabled:!Me.trim()||mt.isRequesting(),style:{borderRadius:"6px"},children:"发送"})]})]}),(0,je.jsx)("div",{style:{position:"absolute",opacity:0,pointerEvents:"none",height:0,overflow:"hidden"},children:(0,je.jsx)(Z.Z,{ref:we,header:Yt,onPasteFile:function(e){var n;null===(n=ke.current)||void 0===n||n.upload(e),Lt(!0)},onSubmit:function(){},value:"",onChange:function(){},loading:!1,onKeyPress:function(){},placeholder:""})}),Kt&&(0,je.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,zIndex:1e3,display:"flex",justifyContent:"center",alignItems:"center",background:"rgba(0, 0, 0, 0.5)"},children:(0,je.jsxs)("div",{style:{background:"#fff",borderRadius:"8px",width:"600px",maxHeight:"80vh",overflow:"auto",padding:"20px"},children:[(0,je.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"16px"},children:[(0,je.jsx)("h3",{style:{margin:0},children:"上传文件"}),(0,je.jsx)(z.ZP,{type:"text",icon:(0,je.jsx)(se.Z,{}),onClick:function(){return Lt(!1)}})]}),(0,je.jsx)(S.Z,{ref:ke,beforeUpload:function(){return!1},fileList:Vn,onChange:Ft,placeholder:function(e){return"drop"===e?{title:"将文件拖放到此处"}:{icon:(0,je.jsx)(L.Z,{}),title:"上传文件",description:"点击或将文件拖拽到此区域上传\n支持PDF、Word、Excel、CSV、PPT、HTML、Markdown、JSON、图片、音频等格式"}}})]})})]}),(0,je.jsx)("div",{children:(0,je.jsx)(Se,{width:450,style:{background:"#fff",padding:"12px",borderLeft:"1px solid #d9d9d9",height:"100%",overflow:"auto"},children:(0,je.jsxs)("div",{style:{padding:"8px 0"},children:[(0,je.jsx)("div",{style:{marginBottom:"16px",display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"1px solid #f0f0f0",paddingBottom:"12px"},children:(0,je.jsx)("div",{style:{fontWeight:500,fontSize:"16px",color:"#333"},children:"知识资源"})}),(0,je.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,je.jsxs)("div",{style:{fontWeight:500,fontSize:"16px",color:"#333",marginBottom:"16px",display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"1px solid #f0f0f0",paddingBottom:"12px"},children:[(0,je.jsx)("span",{children:"已选知识库"}),(0,je.jsx)(z.ZP,{type:"primary",ghost:!0,size:"small",icon:(0,je.jsx)(ie.Z,{}),onClick:u()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:try{sn(!0),Gn(!0)}catch(e){console.error("加载知识库数据失败:",e),I.ZP.error("加载知识库数据失败"),Gn(!1)}finally{sn(!1)}case 1:case"end":return e.stop()}}),e)}))),children:"设置知识库"})]}),Kn&&0!==Kn.length?(0,je.jsx)("div",{children:(0,je.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:"10px"},children:Kn.map((function(e){var n=Mn.find((function(n){return n.id===e||n._id===e}));return(0,je.jsx)("div",{style:{width:"calc(50% - 5px)",padding:"10px",borderRadius:"8px",border:"1px solid #e6f7ff",backgroundColor:"#f0f8ff",boxSizing:"border-box",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:(0,je.jsx)("span",{style:{fontWeight:500,fontSize:"14px",color:"#1890ff",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",display:"block"},children:(null==n?void 0:n.name)||"未知知识库"})},e)}))})}):(0,je.jsxs)("div",{style:{textAlign:"center",padding:"32px 0",color:"#999",backgroundColor:"#f9f9f9",borderRadius:"8px",border:"1px dashed #d9d9d9"},children:[(0,je.jsx)(F.Z,{style:{fontSize:"36px",marginBottom:"16px",color:"#1890ff"}}),(0,je.jsx)("div",{style:{marginBottom:"16px"},children:"暂未选择知识库"}),(0,je.jsx)(z.ZP,{type:"primary",size:"small",onClick:u()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:try{sn(!0),Gn(!0)}catch(e){console.error("加载知识库数据失败:",e),I.ZP.error("加载知识库数据失败"),Gn(!1)}finally{sn(!1)}case 1:case"end":return e.stop()}}),e)}))),children:"选择知识库"})]})]}),me&&me.length>0&&(0,je.jsxs)("div",{style:{marginTop:"24px"},children:[(0,je.jsxs)("div",{style:{fontWeight:500,fontSize:"16px",color:"#333",marginBottom:"16px",display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"1px solid #f0f0f0",paddingBottom:"12px"},children:[(0,je.jsx)("span",{children:"上传文件"}),(0,je.jsx)(z.ZP,{type:"primary",ghost:!0,icon:(0,je.jsx)(ae.Z,{}),size:"small",onClick:function(){qe&&ut(qe)},children:"刷新状态"})]}),(0,je.jsxs)("div",{style:{fontSize:"12px",color:"#666",marginBottom:"12px",backgroundColor:"#f9f9f9",padding:"12px",borderRadius:"8px"},children:[(0,je.jsx)("div",{style:{marginBottom:"8px",fontWeight:500},children:"支持的文件类型：PDF、Word、Excel、CSV、PPT、HTML、Markdown、JSON、图片(JPG/PNG)、音频(WAV/MP3)"}),(0,je.jsx)("div",{style:{marginTop:"8px",color:"#ff7875"},children:"注意：文件上传后需要处理完成才会被检索到，请耐心等待"})]}),me&&0!==me.length?(console.log("渲染文件列表:",me),console.log("上传进度状态:",et),(0,je.jsx)(M.Z,{dataSource:me,size:"small",itemLayout:"horizontal",renderItem:function(e){var n=et[e.uid];console.log("文件 ".concat(e.name," (").concat(e.uid,") 的上传进度:"),n);var t=e.processing_status;return"string"==typeof t&&(t="completed"===t?1:"error"===t?3:"processing"===t?2:"uploading"===t?4:0),console.log("文件 ".concat(e.name," 的处理状态:"),t,e.processing_status),(0,je.jsx)(M.Z.Item,{style:{marginBottom:"10px",padding:"12px 16px",borderRadius:"8px",border:"1px solid #eee",backgroundColor:"#fafafa",transition:"all 0.3s",boxShadow:"0 1px 2px rgba(0,0,0,0.03)"},actions:[(0,je.jsx)(z.ZP,{type:"link",size:"small",danger:!0,icon:(0,je.jsx)(J.Z,{}),onClick:function(){return $t(e.uid)},disabled:2===t},"delete")],children:(0,je.jsx)(M.Z.Item.Meta,{title:(0,je.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"4px"},children:[(0,je.jsx)("span",{style:{fontWeight:500,fontSize:"14px",maxWidth:"260px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e.name}),e.size&&(0,je.jsxs)("div",{style:{fontSize:"12px",color:"#999",marginBottom:"4px"},children:[(e.size/1024).toFixed(2)," KB"]})]}),description:(0,je.jsx)("div",{children:n&&"uploading"===n.status?(0,je.jsxs)("div",{children:[(0,je.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",marginBottom:"2px"},children:[(0,je.jsx)("span",{children:"上传中..."}),(0,je.jsxs)("span",{children:[n.percent,"%"]})]}),(0,je.jsx)(O.Z,{percent:n.percent,size:"small",status:"active",strokeColor:{"0%":"#108ee9","100%":"#87d068"},showInfo:!1})]}):n&&"error"===n.status?(0,je.jsxs)("div",{children:[(0,je.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",marginBottom:"2px",color:"#ff4d4f"},children:[(0,je.jsx)("span",{children:"上传失败"}),(0,je.jsx)("span",{children:"请重试"})]}),(0,je.jsx)(O.Z,{percent:100,size:"small",status:"exception",showInfo:!1})]}):n&&"done"===n.status?(0,je.jsxs)("div",{children:[(0,je.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",marginBottom:"2px",color:"#52c41a"},children:[(0,je.jsx)("span",{children:"上传完成"}),(0,je.jsx)("span",{children:"100%"})]}),(0,je.jsx)(O.Z,{percent:100,size:"small",status:"success",showInfo:!1})]}):1===t?(0,je.jsx)("div",{style:{fontSize:"12px",color:"#52c41a"},children:"处理完成"}):3===t?(0,je.jsx)("div",{style:{fontSize:"12px",color:"#f5222d"},children:"处理失败"}):2===t?(0,je.jsxs)("div",{style:{fontSize:"12px",color:"#1890ff",display:"flex",alignItems:"center"},children:[(0,je.jsx)(ae.Z,{spin:!0,style:{marginRight:"5px"}}),"处理中..."]}):(0,je.jsx)("div",{style:{fontSize:"12px",color:"#faad14"},children:"等待处理"})})})})}})):(0,je.jsxs)("div",{style:{textAlign:"center",padding:"24px 0",color:"#999",backgroundColor:"#f9f9f9",borderRadius:"8px",border:"1px dashed #d9d9d9"},children:[(0,je.jsx)(L.Z,{style:{fontSize:"36px",marginBottom:"16px",color:"#1890ff"}}),(0,je.jsx)("div",{children:"暂无上传文件"})]})]})]})})}),Gt,Jt,(0,je.jsxs)(B.Z,{title:"全球搜索",open:ct,onCancel:function(){return lt(!1)},footer:null,width:600,children:[(0,je.jsx)("div",{style:{marginBottom:16},children:(0,je.jsx)(D.Z.Search,{placeholder:"输入搜索关键词...",enterButton:"搜索",size:"large",onSearch:function(e){e&&(I.ZP.info("正在搜索: ".concat(e)),setTimeout((function(){I.ZP.success("搜索完成"),lt(!1)}),1e3))}})}),(0,je.jsxs)("div",{style:{textAlign:"center",color:"#999",padding:"20px 0"},children:[(0,je.jsx)(ce.Z,{style:{fontSize:48,color:"#d9d9d9",marginBottom:16}}),(0,je.jsx)("p",{children:"输入关键词开始全球搜索"})]})]}),Vt,(0,je.jsx)(Ut,{isOpen:qn,onClose:function(){return Gn(!1)},availableKnowledgeBases:Mn,selectedKnowledgeBaseIds:Kn,setSelectedKnowledgeBaseIds:Ln}),(0,je.jsx)(ge.Z,{visible:In,messageId:En,conversationId:qe,appInfo:Te,onClose:function(){return Bn(!1)}}),(0,je.jsx)(pe.Z,{visible:ln,onCancel:function(){return un(!1)},sceneDescription:Kn.length>0?Kn.map((function(e){var n=Mn.find((function(n){return n._id===e}));return(null==n?void 0:n.name)||""})).filter(Boolean).join("、")+"知识库问答":"知识库问答",onSelectPrompt:function(e){Oe(e),un(!1)},app_info:Te})]})})}},26058:function(e,n,t){t.d(n,{Z:function(){return j}});var r=t(74902),o=t(67294),s=t(93967),a=t.n(s),i=t(98423),c=t(53124),l=t(82401),u=t(50344),d=t(70985);var f=t(24793),p=function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)n.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(t[r[o]]=e[r[o]])}return t};function x(e){let{suffixCls:n,tagName:t,displayName:r}=e;return e=>o.forwardRef(((r,s)=>o.createElement(e,Object.assign({ref:s,suffixCls:n,tagName:t},r))))}const g=o.forwardRef(((e,n)=>{const{prefixCls:t,suffixCls:r,className:s,tagName:i}=e,l=p(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:u}=o.useContext(c.E_),d=u("layout",t),[x,g,h]=(0,f.ZP)(d),m=r?`${d}-${r}`:d;return x(o.createElement(i,Object.assign({className:a()(t||m,s,g,h),ref:n},l)))})),h=o.forwardRef(((e,n)=>{const{direction:t}=o.useContext(c.E_),[s,x]=o.useState([]),{prefixCls:g,className:h,rootClassName:m,children:v,hasSider:y,tagName:b,style:k}=e,j=p(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),w=(0,i.Z)(j,["suffixCls"]),{getPrefixCls:Z,className:S,style:_}=(0,c.dj)("layout"),C=Z("layout",g),P=function(e,n,t){return"boolean"==typeof t?t:!!e.length||(0,u.Z)(n).some((e=>e.type===d.Z))}(s,v,y),[z,I,B]=(0,f.ZP)(C),R=a()(C,{[`${C}-has-sider`]:P,[`${C}-rtl`]:"rtl"===t},S,h,m,I,B),T=o.useMemo((()=>({siderHook:{addSider:e=>{x((n=>[].concat((0,r.Z)(n),[e])))},removeSider:e=>{x((n=>n.filter((n=>n!==e))))}}})),[]);return z(o.createElement(l.V.Provider,{value:T},o.createElement(b,Object.assign({ref:n,className:R,style:Object.assign(Object.assign({},_),k)},w),v)))})),m=x({tagName:"div",displayName:"Layout"})(h),v=x({suffixCls:"header",tagName:"header",displayName:"Header"})(g),y=x({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(g),b=x({suffixCls:"content",tagName:"main",displayName:"Content"})(g);const k=m;k.Header=v,k.Footer=y,k.Content=b,k.Sider=d.Z,k._InternalSiderContext=d.D;var j=k}}]);