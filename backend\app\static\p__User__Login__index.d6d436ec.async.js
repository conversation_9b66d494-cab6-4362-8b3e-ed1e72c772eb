"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9366],{5966:function(e,n,t){var r=t(97685),o=t(1413),s=t(91),i=t(21770),a=t(47019),l=t(55241),c=t(98423),u=t(67294),d=t(62633),p=t(85893),g=["fieldProps","proFieldProps"],f=["fieldProps","proFieldProps"],h="text",m=function(e){var n=(0,i.Z)(e.open||!1,{value:e.open,onChange:e.onOpenChange}),t=(0,r.Z)(n,2),s=t[0],c=t[1];return(0,p.jsx)(a.<PERSON>.Item,{shouldUpdate:!0,noStyle:!0,children:function(n){var t,r=n.getFieldValue(e.name||[]);return(0,p.jsx)(l.Z,(0,o.Z)((0,o.Z)({getPopupContainer:function(e){return e&&e.parentNode?e.parentNode:e},onOpenChange:function(e){return c(e)},content:(0,p.jsxs)("div",{style:{padding:"4px 0"},children:[null===(t=e.statusRender)||void 0===t?void 0:t.call(e,r),e.strengthText?(0,p.jsx)("div",{style:{marginTop:10},children:(0,p.jsx)("span",{children:e.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},e.popoverProps),{},{open:s,children:e.children}))}})},x=function(e){var n=e.fieldProps,t=e.proFieldProps,r=(0,s.Z)(e,g);return(0,p.jsx)(d.Z,(0,o.Z)({valueType:h,fieldProps:n,filedConfig:{valueType:h},proFieldProps:t},r))};x.Password=function(e){var n=e.fieldProps,t=e.proFieldProps,i=(0,s.Z)(e,f),a=(0,u.useState)(!1),l=(0,r.Z)(a,2),g=l[0],x=l[1];return null!=n&&n.statusRender&&i.name?(0,p.jsx)(m,{name:i.name,statusRender:null==n?void 0:n.statusRender,popoverProps:null==n?void 0:n.popoverProps,strengthText:null==n?void 0:n.strengthText,open:g,onOpenChange:x,children:(0,p.jsx)("div",{children:(0,p.jsx)(d.Z,(0,o.Z)({valueType:"password",fieldProps:(0,o.Z)((0,o.Z)({},(0,c.Z)(n,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(e){var t;null==n||null===(t=n.onBlur)||void 0===t||t.call(n,e),x(!1)},onClick:function(e){var t;null==n||null===(t=n.onClick)||void 0===t||t.call(n,e),x(!0)}}),proFieldProps:t,filedConfig:{valueType:h}},i))})}):(0,p.jsx)(d.Z,(0,o.Z)({valueType:"password",fieldProps:n,proFieldProps:t,filedConfig:{valueType:h}},i))},x.displayName="ProFormComponent",n.Z=x},1485:function(e,n,t){t.r(n),t.d(n,{default:function(){return ee}});var r=t(15009),o=t.n(r),s=t(97857),i=t.n(s),a=t(99289),l=t.n(a),c=t(5574),u=t.n(c),d=t(71108),p=t(25538),g=t(10981),f=t(78404),h=t(87547),m=t(1413),x=t(67294),v={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"}}]},name:"lock",theme:"outlined"},Z=t(91146),y=function(e,n){return x.createElement(Z.Z,(0,m.Z)((0,m.Z)({},e),{},{ref:n,icon:v}))};var j=x.forwardRef(y),P=t(91),b=t(10915),k=t(21532),w=t(93967),M=t.n(w),S=t(34994),C=t(4942),F=t(64847),T=function(e){return(0,C.Z)((0,C.Z)({},e.componentCls,{"&-container":{display:"flex",flex:"1",flexDirection:"column",height:"100%",paddingInline:32,paddingBlock:24,overflow:"auto",background:"inherit"},"&-top":{textAlign:"center"},"&-header":{display:"flex",alignItems:"center",justifyContent:"center",height:"44px",lineHeight:"44px",a:{textDecoration:"none"}},"&-title":{position:"relative",insetBlockStart:"2px",color:"@heading-color",fontWeight:"600",fontSize:"33px"},"&-logo":{width:"44px",height:"44px",marginInlineEnd:"16px",verticalAlign:"top",img:{width:"100%"}},"&-desc":{marginBlockStart:"12px",marginBlockEnd:"40px",color:e.colorTextSecondary,fontSize:e.fontSize},"&-main":{minWidth:"328px",maxWidth:"580px",margin:"0 auto","&-other":{marginBlockStart:"24px",lineHeight:"22px",textAlign:"start"}}}),"@media (min-width: @screen-md-min)",(0,C.Z)({},"".concat(e.componentCls,"-container"),{paddingInline:0,paddingBlockStart:32,paddingBlockEnd:24,backgroundRepeat:"no-repeat",backgroundPosition:"center 110px",backgroundSize:"100%"}))};var B=t(85893),N=["logo","message","contentStyle","title","subTitle","actions","children","containerStyle","otherStyle"];function R(e){var n,t,r=e.logo,o=e.message,s=e.contentStyle,i=e.title,a=e.subTitle,l=e.actions,c=e.children,u=e.containerStyle,d=e.otherStyle,p=(0,P.Z)(e,N),g=(0,b.YB)(),f=!1!==p.submitter&&(0,m.Z)((0,m.Z)({searchConfig:{submitText:g.getMessage("loginForm.submitText","登录")}},p.submitter),{},{submitButtonProps:(0,m.Z)({size:"large",style:{width:"100%"}},null===(n=p.submitter)||void 0===n?void 0:n.submitButtonProps),render:function(e,n){var t,r,o,s=n.pop();return"function"==typeof(null==p||null===(t=p.submitter)||void 0===t?void 0:t.render)?null==p||null===(r=p.submitter)||void 0===r||null===(o=r.render)||void 0===o?void 0:o.call(r,e,n):s}}),h=(0,x.useContext)(k.ZP.ConfigContext).getPrefixCls("pro-form-login"),v=(t=h,(0,F.Xj)("LoginForm",(function(e){var n=(0,m.Z)((0,m.Z)({},e),{},{componentCls:".".concat(t)});return[T(n)]}))),Z=v.wrapSSR,y=v.hashId,j=function(e){return"".concat(h,"-").concat(e," ").concat(y)},w=(0,x.useMemo)((function(){return r?"string"==typeof r?(0,B.jsx)("img",{src:r}):r:null}),[r]);return Z((0,B.jsxs)("div",{className:M()(j("container"),y),style:u,children:[(0,B.jsxs)("div",{className:"".concat(j("top")," ").concat(y).trim(),children:[i||w?(0,B.jsxs)("div",{className:"".concat(j("header")),children:[w?(0,B.jsx)("span",{className:j("logo"),children:w}):null,i?(0,B.jsx)("span",{className:j("title"),children:i}):null]}):null,a?(0,B.jsx)("div",{className:j("desc"),children:a}):null]}),(0,B.jsxs)("div",{className:j("main"),style:(0,m.Z)({width:328},s),children:[(0,B.jsxs)(S.A,(0,m.Z)((0,m.Z)({isKeyPressSubmit:!0},p),{},{submitter:f,children:[o,c]})),l?(0,B.jsx)("div",{className:j("main-other"),style:d,children:l}):null]})]}))}var z=t(5966),I=t(22270),L=t(84567),_=t(90789),E=t(62633),H=["options","fieldProps","proFieldProps","valueEnum"],A=x.forwardRef((function(e,n){var t=e.options,r=e.fieldProps,o=e.proFieldProps,s=e.valueEnum,i=(0,P.Z)(e,H);return(0,B.jsx)(E.Z,(0,m.Z)({ref:n,valueType:"checkbox",valueEnum:(0,I.h)(s,void 0),fieldProps:(0,m.Z)({options:t},r),lightProps:(0,m.Z)({labelFormatter:function(){return(0,B.jsx)(E.Z,(0,m.Z)({ref:n,valueType:"checkbox",mode:"read",valueEnum:(0,I.h)(s,void 0),filedConfig:{customLightMode:!0},fieldProps:(0,m.Z)({options:t},r),proFieldProps:o},i))}},i.lightProps),proFieldProps:o},i))})),V=x.forwardRef((function(e,n){var t=e.fieldProps,r=e.children;return(0,B.jsx)(L.Z,(0,m.Z)((0,m.Z)({ref:n},t),{},{children:r}))})),W=(0,_.G)(V,{valuePropName:"checked"});W.Group=A;var q=W,O=t(76772),U=t(38925),D=t(2453),G=t(4393),K=t(28846),X=t(73935),Y=t(67610),$=(0,K.kc)((function(e){var n=e.token;return{action:{marginLeft:"8px",color:"rgba(0, 0, 0, 0.2)",fontSize:"24px",verticalAlign:"middle",cursor:"pointer",transition:"color 0.3s","&:hover":{color:n.colorPrimaryActive}},lang:{width:42,height:42,lineHeight:"42px",position:"fixed",right:16,borderRadius:n.borderRadius,":hover":{backgroundColor:n.colorBgTextHover}},container:{display:"flex",flexDirection:"column",height:"100vh",overflow:"auto",backgroundImage:"url('/static/imgs/login-bg.svg')",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"}}})),J=function(){var e=$().styles;return(0,B.jsx)("div",{className:e.lang,"data-lang":!0,children:O.SelectLang&&(0,B.jsx)(O.SelectLang,{})})},Q=function(e){var n=e.content;return(0,B.jsx)(U.Z,{style:{marginBottom:24},message:n,type:"error",showIcon:!0})},ee=function(){var e=(0,x.useState)({}),n=u()(e,2),t=n[0],r=n[1],s="account",a=(0,O.useModel)("@@initialState"),c=a.initialState,m=a.setInitialState,v=$().styles,Z=(0,O.useIntl)(),y=function(){var e=l()(o()().mark((function e(){var n,t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==c||null===(n=c.fetchUserInfo)||void 0===n?void 0:n.call(c);case 2:(t=e.sent)&&((0,g.ps)(t),(0,X.flushSync)((function(){m((function(e){return i()(i()({},e),{},{currentUser:t})}))})));case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),P=function(){var e=l()(o()().mark((function e(n){var t,a,l,c,u,d,f,h;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,p.x4)(i()(i()({},n),{},{type:s}));case 3:if("ok"!==(t=e.sent).status){e.next=19;break}return a=t.auth_token,l=t.token_expiry,(0,g.uB)(a||""),(0,g.Ok)(l||""),document.cookie="agent_auth_token=".concat(a||"","; path=/"),c=Z.formatMessage({id:"pages.login.success",defaultMessage:"登录成功！"}),D.ZP.success(c),e.next=13,y();case 13:return u=new URL(window.location.href).searchParams,d=u.get("redirect")||"/",O.history.push(d),e.abrupt("return");case 19:if("error"!==t.status){e.next=24;break}return console.log(t),f=Z.formatMessage({id:"pages.login.failure",defaultMessage:t.message||"登录失败，请重试！"}),D.ZP.error(f),e.abrupt("return");case 24:r(t),e.next=32;break;case 27:e.prev=27,e.t0=e.catch(0),console.info("==========================",e.t0),h=Z.formatMessage({id:"pages.login.failure",defaultMessage:"登录失败，请重试！"}),D.ZP.error(h);case 32:case"end":return e.stop()}}),e,null,[[0,27]])})));return function(n){return e.apply(this,arguments)}}(),b=t.status,k=t.type;return(0,B.jsxs)("div",{className:v.container,children:[(0,B.jsx)(O.Helmet,{children:(0,B.jsxs)("title",{children:[Z.formatMessage({id:"menu.login",defaultMessage:"登录页"}),"- ",(0,f.W6)("title",Y.Z.title||"")]})}),(0,B.jsx)(J,{}),(0,B.jsx)("div",{style:{flex:"1",padding:"32px 0",display:"flex",alignItems:"center"},children:(0,B.jsx)(G.Z,{style:{width:"fit-content",margin:"0 auto",backgroundColor:"rgba(255, 255, 255, 0.9)"},children:(0,B.jsxs)(R,{logo:(0,B.jsx)("img",{alt:"logo",src:(0,f.W6)("logo",Y.Z.logo||"")}),title:(0,f.W6)("title",Y.Z.title||""),subTitle:Z.formatMessage({id:"pages.layouts.userLayout.title"}),initialValues:{autoLogin:!0},actions:[],onFinish:function(){var e=l()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,P(n);case 2:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),children:["error"===b&&"account"===k&&(0,B.jsx)(Q,{content:Z.formatMessage({id:"pages.login.accountLogin.errorMessage",defaultMessage:"账户或密码错误"})}),(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)(z.Z,{name:"username",fieldProps:{size:"large",prefix:(0,B.jsx)(h.Z,{})},placeholder:Z.formatMessage({id:"pages.login.username.placeholder",defaultMessage:"登录手机号"}),rules:[{required:!0,message:(0,B.jsx)(O.FormattedMessage,{id:"pages.login.username.required",defaultMessage:"登录手机号!"})}]}),(0,B.jsx)(z.Z.Password,{name:"password",fieldProps:{size:"large",prefix:(0,B.jsx)(j,{})},placeholder:Z.formatMessage({id:"pages.login.password.placeholder",defaultMessage:"登录密码"}),rules:[{required:!0,message:(0,B.jsx)(O.FormattedMessage,{id:"pages.login.password.required",defaultMessage:"请输入密码！"})}]})]}),"error"===b&&"mobile"===k&&(0,B.jsx)(Q,{content:"验证码错误"}),(0,B.jsxs)("div",{style:{marginBottom:24},children:[(0,B.jsx)(q,{noStyle:!0,name:"autoLogin",children:(0,B.jsx)(O.FormattedMessage,{id:"pages.login.rememberMe",defaultMessage:"自动登录"})}),(0,B.jsx)("a",{style:{float:"right"},children:(0,B.jsx)(O.FormattedMessage,{id:"pages.login.forgotPassword",defaultMessage:"忘记密码"})})]})]})})}),(0,B.jsx)(d.$_,{})]})}}}]);