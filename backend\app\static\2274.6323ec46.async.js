"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2274],{99011:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"}}]},name:"clock-circle",theme:"outlined"}},47046:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"}},75573:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"}},71879:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M885.2 446.3l-.2-.8-112.2-285.1c-5-16.1-19.9-27.2-36.8-27.2H281.2c-17 0-32.1 11.3-36.9 27.6L139.4 443l-.3.7-.2.8c-1.3 4.9-1.7 9.9-1 14.8-.1 1.6-.2 3.2-.2 4.8V830a60.9 60.9 0 0060.8 60.8h627.2c33.5 0 60.8-27.3 60.9-60.8V464.1c0-1.3 0-2.6-.1-3.7.4-4.9 0-9.6-1.3-14.1zm-295.8-43l-.3 15.7c-.8 44.9-31.8 75.1-77.1 75.1-22.1 0-41.1-7.1-54.8-20.6S436 441.2 435.6 419l-.3-15.7H229.5L309 210h399.2l81.7 193.3H589.4zm-375 76.8h157.3c24.3 57.1 76 90.8 140.4 90.8 33.7 0 65-9.4 90.3-27.2 22.2-15.6 39.5-37.4 50.7-63.6h156.5V814H214.4V480.1z"}}]},name:"inbox",theme:"outlined"}},82947:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"}},82826:function(e,t,r){r.d(t,{Z:function(){return i}});var o=r(1413),n=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"},c=r(91146),l=function(e,t){return n.createElement(c.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:a}))};var i=n.forwardRef(l)},8751:function(e,t,r){r.d(t,{Z:function(){return i}});var o=r(1413),n=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"},c=r(91146),l=function(e,t){return n.createElement(c.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:a}))};var i=n.forwardRef(l)},66212:function(e,t,r){var o=r(1413),n=r(67294),a=r(99011),c=r(91146),l=function(e,t){return n.createElement(c.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:a.Z}))},i=n.forwardRef(l);t.Z=i},18429:function(e,t,r){r.d(t,{Z:function(){return i}});var o=r(1413),n=r(67294),a={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"}}]},name:"close-circle",theme:"outlined"},c=r(91146),l=function(e,t){return n.createElement(c.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:a}))};var i=n.forwardRef(l)},82061:function(e,t,r){var o=r(1413),n=r(67294),a=r(47046),c=r(91146),l=function(e,t){return n.createElement(c.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:a.Z}))},i=n.forwardRef(l);t.Z=i},47389:function(e,t,r){var o=r(1413),n=r(67294),a=r(27363),c=r(91146),l=function(e,t){return n.createElement(c.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:a.Z}))},i=n.forwardRef(l);t.Z=i},97175:function(e,t,r){r.d(t,{Z:function(){return i}});var o=r(1413),n=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM514.1 580.1l-61.8-102.4c-2.2-3.6-6.1-5.8-10.3-5.8h-38.4c-2.3 0-4.5.6-6.4 1.9-5.6 3.5-7.3 10.9-3.7 16.6l82.3 130.4-83.4 132.8a12.04 12.04 0 0010.2 18.4h34.5c4.2 0 8-2.2 10.2-5.7L510 664.8l62.3 101.4c2.2 3.6 6.1 5.7 10.2 5.7H620c2.3 0 4.5-.7 6.5-1.9 5.6-3.6 7.2-11 3.6-16.6l-84-130.4 85.3-132.5a12.04 12.04 0 00-10.1-18.5h-35.7c-4.2 0-8.1 2.2-10.3 5.8l-61.2 102.3z"}}]},name:"file-excel",theme:"outlined"},c=r(91146),l=function(e,t){return n.createElement(c.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:a}))};var i=n.forwardRef(l)},45699:function(e,t,r){r.d(t,{Z:function(){return i}});var o=r(1413),n=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M553.1 509.1l-77.8 99.2-41.1-52.4a8 8 0 00-12.6 0l-99.8 127.2a7.98 7.98 0 006.3 12.9H696c6.7 0 10.4-7.7 6.3-12.9l-136.5-174a8.1 8.1 0 00-12.7 0zM360 442a40 40 0 1080 0 40 40 0 10-80 0zm494.6-153.4L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file-image",theme:"outlined"},c=r(91146),l=function(e,t){return n.createElement(c.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:a}))};var i=n.forwardRef(l)},97245:function(e,t,r){var o=r(1413),n=r(67294),a=r(75573),c=r(91146),l=function(e,t){return n.createElement(c.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:a.Z}))},i=n.forwardRef(l);t.Z=i},49354:function(e,t,r){r.d(t,{Z:function(){return i}});var o=r(1413),n=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M531.3 574.4l.3-1.4c5.8-23.9 13.1-53.7 7.4-80.7-3.8-21.3-19.5-29.6-32.9-30.2-15.8-.7-29.9 8.3-33.4 21.4-6.6 24-.7 56.8 10.1 98.6-13.6 32.4-35.3 79.5-51.2 107.5-29.6 15.3-69.3 38.9-75.2 68.7-1.2 5.5.2 12.5 3.5 18.8 3.7 7 9.6 12.4 16.5 15 3 1.1 6.6 2 10.8 2 17.6 0 46.1-14.2 84.1-79.4 5.8-1.9 11.8-3.9 17.6-5.9 27.2-9.2 55.4-18.8 80.9-23.1 28.2 15.1 60.3 24.8 82.1 24.8 21.6 0 30.1-12.8 33.3-20.5 5.6-13.5 2.9-30.5-6.2-39.6-13.2-13-45.3-16.4-95.3-10.2-24.6-15-40.7-35.4-52.4-65.8zM421.6 726.3c-13.9 20.2-24.4 30.3-30.1 34.7 6.7-12.3 19.8-25.3 30.1-34.7zm87.6-235.5c5.2 8.9 4.5 35.8.5 49.4-4.9-19.9-5.6-48.1-2.7-51.4.8.1 1.5.7 2.2 2zm-1.6 120.5c10.7 18.5 24.2 34.4 39.1 46.2-21.6 4.9-41.3 13-58.9 20.2-4.2 1.7-8.3 3.4-12.3 5 13.3-24.1 24.4-51.4 32.1-71.4zm155.6 65.5c.1.2.2.5-.4.9h-.2l-.2.3c-.8.5-9 5.3-44.3-8.6 40.6-1.9 45 7.3 45.1 7.4zm191.4-388.2L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file-pdf",theme:"outlined"},c=r(91146),l=function(e,t){return n.createElement(c.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:a}))};var i=n.forwardRef(l)},4161:function(e,t,r){r.d(t,{Z:function(){return i}});var o=r(1413),n=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM528.1 472h-32.2c-5.5 0-10.3 3.7-11.6 9.1L434.6 680l-46.1-198.7c-1.3-5.4-6.1-9.3-11.7-9.3h-35.4a12.02 12.02 0 00-11.6 15.1l74.2 276c1.4 5.2 6.2 8.9 11.6 8.9h32c5.4 0 10.2-3.6 11.6-8.9l52.8-197 52.8 197c1.4 5.2 6.2 8.9 11.6 8.9h31.8c5.4 0 10.2-3.6 11.6-8.9l74.4-276a12.04 12.04 0 00-11.6-15.1H647c-5.6 0-10.4 3.9-11.7 9.3l-45.8 199.1-49.8-199.3c-1.3-5.4-6.1-9.1-11.6-9.1z"}}]},name:"file-word",theme:"outlined"},c=r(91146),l=function(e,t){return n.createElement(c.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:a}))};var i=n.forwardRef(l)},33914:function(e,t,r){var o=r(1413),n=r(67294),a=r(71879),c=r(91146),l=function(e,t){return n.createElement(c.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:a.Z}))},i=n.forwardRef(l);t.Z=i},74842:function(e,t,r){r.d(t,{Z:function(){return i}});var o=r(1413),n=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M719.4 499.1l-296.1-215A15.9 15.9 0 00398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 000-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z"}}]},name:"play-circle",theme:"outlined"},c=r(91146),l=function(e,t){return n.createElement(c.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:a}))};var i=n.forwardRef(l)},43471:function(e,t,r){var o=r(1413),n=r(67294),a=r(82947),c=r(91146),l=function(e,t){return n.createElement(c.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:a.Z}))},i=n.forwardRef(l);t.Z=i},26058:function(e,t,r){r.d(t,{Z:function(){return x}});var o=r(74902),n=r(67294),a=r(93967),c=r.n(a),l=r(98423),i=r(53124),s=r(82401),f=r(50344),u=r(70985);var d=r(24793),g=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]])}return r};function m(e){let{suffixCls:t,tagName:r,displayName:o}=e;return e=>n.forwardRef(((o,a)=>n.createElement(e,Object.assign({ref:a,suffixCls:t,tagName:r},o))))}const h=n.forwardRef(((e,t)=>{const{prefixCls:r,suffixCls:o,className:a,tagName:l}=e,s=g(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:f}=n.useContext(i.E_),u=f("layout",r),[m,h,v]=(0,d.ZP)(u),p=o?`${u}-${o}`:u;return m(n.createElement(l,Object.assign({className:c()(r||p,a,h,v),ref:t},s)))})),v=n.forwardRef(((e,t)=>{const{direction:r}=n.useContext(i.E_),[a,m]=n.useState([]),{prefixCls:h,className:v,rootClassName:p,children:b,hasSider:C,tagName:Z,style:y}=e,x=g(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),z=(0,l.Z)(x,["suffixCls"]),{getPrefixCls:w,className:k,style:H}=(0,i.dj)("layout"),S=w("layout",h),O=function(e,t,r){return"boolean"==typeof r?r:!!e.length||(0,f.Z)(t).some((e=>e.type===u.Z))}(a,b,C),[$,N,E]=(0,d.ZP)(S),M=c()(S,{[`${S}-has-sider`]:O,[`${S}-rtl`]:"rtl"===r},k,v,p,N,E),L=n.useMemo((()=>({siderHook:{addSider:e=>{m((t=>[].concat((0,o.Z)(t),[e])))},removeSider:e=>{m((t=>t.filter((t=>t!==e))))}}})),[]);return $(n.createElement(s.V.Provider,{value:L},n.createElement(Z,Object.assign({ref:t,className:M,style:Object.assign(Object.assign({},H),y)},z),b)))})),p=m({tagName:"div",displayName:"Layout"})(v),b=m({suffixCls:"header",tagName:"header",displayName:"Header"})(h),C=m({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(h),Z=m({suffixCls:"content",tagName:"main",displayName:"Content"})(h);const y=p;y.Header=b,y.Footer=C,y.Content=Z,y.Sider=u.Z,y._InternalSiderContext=u.D;var x=y},66309:function(e,t,r){r.d(t,{Z:function(){return N}});var o=r(67294),n=r(93967),a=r.n(n),c=r(98423),l=r(98787),i=r(69760),s=r(96159),f=r(45353),u=r(53124),d=r(11568),g=r(15063),m=r(14747),h=r(83262),v=r(83559);const p=e=>{const{lineWidth:t,fontSizeIcon:r,calc:o}=e,n=e.fontSizeSM;return(0,h.IX)(e,{tagFontSize:n,tagLineHeight:(0,d.bf)(o(e.lineHeightSM).mul(n).equal()),tagIconSize:o(r).sub(o(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},b=e=>({defaultBg:new g.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var C=(0,v.I$)("Tag",(e=>(e=>{const{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:o,componentCls:n,calc:a}=e,c=a(o).sub(r).equal(),l=a(t).sub(r).equal();return{[n]:Object.assign(Object.assign({},(0,m.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:c,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,d.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${n}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${n}-close-icon`]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${n}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${n}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:c}}),[`${n}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(p(e))),b),Z=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]])}return r};const y=o.forwardRef(((e,t)=>{const{prefixCls:r,style:n,className:c,checked:l,onChange:i,onClick:s}=e,f=Z(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:d,tag:g}=o.useContext(u.E_),m=d("tag",r),[h,v,p]=C(m),b=a()(m,`${m}-checkable`,{[`${m}-checkable-checked`]:l},null==g?void 0:g.className,c,v,p);return h(o.createElement("span",Object.assign({},f,{ref:t,style:Object.assign(Object.assign({},n),null==g?void 0:g.style),className:b,onClick:e=>{null==i||i(!l),null==s||s(e)}})))}));var x=y,z=r(98719);var w=(0,v.bk)(["Tag","preset"],(e=>(e=>(0,z.Z)(e,((t,r)=>{let{textColor:o,lightBorderColor:n,lightColor:a,darkColor:c}=r;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:o,background:a,borderColor:n,"&-inverse":{color:e.colorTextLightSolid,background:c,borderColor:c},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(p(e))),b);const k=(e,t,r)=>{const o="string"!=typeof(n=r)?n:n.charAt(0).toUpperCase()+n.slice(1);var n;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${o}Bg`],borderColor:e[`color${o}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var H=(0,v.bk)(["Tag","status"],(e=>{const t=p(e);return[k(t,"success","Success"),k(t,"processing","Info"),k(t,"error","Error"),k(t,"warning","Warning")]}),b),S=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]])}return r};const O=o.forwardRef(((e,t)=>{const{prefixCls:r,className:n,rootClassName:d,style:g,children:m,icon:h,color:v,onClose:p,bordered:b=!0,visible:Z}=e,y=S(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:x,direction:z,tag:k}=o.useContext(u.E_),[O,$]=o.useState(!0),N=(0,c.Z)(y,["closeIcon","closable"]);o.useEffect((()=>{void 0!==Z&&$(Z)}),[Z]);const E=(0,l.o2)(v),M=(0,l.yT)(v),L=E||M,j=Object.assign(Object.assign({backgroundColor:v&&!L?v:void 0},null==k?void 0:k.style),g),B=x("tag",r),[P,R,V]=C(B),I=a()(B,null==k?void 0:k.className,{[`${B}-${v}`]:L,[`${B}-has-color`]:v&&!L,[`${B}-hidden`]:!O,[`${B}-rtl`]:"rtl"===z,[`${B}-borderless`]:!b},n,d,R,V),T=e=>{e.stopPropagation(),null==p||p(e),e.defaultPrevented||$(!1)},[,_]=(0,i.Z)((0,i.w)(e),(0,i.w)(k),{closable:!1,closeIconRender:e=>{const t=o.createElement("span",{className:`${B}-close-icon`,onClick:T},e);return(0,s.wm)(e,t,(e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),T(t)},className:a()(null==e?void 0:e.className,`${B}-close-icon`)})))}}),F="function"==typeof y.onClick||m&&"a"===m.type,A=h||null,W=A?o.createElement(o.Fragment,null,A,m&&o.createElement("span",null,m)):m,q=o.createElement("span",Object.assign({},N,{ref:t,className:I,style:j}),W,_,E&&o.createElement(w,{key:"preset",prefixCls:B}),M&&o.createElement(H,{key:"status",prefixCls:B}));return P(F?o.createElement(f.Z,{component:"Tag"},q):q)})),$=O;$.CheckableTag=x;var N=$}}]);