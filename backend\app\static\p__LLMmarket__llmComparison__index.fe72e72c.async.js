(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7629],{47046:function(e,n){"use strict";n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"}},82947:function(e,n){"use strict";n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"}},85175:function(e,n,t){"use strict";var r=t(1413),a=t(67294),s=t(48820),i=t(91146),o=function(e,n){return a.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},c=a.forwardRef(o);n.Z=c},82061:function(e,n,t){"use strict";var r=t(1413),a=t(67294),s=t(47046),i=t(91146),o=function(e,n){return a.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},c=a.forwardRef(o);n.Z=c},47389:function(e,n,t){"use strict";var r=t(1413),a=t(67294),s=t(27363),i=t(91146),o=function(e,n){return a.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},c=a.forwardRef(o);n.Z=c},51042:function(e,n,t){"use strict";var r=t(1413),a=t(67294),s=t(42110),i=t(91146),o=function(e,n){return a.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},c=a.forwardRef(o);n.Z=c},43471:function(e,n,t){"use strict";var r=t(1413),a=t(67294),s=t(82947),i=t(91146),o=function(e,n){return a.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s.Z}))},c=a.forwardRef(o);n.Z=c},27496:function(e,n,t){"use strict";t.d(n,{Z:function(){return c}});var r=t(1413),a=t(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M931.4 498.9L94.9 79.5c-3.4-1.7-7.3-2.1-11-1.2a15.99 15.99 0 00-11.7 19.3l86.2 352.2c1.3 5.3 5.2 9.6 10.4 11.3l147.7 50.7-147.6 50.7c-5.2 1.8-9.1 6-10.3 11.3L72.2 926.5c-.9 3.7-.5 7.6 1.2 10.9 3.9 7.9 13.5 11.1 21.5 7.2l836.5-417c3.1-1.5 5.6-4.1 7.2-7.1 3.9-8 .7-17.6-7.2-21.6zM170.8 826.3l50.3-205.6 295.2-101.3c2.3-.8 4.2-2.6 5-5 1.4-4.2-.8-8.7-5-10.2L221.1 403 171 198.2l628 314.9-628.2 313.2z"}}]},name:"send",theme:"outlined"},i=t(91146),o=function(e,n){return a.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:s}))};var c=a.forwardRef(o)},16151:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return se}});var r=t(5574),a=t.n(r),s=t(67294),i=t(26058),o=t(71471),c=t(40411),l=t(96074),u=t(19632),d=t.n(u),f=t(55102),p=t(47019),x=t(17788),h=t(2453),m=t(2487),g=t(4393),v=t(42075),y=t(83062),b=t(83622),j=t(47389),Z=t(85175),w=t(82061),k=t(27496),C=t(51042),S=t(85893),N=f.Z.TextArea,O=o.Z.Text,P=function(e){var n=e.onSend,t=(0,s.useState)([{id:"1",title:"基础理解能力测试",content:"解释量子计算的基本原理以及它如何与传统计算机的工作方式不同。"},{id:"2",title:"代码生成能力测试",content:"使用React和TypeScript实现一个简单的待办事项列表组件，包含添加、删除和标记完成功能。"},{id:"3",title:"创意写作能力测试",content:"写一个短篇科幻故事，描述人工智能在未来医疗领域的应用场景。故事应包含人类医生和AI助手之间的互动。"},{id:"4",title:"逻辑推理能力测试",content:"一个盒子里有5个红球，3个蓝球和2个绿球。如果随机抽取3个球，求抽到至少1个红球和至少1个蓝球的概率是多少？请详细解释你的计算过程。"}]),r=a()(t,2),i=r[0],o=r[1],c=(0,s.useState)(!1),l=a()(c,2),u=l[0],P=l[1],_=(0,s.useState)(null),E=a()(_,2),$=E[0],z=E[1],R=p.Z.useForm(),I=a()(R,1)[0],T=(0,s.useState)(""),L=a()(T,2),M=L[0],H=L[1],D=function(e){e?(z(e),I.setFieldsValue(e)):(z(null),I.resetFields()),P(!0)},A=i.filter((function(e){return e.title.toLowerCase().includes(M.toLowerCase())||e.content.toLowerCase().includes(M.toLowerCase())}));return(0,S.jsxs)("div",{style:{height:"100%",display:"flex",flexDirection:"column"},children:[(0,S.jsx)("div",{style:{padding:"0 16px 16px"},children:(0,S.jsx)(f.Z,{placeholder:"搜索提示词...",value:M,onChange:function(e){return H(e.target.value)},allowClear:!0})}),(0,S.jsx)("div",{style:{flex:1,overflow:"auto",padding:"0 16px"},children:(0,S.jsx)(m.Z,{grid:{gutter:16,column:1},dataSource:A,renderItem:function(e){return(0,S.jsx)(m.Z.Item,{children:(0,S.jsx)(g.Z,{size:"small",title:e.title,extra:(0,S.jsxs)(v.Z,{children:[(0,S.jsx)(y.Z,{title:"编辑",children:(0,S.jsx)(b.ZP,{type:"text",size:"small",icon:(0,S.jsx)(j.Z,{}),onClick:function(){return D(e)}})}),(0,S.jsx)(y.Z,{title:"复制",children:(0,S.jsx)(b.ZP,{type:"text",size:"small",icon:(0,S.jsx)(Z.Z,{}),onClick:function(){return n=e.content,void navigator.clipboard.writeText(n).then((function(){return h.ZP.success("已复制到剪贴板")})).catch((function(){return h.ZP.error("复制失败")}));var n}})}),(0,S.jsx)(y.Z,{title:"删除",children:(0,S.jsx)(b.ZP,{type:"text",size:"small",icon:(0,S.jsx)(w.Z,{}),onClick:function(){return n=e.id,void x.Z.confirm({title:"确认删除",content:"确定要删除这个提示词吗？",onOk:function(){o(i.filter((function(e){return e.id!==n})))}});var n}})})]}),actions:[(0,S.jsx)(b.ZP,{type:"primary",icon:(0,S.jsx)(k.Z,{}),onClick:function(){return n(e.content)},children:"发送"},"send")],children:(0,S.jsx)(O,{children:e.content})})})}})}),(0,S.jsx)("div",{style:{padding:"16px",borderTop:"1px solid #f0f0f0"},children:(0,S.jsx)(b.ZP,{type:"dashed",icon:(0,S.jsx)(C.Z,{}),block:!0,onClick:function(){return D()},children:"添加提示词"})}),(0,S.jsx)(x.Z,{title:$?"编辑提示词":"新建提示词",open:u,onOk:function(){I.validateFields().then((function(e){var n={id:$?$.id:Date.now().toString(),title:e.title,content:e.content};o($?i.map((function(e){return e.id===$.id?n:e})):[].concat(d()(i),[n])),P(!1),I.resetFields()}))},onCancel:function(){P(!1),I.resetFields()},children:(0,S.jsxs)(p.Z,{form:I,layout:"vertical",children:[(0,S.jsx)(p.Z.Item,{name:"title",label:"标题",rules:[{required:!0,message:"请输入提示词标题"}],children:(0,S.jsx)(f.Z,{placeholder:"输入提示词标题"})}),(0,S.jsx)(p.Z.Item,{name:"content",label:"内容",rules:[{required:!0,message:"请输入提示词内容"}],children:(0,S.jsx)(N,{placeholder:"输入提示词内容",autoSize:{minRows:4,maxRows:8}})})]})})]})},_=(0,t(28846).kc)((function(e){e.token;return{main:{maxWidth:"100%",lineHeight:"24px",padding:"0",width:"100%",height:"100%",overflow:"hidden",justifyContent:"center",alignItems:"center"}}})),E=t(97857),$=t.n(E),z=t(15009),R=t.n(z),I=t(99289),T=t.n(I),L=t(34041),M=t(74330),H=t(86250),D=t(43471),A=t(10048),F=t(64599),G=t.n(F),W=t(78158),J=function(){var e=T()(R()().mark((function e(n){return R()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,W.N)("/api/llms",{method:"GET",params:$()($()({},n),{},{active:1})}));case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),Y=function(){return"llmcomp-"+Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15)},B=function(){var e=T()(R()().mark((function e(n,t){var r;return R()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=Y(),e.abrupt("return",(0,W.N)("/api/chat/v1/completions",{method:"POST",body:{llm_id:n,user:{user_id:"1",display_name:"模型比较用户"},conversation_id:r,stream:!0,include_history:!1,messages:[{role:"user",content:t}],app_info:"llmComparison"},getResponse:!0,responseType:"blob"}));case 2:case"end":return e.stop()}}),e)})));return function(n,t){return e.apply(this,arguments)}}(),X=function(){var e=T()(R()().mark((function e(n,t,r,a){var s,i,o,c,l,u,d,f,p,x,h,m,g,v,y,b,j,Z,w;return R()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,i=null===(s=n.body)||void 0===s?void 0:s.getReader()){e.next=5;break}return r("无法读取响应"),e.abrupt("return");case 5:o=new TextDecoder,c="";case 7:return e.next=10,i.read();case 10:if(l=e.sent,u=l.done,d=l.value,!u){e.next=43;break}if(!c.trim()){e.next=42;break}e.prev=15,f=c.split("\n"),p=G()(f),e.prev=18,p.s();case 20:if((x=p.n()).done){e.next=29;break}if(!(h=x.value).trim().startsWith("data:")){e.next=27;break}if("[DONE]"!==(m=h.substring(5).trim())){e.next=26;break}return e.abrupt("continue",27);case 26:if(m)try{(g=JSON.parse(m)).choices&&g.choices[0]&&g.choices[0].delta&&g.choices[0].delta.content&&t(g.choices[0].delta.content)}catch(e){console.warn("解析JSON时出错:",e,m)}case 27:e.next=20;break;case 29:e.next=34;break;case 31:e.prev=31,e.t0=e.catch(18),p.e(e.t0);case 34:return e.prev=34,p.f(),e.finish(34);case 37:e.next=42;break;case 39:e.prev=39,e.t1=e.catch(15),console.warn("处理剩余数据时出错:",e.t1);case 42:return e.abrupt("break",68);case 43:c+=o.decode(d,{stream:!0}),v=c.split("\n"),c=v.pop()||"",y=G()(v),e.prev=47,y.s();case 49:if((b=y.n()).done){e.next=58;break}if(!(j=b.value).trim().startsWith("data:")){e.next=56;break}if("[DONE]"!==(Z=j.substring(5).trim())){e.next=55;break}return e.abrupt("continue",56);case 55:if(Z)try{(w=JSON.parse(Z)).choices&&w.choices[0]&&w.choices[0].delta&&w.choices[0].delta.content&&t(w.choices[0].delta.content)}catch(e){console.warn("解析JSON时出错:",e,Z)}case 56:e.next=49;break;case 58:e.next=63;break;case 60:e.prev=60,e.t2=e.catch(47),y.e(e.t2);case 63:return e.prev=63,y.f(),e.finish(63);case 66:e.next=7;break;case 68:a(),e.next=75;break;case 71:e.prev=71,e.t3=e.catch(0),console.error("处理流式响应时出错:",e.t3),r(e.t3);case 75:case"end":return e.stop()}}),e,null,[[0,71],[15,39],[18,31,34,37],[47,60,63,66]])})));return function(n,t,r,a){return e.apply(this,arguments)}}();var q=t(27484),V=t.n(q),K=o.Z.Text,Q=L.default.Option,U=(0,A.Z)({html:!0,breaks:!0}),ee=function(e){var n=e.onSend,t=(0,s.useState)([]),r=a()(t,2),i=r[0],o=r[1],c=(0,s.useState)([]),l=a()(c,2),u=l[0],p=l[1],x=(0,s.useState)([]),m=a()(x,2),y=m[0],j=m[1],N=(0,s.useState)(!1),O=a()(N,2),P=O[0],_=O[1],E=(0,s.useState)(!1),z=a()(E,2),I=z[0],A=z[1],F=(0,s.useRef)(null),G=(0,s.useRef)(null);(0,s.useEffect)((function(){var e=function(){var e=T()(R()().mark((function e(){var n;return R()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,J({current:1,pageSize:100});case 3:(n=e.sent).success&&Array.isArray(n.data)&&(o(n.data),console.log("获取到模型列表:",n.data)),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取模型列表时出错:",e.t0),h.ZP.error("获取模型列表失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);var W=function(e){return y.find((function(n){return n.id===e}))},q=function(){var e=T()(R()().mark((function e(n,t){var r,a,s,i,o,c;return R()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(W(n)){e.next=3;break}return e.abrupt("return");case 3:return r=Y(),a=V()().format("YYYY-MM-DD HH:mm:ss"),j((function(e){return e.map((function(e){if(e.id===n){var s={conversation_id:e.conversation_id,message_id:r,role:"user",content:t,created_at:a},i={conversation_id:e.conversation_id,message_id:Y(),role:"assistant",content:"",loading:!0,created_at:a};return $()($()({},e),{},{messages:[].concat(d()(e.messages),[s,i]),loading:!0,error:null})}return e}))})),e.prev=6,e.next=9,B(n,t);case 9:return i=e.sent,o=y.findIndex((function(e){return e.id===n})),c=(null===(s=y[o])||void 0===s?void 0:s.messages.length)-1||0,e.next=14,X(i,(function(e){j((function(n){return n.map((function(n,t){if(t===o){var r=d()(n.messages);return r[c]&&(r[c]=$()($()({},r[c]),{},{content:r[c].content+e,loading:!0})),$()($()({},n),{},{messages:r})}return n}))}))}),(function(e){console.error("模型响应错误:",e),j((function(n){return n.map((function(n,t){if(t===o){var r=d()(n.messages);return r[c]&&(r[c]=$()($()({},r[c]),{},{content:"发生错误: "+("string"==typeof e?e:JSON.stringify(e)),loading:!1,error:"错误"})),$()($()({},n),{},{loading:!1,error:"string"==typeof e?e:JSON.stringify(e)})}return n}))}))}),(function(){j((function(e){return e.map((function(e,n){if(n===o){var t=d()(e.messages);return t[c]&&(t[c]=$()($()({},t[c]),{},{loading:!1})),$()($()({},e),{},{loading:!1})}return e}))}))}));case 14:e.next=20;break;case 16:e.prev=16,e.t0=e.catch(6),console.error("请求过程中出错:",e.t0),j((function(e){return e.map((function(e){return e.id===n?$()($()({},e),{},{loading:!1,error:"请求过程中出错"}):e}))}));case 20:case"end":return e.stop()}}),e,null,[[6,16]])})));return function(n,t){return e.apply(this,arguments)}}(),ee=function(){var e=T()(R()().mark((function e(t){var r;return R()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(""!==t.trim()){e.next=3;break}return h.ZP.warning("请输入有效的提示词"),e.abrupt("return");case 3:if(0!==u.length){e.next=6;break}return h.ZP.warning("请至少添加一个模型到比较面板"),e.abrupt("return");case 6:return A(!0),r=u.map((function(e){return q(e,t)})),e.prev=8,e.next=11,Promise.all(r);case 11:e.next=16;break;case 13:e.prev=13,e.t0=e.catch(8),console.error("发送消息时出错:",e.t0);case 16:return e.prev=16,A(!1),e.finish(16);case 19:n(t),G.current&&(G.current.value="");case 21:case"end":return e.stop()}}),e,null,[[8,13,16,19]])})));return function(n){return e.apply(this,arguments)}}(),ne=function(){var e=T()(R()().mark((function e(n){var t,r;return R()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if((t=W(n))&&0!==t.messages.length){e.next=3;break}return e.abrupt("return");case 3:if(r=d()(t.messages).reverse().find((function(e){return"user"===e.role}))){e.next=6;break}return e.abrupt("return");case 6:return j((function(e){return e.map((function(e){if(e.id===n){var t=e.messages.findIndex((function(e){return e.message_id===r.message_id}));if(-1!==t)return $()($()({},e),{},{messages:e.messages.slice(0,t+1)})}return e}))})),e.next=9,q(n,r.content);case 9:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),te=function(e){return e.loading?(0,S.jsxs)("div",{className:"message-content",children:[(0,S.jsx)("div",{dangerouslySetInnerHTML:{__html:U.render(e.content||"")}}),(0,S.jsx)(M.Z,{size:"small"})]}):e.error?(0,S.jsx)("div",{className:"message-content error",children:(0,S.jsx)(K,{type:"danger",children:e.content})}):(0,S.jsxs)("div",{className:"message-content",children:[(0,S.jsx)("div",{dangerouslySetInnerHTML:{__html:U.render(e.content||"")}}),"assistant"===e.role&&(0,S.jsx)(b.ZP,{size:"small",type:"text",icon:(0,S.jsx)(Z.Z,{}),onClick:function(){return n=e.content,void navigator.clipboard.writeText(n).then((function(){return h.ZP.success("已复制到剪贴板")})).catch((function(e){console.error("复制失败:",e),h.ZP.error("复制失败")}));var n},className:"copy-button"})]})};return(0,S.jsxs)("div",{style:{height:"100%",display:"flex",flexDirection:"column"},children:[(0,S.jsxs)("div",{ref:F,style:{padding:"10px",display:"flex",overflowX:"auto",overflowY:"hidden",flex:1},className:"model-container",children:[y.map((function(e){return(0,S.jsx)(g.Z,{size:"small",title:(0,S.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,S.jsx)("span",{children:e.name}),(0,S.jsxs)(v.Z,{children:[(0,S.jsx)(b.ZP,{type:"text",icon:(0,S.jsx)(D.Z,{}),onClick:function(){return ne(e.id)},size:"small",title:"重新生成",disabled:e.loading||0===e.messages.length}),(0,S.jsx)(b.ZP,{type:"text",icon:(0,S.jsx)(w.Z,{}),onClick:function(){return n=e.id,p((function(e){return e.filter((function(e){return e!==n}))})),void j((function(e){return e.filter((function(e){return e.id!==n}))}));var n},size:"small",title:"移除模型"})]})]}),style:{minWidth:"350px",width:"350px",height:"100%",marginRight:"10px",display:"flex",flexDirection:"column",flexShrink:0},bodyStyle:{flex:1,overflow:"auto",padding:"10px",height:"100%"},children:(0,S.jsxs)("div",{style:{height:"100%",overflow:"auto"},children:[e.messages.map((function(n,t){return(0,S.jsxs)("div",{className:"message ".concat(n.role),children:[(0,S.jsxs)("div",{className:"message-header",children:[(0,S.jsx)("strong",{children:"user"===n.role?"用户":e.name}),(0,S.jsx)("span",{className:"message-time",children:n.created_at})]}),te(n)]},t)})),e.loading&&0===e.messages.length&&(0,S.jsxs)("div",{style:{textAlign:"center",padding:"20px"},children:[(0,S.jsx)(M.Z,{})," ",(0,S.jsx)(K,{type:"secondary",children:"等待响应..."})]}),e.error&&(0,S.jsx)("div",{style:{textAlign:"center",padding:"10px",color:"red"},children:e.error})]})},e.id)})),P?(0,S.jsx)(g.Z,{size:"small",title:"添加模型",style:{minWidth:"350px",width:"350px",height:"fit-content",flexShrink:0},children:(0,S.jsxs)(v.Z,{direction:"vertical",style:{width:"100%"},children:[(0,S.jsx)(L.default,{placeholder:"选择一个模型",style:{width:"100%"},onChange:function(e){if(e){var n=i.find((function(n){return n.id===e}));if(n)if(u.includes(e))h.ZP.warning("该模型已添加到比较面板");else{var t=Y();p((function(n){return[].concat(d()(n),[e])})),j((function(r){return[].concat(d()(r),[{id:e,name:n.name||"模型 ".concat(e),conversation_id:t,messages:[],loading:!1,error:null}])})),_(!1),setTimeout((function(){F.current&&(F.current.scrollLeft=F.current.scrollWidth)}),100)}else h.ZP.error("找不到选择的模型")}},showSearch:!0,optionFilterProp:"children",filterOption:function(e,n){var t;return(null==n||null===(t=n.children)||void 0===t?void 0:t.toString().toLowerCase().indexOf(e.toLowerCase()))>=0},children:i.map((function(e){return(0,S.jsx)(Q,{value:e.id,children:e.name||"模型 ".concat(e.id)},e.id)}))}),(0,S.jsxs)(H.Z,{justify:"space-between",children:[(0,S.jsx)(b.ZP,{onClick:function(){return _(!1)},children:"取消"}),(0,S.jsx)(b.ZP,{type:"primary",onClick:function(){return h.ZP.warning("请从下拉列表中选择一个模型")},children:"确认"})]})]})}):(0,S.jsx)(b.ZP,{type:"dashed",icon:(0,S.jsx)(C.Z,{}),style:{minWidth:"120px",height:"100%",flexShrink:0},onClick:function(){return _(!0)},children:"添加模型"})]}),(0,S.jsx)("div",{style:{borderTop:"1px solid #f0f0f0",padding:"10px",backgroundColor:"#fff"},children:(0,S.jsxs)(v.Z,{style:{width:"100%"},direction:"vertical",children:[(0,S.jsxs)(K,{type:"secondary",children:["已选择 ",u.length," 个模型进行比较"]}),(0,S.jsxs)(H.Z,{gap:"small",children:[(0,S.jsx)(f.Z.TextArea,{ref:G,placeholder:"输入提示词，将同时发送给所有选中的模型",rows:2,onPressEnter:function(e){e.shiftKey||(e.preventDefault(),ee(e.target.value))}}),(0,S.jsx)(b.ZP,{type:"primary",icon:(0,S.jsx)(k.Z,{}),loading:I,onClick:function(){G.current&&ee(G.current.value)},children:"发送"})]}),(0,S.jsx)(K,{type:"secondary",style:{fontSize:"12px"},children:"提示: 按Enter发送，Shift+Enter换行"})]})}),(0,S.jsx)("style",{children:"\n        .model-container::-webkit-scrollbar {\n          height: 8px;\n        }\n        \n        .model-container::-webkit-scrollbar-track {\n          background: #f0f0f0;\n          border-radius: 4px;\n        }\n        \n        .model-container::-webkit-scrollbar-thumb {\n          background: #ccc;\n          border-radius: 4px;\n        }\n        \n        .model-container::-webkit-scrollbar-thumb:hover {\n          background: #aaa;\n        }\n        \n        .message {\n          margin-bottom: 16px;\n          border-radius: 8px;\n        }\n        \n        .message.user {\n          background-color: #f0f0f0;\n          padding: 12px;\n        }\n        \n        .message.assistant {\n          background-color: #f9f9f9;\n          padding: 12px;\n        }\n        \n        .message-header {\n          margin-bottom: 8px;\n          color: #555;\n          display: flex;\n          justify-content: space-between;\n        }\n        \n        .message-time {\n          color: #999;\n          font-size: 12px;\n        }\n        \n        .message-content {\n          position: relative;\n        }\n        \n        .message-content pre {\n          background-color: #f3f3f3;\n          padding: 10px;\n          border-radius: 4px;\n          overflow-x: auto;\n        }\n        \n        .message-content code {\n          font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;\n          background-color: #f3f3f3;\n          padding: 2px 4px;\n          border-radius: 3px;\n        }\n        \n        .copy-button {\n          position: absolute;\n          right: 0;\n          top: 0;\n          opacity: 0;\n          transition: opacity 0.2s;\n        }\n        \n        .message-content:hover .copy-button {\n          opacity: 1;\n        }\n        \n        .error {\n          color: #ff4d4f;\n        }\n      "})]})},ne=i.Z.Sider,te=i.Z.Content,re=o.Z.Title,ae=o.Z.Text,se=function(){var e=window.innerHeight,n=_().styles,t=(0,s.useState)(""),r=a()(t,2),o=r[0],u=r[1],d=function(e){console.log("发送提示:",e),u(e)};return(0,S.jsx)("div",{className:n.main,style:{height:e-56},children:(0,S.jsxs)(i.Z,{style:{height:"100%"},children:[(0,S.jsxs)(ne,{width:320,theme:"light",style:{borderRight:"1px solid #f0f0f0",overflow:"auto"},children:[(0,S.jsxs)("div",{style:{padding:"16px"},children:[(0,S.jsx)(re,{level:4,children:"提示词管理"}),(0,S.jsx)(ae,{type:"secondary",children:"选择或创建提示词，发送到多个模型进行比较"}),o&&(0,S.jsx)(c.Z,{color:"blue",style:{marginLeft:8},title:"最新发送的提示词"}),(0,S.jsx)(l.Z,{})]}),(0,S.jsx)(P,{onSend:d})]}),(0,S.jsx)(te,{style:{position:"relative",height:"100%",overflow:"hidden"},children:(0,S.jsx)(ee,{onSend:d})})]})})}},86250:function(e,n,t){"use strict";t.d(n,{Z:function(){return w}});var r=t(67294),a=t(93967),s=t.n(a),i=t(98423),o=t(98065),c=t(53124),l=t(83559),u=t(83262);const d=["wrap","nowrap","wrap-reverse"],f=["flex-start","flex-end","start","end","center","space-between","space-around","space-evenly","stretch","normal","left","right"],p=["center","start","end","flex-start","flex-end","self-start","self-end","baseline","normal","stretch"];var x=function(e,n){return s()(Object.assign(Object.assign(Object.assign({},((e,n)=>{const t=!0===n.wrap?"wrap":n.wrap;return{[`${e}-wrap-${t}`]:t&&d.includes(t)}})(e,n)),((e,n)=>{const t={};return p.forEach((r=>{t[`${e}-align-${r}`]=n.align===r})),t[`${e}-align-stretch`]=!n.align&&!!n.vertical,t})(e,n)),((e,n)=>{const t={};return f.forEach((r=>{t[`${e}-justify-${r}`]=n.justify===r})),t})(e,n)))};const h=e=>{const{componentCls:n}=e;return{[n]:{display:"flex",margin:0,padding:0,"&-vertical":{flexDirection:"column"},"&-rtl":{direction:"rtl"},"&:empty":{display:"none"}}}},m=e=>{const{componentCls:n}=e;return{[n]:{"&-gap-small":{gap:e.flexGapSM},"&-gap-middle":{gap:e.flexGap},"&-gap-large":{gap:e.flexGapLG}}}},g=e=>{const{componentCls:n}=e,t={};return d.forEach((e=>{t[`${n}-wrap-${e}`]={flexWrap:e}})),t},v=e=>{const{componentCls:n}=e,t={};return p.forEach((e=>{t[`${n}-align-${e}`]={alignItems:e}})),t},y=e=>{const{componentCls:n}=e,t={};return f.forEach((e=>{t[`${n}-justify-${e}`]={justifyContent:e}})),t};var b=(0,l.I$)("Flex",(e=>{const{paddingXS:n,padding:t,paddingLG:r}=e,a=(0,u.IX)(e,{flexGapSM:n,flexGap:t,flexGapLG:r});return[h(a),m(a),g(a),v(a),y(a)]}),(()=>({})),{resetStyle:!1}),j=function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]])}return t};const Z=r.forwardRef(((e,n)=>{const{prefixCls:t,rootClassName:a,className:l,style:u,flex:d,gap:f,children:p,vertical:h=!1,component:m="div"}=e,g=j(e,["prefixCls","rootClassName","className","style","flex","gap","children","vertical","component"]),{flex:v,direction:y,getPrefixCls:Z}=r.useContext(c.E_),w=Z("flex",t),[k,C,S]=b(w),N=null!=h?h:null==v?void 0:v.vertical,O=s()(l,a,null==v?void 0:v.className,w,C,S,x(w,e),{[`${w}-rtl`]:"rtl"===y,[`${w}-gap-${f}`]:(0,o.n)(f),[`${w}-vertical`]:N}),P=Object.assign(Object.assign({},null==v?void 0:v.style),u);return d&&(P.flex=d),f&&!(0,o.n)(f)&&(P.gap=f),k(r.createElement(m,Object.assign({ref:n,className:O,style:P},(0,i.Z)(g,["justify","wrap","align"])),p))}));var w=Z},26058:function(e,n,t){"use strict";t.d(n,{Z:function(){return Z}});var r=t(74902),a=t(67294),s=t(93967),i=t.n(s),o=t(98423),c=t(53124),l=t(82401),u=t(50344),d=t(70985);var f=t(24793),p=function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]])}return t};function x(e){let{suffixCls:n,tagName:t,displayName:r}=e;return e=>a.forwardRef(((r,s)=>a.createElement(e,Object.assign({ref:s,suffixCls:n,tagName:t},r))))}const h=a.forwardRef(((e,n)=>{const{prefixCls:t,suffixCls:r,className:s,tagName:o}=e,l=p(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:u}=a.useContext(c.E_),d=u("layout",t),[x,h,m]=(0,f.ZP)(d),g=r?`${d}-${r}`:d;return x(a.createElement(o,Object.assign({className:i()(t||g,s,h,m),ref:n},l)))})),m=a.forwardRef(((e,n)=>{const{direction:t}=a.useContext(c.E_),[s,x]=a.useState([]),{prefixCls:h,className:m,rootClassName:g,children:v,hasSider:y,tagName:b,style:j}=e,Z=p(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),w=(0,o.Z)(Z,["suffixCls"]),{getPrefixCls:k,className:C,style:S}=(0,c.dj)("layout"),N=k("layout",h),O=function(e,n,t){return"boolean"==typeof t?t:!!e.length||(0,u.Z)(n).some((e=>e.type===d.Z))}(s,v,y),[P,_,E]=(0,f.ZP)(N),$=i()(N,{[`${N}-has-sider`]:O,[`${N}-rtl`]:"rtl"===t},C,m,g,_,E),z=a.useMemo((()=>({siderHook:{addSider:e=>{x((n=>[].concat((0,r.Z)(n),[e])))},removeSider:e=>{x((n=>n.filter((n=>n!==e))))}}})),[]);return P(a.createElement(l.V.Provider,{value:z},a.createElement(b,Object.assign({ref:n,className:$,style:Object.assign(Object.assign({},S),j)},w),v)))})),g=x({tagName:"div",displayName:"Layout"})(m),v=x({suffixCls:"header",tagName:"header",displayName:"Header"})(h),y=x({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(h),b=x({suffixCls:"content",tagName:"main",displayName:"Content"})(h);const j=g;j.Header=v,j.Footer=y,j.Content=b,j.Sider=d.Z,j._InternalSiderContext=d.D;var Z=j},64599:function(e,n,t){var r=t(96263);e.exports=function(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=r(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var a=0,s=function(){};return{s:s,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==t.return||t.return()}finally{if(c)throw i}}}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);