"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5917],{82947:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"}},52197:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"}}]},name:"star",theme:"filled"}},82826:function(e,t,n){n.d(t,{Z:function(){return i}});var r=n(1413),a=n(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"},s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};var i=a.forwardRef(c)},85175:function(e,t,n){var r=n(1413),a=n(67294),o=n(48820),s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o.Z}))},i=a.forwardRef(c);t.Z=i},82061:function(e,t,n){var r=n(1413),a=n(67294),o=n(47046),s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o.Z}))},i=a.forwardRef(c);t.Z=i},47389:function(e,t,n){var r=n(1413),a=n(67294),o=n(27363),s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o.Z}))},i=a.forwardRef(c);t.Z=i},1832:function(e,t,n){n.d(t,{Z:function(){return i}});var r=n(1413),a=n(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M834.1 469.2A347.49 347.49 0 00751.2 354l-29.1-26.7a8.09 8.09 0 00-13 3.3l-13 37.3c-8.1 23.4-23 47.3-44.1 70.8-1.4 1.5-3 1.9-4.1 2-1.1.1-2.8-.1-4.3-1.5-1.4-1.2-2.1-3-2-4.8 3.7-60.2-14.3-128.1-53.7-202C555.3 171 510 123.1 453.4 89.7l-41.3-24.3c-5.4-3.2-12.3 1-12 7.3l2.2 48c1.5 32.8-2.3 61.8-11.3 85.9-11 29.5-26.8 56.9-47 81.5a295.64 295.64 0 01-47.5 46.1 352.6 352.6 0 00-100.3 121.5A347.75 347.75 0 00160 610c0 47.2 9.3 92.9 27.7 136a349.4 349.4 0 0075.5 110.9c32.4 32 70 57.2 111.9 74.7C418.5 949.8 464.5 959 512 959s93.5-9.2 136.9-27.3A348.6 348.6 0 00760.8 857c32.4-32 57.8-69.4 75.5-110.9a344.2 344.2 0 0027.7-136c0-48.8-10-96.2-29.9-140.9zM713 808.5c-53.7 53.2-125 82.4-201 82.4s-147.3-29.2-201-82.4c-53.5-53.1-83-123.5-83-198.4 0-43.5 9.8-85.2 29.1-124 18.8-37.9 46.8-71.8 80.8-97.9a349.6 349.6 0 0058.6-56.8c25-30.5 44.6-64.5 58.2-101a240 240 0 0012.1-46.5c24.1 22.2 44.3 49 61.2 80.4 33.4 62.6 48.8 118.3 45.8 165.7a74.01 74.01 0 0024.4 59.8 73.36 73.36 0 0053.4 18.8c19.7-1 37.8-9.7 51-24.4 13.3-14.9 24.8-30.1 34.4-45.6 14 17.9 25.7 37.4 35 58.4 15.9 35.8 24 73.9 24 113.1 0 74.9-29.5 145.4-83 198.4z"}}]},name:"fire",theme:"outlined"},s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};var i=a.forwardRef(c)},12906:function(e,t,n){n.d(t,{Z:function(){return i}});var r=n(1413),a=n(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM512 533c-85.5 0-155.6 67.3-160 151.6a8 8 0 008 8.4h48.1c4.2 0 7.8-3.2 8.1-7.4C420 636.1 461.5 597 512 597s92.1 39.1 95.8 88.6c.3 4.2 3.9 7.4 8.1 7.4H664a8 8 0 008-8.4C667.6 600.3 597.5 533 512 533z"}}]},name:"frown",theme:"outlined"},s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};var i=a.forwardRef(c)},43471:function(e,t,n){var r=n(1413),a=n(67294),o=n(82947),s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o.Z}))},i=a.forwardRef(c);t.Z=i},25820:function(e,t,n){var r=n(1413),a=n(67294),o=n(52197),s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o.Z}))},i=a.forwardRef(c);t.Z=i},75750:function(e,t,n){n.d(t,{Z:function(){return i}});var r=n(1413),a=n(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"},s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};var i=a.forwardRef(c)},87784:function(e,t,n){n.d(t,{Z:function(){return i}});var r=n(1413),a=n(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"},s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};var i=a.forwardRef(c)},80365:function(e,t,n){n.r(t),n.d(t,{default:function(){return ye}});var r=n(97857),a=n.n(r),o=n(15009),s=n.n(o),c=n(64599),i=n.n(c),l=n(19632),u=n.n(l),d=n(99289),f=n.n(d),p=n(5574),h=n.n(p),x=n(9783),m=n.n(x),g=n(67294),v=n(55102),b=n(47019),y=n(2453),k=n(83062),Z=n(4393),j=n(34041),w=n(54880),S=n(83622),_=n(82826),C=n(27484),O=n.n(C),P=n(28846),I=(0,P.kc)((function(e){var t=e.token;return{reference:{background:"".concat(t.colorBgLayout,"80"),maxWidth:1080,margin:"0 auto",padding:"20px",height:"100%",overflowY:"auto"}}})),D=n(85893),z=v.Z.TextArea,R=(0,g.forwardRef)((function(e,t){var n=e.messages,r=b.Z.useForm(),a=h()(r,1)[0],o=I().styles,s=(0,g.useState)([]),c=h()(s,2),i=c[0],l=c[1];(0,g.useImperativeHandle)(t,(function(){return{updateMessagesList:function(e){l(e)},getMessageId:function(e){var t;return(null===(t=i[e])||void 0===t?void 0:t.message_id)||""}}})),(0,g.useEffect)((function(){n&&n.length>0?l(n):l([])}),[n]);var u,d=function(e){if(i.length>0){var t=i[i.length-1],n=JSON.parse((r=t.content,o={},s=r.match(/Troubleshooting Suggestions:\n([\s\S]*?)\n\n/),c=r.match(/Categories:\s*([\s\S]*?)\n/),l=r.match(/Subcategories:\s*([\s\S]*?)\n\n/),u=r.match(/Comments:\n([\s\S]*)/),s&&(o["Troubleshooting Suggestions"]=String(s[1].trim().split("\n").map((function(e){return e.trim()})).filter((function(e){return e})))),c&&(o.Categories=String(c[1].trim())),l&&(o.Subcategories=String(l[1].trim())),u&&(o.Comments=String(u[1].trim().split("\n").map((function(e){return e.trim()})).filter((function(e){return e})))),JSON.stringify(o,null,4)));console.info("Last Message:",t),console.info("Parsed JSON Data:",n),console.info("Parsed JSON Data:",n["Ticket Reporting Content Suggestion"]),n&&n[e]?"Comments"===e?a.setFieldsValue(m()({},"Comments",n["Ticket Reporting Content Suggestion"]||n.Comments)):a.setFieldsValue(m()({},e,n[e])):y.ZP.error("没有对应的AI生成数据")}var r,o,s,c,l,u};return(0,D.jsx)(Z.Z,{title:"IT事件报告",className:o.reference,children:(0,D.jsxs)(b.Z,{form:a,layout:"vertical",initialValues:{current_date:O()("2024-09-09"),estimated_end_date:O()("2024-09-12"),status:"initialize"},children:[(0,D.jsx)(b.Z.Item,{label:(0,D.jsx)("span",{children:"Request by"}),name:"request_by",style:{flex:1,margin:"8px"},children:(0,D.jsx)(v.Z,{placeholder:"Tom Adj"})}),(0,D.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,D.jsx)(b.Z.Item,{label:(0,D.jsxs)("span",{children:["Categories",(0,D.jsx)(k.Z,{title:"AI生成",children:(0,D.jsx)(_.Z,{style:{marginLeft:8,color:"#1890ff"},onClick:function(){d("Categories")}})})]}),name:"Categories",style:{flex:1,margin:"8px"},children:(0,D.jsxs)(j.default,{children:[(0,D.jsx)(j.default.Option,{value:"Hardware",children:"Hardware"}),(0,D.jsx)(j.default.Option,{value:"Software",children:"Software"}),(0,D.jsx)(j.default.Option,{value:"Network",children:"Network"}),(0,D.jsx)(j.default.Option,{value:"Security",children:"Security"}),(0,D.jsx)(j.default.Option,{value:"Security",children:"Security"})]})}),(0,D.jsx)(b.Z.Item,{name:"Subcategories",label:(0,D.jsxs)("span",{children:["Subcategories",(0,D.jsx)(k.Z,{title:"AI生成",children:(0,D.jsx)(_.Z,{style:{marginLeft:8,color:"#1890ff"},onClick:function(){d("Subcategories")}})})]}),style:{flex:1,margin:"8px"},children:(0,D.jsxs)(j.default,{children:[(0,D.jsx)(j.default.Option,{value:"Laptop troubleshoot and technical support",children:"Laptop troubleshoot and technical support"}),(0,D.jsx)(j.default.Option,{value:"Printer troubleshoot and technical support",children:"Printer troubleshoot and technical support"}),(0,D.jsx)(j.default.Option,{value:"Scanner troubleshoot and technical support",children:"Scanner troubleshoot and technical support"}),(0,D.jsx)(j.default.Option,{value:"Other hardware troubleshoot and technical support",children:"Other hardware troubleshoot and technical support"}),(0,D.jsx)(j.default.Option,{value:"Software installation and configuration",children:"Software installation and configuration"}),(0,D.jsx)(j.default.Option,{value:"Software update and upgrade",children:"Software update and upgrade"}),(0,D.jsx)(j.default.Option,{value:"Software troubleshooting and technical support",children:"Software troubleshooting and technical support"}),(0,D.jsx)(j.default.Option,{value:"Other software troubleshoot and technical support",children:"Other software troubleshoot and technical support"}),(0,D.jsx)(j.default.Option,{value:"Network configuration and setup",children:"Network configuration and setup"}),(0,D.jsx)(j.default.Option,{value:"Network troubleshooting and technical support",children:"Network troubleshooting and technical support"}),(0,D.jsx)(j.default.Option,{value:"Other network troubleshoot and technical support",children:"Other network troubleshoot and technical support"}),(0,D.jsx)(j.default.Option,{value:"Security configuration and setup",children:"Security configuration and setup"}),(0,D.jsx)(j.default.Option,{value:"Security troubleshooting and technical support",children:"Security troubleshooting and technical support"}),(0,D.jsx)(j.default.Option,{value:"Other security troubleshoot and technical support",children:"Other security troubleshoot and technical support"}),(0,D.jsx)(j.default.Option,{value:"Database configuration and setup",children:"Database configuration and setup"}),(0,D.jsx)(j.default.Option,{value:"Database troubleshooting and technical support",children:"Database troubleshooting and technical support"}),(0,D.jsx)(j.default.Option,{value:"Other database troubleshoot and technical support",children:"Other database troubleshoot and technical support"})]})})]}),(0,D.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,D.jsx)(b.Z.Item,{label:(0,D.jsx)("span",{children:"Branch"}),name:"branch",style:{flex:1,margin:"8px"},children:(0,D.jsx)(v.Z,{placeholder:"IT Centre"})}),(0,D.jsx)(b.Z.Item,{label:(0,D.jsx)("span",{children:"Department"}),name:"department",style:{flex:1,margin:"8px"},children:(0,D.jsx)(j.default,{children:(0,D.jsx)(j.default.Option,{value:"initialize",children:"Initialize"})})})]}),(0,D.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,D.jsx)(b.Z.Item,{label:(0,D.jsx)("span",{children:"Current Date"}),name:"current_date",style:{flex:1,margin:"8px"},children:(0,D.jsx)(w.default,{style:{width:"100%"}})}),(0,D.jsx)(b.Z.Item,{label:(0,D.jsx)("span",{children:"Estimated End Date"}),name:"estimated_end_date",style:{flex:1,margin:"8px"},children:(0,D.jsx)(w.default,{style:{width:"100%"}})})]}),(0,D.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,D.jsx)(b.Z.Item,{label:(0,D.jsx)("span",{children:"Contact No."}),name:"contact_no",style:{flex:1,margin:"8px"},children:(0,D.jsx)(v.Z,{placeholder:"IT Centre"})}),(0,D.jsx)(b.Z.Item,{label:(0,D.jsx)("span",{children:"Status"}),name:"status",style:{flex:1,margin:"8px"},children:(0,D.jsx)(j.default,{children:(0,D.jsx)(j.default.Option,{value:"initialize",children:"Initialize"})})})]}),(0,D.jsx)(b.Z.Item,{label:(u="Comments",(0,D.jsxs)("span",{children:[u,(0,D.jsx)(k.Z,{title:"AI生成",children:(0,D.jsx)(_.Z,{style:{marginLeft:8,color:"#1890ff"},onClick:function(){d(u)}})})]})),name:"Comments",style:{flex:1,margin:"8px"},children:(0,D.jsx)(z,{rows:4,placeholder:"Issue Summary: ..."})}),(0,D.jsx)(b.Z.Item,{style:{flex:1,margin:"8px 8px"},children:(0,D.jsx)(S.ZP,{type:"primary",htmlType:"submit",children:"Submit"})})]})})})),M=n(93461),T=n(34114),N=n(78205),E=n(78919),L=n(4628),A=n(9502),F=n(37864),H=n(42075),Y=n(71471),B=n(17788),J=n(74330),W=n(86250),q=n(85265),G=n(10048),K=n(10981),V=n(78404),X=n(1832),U=n(14079),$=n(51042),Q=n(82061),ee=n(47389),te=n(87784),ne=n(25820),re=n(75750),ae=n(12906),oe=n(85175),se=n(43471),ce=(0,P.kc)((function(e){var t=e.token;return{reference:{background:"".concat(t.colorBgLayout,"80"),minWidth:"400px",maxWidth:"1080px",width:"60%"},layout:{width:"100%",minWidth:"800px",borderRadius:t.borderRadius,display:"flex",background:t.colorBgContainer,fontFamily:"AlibabaPuHuiTi, ".concat(t.fontFamily,", sans-serif"),".ant-prompts":{color:t.colorText},border:"3px solid",borderImage:"linear-gradient(45deg, ".concat(t.colorPrimary,", ").concat(t.colorSplit,") 1")},menu:{height:"100%",display:"flex",flexDirection:"column"},conversations:{padding:"0 12px",flex:1,overflowY:"auto"},chat:{height:"100%",width:"40%",maxWidth:"1080px",margin:"0 auto",boxSizing:"border-box",display:"flex",flexDirection:"column",padding:t.paddingLG,gap:"16px"},messages:{flex:1},placeholder:{paddingTop:"32px"},sender:{boxShadow:t.boxShadow},logo:{display:"flex",height:"72px",alignItems:"center",justifyContent:"start",padding:"0 24px",boxSizing:"border-box",img:{width:"24px",height:"24px",display:"inline-block"},span:{display:"inline-block",margin:"0 8px",fontWeight:"bold",color:t.colorText,fontSize:"16px"}},addBtn:{background:"#1677ff0f",border:"1px solid #1677ff34",width:"calc(100% - 24px)",margin:"0 12px 24px 12px"}}})),ie=n(93933),le=n(13973);function ue(e){return e+"-"+Date.now()}var de,fe,pe=[{key:"1",label:(de=(0,D.jsx)(X.Z,{style:{color:"#FF4D4F"}}),fe="IT incident reporting",(0,D.jsxs)(H.Z,{align:"start",children:[de,(0,D.jsx)("span",{children:fe})]})),description:"IT incident reporting",children:[{key:"1-1",description:"My laptop cannot connect to the Wi-Fi network."},{key:"1-2",description:'My printer is not working and shows a "paper jam" error.'},{key:"1-3",description:"My computer shows a blue screen error on startup."}]}],he=[{key:"historyConversation",description:"历史对话",icon:(0,D.jsx)(U.Z,{style:{color:"#FF4D4F"}})},{key:"newConversation",description:"新建对话",icon:(0,D.jsx)($.Z,{style:{color:"#1890FF"}})},{key:"clearConversation",description:"清空对话",icon:(0,D.jsx)(Q.Z,{style:{color:"#1890FF"}})}],xe=(0,K.bG)(),me=(0,V.kH)(),ge=(0,G.Z)({html:!0,breaks:!0}),ve=function(e){return(0,D.jsx)(Y.Z,{style:{marginBottom:0},children:(0,D.jsx)("div",{dangerouslySetInnerHTML:{__html:ge.render(e)}})})},be="ITIncidentReportReport",ye=function(){var e,t=ce().styles,n=(0,g.useState)(window.innerHeight),r=h()(n,1)[0],o=g.useRef(),c=g.useState(!1),l=h()(c,2),d=l[0],p=l[1],x=g.useState(""),m=h()(x,2),b=m[0],Z=m[1],j=g.useState([]),w=h()(j,2),_=w[0],C=w[1],P=g.useState(),I=h()(P,2),z=I[0],Y=I[1],G=(0,g.useState)(!1),V=h()(G,2),X=V[0],U=V[1],de=(0,g.useState)(!1),fe=h()(de,2),ge=fe[0],ye=fe[1],ke=(0,g.useState)(!1),Ze=h()(ke,2),je=Ze[0],we=Ze[1],Se=(0,g.useState)(""),_e=h()(Se,2),Ce=_e[0],Oe=_e[1],Pe=(0,g.useState)(""),Ie=h()(Pe,2),De=Ie[0],ze=Ie[1],Re=(0,g.useRef)(null),Me=(0,g.useState)([]),Te=h()(Me,2),Ne=Te[0],Ee=Te[1],Le=(0,g.useState)(!1),Ae=h()(Le,2),Fe=Ae[0],He=Ae[1],Ye=(0,g.useState)(""),Be=h()(Ye,2),Je=Be[0],We=Be[1],qe=function(e){We(e),He(!0)},Ge=function(e){var t=Ne.find((function(t){return t.message_id===e}));t&&navigator.clipboard.writeText(t.content).then((function(){y.ZP.success("复制成功")})).catch((function(){y.ZP.error("复制失败")}))},Ke=(0,M.Z)({request:(e=f()(s()().mark((function e(t,n){var r,a,c,l,d,f,p,h,x,m,g,v,b,k,Z,j,w,S,_,C,P,I,D,z,R,M,T,N,E,L,A,F,H,Y,B,J,W;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.messages,a=t.message,c=n.onSuccess,l=n.onUpdate,d=n.onError,e.prev=2,!ge){e.next=6;break}return y.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 6:if(ye(!0),p=(0,K.bW)(),h=a?a.id:ue(o.current),a||c({content:"出现了异常:",role:"assistant",id:h,references:[],collected:!1}),x={conversation_id:o.current||"",message_id:h,meta_data:{},extra:{},role:a?a.role:"user",content:a?a.content:"",app_info:be,user_id:null==xe?void 0:xe.id,user_name:null==xe?void 0:xe.name,references:[],token_count:null,price:null,collected:!1,created_at:O()().format("YYYY-MM-DD HH:mm:ss")},Ee((function(e){var t=[].concat(u()(e),[x]);return console.log("更新后的消息列表:",t),t})),o.current){e.next=15;break}throw y.ZP.error("No active conversation selected"),new Error("No active conversation selected");case 15:return console.log("activeKey===>",o.current),m={conversation_id:o.current,app_info:be,user_id:null==xe?void 0:xe.id,user_name:null==xe?void 0:xe.name,extra:{},messages:r},g={id:ue(o.current),role:"user",content:"",references:[],collected:!1},v=!1,b="",k=[],l(g),e.next=24,fetch("/api/app/chat/system_app_completions",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(p)},body:JSON.stringify(m)});case 24:if((Z=e.sent).ok){e.next=27;break}throw new Error("HTTP 错误！状态码：".concat(Z.status));case 27:if(j=null===(f=Z.body)||void 0===f?void 0:f.getReader()){e.next=30;break}throw new Error("当前浏览器不支持 ReadableStream。");case 30:w=new TextDecoder("utf-8"),S={conversation_id:o.current||"",message_id:g.id,meta_data:{},extra:{},role:"assistant",content:"",app_info:be,user_id:null==xe?void 0:xe.id,user_name:null==xe?void 0:xe.name,references:[],token_count:null,price:null,collected:!1,created_at:O()().format("YYYY-MM-DD HH:mm:ss")};case 32:if(v){e.next=100;break}return e.next=35,j.read();case 35:_=e.sent,C=_.value,_.done&&(v=!0),b+=w.decode(C,{stream:!0}),P=b.split("\n\n"),b=P.pop()||"",I=i()(P),e.prev=43,I.s();case 45:if((D=I.n()).done){e.next=90;break}if(""!==(z=D.value).trim()){e.next=49;break}return e.abrupt("continue",88);case 49:R=z.split("\n"),M=null,T=null,N=i()(R);try{for(N.s();!(E=N.n()).done;)(L=E.value).startsWith("event: ")?M=L.substring(7).trim():L.startsWith("data: ")&&(T=L.substring(6))}catch(e){N.e(e)}finally{N.f()}if(!T){e.next=88;break}e.t0=M,e.next="answer"===e.t0?58:"moduleStatus"===e.t0?70:"appStreamResponse"===e.t0?72:"flowResponses"===e.t0?74:"end"===e.t0?76:"error"===e.t0?78:88;break;case 58:if("[DONE]"===T){e.next=69;break}e.prev=59,F=JSON.parse(T),(H=(null===(A=F.choices[0])||void 0===A||null===(A=A.delta)||void 0===A?void 0:A.content)||"")&&(g.content+=H,l(g)),e.next=69;break;case 65:return e.prev=65,e.t1=e.catch(59),console.error("Error parsing answer data:",e.t1),e.abrupt("return",c({content:"出现了异常:"+T,role:"assistant",id:ue(o.current),references:[],collected:!1}));case 69:return e.abrupt("break",88);case 70:try{Y=JSON.parse(T),console.log("模块状态：",Y)}catch(e){console.error("Error parsing moduleStatus data:",e)}return e.abrupt("break",88);case 72:try{B=JSON.parse(T),console.log("appStreamData===>",B),k=B,g.references=k}catch(e){console.error("Error parsing appStreamResponse data:",e)}return e.abrupt("break",88);case 74:try{console.log("flowResponsesData",T)}catch(e){console.error("Error parsing flowResponses data:",e)}return e.abrupt("break",88);case 76:return v=!0,e.abrupt("break",88);case 78:e.prev=78,J=JSON.parse(T),l(J),e.next=87;break;case 83:throw e.prev=83,e.t2=e.catch(78),console.error("Error event received:",e.t2),e.t2;case 87:return e.abrupt("break",88);case 88:e.next=45;break;case 90:e.next=95;break;case 92:e.prev=92,e.t3=e.catch(43),I.e(e.t3);case 95:return e.prev=95,I.f(),e.finish(95);case 98:e.next=32;break;case 100:if(c(g),!g.content||""===g.content.trim()){e.next=108;break}return S.content=g.content,S.references=k,e.next=106,(0,ie.tn)(S);case 106:(W=e.sent).success?(S.message_id=W.data.message_id,console.log("创建消息成功，返回数据:",W.data),Ee((function(e){var t=[].concat(u()(e),[W.data]);return console.log("更新后的消息列表:",t),t}))):y.ZP.error("消息上报失败");case 108:e.next=115;break;case 110:e.prev=110,e.t4=e.catch(2),console.log("error===>",e.t4),c({content:"出现了，系统正在处理其他对话。请稍后重试",role:"assistant",id:ue(o.current),references:[],collected:!1}),d(e.t4 instanceof Error?e.t4:new Error("Unknown error"));case 115:return e.prev=115,ye(!1),e.finish(115);case 118:case"end":return e.stop()}}),e,null,[[2,110,115,118],[43,92,95,98],[59,65],[78,83]])}))),function(t,n){return e.apply(this,arguments)})}),Ve=h()(Ke,1)[0],Xe=(0,T.Z)({agent:Ve}),Ue=Xe.onRequest,$e=Xe.messages,Qe=Xe.setMessages,et=function(e){Y(e),console.log("activeKey 设置",e),o.current=e},tt=function(e){var t=e.filter((function(e){return e.pinned})).sort((function(e,t){return new Date(t.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return a()(a()({},e),{},{key:e.id||"",label:e.conversation_name||e.id,group:"置顶"})})),n=e.filter((function(e){return!e.pinned})).sort((function(e,t){return new Date(t.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return a()(a()({},e),{},{key:e.id||"",label:e.conversation_name||"",group:"对话"})}));C([].concat(u()(t),u()(n)))},nt=function(e){return 0===e.length?null:e.reduce((function(e,t){return new Date(t.active_at)>new Date(e.active_at)?t:e}))},rt=function(){var e=f()(s()().mark((function e(t){var n,r,a;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,ye(!0),console.info("获取对话信息",t),n=O()().format("YYYY-MM-DD HH:mm:ss"),e.next=6,(0,ie.$o)(t,{conversation_name:null,active_at:n,pinned_at:null,pinned:null});case 6:null!=(r=e.sent)&&r.messages?(console.info("设置对话信息",r.messages),a=r.messages.map((function(e){return{id:e.id||e.message_id,message:{id:e.id||e.message_id,content:e.content,role:e.role,references:e.references||[],collected:e.collected||!1},status:"assistant"===e.role?"success":"local",meta:e.meta||{avatar:"assistant"===e.role?(null==me?void 0:me.logo)||"/static/logo.png":(null==xe?void 0:xe.avatar)||"/avatar/default.jpeg"}}})),Ee(r.messages),Qe(a),et(t)):y.ZP.error("获取对话信息失败"),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),console.error("切换对话时出错：",e.t0);case 13:return e.prev=13,ye(!1),Y(t),e.finish(13);case 17:case"end":return e.stop()}}),e,null,[[0,10,13,17]])})));return function(t){return e.apply(this,arguments)}}(),at=function(){var e=f()(s()().mark((function e(){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o.current){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,(0,ie.Db)(o.current);case 4:e.sent.success?(Ee([]),Qe([])):y.ZP.error("清空对话失败");case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ot=function(){var e=f()(s()().mark((function e(){var t,n,r,a;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!ge){e.next=3;break}return y.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(!(t=(0,K.bG)())){e.next=19;break}return e.prev=5,n=(new Date).toLocaleString(),r="对话-".concat(n),e.next=10,(0,ie.Xw)({user_id:t.id,user_name:t.name,conversation_name:r,app_info:be});case 10:a=e.sent,tt([].concat(u()(_),[{key:a.id||"",id:a.id||"",label:a.conversation_name||"",conversation_name:a.conversation_name||"",active_at:a.active_at||"",pinned_at:a.pinned_at,pinned:a.pinned||!1,messages:[]}])),et(a.id||""),at(),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(5),console.error("创建新对话时出错：",e.t0);case 19:case"end":return e.stop()}}),e,null,[[5,16]])})));return function(){return e.apply(this,arguments)}}(),st=function(){var e=f()(s()().mark((function e(t){var n,r,o,c,i;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=_.find((function(e){return e.key===t}))){e.next=3;break}return e.abrupt("return");case 3:return r=n.pinned,o=!r,e.prev=6,c=O()().format("YYYY-MM-DD HH:mm:ss"),e.next=10,(0,ie.X1)(t,{conversation_name:null,active_at:null,pinned:o,pinned_at:c});case 10:i=_.map((function(e){return e.key===t?a()(a()({},e),{},{pinned:o}):e})),tt(i),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(6),console.error("更新置顶状态时出错：",e.t0);case 17:case"end":return e.stop()}}),e,null,[[6,14]])})));return function(t){return e.apply(this,arguments)}}(),ct=function(){var e=f()(s()().mark((function e(t){var n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,ie.SJ)(t);case 3:n=_.filter((function(e){return e.key!==t})),tt(n),o.current===t&&n.length>0&&rt(n[0].key||""),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除对话时出错：",e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(t){return e.apply(this,arguments)}}(),it=function(){var e=f()(s()().mark((function e(t,n){var r,o;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,_.find((function(e){return e.key===t}))){e.next=4;break}return e.abrupt("return");case 4:return r={conversation_name:n,active_at:null,pinned_at:null,pinned:null},e.next=7,(0,ie.X1)(t,r);case 7:null!=(o=e.sent)&&o.success?C((function(e){return e.map((function(e){return e.key===t?a()(a()({},e),{},{label:n}):e}))})):y.ZP.error("更新对话标题失败"),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("更新对话标题时出错：",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t,n){return e.apply(this,arguments)}}(),lt=function(){var e=f()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!ge){e.next=3;break}return y.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:return e.next=5,rt(t);case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();(0,g.useEffect)((function(){var e=function(){var e=f()(s()().mark((function e(){var t,n,r;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t=(0,K.bG)())){e.next=20;break}return e.prev=2,e.next=5,(0,ie.Mw)({user_id:t.id,app_info:be});case 5:if(!(n=e.sent).success||!Array.isArray(n.data)){e.next=15;break}if(0!==n.data.length){e.next=12;break}return e.next=10,ot();case 10:e.next=15;break;case 12:r=nt(n.data),tt(n.data),rt(r?r.id:n.data[0].id||"");case 15:e.next=20;break;case 17:e.prev=17,e.t0=e.catch(2),console.error("初始化对话时出错：",e.t0);case 20:case"end":return e.stop()}}),e,null,[[2,17]])})));return function(){return e.apply(this,arguments)}}();e()}),[be]);var ut=function(){var e=f()(s()().mark((function e(t){var n,r,a,o;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("重新生成消息:",t),e.prev=1,console.info(Ne),n=Ne.findIndex((function(e){return e.message_id===t})),console.log("currentIndex===>",n),-1!==n){e.next=8;break}return y.ZP.error("未找到指定消息"),e.abrupt("return");case 8:return r=Ne[n],a=Ne.slice(n),console.log("将要删除的消息:",a),e.next=13,(0,ie.qP)(a.map((function(e){return e.message_id})));case 13:e.sent.success||y.ZP.error("删除消息失败"),Ee((function(e){return e.slice(0,n)})),Qe((function(e){return e.slice(0,n)})),"assistant"===r.role?(o=Ne.slice(0,n).reverse().find((function(e){return"user"===e.role})))&&Ue({id:t,role:"user",content:o.content,references:[],collected:!1}):Ue({id:t,role:"user",content:r.content,references:[],collected:!1}),y.ZP.success("正在重新生成回复..."),e.next=25;break;case 21:e.prev=21,e.t0=e.catch(1),console.error("重新生成消息时出错：",e.t0),y.ZP.error("重新生成消息失败");case 25:case"end":return e.stop()}}),e,null,[[1,21]])})));return function(t){return e.apply(this,arguments)}}(),dt=function(){var e=f()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:B.Z.confirm({title:"确认删除",content:"确定要删除这条消息吗？删除后不可恢复。",okText:"确认",cancelText:"取消",onOk:function(){return f()(s()().mark((function e(){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("delete messageId===>",t),e.next=4,(0,ie.$Z)(t);case 4:e.sent.success?(Ee((function(e){return e.filter((function(e){return e.message_id!==t}))})),console.log("delete currentConversationMessages===>",Ne),Qe((function(e){return e.filter((function(e){return e.message.id!==t}))})),console.log("delete messages===>",$e),y.ZP.success("消息及相关引用已删除")):y.ZP.error("删除消息失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除消息时出错：",e.t0),y.ZP.error("删除消息失败");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})))()}});case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ft=function(){var e=f()(s()().mark((function e(t,n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("收藏状态切换:",t,n),e.next=3,(0,ie.bk)({message_id:t,collected:!n});case 3:e.sent.success?(y.ZP.success(n?"取消收藏成功":"收藏成功"),Qe((function(e){return e.map((function(e){return e.id===t?a()(a()({},e),{},{message:a()(a()({},e.message),{},{collected:!n})}):e}))})),Ee((function(e){return e.map((function(e){return e.message_id===t?a()(a()({},e),{},{collected:!n}):e}))}))):y.ZP.error(n?"取消收藏失败":"收藏失败");case 5:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),pt=function(){var e=f()(s()().mark((function e(t){var n,r,a;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!ge){e.next=3;break}return y.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(n=t.data,r=n.key,a=n.description,"historyConversation"!==r){e.next=8;break}U(!0),e.next=19;break;case 8:if("newConversation"!==r){e.next=13;break}return e.next=11,ot();case 11:e.next=19;break;case 13:if("clearConversation"!==r){e.next=18;break}return e.next=16,at();case 16:e.next=19;break;case 18:Ue({id:ue(o.current),role:"user",content:a,references:[],collected:!1});case 19:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ht=(0,D.jsxs)(H.Z,{direction:"vertical",size:16,className:t.placeholder,children:[(0,D.jsx)(N.Z,{variant:"borderless",icon:(0,D.jsx)("img",{src:(null==me?void 0:me.logo)||"/static/logo.png",alt:"logo"}),title:"您好，我是智能客服助手",description:"我可以为您解答产品咨询、业务办理等相关问题，请问有什么可以帮您？"}),(0,D.jsx)(E.Z,{title:"以下是常见问题，您可以直接点击进行咨询",items:pe,styles:{list:{width:"100%"},item:{backgroundImage:"linear-gradient(137deg, #f0f7ff 0%, #fff1f0 100%)",border:0,flex:1}},onItemClick:pt})]}),xt=$e.length>0?$e.map((function(e){var t=e.id,n=e.message,r=e.status;return{key:o.current+"_"+t,loadingRender:function(){return(0,D.jsxs)(H.Z,{children:[(0,D.jsx)(J.Z,{size:"small"}),"模型思考中..."]})},loading:"loading"===r&&n.content.length<1,content:n.content,shape:"local"===r?"corner":"round",variant:"local"===r?"filled":"borderless",rols:n.role,messageRender:ve,avatar:"local"===r?{src:(null==xe?void 0:xe.avatar)||"/avatar/default.jpeg"}:{src:(null==me?void 0:me.logo)||"/static/logo.png"},placement:"local"!==r?"start":"end",footer:"local"!==r?(0,D.jsxs)(W.Z,{children:[(0,D.jsx)(S.ZP,{size:"small",type:"text",icon:n.collected?(0,D.jsx)(ne.Z,{style:{color:"#FFD700"}}):(0,D.jsx)(re.Z,{style:{color:"#ccc"}}),onClick:function(){return ft(n.id,n.collected)}}),(0,D.jsx)(S.ZP,{size:"small",type:"text",icon:(0,D.jsx)(ae.Z,{style:{color:"#ccc"}}),onClick:function(){return qe(n.id)}}),(0,D.jsx)(S.ZP,{size:"small",type:"text",icon:(0,D.jsx)(oe.Z,{style:{color:"#ccc"}}),onClick:function(){return Ge(n.id)}})]}):(0,D.jsxs)(W.Z,{children:[(0,D.jsx)(S.ZP,{size:"small",type:"text",icon:(0,D.jsx)(se.Z,{style:{color:"#ccc"}}),onClick:function(){return ut(n.id)}}),(0,D.jsx)(S.ZP,{size:"small",type:"text",icon:(0,D.jsx)(Q.Z,{style:{color:"#ccc"}}),onClick:function(){return dt(n.id)}}),(0,D.jsx)(S.ZP,{size:"small",type:"text",icon:n.collected?(0,D.jsx)(ne.Z,{style:{color:"#FFD700"}}):(0,D.jsx)(re.Z,{style:{color:"#ccc"}}),onClick:function(){return ft(n.id,n.collected)}}),(0,D.jsx)(S.ZP,{size:"small",type:"text",icon:(0,D.jsx)(ae.Z,{style:{color:"#ccc"}}),onClick:function(){return qe(n.id)}}),(0,D.jsx)(S.ZP,{size:"small",type:"text",icon:(0,D.jsx)(oe.Z,{style:{color:"#ccc"}}),onClick:function(){return Ge(n.id)}})]})}})):[{content:ht,variant:"borderless"}],mt=(0,D.jsx)(L.Z.Header,{title:"Attachments",open:d,onOpenChange:p,styles:{content:{padding:0}}}),gt=(0,D.jsxs)("div",{className:t.logo,children:[(0,D.jsx)("span",{children:"对话记录"}),(0,D.jsx)(k.Z,{title:"新对话",children:(0,D.jsx)(S.ZP,{type:"text",icon:(0,D.jsx)($.Z,{}),onClick:ot,style:{fontSize:"16px"}})})]}),vt=(0,D.jsx)(B.Z,{title:"修改对话标题",open:je,onOk:function(){De&&Ce.trim()&&(it(De,Ce.trim()),we(!1))},onCancel:function(){we(!1),Oe(""),ze("")},children:(0,D.jsx)(v.Z,{value:Ce,onChange:function(e){return Oe(e.target.value)},placeholder:"请输入新的对话标题"})}),bt=(0,D.jsx)(q.Z,{title:"历史对话",placement:"right",width:400,onClose:function(){return U(!1)},open:X,children:(0,D.jsxs)("div",{className:t.menu,children:[gt,(0,D.jsx)(A.Z,{items:_,activeKey:z,onActiveChange:lt,menu:function(e){return{items:[{label:"重命名",key:"edit",icon:(0,D.jsx)(ee.Z,{})},{label:"置顶",key:"pin",icon:(0,D.jsx)(te.Z,{})},{label:"删除",key:"delete",icon:(0,D.jsx)(Q.Z,{}),danger:!0}],onClick:function(t){switch(console.log("menuInfo","Click ".concat(e.key," - ").concat(t.key)),t.key){case"edit":ze(e.key),Oe(e.label),we(!0);break;case"pin":st(e.key);break;case"delete":if(ge)return void y.ZP.error("系统正在处理其他对话。请稍😊");ct(e.key)}}}},groupable:!0})]})});return(0,g.useEffect)((function(){console.log("currentConversationMessages 更新了:",Ne)}),[Ne]),(0,D.jsxs)("div",{className:t.layout,style:{height:r-56},children:[(0,D.jsx)("div",{className:t.reference,children:(0,D.jsx)(R,{ref:Re,messages:Ne})}),(0,D.jsxs)("div",{className:t.chat,children:[(0,D.jsx)(F.Z.List,{items:xt,className:t.messages}),(0,D.jsx)(E.Z,{items:he,onItemClick:pt}),(0,D.jsx)(L.Z,{value:b,header:mt,onSubmit:function(e){console.log("nextContent===>",e),e&&(Ue({id:ue(o.current),role:"user",content:e,references:[],collected:!1}),Z(""))},onChange:Z,loading:Ve.isRequesting(),className:t.sender})]}),vt,bt,(0,D.jsx)(le.Z,{visible:Fe,messageId:Je,conversationId:z,appInfo:be,onClose:function(){return He(!1)}})]})}}}]);