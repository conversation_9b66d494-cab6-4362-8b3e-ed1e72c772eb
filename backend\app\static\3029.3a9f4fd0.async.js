"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3029],{47046:function(e,o){o.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"}},82947:function(e,o){o.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"}},8751:function(e,o,t){t.d(o,{Z:function(){return i}});var r=t(1413),n=t(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"},a=t(91146),l=function(e,o){return n.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:o,icon:c}))};var i=n.forwardRef(l)},88284:function(e,o,t){var r=t(1413),n=t(67294),c=t(32857),a=t(91146),l=function(e,o){return n.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:o,icon:c.Z}))},i=n.forwardRef(l);o.Z=i},18429:function(e,o,t){t.d(o,{Z:function(){return i}});var r=t(1413),n=t(67294),c={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"}}]},name:"close-circle",theme:"outlined"},a=t(91146),l=function(e,o){return n.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:o,icon:c}))};var i=n.forwardRef(l)},28508:function(e,o,t){var r=t(1413),n=t(67294),c=t(89503),a=t(91146),l=function(e,o){return n.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:o,icon:c.Z}))},i=n.forwardRef(l);o.Z=i},82061:function(e,o,t){var r=t(1413),n=t(67294),c=t(47046),a=t(91146),l=function(e,o){return n.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:o,icon:c.Z}))},i=n.forwardRef(l);o.Z=i},31545:function(e,o,t){t.d(o,{Z:function(){return i}});var r=t(1413),n=t(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M688 312v-48c0-4.4-3.6-8-8-8H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8zm-392 88c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H296zm144 452H208V148h560v344c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V108c0-17.7-14.3-32-32-32H168c-17.7 0-32 14.3-32 32v784c0 17.7 14.3 32 32 32h272c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm445.7 51.5l-93.3-93.3C814.7 780.7 828 743.9 828 704c0-97.2-78.8-176-176-176s-176 78.8-176 176 78.8 176 176 176c35.8 0 69-10.7 96.8-29l94.7 94.7c1.6 1.6 3.6 2.3 5.6 2.3s4.1-.8 5.6-2.3l31-31a7.9 7.9 0 000-11.2zM652 816c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"}}]},name:"file-search",theme:"outlined"},a=t(91146),l=function(e,o){return n.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:o,icon:c}))};var i=n.forwardRef(l)},13923:function(e,o,t){t.d(o,{Z:function(){return i}});var r=t(1413),n=t(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M536.1 273H488c-4.4 0-8 3.6-8 8v275.3c0 2.6 1.2 5 3.3 6.5l165.3 120.7c3.6 2.6 8.6 1.9 11.2-1.7l28.6-39c2.7-3.7 1.9-8.7-1.7-11.2L544.1 528.5V281c0-4.4-3.6-8-8-8zm219.8 75.2l156.8 38.3c5 1.2 9.9-2.6 9.9-7.7l.8-161.5c0-6.7-7.7-10.5-12.9-6.3L752.9 334.1a8 8 0 003 14.1zm167.7 301.1l-56.7-19.5a8 8 0 00-10.1 4.8c-1.9 5.1-3.9 10.1-6 15.1-17.8 42.1-43.3 80-75.9 112.5a353 353 0 01-112.5 75.9 352.18 352.18 0 01-137.7 27.8c-47.8 0-94.1-9.3-137.7-27.8a353 353 0 01-112.5-75.9c-32.5-32.5-58-70.4-75.9-112.5A353.44 353.44 0 01171 512c0-47.8 9.3-94.2 27.8-137.8 17.8-42.1 43.3-80 75.9-112.5a353 353 0 01112.5-75.9C430.6 167.3 477 158 524.8 158s94.1 9.3 137.7 27.8A353 353 0 01775 261.7c10.2 10.3 19.8 21 28.6 32.3l59.8-46.8C784.7 146.6 662.2 81.9 524.6 82 285 82.1 92.6 276.7 95 516.4 97.4 751.9 288.9 942 524.8 942c185.5 0 343.5-117.6 403.7-282.3 1.5-4.2-.7-8.9-4.9-10.4z"}}]},name:"history",theme:"outlined"},a=t(91146),l=function(e,o){return n.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:o,icon:c}))};var i=n.forwardRef(l)},79090:function(e,o,t){var r=t(1413),n=t(67294),c=t(15294),a=t(91146),l=function(e,o){return n.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:o,icon:c.Z}))},i=n.forwardRef(l);o.Z=i},37446:function(e,o,t){t.d(o,{Z:function(){return i}});var r=t(1413),n=t(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"},a=t(91146),l=function(e,o){return n.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:o,icon:c}))};var i=n.forwardRef(l)},51042:function(e,o,t){var r=t(1413),n=t(67294),c=t(42110),a=t(91146),l=function(e,o){return n.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:o,icon:c.Z}))},i=n.forwardRef(l);o.Z=i},48489:function(e,o,t){t.d(o,{Z:function(){return i}});var r=t(1413),n=t(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M764 280.9c-14-30.6-33.9-58.1-59.3-81.6C653.1 151.4 584.6 125 512 125s-141.1 26.4-192.7 74.2c-25.4 23.6-45.3 51-59.3 81.7-14.6 32-22 65.9-22 100.9v27c0 6.2 5 11.2 11.2 11.2h54c6.2 0 11.2-5 11.2-11.2v-27c0-99.5 88.6-180.4 197.6-180.4s197.6 80.9 197.6 180.4c0 40.8-14.5 79.2-42 111.2-27.2 31.7-65.6 54.4-108.1 64-24.3 5.5-46.2 19.2-61.7 38.8a110.85 110.85 0 00-23.9 68.6v31.4c0 6.2 5 11.2 11.2 11.2h54c6.2 0 11.2-5 11.2-11.2v-31.4c0-15.7 10.9-29.5 26-32.9 58.4-13.2 111.4-44.7 149.3-88.7 19.1-22.3 34-47.1 44.3-74 10.7-27.9 16.1-57.2 16.1-87 0-35-7.4-69-22-100.9zM512 787c-30.9 0-56 25.1-56 56s25.1 56 56 56 56-25.1 56-56-25.1-56-56-56z"}}]},name:"question",theme:"outlined"},a=t(91146),l=function(e,o){return n.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:o,icon:c}))};var i=n.forwardRef(l)},43471:function(e,o,t){var r=t(1413),n=t(67294),c=t(82947),a=t(91146),l=function(e,o){return n.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:o,icon:c.Z}))},i=n.forwardRef(l);o.Z=i},98165:function(e,o,t){t.d(o,{Z:function(){return i}});var r=t(1413),n=t(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"},a=t(91146),l=function(e,o){return n.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:o,icon:c}))};var i=n.forwardRef(l)},63960:function(e,o,t){t.d(o,{Z:function(){return C}});var r=t(98423),n=t(8745),c=t(34041),a=t(67294),l=t(93967),i=t.n(l),s=t(50344),u=t(87263),d=t(53124);const{Option:f}=c.default;function p(e){return(null==e?void 0:e.type)&&(e.type.isSelectOption||e.type.isSelectOptGroup)}const g=(e,o)=>{var t,n;const{prefixCls:l,className:g,popupClassName:v,dropdownClassName:m,children:h,dataSource:b,dropdownStyle:C,dropdownRender:Z,popupRender:y,onDropdownVisibleChange:w,onOpenChange:k,styles:O,classNames:x}=e,E=(0,s.Z)(h),S=(null===(t=null==O?void 0:O.popup)||void 0===t?void 0:t.root)||C,z=(null===(n=null==x?void 0:x.popup)||void 0===n?void 0:n.root)||v||m,$=y||Z,B=k||w;let N;1===E.length&&a.isValidElement(E[0])&&!p(E[0])&&([N]=E);const R=N?()=>N:void 0;let j;j=E.length&&p(E[0])?h:b?b.map((e=>{if(a.isValidElement(e))return e;switch(typeof e){case"string":return a.createElement(f,{key:e,value:e},e);case"object":{const{value:o}=e;return a.createElement(f,{key:o,value:o},e.text)}default:return}})):[];const{getPrefixCls:I}=a.useContext(d.E_),M=I("select",l),[H]=(0,u.Cn)("SelectLike",null==S?void 0:S.zIndex);return a.createElement(c.default,Object.assign({ref:o,suffixIcon:null},(0,r.Z)(e,["dataSource","dropdownClassName","popupClassName"]),{prefixCls:M,classNames:{popup:{root:z},root:null==x?void 0:x.root},styles:{popup:{root:Object.assign(Object.assign({},S),{zIndex:H})},root:null==O?void 0:O.root},className:i()(`${M}-auto-complete`,g),mode:c.default.SECRET_COMBOBOX_MODE_DO_NOT_USE,popupRender:$,onOpenChange:B,getInputElement:R}),j)};var v=a.forwardRef(g);const{Option:m}=c.default,h=(0,n.Z)(v,"dropdownAlign",(e=>(0,r.Z)(e,["visible"]))),b=v;b.Option=m,b._InternalPanelDoNotUseOrYouWillBeFired=h;var C=b},66309:function(e,o,t){t.d(o,{Z:function(){return B}});var r=t(67294),n=t(93967),c=t.n(n),a=t(98423),l=t(98787),i=t(69760),s=t(96159),u=t(45353),d=t(53124),f=t(11568),p=t(15063),g=t(14747),v=t(83262),m=t(83559);const h=e=>{const{lineWidth:o,fontSizeIcon:t,calc:r}=e,n=e.fontSizeSM;return(0,v.IX)(e,{tagFontSize:n,tagLineHeight:(0,f.bf)(r(e.lineHeightSM).mul(n).equal()),tagIconSize:r(t).sub(r(o).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},b=e=>({defaultBg:new p.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var C=(0,m.I$)("Tag",(e=>(e=>{const{paddingXXS:o,lineWidth:t,tagPaddingHorizontal:r,componentCls:n,calc:c}=e,a=c(r).sub(t).equal(),l=c(o).sub(t).equal();return{[n]:Object.assign(Object.assign({},(0,g.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:a,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,f.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${n}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${n}-close-icon`]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${n}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${n}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:a}}),[`${n}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(h(e))),b),Z=function(e,o){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&o.indexOf(r)<0&&(t[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(r=Object.getOwnPropertySymbols(e);n<r.length;n++)o.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(t[r[n]]=e[r[n]])}return t};const y=r.forwardRef(((e,o)=>{const{prefixCls:t,style:n,className:a,checked:l,onChange:i,onClick:s}=e,u=Z(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:f,tag:p}=r.useContext(d.E_),g=f("tag",t),[v,m,h]=C(g),b=c()(g,`${g}-checkable`,{[`${g}-checkable-checked`]:l},null==p?void 0:p.className,a,m,h);return v(r.createElement("span",Object.assign({},u,{ref:o,style:Object.assign(Object.assign({},n),null==p?void 0:p.style),className:b,onClick:e=>{null==i||i(!l),null==s||s(e)}})))}));var w=y,k=t(98719);var O=(0,m.bk)(["Tag","preset"],(e=>(e=>(0,k.Z)(e,((o,t)=>{let{textColor:r,lightBorderColor:n,lightColor:c,darkColor:a}=t;return{[`${e.componentCls}${e.componentCls}-${o}`]:{color:r,background:c,borderColor:n,"&-inverse":{color:e.colorTextLightSolid,background:a,borderColor:a},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(h(e))),b);const x=(e,o,t)=>{const r="string"!=typeof(n=t)?n:n.charAt(0).toUpperCase()+n.slice(1);var n;return{[`${e.componentCls}${e.componentCls}-${o}`]:{color:e[`color${t}`],background:e[`color${r}Bg`],borderColor:e[`color${r}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var E=(0,m.bk)(["Tag","status"],(e=>{const o=h(e);return[x(o,"success","Success"),x(o,"processing","Info"),x(o,"error","Error"),x(o,"warning","Warning")]}),b),S=function(e,o){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&o.indexOf(r)<0&&(t[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(r=Object.getOwnPropertySymbols(e);n<r.length;n++)o.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(t[r[n]]=e[r[n]])}return t};const z=r.forwardRef(((e,o)=>{const{prefixCls:t,className:n,rootClassName:f,style:p,children:g,icon:v,color:m,onClose:h,bordered:b=!0,visible:Z}=e,y=S(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:w,direction:k,tag:x}=r.useContext(d.E_),[z,$]=r.useState(!0),B=(0,a.Z)(y,["closeIcon","closable"]);r.useEffect((()=>{void 0!==Z&&$(Z)}),[Z]);const N=(0,l.o2)(m),R=(0,l.yT)(m),j=N||R,I=Object.assign(Object.assign({backgroundColor:m&&!j?m:void 0},null==x?void 0:x.style),p),M=w("tag",t),[H,P,T]=C(M),L=c()(M,null==x?void 0:x.className,{[`${M}-${m}`]:j,[`${M}-has-color`]:m&&!j,[`${M}-hidden`]:!z,[`${M}-rtl`]:"rtl"===k,[`${M}-borderless`]:!b},n,f,P,T),_=e=>{e.stopPropagation(),null==h||h(e),e.defaultPrevented||$(!1)},[,A]=(0,i.Z)((0,i.w)(e),(0,i.w)(x),{closable:!1,closeIconRender:e=>{const o=r.createElement("span",{className:`${M}-close-icon`,onClick:_},e);return(0,s.wm)(e,o,(e=>({onClick:o=>{var t;null===(t=null==e?void 0:e.onClick)||void 0===t||t.call(e,o),_(o)},className:c()(null==e?void 0:e.className,`${M}-close-icon`)})))}}),D="function"==typeof y.onClick||g&&"a"===g.type,F=v||null,V=F?r.createElement(r.Fragment,null,F,g&&r.createElement("span",null,g)):g,W=r.createElement("span",Object.assign({},B,{ref:o,className:L,style:I}),V,A,N&&r.createElement(O,{key:"preset",prefixCls:M}),R&&r.createElement(E,{key:"status",prefixCls:M}));return H(D?r.createElement(u.Z,{component:"Tag"},W):W)})),$=z;$.CheckableTag=w;var B=$}}]);