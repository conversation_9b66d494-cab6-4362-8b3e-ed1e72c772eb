"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8507],{15932:function(e,r,t){t.r(r);var n=t(97857),a=t.n(n),u=t(15009),c=t.n(u),s=t(99289),i=t.n(s),o=t(5574),l=t.n(o),p=t(67294),d=t(97131),f=t(12453),v=t(47019),x=t(2453),h=t(17788),m=t(83622),Z=t(55102),k=t(78158),b=t(85893);r.default=function(){var e=(0,p.useState)(!1),r=l()(e,2),t=r[0],n=r[1],u=(0,p.useState)(void 0),s=l()(u,2),o=s[0],w=s[1],y=(0,p.useRef)(),T=v.Z.useForm(),P=l()(T,1)[0],_=function(){var e=i()(c()().mark((function e(r){var t,a;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=x.ZP.loading("正在添加"),e.prev=1,e.next=4,(0,k.N)("/api/conversations",{method:"POST",data:r});case 4:return t(),x.ZP.success("添加成功"),n(!1),null===(a=y.current)||void 0===a||a.reload(),e.abrupt("return",!0);case 11:return e.prev=11,e.t0=e.catch(1),t(),x.ZP.error("添加失败，请重试"),e.abrupt("return",!1);case 16:case"end":return e.stop()}}),e,null,[[1,11]])})));return function(r){return e.apply(this,arguments)}}(),j=function(){var e=i()(c()().mark((function e(r){var t,n;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=x.ZP.loading("正在更新"),e.prev=1,e.next=4,(0,k.N)("/api/conversations/".concat(r.id),{method:"PUT",data:r});case 4:return t(),x.ZP.success("更新成功"),w(void 0),null===(n=y.current)||void 0===n||n.reload(),e.abrupt("return",!0);case 11:return e.prev=11,e.t0=e.catch(1),t(),x.ZP.error("更新失败，请重试"),e.abrupt("return",!1);case 16:case"end":return e.stop()}}),e,null,[[1,11]])})));return function(r){return e.apply(this,arguments)}}(),g=function(){var e=i()(c()().mark((function e(r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:h.Z.confirm({title:"确认删除",content:"确定要删除这个对话吗？",okText:"确认",cancelText:"取消",onOk:function(){var e=i()(c()().mark((function e(){var t,n;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=x.ZP.loading("正在删除"),e.prev=1,e.next=4,(0,k.N)("/api/conversations/".concat(r.id),{method:"DELETE"});case 4:return t(),x.ZP.success("删除成功"),null===(n=y.current)||void 0===n||n.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),t(),x.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),I=[{title:"名称",dataIndex:"conversation_name",valueType:"text"},{title:"应用信息",dataIndex:"app_info",valueType:"text",search:!1},{title:"用户ID",dataIndex:"user_name",valueType:"text",search:!1},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",search:!1},{title:"操作",dataIndex:"option",valueType:"option",render:function(e,r){return[(0,b.jsx)(m.ZP,{type:"link",danger:!0,onClick:function(){return g(r)},children:"删除"})]}}];return(0,b.jsxs)(d._z,{children:[(0,b.jsx)(f.Z,{headerTitle:"对话管理",actionRef:y,rowKey:"id",search:{labelWidth:120},toolBarRender:function(){return[]},request:function(){var e=i()(c()().mark((function e(r){var t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,k.N)("/api/conversations",{method:"GET",params:a()({current:r.current,pageSize:r.pageSize},r)});case 2:return t=e.sent,e.abrupt("return",{data:t.data,success:t.success,total:t.total});case 4:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),columns:I}),(0,b.jsx)(h.Z,{title:o?"编辑对话":"新建对话",visible:t,onOk:i()(c()().mark((function e(){var r;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,P.validateFields();case 2:if(r=e.sent,!o){e.next=8;break}return e.next=6,j(a()(a()({},o),r));case 6:e.next=10;break;case 8:return e.next=10,_(r);case 10:case"end":return e.stop()}}),e)}))),onCancel:function(){n(!1),w(void 0)},children:(0,b.jsxs)(v.Z,{form:P,layout:"vertical",children:[(0,b.jsx)(v.Z.Item,{name:"name",label:"名称",rules:[{required:!0}],children:(0,b.jsx)(Z.Z,{})}),(0,b.jsx)(v.Z.Item,{name:"app_info",label:"应用信息",rules:[{required:!0}],children:(0,b.jsx)(Z.Z,{})})]})})]})}}}]);