"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2524],{44708:function(e,r,t){t.r(r),t.d(r,{default:function(){return me}});var n=t(64599),i=t.n(n),s=t(19632),c=t.n(s),a=t(15009),o=t.n(a),u=t(97857),l=t.n(u),p=t(99289),d=t.n(p),f=t(5574),h=t.n(f),x=t(67294),m=t(11941),v=t(55102),Z=t(47019),y=t(2453),j=t(17788),g=t(83062),k=t(66309),w=t(42075),b=t(83622),P=t(86738),S=t(4393),_=t(71230),T=t(15746),N=t(53531),C=t(67839),I=t(11550),z=t(34041),F=t(71471),E=t(97131),O=t(47389),A=t(82061),J=t(29158),U=t(13520),V=t(90389),L=t(51042),q=t(88484),B=t(69753),K=t(63783),D=t(78158);function G(){return M.apply(this,arguments)}function M(){return M=d()(o()().mark((function e(){var r,t=arguments;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.length>0&&void 0!==t[0]?t[0]:{},e.abrupt("return",(0,D.N)("/api/consumer-protection-rules",{method:"GET",params:l()(l()({},r),{},{current:r.current||1,pageSize:r.pageSize||10})}));case 2:case"end":return e.stop()}}),e)}))),M.apply(this,arguments)}function Q(e){return R.apply(this,arguments)}function R(){return(R=d()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/consumer-protection-rules",{method:"POST",data:{ruleName:r.ruleName,ruleType:r.ruleType,description:r.description,source:r.source,logic:r.logic,result_definition:r.result_definition}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function W(e,r){return H.apply(this,arguments)}function H(){return(H=d()(o()().mark((function e(r,t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/consumer-protection-rules/".concat(r),{method:"PUT",data:{ruleName:t.ruleName,ruleType:t.ruleType,description:t.description,source:t.source,logic:t.logic,result_definition:t.result_definition}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function X(e){return Y.apply(this,arguments)}function Y(){return(Y=d()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/consumer-protection-rules/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function $(){return ee.apply(this,arguments)}function ee(){return ee=d()(o()().mark((function e(){var r,t=arguments;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.length>0&&void 0!==t[0]?t[0]:{},e.abrupt("return",(0,D.N)("/api/consumer_protection/protection-types",{method:"GET",params:r}));case 2:case"end":return e.stop()}}),e)}))),ee.apply(this,arguments)}function re(e){return te.apply(this,arguments)}function te(){return(te=d()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/consumer_protection/protection-types",{method:"POST",data:{name:r.name,description:r.description||"",is_active:void 0===r.is_active||r.is_active,rules:[]}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function ne(e,r){return ie.apply(this,arguments)}function ie(){return(ie=d()(o()().mark((function e(r,t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/consumer_protection/protection-types/".concat(r),{method:"PUT",data:{name:t.name,description:t.description||"",is_active:t.is_active,rules:t.rules||[]}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function se(e){return ce.apply(this,arguments)}function ce(){return(ce=d()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/consumer_protection/protection-types/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function ae(e){return oe.apply(this,arguments)}function oe(){return(oe=d()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/consumer_protection/protection-types/".concat(r,"/rules"),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function ue(e,r){return le.apply(this,arguments)}function le(){return(le=d()(o()().mark((function e(r,t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/consumer_protection/protection-types/".concat(r,"/rules"),{method:"PUT",data:{rule_id:t}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function pe(e,r){return de.apply(this,arguments)}function de(){return(de=d()(o()().mark((function e(r,t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,D.N)("/api/consumer_protection/protection-types/".concat(r,"/rules/").concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var fe=t(85893),he=m.Z.TabPane,xe=v.Z.TextArea,me=function(){var e=(0,x.useState)([]),r=h()(e,2),t=r[0],n=r[1],s=(0,x.useState)(!1),a=h()(s,2),u=a[0],p=a[1],f=(0,x.useState)(0),D=h()(f,2),M=D[0],R=D[1],H=(0,x.useState)({current:1,pageSize:10}),Y=h()(H,2),ee=Y[0],te=Y[1],ie=(0,x.useState)("all"),ce=h()(ie,2),oe=ce[0],le=(ce[1],(0,x.useState)([])),de=h()(le,2),me=de[0],ve=de[1],Ze=(0,x.useState)(!1),ye=h()(Ze,2),je=ye[0],ge=ye[1],ke=(0,x.useState)(0),we=h()(ke,2),be=we[0],Pe=we[1],Se=(0,x.useState)({current:1,pageSize:10}),_e=h()(Se,2),Te=_e[0],Ne=_e[1],Ce=(0,x.useState)(!1),Ie=h()(Ce,2),ze=Ie[0],Fe=Ie[1],Ee=(0,x.useState)(null),Oe=h()(Ee,2),Ae=Oe[0],Je=Oe[1],Ue=Z.Z.useForm(),Ve=h()(Ue,1)[0],Le=(0,x.useState)(!1),qe=h()(Le,2),Be=qe[0],Ke=qe[1],De=(0,x.useState)(""),Ge=h()(De,2),Me=Ge[0],Qe=Ge[1],Re=(0,x.useState)(""),We=h()(Re,2),He=We[0],Xe=We[1],Ye=(0,x.useState)([]),$e=h()(Ye,2),er=$e[0],rr=$e[1],tr=(0,x.useState)([]),nr=h()(tr,2),ir=nr[0],sr=nr[1],cr=(0,x.useState)(!1),ar=h()(cr,2),or=ar[0],ur=ar[1],lr=(0,x.useState)(""),pr=h()(lr,2),dr=pr[0],fr=pr[1],hr=(0,x.useState)(!1),xr=h()(hr,2),mr=xr[0],vr=xr[1],Zr=Z.Z.useForm(),yr=h()(Zr,1)[0],jr=(0,x.useState)([]),gr=h()(jr,2),kr=gr[0],wr=gr[1],br=(0,x.useState)(""),Pr=h()(br,2),Sr=Pr[0],_r=Pr[1],Tr=(0,x.useState)(null),Nr=h()(Tr,2),Cr=Nr[0],Ir=Nr[1],zr=(0,x.useState)(!1),Fr=h()(zr,2),Er=Fr[0],Or=Fr[1],Ar=(0,x.useState)([]),Jr=h()(Ar,2),Ur=Jr[0],Vr=Jr[1],Lr=(0,x.useState)([]),qr=h()(Lr,2),Br=qr[0],Kr=qr[1],Dr=(0,x.useState)(!1),Gr=h()(Dr,2),Mr=Gr[0],Qr=Gr[1],Rr=((0,x.useMemo)((function(){return{total:t.length,auto:t.filter((function(e){return"auto"===e.source})).length,manual:t.filter((function(e){return"manual"===e.source})).length}}),[t]),(0,x.useMemo)((function(){return{total:me.length,active:me.filter((function(e){return e.is_active})).length,inactive:me.filter((function(e){return!e.is_active})).length}}),[me])),Wr=((0,x.useMemo)((function(){return me.map((function(e){return{value:e.id,label:e.name}}))}),[me]),function(){var e=d()(o()().mark((function e(){var r,t,i,s;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return p(!0),e.prev=1,r=ee.current,t=ee.pageSize,i=l()({current:r,pageSize:t},"all"!==oe?{source:oe}:{}),e.next=6,G(i);case 6:(s=e.sent).success?(n(s.data),R(s.total)):y.ZP.error("获取规则列表失败"),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(1),console.error("获取规则列表出错:",e.t0),y.ZP.error("获取规则列表出错");case 14:return e.prev=14,p(!1),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[1,10,14,17]])})));return function(){return e.apply(this,arguments)}}()),Hr=function(){var e=d()(o()().mark((function e(){var r,t,n,i;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ge(!0),e.prev=1,r=Te.current,t=Te.pageSize,n={skip:(r-1)*t,limit:t},e.next=6,$(n);case 6:(i=e.sent).success?(ve(i.data),Pe(i.total)):y.ZP.error("获取类型列表失败"),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(1),console.error("获取类型列表出错:",e.t0),y.ZP.error("获取类型列表出错");case 14:return e.prev=14,ge(!1),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[1,10,14,17]])})));return function(){return e.apply(this,arguments)}}(),Xr=function(){var e=d()(o()().mark((function e(r,t){var n,i,s,c;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ur(!0),e.prev=1,e.next=4,ae(r);case 4:if(n=e.sent,console.log("关联规则响应:",n),!n||!n.success){e.next=18;break}return rr(n.data||[]),Qe(r),Xe(t),e.next=12,G({pageSize:1e3});case 12:i=e.sent,console.log("所有规则响应:",i),i&&i.success?(s=n.data?n.data.map((function(e){return e.id})):[],c=i.data.filter((function(e){return!s.includes(e.id)})),console.log("过滤后的可用规则:",c),sr(c),fr("")):(sr([]),y.ZP.error("获取可用规则失败")),Ke(!0),e.next=19;break;case 18:y.ZP.error("获取关联规则失败");case 19:e.next=26;break;case 21:e.prev=21,e.t0=e.catch(1),console.error("获取关联规则出错:",e.t0),y.ZP.error("获取关联规则出错"),sr([]);case 26:return e.prev=26,ur(!1),e.finish(26);case 29:case"end":return e.stop()}}),e,null,[[1,21,26,29]])})));return function(r,t){return e.apply(this,arguments)}}(),Yr=function(){var e=d()(o()().mark((function e(){var r;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,$({limit:1e3});case 3:(r=e.sent).success&&ve(r.data),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取所有类型失败:",e.t0);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();(0,x.useEffect)((function(){Wr()}),[ee.current,ee.pageSize,oe]),(0,x.useEffect)((function(){Hr()}),[Te.current,Te.pageSize]),(0,x.useEffect)((function(){Yr()}),[]);var $r=function(){var e=d()(o()().mark((function e(){var r,t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Ve.validateFields();case 3:if(r=e.sent,console.log("表单提交的值:",r),!Ae){e.next=12;break}return e.next=8,ne(Ae.id,{name:r.name,description:r.description,is_active:r.is_active});case 8:e.sent?(y.ZP.success("更新类型成功"),Fe(!1),Ve.resetFields(),Hr(),Yr()):y.ZP.error("更新类型失败"),e.next=18;break;case 12:return t={name:r.name,description:r.description||"",is_active:void 0===r.is_active||r.is_active},console.log("创建类型数据:",t),e.next=16,re(t);case 16:e.sent?(y.ZP.success("创建类型成功"),Fe(!1),Ve.resetFields(),Hr(),Yr()):y.ZP.error("创建类型失败");case 18:e.next=24;break;case 20:e.prev=20,e.t0=e.catch(0),console.error("表单提交出错:",e.t0),y.ZP.error("表单验证失败，请检查必填字段");case 24:case"end":return e.stop()}}),e,null,[[0,20]])})));return function(){return e.apply(this,arguments)}}(),et=function(){var e=d()(o()().mark((function e(){var r,t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(dr&&Me){e.next=3;break}return y.ZP.warning("请选择要添加的规则"),e.abrupt("return");case 3:return e.prev=3,e.next=6,ue(Me,dr);case 6:if(!(r=e.sent).success){e.next=15;break}return y.ZP.success("规则添加成功"),e.next=11,ae(Me);case 11:(t=e.sent).success&&(rr(t.data),sr(ir.filter((function(e){return e.id!==dr}))),fr("")),e.next=16;break;case 15:y.ZP.error(r.message||"规则添加失败");case 16:e.next=22;break;case 18:e.prev=18,e.t0=e.catch(3),console.error("添加规则出错:",e.t0),y.ZP.error("添加规则出错");case 22:case"end":return e.stop()}}),e,null,[[3,18]])})));return function(){return e.apply(this,arguments)}}(),rt=function(){var e=d()(o()().mark((function e(r){var t,n;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,pe(Me,r);case 3:(t=e.sent).success?(y.ZP.success("规则移除成功"),rr(er.filter((function(e){return e.id!==r}))),(n=er.find((function(e){return e.id===r})))&&sr([].concat(c()(ir),[n]))):y.ZP.error(t.message||"规则移除失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("移除规则出错:",e.t0),y.ZP.error("移除规则出错");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(r){return e.apply(this,arguments)}}(),tt=function(){var e=d()(o()().mark((function e(r){var n;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:try{(n=t.find((function(e){return e.id===r})))&&(Ir(n),yr.setFieldsValue({ruleName:n.ruleName,description:n.description,logic:n.logic}),wr(n.result_definition||[]),vr(!0))}catch(e){y.ZP.error("加载规则详情失败")}case 1:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),nt=function(){var e=d()(o()().mark((function e(){var r,t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,yr.validateFields();case 3:if(r=e.sent,t={ruleName:r.ruleName,ruleType:"",description:r.description,source:"manual",logic:r.logic,result_definition:kr},!Cr){e.next=12;break}return e.next=8,W(Cr.id,t);case 8:e.sent?(y.ZP.success("编辑规则成功"),vr(!1),Ir(null),yr.resetFields(),wr([]),Wr()):y.ZP.error("编辑规则失败"),e.next=16;break;case 12:return e.next=14,Q(t);case 14:e.sent?(y.ZP.success("创建规则成功"),vr(!1),yr.resetFields(),wr([]),Wr()):y.ZP.error("创建规则失败");case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(0),y.ZP.error("表单验证失败，请检查必填字段");case 21:case"end":return e.stop()}}),e,null,[[0,18]])})));return function(){return e.apply(this,arguments)}}(),it=function(e){var r=new FileReader;return r.onload=function(r){try{var t;if(!e.name.endsWith(".json"))return y.ZP.error("不支持的文件格式，请上传JSON文件"),!1;var n=null===(t=r.target)||void 0===t?void 0:t.result,i=JSON.parse(n);if(!Array.isArray(i))return y.ZP.error("JSON文件格式不正确，应为数组格式"),!1;var s=i.map((function(e){return{ruleName:e.ruleName||"",ruleType:e.ruleType||"",description:e.description||"",source:"manual",logic:e.logic||"",result_definition:e.result_definition||[]}})).filter((function(e){return e.ruleName&&e.description&&e.logic}));if(0===s.length)return y.ZP.error("没有找到有效的规则数据"),!1;Vr(s),Kr(s.map((function(e,r){return r.toString()}))),Or(!0)}catch(e){console.error("解析文件出错:",e),y.ZP.error("解析文件失败，请检查文件格式")}},e.name.endsWith(".json")?(r.readAsText(e),!1):(y.ZP.error("不支持的文件格式，请上传JSON文件"),!1)},st=function(){var e=d()(o()().mark((function e(){var r,t,n,s,c,a;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==Br.length){e.next=3;break}return y.ZP.warning("请选择要导入的规则"),e.abrupt("return");case 3:Qr(!0),e.prev=4,r=Br.map((function(e){return Ur[parseInt(e)]})),t=0,n=0,s=i()(r),e.prev=9,s.s();case 11:if((c=s.n()).done){e.next=26;break}return a=c.value,e.prev=13,e.next=16,Q(a);case 16:e.sent?t++:n++,e.next=24;break;case 20:e.prev=20,e.t0=e.catch(13),console.error("导入规则失败:",e.t0),n++;case 24:e.next=11;break;case 26:e.next=31;break;case 28:e.prev=28,e.t1=e.catch(9),s.e(e.t1);case 31:return e.prev=31,s.f(),e.finish(31);case 34:t>0?(y.ZP.success("成功导入".concat(t,"条规则").concat(n>0?"，".concat(n,"条失败"):"")),Or(!1),Wr()):y.ZP.error("导入失败，请检查数据格式"),e.next=41;break;case 37:e.prev=37,e.t2=e.catch(4),console.error("批量导入出错:",e.t2),y.ZP.error("批量导入出错");case 41:return e.prev=41,Qr(!1),e.finish(41);case 44:case"end":return e.stop()}}),e,null,[[4,37,41,44],[9,28,31,34],[13,20]])})));return function(){return e.apply(this,arguments)}}(),ct=[{title:"规则名称",dataIndex:"ruleName",key:"ruleName",width:250,ellipsis:!0,render:function(e,r){return(0,fe.jsx)(g.Z,{title:r.description,children:(0,fe.jsx)("span",{children:e})})}},{title:"规则类型",dataIndex:"ruleType",key:"ruleType",width:100},{title:"来源",dataIndex:"source",key:"source",width:100,render:function(e){return(0,fe.jsx)(k.Z,{color:"auto"===e?"blue":"green",children:"auto"===e?"自动生成":"手动创建"})}},{title:"创建时间",dataIndex:"created_at",key:"created_at",width:180},{title:"操作",key:"action",width:150,render:function(e,r){return(0,fe.jsxs)(w.Z,{size:"small",children:[(0,fe.jsx)(b.ZP,{type:"link",icon:(0,fe.jsx)(O.Z,{}),onClick:function(){return tt(r.id)},children:"编辑"}),(0,fe.jsx)(P.Z,{title:"确定删除此规则?",onConfirm:function(){return e=r.id,void j.Z.confirm({title:"确认删除规则？",content:"此操作不可撤销",onOk:(t=d()(o()().mark((function r(){var t;return o()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,X(e);case 3:(t=r.sent).success?(y.ZP.success("删除成功"),Wr()):y.ZP.error(t.message||"删除失败"),r.next=11;break;case 7:r.prev=7,r.t0=r.catch(0),console.error("删除规则出错:",r.t0),y.ZP.error("删除规则出错");case 11:case"end":return r.stop()}}),r,null,[[0,7]])}))),function(){return t.apply(this,arguments)})});var e,t},okText:"确定",cancelText:"取消",children:(0,fe.jsx)(b.ZP,{type:"link",danger:!0,icon:(0,fe.jsx)(A.Z,{}),children:"删除"})})]})}}],at=[{title:"名称",dataIndex:"name",key:"name",width:200,render:function(e,r){return(0,fe.jsx)(g.Z,{title:r.description,children:(0,fe.jsx)("span",{children:e})})}},{title:"规则数量",dataIndex:"rules_count",key:"rules_count",width:100},{title:"状态",dataIndex:"is_active",key:"is_active",width:100,render:function(e){return(0,fe.jsx)(k.Z,{color:e?"green":"red",children:e?"启用":"禁用"})}},{title:"创建时间",dataIndex:"created_at",key:"created_at",width:180},{title:"操作",key:"action",width:220,render:function(e,r){return(0,fe.jsxs)(w.Z,{size:"small",children:[(0,fe.jsx)(b.ZP,{type:"link",icon:(0,fe.jsx)(J.Z,{}),onClick:function(){return Xr(r.id,r.name)},children:"关联规则"}),(0,fe.jsx)(b.ZP,{type:"link",icon:(0,fe.jsx)(O.Z,{}),onClick:function(){return Je(e=r),Ve.setFieldsValue({name:e.name,description:e.description,is_active:e.is_active}),void Fe(!0);var e},children:"编辑"}),(0,fe.jsx)(P.Z,{title:"确定删除此类型?",onConfirm:function(){return e=r.id,void j.Z.confirm({title:"确认删除类型？",content:"此操作不可撤销，且会解除与所有规则的关联",onOk:(t=d()(o()().mark((function r(){var t;return o()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,se(e);case 3:(t=r.sent).success?(y.ZP.success("删除成功"),Hr()):y.ZP.error(t.message||"删除失败"),r.next=11;break;case 7:r.prev=7,r.t0=r.catch(0),console.error("删除类型出错:",r.t0),y.ZP.error("删除类型出错");case 11:case"end":return r.stop()}}),r,null,[[0,7]])}))),function(){return t.apply(this,arguments)})});var e,t},okText:"确定",cancelText:"取消",children:(0,fe.jsx)(b.ZP,{type:"link",danger:!0,icon:(0,fe.jsx)(A.Z,{}),children:"删除"})})]})}}],ot=[{title:"规则名称",dataIndex:"ruleName",key:"ruleName",width:250,ellipsis:!0},{title:"来源",dataIndex:"source",key:"source",width:100,render:function(e){return(0,fe.jsx)(k.Z,{color:"auto"===e?"blue":"green",children:"auto"===e?"自动生成":"手动创建"})}},{title:"操作",key:"action",width:100,render:function(e,r){return(0,fe.jsx)(P.Z,{title:"确定移除此规则?",onConfirm:function(){return rt(r.id)},okText:"确定",cancelText:"取消",children:(0,fe.jsx)(b.ZP,{type:"link",danger:!0,children:"移除"})})}}],ut=[{title:"规则名称",dataIndex:"ruleName",key:"ruleName",width:200,ellipsis:!0,render:function(e,r){return(0,fe.jsx)(g.Z,{title:r.description,children:(0,fe.jsx)("span",{children:e})})}},{title:"规则类型",dataIndex:"ruleType",key:"ruleType",width:150,ellipsis:!0},{title:"分析逻辑",dataIndex:"logic",key:"logic",width:250,ellipsis:!0}];return(0,fe.jsxs)(E._z,{children:[(0,fe.jsxs)(m.Z,{defaultActiveKey:"types",children:[(0,fe.jsx)(he,{tab:(0,fe.jsxs)("span",{children:[(0,fe.jsx)(U.Z,{}),"分析任务类型"]}),children:(0,fe.jsxs)(S.Z,{children:[(0,fe.jsxs)(_.Z,{gutter:16,style:{marginBottom:16},children:[(0,fe.jsx)(T.Z,{span:6,children:(0,fe.jsx)(N.Z,{title:"类型总数",value:Rr.total,prefix:(0,fe.jsx)(U.Z,{})})}),(0,fe.jsx)(T.Z,{span:6,children:(0,fe.jsx)(N.Z,{title:"启用状态",value:Rr.active,prefix:(0,fe.jsx)(V.Z,{})})}),(0,fe.jsx)(T.Z,{span:6,children:(0,fe.jsx)(N.Z,{title:"禁用状态",value:Rr.inactive,prefix:(0,fe.jsx)(V.Z,{})})}),(0,fe.jsx)(T.Z,{span:6,style:{textAlign:"right"},children:(0,fe.jsx)(b.ZP,{type:"primary",icon:(0,fe.jsx)(L.Z,{}),onClick:function(){Je(null),Ve.resetFields(),Ve.setFieldsValue({is_active:!0}),Fe(!0)},children:"新建类型"})})]}),(0,fe.jsx)(C.Z,{columns:at,dataSource:me,rowKey:"id",loading:je,pagination:{current:Te.current,pageSize:Te.pageSize,total:be,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(e){return"共 ".concat(e," 条类型")},onChange:function(e,r){Ne({current:e,pageSize:r})}}})]})},"types"),(0,fe.jsx)(he,{tab:(0,fe.jsxs)("span",{children:[(0,fe.jsx)(V.Z,{}),"规则管理"]}),children:(0,fe.jsxs)(S.Z,{children:[(0,fe.jsx)(_.Z,{gutter:16,style:{marginBottom:16},children:(0,fe.jsx)(T.Z,{span:12,children:(0,fe.jsxs)(w.Z,{children:[(0,fe.jsx)(b.ZP,{type:"primary",icon:(0,fe.jsx)(L.Z,{}),onClick:function(){Ir(null),yr.resetFields(),wr([]),vr(!0)},children:"新建规则"}),(0,fe.jsx)(I.Z,{beforeUpload:it,showUploadList:!1,accept:".json",children:(0,fe.jsx)(b.ZP,{icon:(0,fe.jsx)(q.Z,{}),children:"批量导入"})}),(0,fe.jsx)(b.ZP,{icon:(0,fe.jsx)(B.Z,{}),onClick:function(){return window.open("/static/样例.json")},children:"导入文件样例"})]})})}),(0,fe.jsx)(C.Z,{columns:ct,dataSource:t,rowKey:"id",loading:u,scroll:{x:800},pagination:{current:ee.current,pageSize:ee.pageSize,total:M,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(e){return"共 ".concat(e," 条规则")},onChange:function(e,r){te({current:e,pageSize:r})}}})]})},"rules")]}),(0,fe.jsx)(j.Z,{title:Cr?"编辑规则":"新建规则",open:mr,onOk:nt,onCancel:function(){vr(!1),Ir(null),yr.resetFields(),wr([])},width:800,children:(0,fe.jsxs)(Z.Z,{form:yr,layout:"vertical",initialValues:{source:"manual"},children:[(0,fe.jsx)(Z.Z.Item,{name:"ruleName",label:"规则名称",rules:[{required:!0,message:"请输入规则名称"}],children:(0,fe.jsx)(v.Z,{placeholder:"请输入规则名称"})}),(0,fe.jsx)(Z.Z.Item,{name:"description",label:"规则描述",rules:[{required:!0,message:"请输入规则描述"}],children:(0,fe.jsx)(xe,{rows:4,placeholder:"请输入规则描述"})}),(0,fe.jsx)(Z.Z.Item,{name:"logic",label:(0,fe.jsxs)("span",{children:["分析逻辑"," ",(0,fe.jsx)(g.Z,{title:"这个就是给大模型的提示词，用于指导AI如何分析内容",children:(0,fe.jsx)(K.Z,{style:{color:"#1890ff"}})})]}),rules:[{required:!0,message:"请输入分析逻辑"}],children:(0,fe.jsx)(xe,{rows:6,placeholder:"请输入分析逻辑（提示词），用于指导AI如何分析内容"})}),(0,fe.jsx)(Z.Z.Item,{label:(0,fe.jsxs)("span",{children:["分析结果定义"," ",(0,fe.jsx)(g.Z,{title:"分析后的修改建议，如'删除'、'替换'，或具体修改成什么样的内容",children:(0,fe.jsx)(K.Z,{style:{color:"#1890ff"}})})]}),required:!0,help:"按回车键添加标签",children:(0,fe.jsxs)(w.Z,{direction:"vertical",style:{width:"100%"},children:[(0,fe.jsx)(v.Z,{placeholder:"请输入可能的分析结果或修改建议，如'删除'、'替换'等，按回车添加",value:Sr,onChange:function(e){return _r(e.target.value)},onPressEnter:function(){Sr&&Sr.trim()&&!kr.includes(Sr.trim())&&(wr([].concat(c()(kr),[Sr.trim()])),_r(""))},style:{width:"100%"}}),(0,fe.jsx)("div",{style:{marginTop:8},children:kr.map((function(e){return(0,fe.jsx)(k.Z,{closable:!0,onClose:function(){return r=e,void wr(kr.filter((function(e){return e!==r})));var r},style:{marginBottom:8},children:e},e)}))})]})})]})}),(0,fe.jsx)(j.Z,{title:Ae?"编辑类型":"新建类型",open:ze,onOk:$r,onCancel:function(){Fe(!1),Ve.resetFields()},width:600,children:(0,fe.jsxs)(Z.Z,{form:Ve,layout:"vertical",initialValues:{is_active:!0},children:[(0,fe.jsx)(Z.Z.Item,{name:"name",label:"类型名称",rules:[{required:!0,message:"请输入类型名称"}],children:(0,fe.jsx)(v.Z,{placeholder:"请输入类型名称，如 合同审核"})}),(0,fe.jsx)(Z.Z.Item,{name:"description",label:"类型描述",children:(0,fe.jsx)(xe,{rows:4,placeholder:"请输入类型描述"})}),(0,fe.jsx)(Z.Z.Item,{name:"is_active",label:"启用状态",initialValue:!0,children:(0,fe.jsx)(z.default,{defaultValue:!0,options:[{value:!0,label:"启用"},{value:!1,label:"禁用"}]})})]})}),(0,fe.jsxs)(j.Z,{title:"".concat(He||"未知类型"," - 关联规则管理"),open:Be,onCancel:function(){return Ke(!1)},footer:null,width:900,children:[(0,fe.jsx)("div",{style:{marginBottom:16},children:(0,fe.jsxs)(_.Z,{gutter:16,children:[(0,fe.jsx)(T.Z,{span:16,children:(0,fe.jsx)(z.default,{style:{width:"100%"},placeholder:"选择要添加的规则",value:dr,onChange:function(e){return fr(e)},options:ir.map((function(e){return console.log("规则详情:",e),{value:e.id,label:e.ruleType+"-"+(e.ruleName||"未命名规则")}})),notFoundContent:0===ir.length?"没有可添加的规则":void 0})}),(0,fe.jsx)(T.Z,{span:8,children:(0,fe.jsx)(b.ZP,{type:"primary",onClick:et,disabled:!dr,children:"添加规则"})})]})}),(0,fe.jsx)(C.Z,{columns:ot,dataSource:er,rowKey:"id",loading:or,scroll:{x:600},pagination:!1})]}),(0,fe.jsxs)(j.Z,{title:"批量导入规则",open:Er,onOk:st,onCancel:function(){return Or(!1)},width:900,confirmLoading:Mr,okText:"导入选中规则",cancelText:"取消",children:[(0,fe.jsx)("div",{style:{marginBottom:16},children:(0,fe.jsxs)("span",{children:["共解析到 ",Ur.length," 条规则，已选择 ",Br.length," 条"]})}),(0,fe.jsx)(C.Z,{rowSelection:{selectedRowKeys:Br,onChange:function(e){Kr(e.map((function(e){return e.toString()})))}},columns:ut,dataSource:Ur.map((function(e,r){return l()(l()({},e),{},{key:r.toString()})})),scroll:{y:400},pagination:!1}),(0,fe.jsxs)("div",{style:{marginTop:16},children:[(0,fe.jsx)(I.Z,{beforeUpload:it,showUploadList:!1,accept:".json",children:(0,fe.jsx)(b.ZP,{icon:(0,fe.jsx)(q.Z,{}),children:"重新上传"})}),(0,fe.jsx)("div",{style:{marginTop:8},children:(0,fe.jsx)(F.Z.Text,{type:"secondary",children:"支持的文件格式：JSON(.json)"})})]})]})]})}}}]);