"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6716],{64317:function(e,r,o){var n=o(1413),l=o(91),a=o(22270),t=o(67294),s=o(66758),i=o(62633),p=o(85893),u=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","showSearch","options"],d=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","options"],c=function(e,r){var o=e.fieldProps,d=e.children,c=e.params,v=e.proFieldProps,f=e.mode,h=e.valueEnum,m=e.request,P=e.showSearch,Z=e.options,g=(0,l.Z)(e,u),x=(0,t.useContext)(s.Z);return(0,p.jsx)(i.Z,(0,n.Z)((0,n.Z)({valueEnum:(0,a.h)(h),request:m,params:c,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:(0,n.Z)({options:Z,mode:f,showSearch:P,getPopupContainer:x.getPopupContainer},o),ref:r,proFieldProps:v},g),{},{children:d}))},v=t.forwardRef((function(e,r){var o=e.fieldProps,u=e.children,c=e.params,v=e.proFieldProps,f=e.mode,h=e.valueEnum,m=e.request,P=e.options,Z=(0,l.Z)(e,d),g=(0,n.Z)({options:P,mode:f||"multiple",labelInValue:!0,showSearch:!0,suffixIcon:null,autoClearSearchValue:!0,optionLabelProp:"label"},o),x=(0,t.useContext)(s.Z);return(0,p.jsx)(i.Z,(0,n.Z)((0,n.Z)({valueEnum:(0,a.h)(h),request:m,params:c,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:(0,n.Z)({getPopupContainer:x.getPopupContainer},g),ref:r,proFieldProps:v},Z),{},{children:u}))})),f=t.forwardRef(c);f.SearchSelect=v,f.displayName="ProFormComponent",r.Z=f},90672:function(e,r,o){var n=o(1413),l=o(91),a=o(67294),t=o(62633),s=o(85893),i=["fieldProps","proFieldProps"],p=function(e,r){var o=e.fieldProps,a=e.proFieldProps,p=(0,l.Z)(e,i);return(0,s.jsx)(t.Z,(0,n.Z)({ref:r,valueType:"textarea",fieldProps:o,proFieldProps:a},p))};r.Z=a.forwardRef(p)},5966:function(e,r,o){var n=o(97685),l=o(1413),a=o(91),t=o(21770),s=o(47019),i=o(55241),p=o(98423),u=o(67294),d=o(62633),c=o(85893),v=["fieldProps","proFieldProps"],f=["fieldProps","proFieldProps"],h="text",m=function(e){var r=(0,t.Z)(e.open||!1,{value:e.open,onChange:e.onOpenChange}),o=(0,n.Z)(r,2),a=o[0],p=o[1];return(0,c.jsx)(s.Z.Item,{shouldUpdate:!0,noStyle:!0,children:function(r){var o,n=r.getFieldValue(e.name||[]);return(0,c.jsx)(i.Z,(0,l.Z)((0,l.Z)({getPopupContainer:function(e){return e&&e.parentNode?e.parentNode:e},onOpenChange:function(e){return p(e)},content:(0,c.jsxs)("div",{style:{padding:"4px 0"},children:[null===(o=e.statusRender)||void 0===o?void 0:o.call(e,n),e.strengthText?(0,c.jsx)("div",{style:{marginTop:10},children:(0,c.jsx)("span",{children:e.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},e.popoverProps),{},{open:a,children:e.children}))}})},P=function(e){var r=e.fieldProps,o=e.proFieldProps,n=(0,a.Z)(e,v);return(0,c.jsx)(d.Z,(0,l.Z)({valueType:h,fieldProps:r,filedConfig:{valueType:h},proFieldProps:o},n))};P.Password=function(e){var r=e.fieldProps,o=e.proFieldProps,t=(0,a.Z)(e,f),s=(0,u.useState)(!1),i=(0,n.Z)(s,2),v=i[0],P=i[1];return null!=r&&r.statusRender&&t.name?(0,c.jsx)(m,{name:t.name,statusRender:null==r?void 0:r.statusRender,popoverProps:null==r?void 0:r.popoverProps,strengthText:null==r?void 0:r.strengthText,open:v,onOpenChange:P,children:(0,c.jsx)("div",{children:(0,c.jsx)(d.Z,(0,l.Z)({valueType:"password",fieldProps:(0,l.Z)((0,l.Z)({},(0,p.Z)(r,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(e){var o;null==r||null===(o=r.onBlur)||void 0===o||o.call(r,e),P(!1)},onClick:function(e){var o;null==r||null===(o=r.onClick)||void 0===o||o.call(r,e),P(!0)}}),proFieldProps:o,filedConfig:{valueType:h}},t))})}):(0,c.jsx)(d.Z,(0,l.Z)({valueType:"password",fieldProps:r,proFieldProps:o,filedConfig:{valueType:h}},t))},P.displayName="ProFormComponent",r.Z=P},86380:function(e,r,o){o.r(r),o.d(r,{default:function(){return x}});var n=o(15009),l=o.n(n),a=o(99289),t=o.n(a),s=(o(67294),o(97131)),i=o(34994),p=o(5966),u=o(90672),d=o(64317),c=o(2453),v=o(4393),f=o(44373),h=o(76772),m=(0,o(28846).kc)((function(e){return{optional:{color:e.token.colorTextSecondary,fontStyle:"normal"}}})),P=o(85893),Z=[{label:"微调",value:"微调"},{label:"预训练",value:"预训练"},{label:"测试",value:"测试"},{label:"金融问答",value:"金融问答"},{label:"信贷",value:"信贷"},{label:"LLM微调",value:"LLM微调"},{label:"对话数据",value:"对话数据"},{label:"问答配对",value:"问答配对"},{label:"生成式任务",value:"生成式任务"},{label:"指令微调",value:"指令微调"},{label:"上下文理解",value:"上下文理解"},{label:"语义匹配",value:"语义匹配"},{label:"知识检索",value:"知识检索"},{label:"补全任务",value:"补全任务"},{label:"语言翻译",value:"语言翻译"}],g=function(e){console.log("selected ".concat(e))},x=function(){var e=m().styles,r=function(){var e=t()(l()().mark((function e(r){var o;return l()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,f.iK)({name:r.name,description:r.description,tags:r.tags||[]});case 3:(o=e.sent)?(c.ZP.success("数据集创建成功"),h.history.push("/dataset/structured")):c.ZP.error((null==o?void 0:o.message)||"创建数据集失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),c.ZP.error((null===e.t0||void 0===e.t0?void 0:e.t0.message)||"创建数据集失败"),console.error("创建数据集错误:",e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(r){return e.apply(this,arguments)}}();return(0,P.jsx)(s._z,{content:"表单页用于向用户收集或验证信息，基础表单常见于数据项较少的表单场景。",children:(0,P.jsx)(v.Z,{bordered:!1,children:(0,P.jsxs)(i.A,{style:{margin:"auto",marginTop:8,marginBottom:24,maxWidth:600,paddingBottom:24},name:"basic",layout:"vertical",initialValues:{name:"新建数据集",description:"这是一个新的数据集，用于..."},onFinish:r,children:[(0,P.jsx)(p.Z,{width:"md",label:(0,P.jsxs)("span",{children:["数据集名称",(0,P.jsx)("em",{className:e.optional,children:"（选填）"})]}),tooltip:"请输入数据集的名称",name:"name",rules:[{required:!0,message:"请输入标题"}],placeholder:"给目标起个名字"}),(0,P.jsx)(u.Z,{label:(0,P.jsxs)("span",{children:["数据集描述",(0,P.jsx)("em",{className:e.optional,children:"（选填,描述数据集特点和作用等。）"})]}),name:"description",rules:[{required:!0,message:"请输入目标描述"}],placeholder:"请输入你的阶段性工作目标"}),(0,P.jsx)(d.Z,{name:"tags",width:"xl",label:"数据集标签",style:{width:"100%"},mode:"tags",placeholder:"请输入数据标签",onChange:g,options:Z})]})})})}}}]);