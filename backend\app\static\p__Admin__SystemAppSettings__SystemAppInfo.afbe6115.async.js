"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1446],{64317:function(e,t,n){var r=n(1413),a=n(91),s=n(22270),i=n(67294),o=n(66758),l=n(62633),u=n(85893),c=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","showSearch","options"],p=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","options"],d=function(e,t){var n=e.fieldProps,p=e.children,d=e.params,m=e.proFieldProps,f=e.mode,h=e.valueEnum,x=e.request,v=e.showSearch,y=e.options,g=(0,a.Z)(e,c),b=(0,i.useContext)(o.Z);return(0,u.jsx)(l.Z,(0,r.Z)((0,r.Z)({valueEnum:(0,s.h)(h),request:x,params:d,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:(0,r.Z)({options:y,mode:f,showSearch:v,getPopupContainer:b.getPopupContainer},n),ref:t,proFieldProps:m},g),{},{children:p}))},m=i.forwardRef((function(e,t){var n=e.fieldProps,c=e.children,d=e.params,m=e.proFieldProps,f=e.mode,h=e.valueEnum,x=e.request,v=e.options,y=(0,a.Z)(e,p),g=(0,r.Z)({options:v,mode:f||"multiple",labelInValue:!0,showSearch:!0,suffixIcon:null,autoClearSearchValue:!0,optionLabelProp:"label"},n),b=(0,i.useContext)(o.Z);return(0,u.jsx)(l.Z,(0,r.Z)((0,r.Z)({valueEnum:(0,s.h)(h),request:x,params:d,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:(0,r.Z)({getPopupContainer:b.getPopupContainer},g),ref:t,proFieldProps:m},y),{},{children:c}))})),f=i.forwardRef(d);f.SearchSelect=m,f.displayName="ProFormComponent",t.Z=f},90672:function(e,t,n){var r=n(1413),a=n(91),s=n(67294),i=n(62633),o=n(85893),l=["fieldProps","proFieldProps"],u=function(e,t){var n=e.fieldProps,s=e.proFieldProps,u=(0,a.Z)(e,l);return(0,o.jsx)(i.Z,(0,r.Z)({ref:t,valueType:"textarea",fieldProps:n,proFieldProps:s},u))};t.Z=s.forwardRef(u)},5966:function(e,t,n){var r=n(97685),a=n(1413),s=n(91),i=n(21770),o=n(47019),l=n(55241),u=n(98423),c=n(67294),p=n(62633),d=n(85893),m=["fieldProps","proFieldProps"],f=["fieldProps","proFieldProps"],h="text",x=function(e){var t=(0,i.Z)(e.open||!1,{value:e.open,onChange:e.onOpenChange}),n=(0,r.Z)(t,2),s=n[0],u=n[1];return(0,d.jsx)(o.Z.Item,{shouldUpdate:!0,noStyle:!0,children:function(t){var n,r=t.getFieldValue(e.name||[]);return(0,d.jsx)(l.Z,(0,a.Z)((0,a.Z)({getPopupContainer:function(e){return e&&e.parentNode?e.parentNode:e},onOpenChange:function(e){return u(e)},content:(0,d.jsxs)("div",{style:{padding:"4px 0"},children:[null===(n=e.statusRender)||void 0===n?void 0:n.call(e,r),e.strengthText?(0,d.jsx)("div",{style:{marginTop:10},children:(0,d.jsx)("span",{children:e.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},e.popoverProps),{},{open:s,children:e.children}))}})},v=function(e){var t=e.fieldProps,n=e.proFieldProps,r=(0,s.Z)(e,m);return(0,d.jsx)(p.Z,(0,a.Z)({valueType:h,fieldProps:t,filedConfig:{valueType:h},proFieldProps:n},r))};v.Password=function(e){var t=e.fieldProps,n=e.proFieldProps,i=(0,s.Z)(e,f),o=(0,c.useState)(!1),l=(0,r.Z)(o,2),m=l[0],v=l[1];return null!=t&&t.statusRender&&i.name?(0,d.jsx)(x,{name:i.name,statusRender:null==t?void 0:t.statusRender,popoverProps:null==t?void 0:t.popoverProps,strengthText:null==t?void 0:t.strengthText,open:m,onOpenChange:v,children:(0,d.jsx)("div",{children:(0,d.jsx)(p.Z,(0,a.Z)({valueType:"password",fieldProps:(0,a.Z)((0,a.Z)({},(0,u.Z)(t,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(e){var n;null==t||null===(n=t.onBlur)||void 0===n||n.call(t,e),v(!1)},onClick:function(e){var n;null==t||null===(n=t.onClick)||void 0===n||n.call(t,e),v(!0)}}),proFieldProps:n,filedConfig:{valueType:h}},i))})}):(0,d.jsx)(p.Z,(0,a.Z)({valueType:"password",fieldProps:t,proFieldProps:n,filedConfig:{valueType:h}},i))},v.displayName="ProFormComponent",t.Z=v},1861:function(e,t,n){n.r(t),n.d(t,{default:function(){return se}});var r=n(15009),a=n.n(r),s=n(99289),i=n.n(s),o=n(5574),l=n.n(o),u=n(97131),c=n(88372),p=n(48054),d=n(26412),m=n(66309),f=n(83622),h=n(42075),x=n(71471),v=n(67294),y=n(69992),g=function(e){return e.KNOWLEDGE_QA="知识库问答",e.LLM="大语言模型",e.OTHER="其他",e.WISERAG="网智天元知识库系统API",e.WISEBI="网智天元知识库系统API",e.AGENT="智能体应用",e.EXPERT_SYSTEM="专家系统",e}({}),b=function(e){return e.WISERAG="网智天元知识库系统API",e.WISEBI="网智天元知识库系统API",e.KNOWLEDGE_QA="知识库Agent应用",e.AGENT="智能体应用",e.EXPERT_SYSTEM="专家系统",e.LLM="大语言模型",e.OTHER="其他",e}({}),k=n(76772),_=n(28846),j=n(27484),w=n.n(j),Z=n(19632),P=n.n(Z),E=n(17788),S=n(4393),I=n(2453),C=n(51042),T=n(97321),A=n(34994),R=n(5966),L=n(64317),N=n(90672),O=n(5155),F=n(85893),G=[{id:"1",name:"通用问答模板",description:"适用于一般性的问答场景",content:"你是一个知识库问答助手。请基于知识库内容回答用户问题，如果无法从知识库中找到相关信息，请明确告知。"},{id:"2",name:"技术文档模板",description:"适用于技术文档解读和问答",content:"你是一个技术文档助手。请基于技术文档内容回答用户问题，回答要专业、准确，并尽可能提供相关的代码示例或操作步骤。"},{id:"3",name:"客服模板",description:"适用于客户服务场景",content:"你是一个客服助手。请用友善、专业的语气回答用户问题，确保回答准确、简洁、易懂。如果遇到复杂问题，建议用户联系人工客服。"}],q=function(e){var t=e.visible,n=e.onOk,r=e.onCancel,a=e.templates,s=e.initialSelected,i=(0,v.useState)(s||""),o=l()(i,2),u=o[0],c=o[1];(0,v.useEffect)((function(){c(s||"")}),[s]);var p=[].concat(G,P()(a));return(0,F.jsxs)(E.Z,{title:"选择提示词模板",open:t,onOk:function(){return n(u)},onCancel:r,width:1e3,style:{maxHeight:"80vh",overflow:"auto",padding:"24px"},destroyOnClose:!1,footer:null,children:[(0,F.jsx)("div",{style:{maxHeight:"70vh",overflowY:"auto"},children:(0,F.jsx)(T.Z.Group,{onChange:function(e){console.log("Selected template:",e),c(e)},value:u,children:p.map((function(e){return(0,F.jsx)(T.Z,{title:e.name,description:e.description,value:e.id,style:{height:"100px",width:"200px",marginBottom:"16px"},extra:(0,F.jsx)(f.ZP,{type:"link",onClick:function(t){t.stopPropagation(),E.Z.info({title:e.name,content:(0,F.jsx)("pre",{style:{whiteSpace:"pre-wrap"},children:e.content}),width:600})},children:"预览"})},e.id)}))})}),(0,F.jsxs)("div",{style:{textAlign:"right",marginTop:"16px"},children:[(0,F.jsx)(f.ZP,{type:"primary",onClick:function(){return n(u)},children:"确定"}),(0,F.jsx)(f.ZP,{onClick:r,style:{marginLeft:"8px"},children:"取消"})]})]})},D=function(e){var t=e.visible,n=e.onOk,r=e.onCancel,a=e.availableKnowledgeBases,s=e.initialSelected,i=(0,v.useState)(s),o=l()(i,2),u=o[0],c=o[1];return(0,v.useEffect)((function(){c(s)}),[s]),(0,F.jsxs)(E.Z,{title:"选择知识库",open:t,onOk:function(){return n(u)},onCancel:r,width:1e3,style:{maxHeight:"80vh",overflow:"auto",padding:"24px"},destroyOnClose:!1,footer:null,children:[(0,F.jsx)("div",{style:{maxHeight:"70vh",overflowY:"auto"},children:(0,F.jsx)(T.Z.Group,{multiple:!0,onChange:function(e){console.log("Selected values:",e),c(e)},value:u,children:a.map((function(e){return(0,F.jsx)(T.Z,{title:e.name,description:e.description?e.description.slice(0,40)+(e.description.length>49?"...":""):"",value:e.id,style:{height:"100px",width:"200px"}},e.id)}))})}),(0,F.jsxs)("div",{style:{textAlign:"right",marginTop:"16px"},children:[(0,F.jsx)(f.ZP,{type:"primary",onClick:function(){return n(u)},children:"确定"}),(0,F.jsx)(f.ZP,{onClick:r,style:{marginLeft:"8px"},children:"取消"})]})]})},M=[{name:"基础指令模板",content:"你是一个智能助手，请根据提供的知识库内容回答问题。\n要求：\n1. 回答需准确基于参考资料\n2. 使用简洁的中文回答\n3. 避免编造不知道的内容\n4. 涉及敏感内容时明确拒绝回答"},{name:"严格校验模板",content:"你是一个严谨的行业专家，请严格按以下规则回答：\n1. 仅使用提供的参考资料内容\n2. 标明每个观点的来源编号\n3. 不确定的内容需明确说明\n4. 使用专业术语保持准确性"}],B=[{name:"标准问答模板",content:"{{quote}}\n请根据以上参考内容，用中文简洁明了地回答：{{question}}"},{name:"分点回答模板",content:"参考资料：\n{{quote}}\n\n请根据上述资料：\n1. 用中文分点列出关键信息\n2. 每个点标注来源编号[{{index}}]\n3. 最后总结回答：{{question}}"}],W=[{name:"标准引用格式",content:"【文档{{index}}】来源：{{source}}（ID:{{sourceId}}）\n更新时间：{{time}}\nQ: {{q}}\nA: {{a}}"},{name:"简洁引用格式",content:"[{{index}}] {{source}} | {{time}}\n问：{{q}}\n答：{{a}}"},{name:"学术引用格式",content:"参考文献{{index}}：\n来源机构：{{source}}\n文献编号：{{sourceId}}\n采集时间：{{time}}\n相关问答：\n问题：{{q}}\n回答：{{a}}"}],K=function(e){var t=e.visible,n=e.templates,r=e.onSelect,a=e.onCancel;return(0,F.jsx)(E.Z,{title:"选择模板",open:t,onCancel:a,footer:null,width:800,children:(0,F.jsx)("div",{style:{maxHeight:"60vh",overflowY:"auto"},children:n.map((function(e){return(0,F.jsxs)(S.Z,{hoverable:!0,onClick:function(){return r(e.content)},style:{marginBottom:16},children:[(0,F.jsx)("h4",{children:e.name}),(0,F.jsx)("pre",{style:{whiteSpace:"pre-wrap"},children:e.content})]},e.name)}))})})},H=function(e){var t=e.appInfo,n=(0,v.useState)(t.type),r=l()(n,2),s=r[0],o=r[1],u=(0,v.useState)([]),c=l()(u,2),p=c[0],d=c[1],m=(0,v.useState)(!1),x=l()(m,2),g=x[0],k=x[1],_=(0,v.useState)([]),j=l()(_,2),w=j[0],Z=j[1],P=(0,v.useState)([]),E=l()(P,2),T=E[0],H=E[1],V=(0,v.useState)([]),Y=l()(V,2),Q=Y[0],z=Y[1],U=A.A.useForm(),X=l()(U,1)[0],J=(0,v.useState)(!1),$=l()(J,2),ee=$[0],te=$[1],ne=(0,v.useState)(),re=l()(ne,2),ae=re[0],se=re[1],ie=(0,v.useState)(!1),oe=l()(ie,2),le=oe[0],ue=oe[1],ce=(0,v.useState)("system"),pe=l()(ce,2),de=pe[0],me=pe[1];(0,v.useEffect)((function(){var e=function(){var e=i()(a()().mark((function e(){var t,n,r,s,i,o;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,y.VB)();case 3:return(t=e.sent).success&&Array.isArray(t.data)&&(n=t.data.map((function(e){return{id:String(e.id),name:e.name||e.m_name}})),d(n)),e.next=7,(0,y.aD)();case 7:return(r=e.sent).success&&Array.isArray(r.data)&&(s=r.data.map((function(e){return{id:e.id,name:e.name,description:e.description}})),Z(s),console.log("formattedKbs",s)),e.next=11,(0,y.uG)();case 11:(i=e.sent).success&&(o=i.data.map((function(e){return{name:e.name,id:String(e.id)}})),H(o)),e.next=18;break;case 15:e.prev=15,e.t0=e.catch(0),I.ZP.error("获取数据失败");case 18:case"end":return e.stop()}}),e,null,[[0,15]])})));return function(){return e.apply(this,arguments)}}();e()}),[]),(0,v.useEffect)((function(){var e;if(t.params)switch(t.type){case"WISERAG":e=t.params,X.setFieldsValue({api_version:e.api_version,service_url:e.service_url,max_tokens:e.max_tokens,token_key:e.token_key,token_price:e.token_price});break;case"LLM":e=t.params,X.setFieldsValue({model_selection:e.llm_id,system_prompt:e.prompt});break;case"KNOWLEDGE_QA":e=t.params,X.setFieldsValue({system_prompt:e.system_prompt,knowledge_bases:e.knowledge_base_ids,model_selection:e.llm_id,app_system_prompt:e.prompt,prompt_retrieval:e.prompt_retrieval,rerank_id:e.rerank_id}),z(e.knowledge_base_ids||[]);break;case"OTHER":var n=t.params,r=Object.entries(n||{}).map((function(e){var t=l()(e,2);return{key:t[0],value:t[1]}}));X.setFieldsValue({otherParams:r})}}),[t.params,X]);var fe=function(){var e=i()(a()().mark((function e(){var n,r,i,o;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,X.validateFields();case 3:n=e.sent,e.t0=s,e.next="WISERAG"===e.t0?7:"LLM"===e.t0?9:"KNOWLEDGE_QA"===e.t0?11:"OTHER"===e.t0?13:15;break;case 7:return r={api_version:n.api_version,service_url:n.service_url,max_tokens:Number(n.max_tokens),token_key:n.token_key,token_price:n.token_price},e.abrupt("break",16);case 9:return r={llm_id:n.model_selection,prompt:n.system_prompt},e.abrupt("break",16);case 11:return r={prompt:n.app_system_prompt,prompt_retrieval:n.prompt_retrieval,system_prompt:n.system_prompt,knowledge_base_ids:n.knowledge_bases,llm_id:n.model_selection,rerank_id:n.rerank_id},e.abrupt("break",16);case 13:return r=(n.otherParams||[]).reduce((function(e,t){return t&&t.key&&(e[t.key]=t.value),e}),{}),e.abrupt("break",16);case 15:throw new Error("未知的应用类型");case 16:return i={type:s,params:r},e.next=19,(0,y.YS)(t.id,i);case 19:(o=e.sent).success?I.ZP.success("设置已保存"):I.ZP.error(o.message||"保存失败"),e.next=27;break;case 23:e.prev=23,e.t1=e.catch(0),console.error("Save settings failed:",e.t1),I.ZP.error("请检查表单填写是否完整");case 27:case"end":return e.stop()}}),e,null,[[0,23]])})));return function(){return e.apply(this,arguments)}}();return(0,F.jsxs)(S.Z,{title:"应用设置",bordered:!1,children:[(0,F.jsx)(L.Z,{name:"app_type",label:"应用类型",fieldProps:{value:s,onChange:o},options:[{label:b.WISERAG,value:"WISERAG"},{label:b.LLM,value:"LLM"},{label:b.KNOWLEDGE_QA,value:"KNOWLEDGE_QA"},{label:b.OTHER,value:"OTHER"}]}),(0,F.jsx)("div",{style:{border:"1px solid #d9d9d9",borderRadius:"8px",padding:"24px",marginBottom:"24px"},children:function(){switch(s){case"WISERAG":return(0,F.jsxs)(A.A,{form:X,layout:"vertical",submitter:!1,children:[(0,F.jsx)(R.Z,{name:"service_url",label:"服务地址",rules:[{required:!0,message:"请输入服务地址"}],width:"xl"}),(0,F.jsx)(R.Z,{name:"max_tokens",label:"最大token数",rules:[{required:!0,message:"请输入最大token数",type:"number",transform:function(e){return Number(e)}}],width:"xl"}),(0,F.jsx)(R.Z,{name:"token_key",label:"Token Key",rules:[{required:!0,message:"请输入 Token Key"}],width:"xl"}),(0,F.jsx)(R.Z,{name:"api_version",label:"API版本",rules:[{required:!1,message:"请输入API版本"}],width:"xl"})]});case"LLM":return(0,F.jsxs)(A.A,{form:X,layout:"vertical",submitter:!1,children:[(0,F.jsx)(L.Z,{name:"model_selection",label:"选择系统大模型",rules:[{required:!0,message:"请选择一个模型"}],options:p.map((function(e){return{label:e.name,value:e.id}})),width:"xl"}),(0,F.jsx)(N.Z,{name:"system_prompt",label:(0,F.jsxs)(h.Z,{children:["系统提示词",(0,F.jsx)(f.ZP,{type:"link",onClick:function(){me("system"),ue(!0)},children:"选择模板"})]}),rules:[{required:!0,message:"请输入系统提示词"}],width:"xl",fieldProps:{rows:10}})]});case"KNOWLEDGE_QA":return(0,F.jsxs)(A.A,{form:X,layout:"vertical",submitter:!1,children:[(0,F.jsx)(N.Z,{name:"system_prompt",label:(0,F.jsxs)(h.Z,{children:["系统提示词",(0,F.jsx)(f.ZP,{type:"link",onClick:function(){me("system"),ue(!0)},children:"选择模板"})]}),rules:[{required:!0,message:"请输入系统提示词"}],width:"xl",fieldProps:{rows:10}}),(0,F.jsx)(N.Z,{name:"app_system_prompt",label:(0,F.jsxs)(h.Z,{children:["应用系统信息提示词",(0,F.jsx)(f.ZP,{type:"link",onClick:function(){me("app"),ue(!0)},children:"选择模板"})]}),rules:[{required:!0,message:"请输入应用系统提示词"}],width:"xl",fieldProps:{rows:10}}),(0,F.jsx)(N.Z,{name:"prompt_retrieval",label:(0,F.jsxs)(h.Z,{children:["引用信息提示词模版",(0,F.jsx)(f.ZP,{type:"link",onClick:function(){me("retrieval"),ue(!0)},children:"选择模板"})]}),rules:[{required:!0,message:"请输入引用提示词"}],width:"xl",fieldProps:{rows:10}}),(0,F.jsx)(L.Z,{name:"knowledge_bases",label:(0,F.jsxs)(h.Z,{children:["选择知识库",(0,F.jsx)(C.Z,{onClick:function(){return k(!0)},style:{cursor:"pointer"}})]}),rules:[{required:!0,message:"请选择至少一个知识库"}],width:"xl",mode:"multiple",options:w.map((function(e){return{label:e.name,value:e.id}}))}),(0,F.jsx)(L.Z,{name:"model_selection",label:"选择大模型",rules:[{required:!0,message:"请选择一个模型"}],options:p.map((function(e){return{label:e.name,value:e.id}})),width:"xl"}),(0,F.jsx)(L.Z,{width:"xl",name:"rerank_id",label:"选择重排模型",options:T.map((function(e){return{label:e.name,value:e.id}})),rules:[{required:!0,message:"请选择重排模型!"}],tooltip:"用于文本重排的预训练模型",placeholder:"请选择重排模型",fieldProps:{loading:0===T.length}})]});case"OTHER":return(0,F.jsx)(A.A,{form:X,layout:"vertical",submitter:!1,initialValues:{otherParams:[{key:"",value:""}]},children:(0,F.jsx)(O.u,{name:"otherParams",label:"自定义参数",creatorButtonProps:{creatorButtonText:"添加参数"},copyIconProps:!1,deleteIconProps:{tooltipText:"删除此参数"},itemRender:function(e,t){var n,r=e.action,s=t.index;return(0,F.jsxs)(h.Z,{style:{display:"flex",marginBottom:8},align:"baseline",children:[(0,F.jsx)(L.Z,{name:"key",placeholder:"参数名 (Key)",rules:[{required:!0,message:"请输入参数名"},{validator:(n=i()(a()().mark((function e(t,n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(n&&Array.isArray(n)&&n.length>1)){e.next=2;break}return e.abrupt("return",Promise.reject(new Error("只能输入或选择一个参数名")));case 2:return e.abrupt("return",Promise.resolve());case 3:case"end":return e.stop()}}),e)}))),function(e,t){return n.apply(this,arguments)})}],showSearch:!0,allowClear:!0,width:"md",fieldProps:{mode:"tags",tokenSeparators:[","]},options:[{label:"模型ID",value:"MODEL_ID"},{label:"Embedding模型ID",value:"EMBEDDING_MODEL_ID"},{label:"服务地址",value:"SERVICE_URL"},{label:"API密钥",value:"API_KEY"},{label:"API接口",value:"API_URL"},{label:"最大Token数",value:"MAX_TOKENS"},{label:"温度系数",value:"TEMPERATURE"},{label:"知识库ID列表",value:"KNOWLEDGE_BASE_IDS"},{label:"重排模型ID",value:"RERANK_MODEL_ID"},{label:"系统提示词",value:"SYSTEM_PROMPT"}]}),(0,F.jsx)(R.Z,{name:"value",placeholder:"参数值 (Value)",rules:[{required:!0,message:"请输入参数值"}],width:"md"}),r," "]},s)}})});default:return null}}()}),(0,F.jsx)("div",{style:{textAlign:"right"},children:(0,F.jsx)(f.ZP,{type:"primary",onClick:fe,children:"保存设置"})}),(0,F.jsx)(D,{visible:g,onOk:function(e){X.setFieldsValue({knowledge_bases:e}),z(e),k(!1)},onCancel:function(){return k(!1)},availableKnowledgeBases:w,initialSelected:Q}),(0,F.jsx)(q,{visible:ee,onOk:function(e){var t=G.find((function(t){return t.id===e}));t&&X.setFieldsValue({system_prompt:t.content}),se(e),te(!1)},onCancel:function(){return te(!1)},templates:[],initialSelected:ae}),(0,F.jsx)(K,{visible:le,templates:"system"===de?M:"app"===de?B:W,onSelect:function(e){X.setFieldValue({system:"system_prompt",app:"app_system_prompt",retrieval:"prompt_retrieval"}[de],e),ue(!1)},onCancel:function(){return ue(!1)}})]})},V=function(e){e.appInfo;return(0,F.jsx)(S.Z,{title:"使用统计",bordered:!1})},Y=n(12453),Q=function(e){var t=e.appInfo,n=(0,v.useRef)();console.log("appInfo",t);var r=[{title:"对话时间",dataIndex:"created_at",valueType:"dateTime",search:!1},{title:"用户",dataIndex:"user_name",ellipsis:!0,search:!0},{title:"对话名称",dataIndex:"conversation_name",ellipsis:!0,search:!0},{title:"信息",dataIndex:"answer",search:!1,ellipsis:!0,render:function(e,t){return(0,F.jsx)(f.ZP,{type:"link",onClick:function(){E.Z.info({title:"对话详情",content:(0,F.jsxs)("div",{children:[(0,F.jsx)("h4",{children:"对话名称："}),(0,F.jsx)("p",{children:t.conversation_name}),(0,F.jsx)("h4",{children:"问题："}),(0,F.jsx)("p",{children:t.question}),(0,F.jsx)("h4",{children:"回答："}),(0,F.jsx)("p",{children:t.answer}),t.source_documents&&(0,F.jsxs)(F.Fragment,{children:[(0,F.jsx)("h4",{children:"参考来源："}),(0,F.jsx)("pre",{style:{maxHeight:"200px",overflow:"auto",whiteSpace:"pre-wrap",wordBreak:"break-all"},children:t.source_documents})]})]})})},children:"查看详情"})}}];return(0,F.jsx)(S.Z,{title:"对话记录",bordered:!1,children:(0,F.jsx)(Y.Z,{actionRef:n,columns:r,request:function(){var e=i()(a()().mark((function e(n){var r,s,i;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,y.tv)(t.app_info,null!==(r=n.current)&&void 0!==r?r:1,null!==(s=n.pageSize)&&void 0!==s?s:10,n.question);case 3:return i=e.sent,e.abrupt("return",{data:i.data||[],success:!0,total:i.total||0});case 7:return e.prev=7,e.t0=e.catch(0),I.ZP.error("获取对话记录失败"),e.abrupt("return",{data:[],success:!1,total:0});case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}(),rowKey:"id",search:{labelWidth:"auto"},pagination:{pageSize:10},dateFormatter:"string",headerTitle:"对话历史",toolBarRender:!1,size:"small"})})},z=function(e){e.appInfo;return(0,F.jsx)(S.Z,{title:"用户反馈",bordered:!1})},U=n(97857),X=n.n(U),J=function(e){var t=e.appInfo,n=(0,v.useRef)(),r=function(){var e=i()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:E.Z.confirm({title:"确认恢复",content:"确定要恢复到此版本吗？",okText:"确认",cancelText:"取消",onOk:function(){var e=i()(a()().mark((function e(){var s,i,o;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,s={type:r.type,params:r.params},e.next=4,(0,y.YS)(Number(t.id),s);case 4:(i=e.sent).success?(I.ZP.success("设置已恢复"),null===(o=n.current)||void 0===o||o.reload(),window.location.reload()):I.ZP.error(i.message||"恢复设置失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("Restore settings failed:",e.t0),I.ZP.error("恢复设置失败");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),s=[{title:"操作时间",dataIndex:"created_at",valueType:"dateTime",search:!1},{title:"操作人",dataIndex:"created_name",ellipsis:!0,search:!1},{title:"应用类型",dataIndex:"type",valueEnum:{WISERAG:{text:g.WISERAG},LLM:{text:g.LLM},KNOWLEDGE_QA:{text:g.KNOWLEDGE_QA}}},{title:"参数变更",dataIndex:"params",search:!1,ellipsis:!0,render:function(e,t){return(0,F.jsx)(f.ZP,{type:"link",onClick:function(){E.Z.info({title:"参数详情",content:(0,F.jsx)("pre",{style:{maxHeight:"400px",overflow:"auto",whiteSpace:"pre-wrap",wordBreak:"break-all"},children:JSON.stringify(t.params,null,2)}),width:600})},children:"查看详情"})}},{title:"操作",dataIndex:"action",valueType:"option",render:function(e,t){return[(0,F.jsx)(f.ZP,{type:"link",onClick:function(){return r(t)},children:"恢复"},"restore")]}}];return(0,F.jsx)(S.Z,{title:"修改记录",bordered:!1,children:(0,F.jsx)(Y.Z,{actionRef:n,columns:s,request:function(){var e=i()(a()().mark((function e(n){var r,s,i,o,l;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,y.xQ)(t.id,null!==(r=n.current)&&void 0!==r?r:1,null!==(s=n.pageSize)&&void 0!==s?s:10,n.operator,n.type);case 3:return o=e.sent,l=(null===(i=o.data)||void 0===i?void 0:i.map((function(e){var t,n;return X()(X()({},e),{},{app_info:null!==(t=e.app_info)&&void 0!==t?t:"",description:null!==(n=e.description)&&void 0!==n?n:""})})))||[],e.abrupt("return",{data:l,success:!0,total:o.total||0});case 8:return e.prev=8,e.t0=e.catch(0),I.ZP.error("获取历史记录失败"),e.abrupt("return",{data:[],success:!1,total:0});case 12:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(t){return e.apply(this,arguments)}}(),rowKey:"id",search:{labelWidth:"auto"},pagination:{pageSize:10},dateFormatter:"string",headerTitle:"设置历史",toolBarRender:!1,size:"small"})})},$=n(1413),ee=n(26554),te=n(91146),ne=function(e,t){return v.createElement(te.Z,(0,$.Z)((0,$.Z)({},e),{},{ref:t,icon:ee.Z}))};var re=v.forwardRef(ne),ae=(0,_.kc)((function(e){var t=e.token;return{main:{background:t.colorBgContainer,borderRadius:t.borderRadius,"& .ant-card":{borderRadius:0}},headerList:{margin:"20px 0"},moreInfo:{display:"flex",justifyContent:"space-between",width:"200px"}}})),se=function(){var e,t=ae().styles,n=(0,k.useSearchParams)(),r=l()(n,1)[0].get("id")||"",s=(0,v.useState)(!0),o=l()(s,2),b=o[0],_=o[1],j=(0,v.useState)(null),Z=l()(j,2),P=Z[0],E=Z[1],S=(0,v.useState)("settings"),I=l()(S,2),C=I[0],T=I[1],A=(0,v.useState)({settings:(0,F.jsxs)("div",{children:[" ",(0,F.jsx)(p.Z,{active:!0})]}),statistics:(0,F.jsxs)("div",{children:[" ",(0,F.jsx)(p.Z,{active:!0})]}),chatLogs:(0,F.jsxs)("div",{children:[" ",(0,F.jsx)(p.Z,{active:!0})]}),feedback:(0,F.jsxs)("div",{children:[" ",(0,F.jsx)(p.Z,{active:!0})]})}),R=l()(A,2),L=R[0],N=R[1],O=function(){var e=i()(a()().mark((function e(){var t;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("Fetching app info, app:",r),!r){e.next=19;break}return e.prev=2,_(!0),e.next=6,(0,y.BN)(r);case 6:t=e.sent,console.log("Fetched data:",t),t.success?(E(t.data),N({settings:(0,F.jsx)(H,{appInfo:t.data}),statistics:(0,F.jsx)(V,{appInfo:t.data}),chatLogs:(0,F.jsx)(Q,{appInfo:t.data}),feedback:(0,F.jsx)(z,{appInfo:t.data}),logs:(0,F.jsx)(J,{appInfo:t.data})})):(console.error("数据获取失败"),E(null)),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(2),console.error("加载文件失败:",e.t0);case 14:return e.prev=14,_(!1),e.finish(14);case 17:e.next=20;break;case 19:_(!1);case 20:case"end":return e.stop()}}),e,null,[[2,11,14,17]])})));return function(){return e.apply(this,arguments)}}();(0,v.useEffect)((function(){console.log("App changed:",r),O()}),[r]);var G=(0,F.jsxs)(d.Z,{className:t.headerList,size:"small",column:2,children:[(0,F.jsx)(d.Z.Item,{label:"应用描述",children:(null==P?void 0:P.description)||"-"}),(0,F.jsx)(d.Z.Item,{label:"应用类型",children:g[null==P?void 0:P.type]||(null==P?void 0:P.type)||"-"}),(0,F.jsx)(d.Z.Item,{label:"创建时间",children:null!=P&&P.created_at?w()(P.created_at).format("YYYY-MM-DD HH:mm:ss"):"-"}),(0,F.jsx)(d.Z.Item,{label:"标签",children:(null==P||null===(e=P.tags)||void 0===e?void 0:e.map((function(e){return(0,F.jsx)(m.Z,{color:"purple",style:{marginRight:4},children:e},e)})))||"-"})]}),q=(0,F.jsx)("div",{className:t.moreInfo}),D=(0,F.jsx)(f.ZP,{type:"primary",onClick:function(){return k.history.push("/admin/system-app-settings")},children:"返回系统应用"});return(0,F.jsx)(u._z,{title:(0,F.jsxs)(h.Z,{children:[(0,F.jsxs)(x.Z.Link,{onClick:function(){return k.history.push("/admin/system-app-settings")},children:[(0,F.jsx)(re,{})," 返回列表"]}),(null==P?void 0:P.name)+"("+(null==P?void 0:P.app_info)+")"||0]}),extra:D,content:b?(0,F.jsx)("div",{children:"加载中..."}):G,extraContent:b?(0,F.jsx)("div",{children:"加载中..."}):q,tabActiveKey:C,onTabChange:function(e){T(e)},tabList:[{key:"settings",tab:"设置"},{key:"statistics",tab:"统计"},{key:"chatLogs",tab:"对话日志"},{key:"feedback",tab:"反馈"},{key:"logs",tab:"版本管理"}],children:(0,F.jsx)(c.f,{children:(0,F.jsx)("div",{className:t.main,children:L[C]})})})}},69992:function(e,t,n){n.d(t,{BN:function(){return l},C3:function(){return w},VB:function(){return f},YS:function(){return d},aD:function(){return y},j6:function(){return P},tv:function(){return _},uG:function(){return x},vQ:function(){return c},xQ:function(){return b}});var r=n(15009),a=n.n(r),s=n(99289),i=n.n(s),o=n(78158);function l(e){return u.apply(this,arguments)}function u(){return(u=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/system-app-info/".concat(t),{method:"GET",id:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function c(e){return p.apply(this,arguments)}function p(){return(p=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/system_app_settings",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function d(e,t){return m.apply(this,arguments)}function m(){return(m=i()(a()().mark((function e(t,n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/system-app-settings/".concat(t),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function f(){return h.apply(this,arguments)}function h(){return(h=i()(a()().mark((function e(){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api//system_app_settings/llms",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function x(){return v.apply(this,arguments)}function v(){return(v=i()(a()().mark((function e(){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/rerankModelList",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function y(){return g.apply(this,arguments)}function g(){return(g=i()(a()().mark((function e(){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/system_app_settings/knowledge_bases",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function b(e,t,n,r,a){return k.apply(this,arguments)}function k(){return(k=i()(a()().mark((function e(t,n,r,s,i){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/system-app-setting/logs",{method:"GET",params:{app_id:t,current:n,pageSize:r,operator:s,type:i}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function _(e,t,n,r){return j.apply(this,arguments)}function j(){return(j=i()(a()().mark((function e(t,n,r,s){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/system-app-setting/chat",{method:"GET",params:{app_info:t,current:n,pageSize:r,question:s}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function w(e){return Z.apply(this,arguments)}function Z(){return(Z=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/system_app_settings",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function P(e){return E.apply(this,arguments)}function E(){return(E=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/system_app_settings/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}}}]);