"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4901],{84901:function(e,t,n){n.d(t,{Z:function(){return J}});var r=n(97857),i=n.n(r),o=n(5574),l=n.n(o),a=n(88310),c=n(17598),s=n(15525),d=n(19050),x=n(19669),p=n(42075),u=n(83062),f=n(83622),m=n(34041),h=n(85265),g=n(2487),v=n(67294),y=n(15009),j=n.n(y),b=n(99289),k=n.n(b),w=n(13769),T=n.n(w),S=n(27484),Z=n.n(S),H=n(9783),L=n.n(H),I=n(28846),C=(0,I.kc)((function(e){var t=e.token;return{description:{maxWidth:"720px",lineHeight:"22px"},title:{},extra:L()({marginTop:"16px",color:t.colorTextSecondary,lineHeight:"22px","& > em":{marginLeft:"16px",color:t.colorTextDisabled,fontStyle:"normal"}},"@media screen and (max-width: ".concat(t.screenXS,"px)"),{"& > em":{display:"block",marginTop:"8px",marginLeft:"0"}})}})),D=n(90389),_=n(10048),E=n(22424),M=n(58258),Y=n(2453),R=n(71471),P=n(74330),A=n(85893),N=["globalExpanded"],W=(0,_.Z)({html:!0,breaks:!0,typographer:!0}),z=function(e){(0,v.useRef)(""),(0,v.useRef)("");var t=(0,v.useState)(),n=l()(t,2),r=(n[0],n[1],(0,v.useState)()),i=l()(r,2),o=(i[0],i[1],C().styles),a=e.globalExpanded,c=T()(e,N),s=v.useState(!1),d=l()(s,2),x=d[0],p=d[1];v.useEffect((function(){p(a||!1)}),[a]);var f=c.a+" "+c.q,m=(0,v.useState)(!1),g=l()(m,2),y=g[0],b=g[1],w=(0,v.useState)(null),S=l()(w,2),H=S[0],L=(S[1],(0,v.useState)("")),I=l()(L,2),_=I[0],z=(I[1],(0,v.useState)(!1)),F=l()(z,2),q=F[0],B=(F[1],(0,v.useState)("")),O=l()(B,2),U=O[0],X=(O[1],(0,v.useState)("")),K=l()(X,2),G=K[0],V=(K[1],function(){var e=k()(j()().mark((function e(t){var n,r,i,o,l;return j()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("🚀 ~ handlePdfHighlight ~ item:",t),n=(0,M.E4)(t.sourceId),e.next=4,fetch(n);case 4:return r=e.sent,console.log("🚀 ~ handlePdfHighlight ~ response:",r),e.next=8,r.blob();case 8:i=e.sent,o=URL.createObjectURL(i),(l=document.createElement("a")).href=o,l.download="file_preview.pdf",document.body.appendChild(l),l.click(),document.body.removeChild(l);case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),J=function(){var e=k()(j()().mark((function e(){return j()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(H){e.next=2;break}return e.abrupt("return");case 2:try{window.open("/api/documents/download?path=".concat(encodeURIComponent(H.source_name)),"_blank")}catch(e){console.error("下载文件出错：",e),Y.ZP.error("下载文件失败")}case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return(0,A.jsxs)(A.Fragment,{children:[(0,A.jsxs)("div",{id:"knowledge-".concat(c.id),children:[(0,A.jsxs)("span",{style:{fontSize:"14px",fontWeight:"bold",textOverflow:"ellipsis",overflow:"hidden"},children:[(0,A.jsx)(D.Z,{style:{marginRight:"8px"}}),c.source_name]}),(0,A.jsxs)("div",{className:o.description,style:{marginTop:"16px"},children:[(0,A.jsx)("div",{style:{maxHeight:x?"none":60,overflow:"hidden",lineHeight:1.6},dangerouslySetInnerHTML:{__html:E.Z.sanitize(W.render(f),{ALLOWED_TAGS:["a","p","strong","em","ul","ol","li","code","pre","blockquote"],ALLOWED_ATTR:["href","target"]})}}),(0,A.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,A.jsx)("a",{onClick:function(){return p(!x)},style:{cursor:"pointer",color:"#1890ff"},children:x?"收起":"展开"}),(0,A.jsx)(u.Z,{title:"特朗普宣布赢得总统选举当地时间11月8日凌晨，美国共和党总统候选人、前总统特朗普在佛里德里亚棕榈会议中心发表讲话，宣布在2024年总统选举中获胜。#### 发表“胜选”讲话；将停止战争讲解查看PDF",children:(0,A.jsx)("a",{href:"#",style:{cursor:"pointer",color:"#1890ff"},children:"1"})}),(0,A.jsx)("a",{onClick:function(e){e.preventDefault(),V(c)},style:{cursor:"pointer",color:"#1890ff"},children:"查看原文"})]})]}),(0,A.jsx)("div",{className:o.extra,children:c.updatedAt&&(0,A.jsxs)("em",{children:[" ",Z()(c.updatedAt).format("YYYY-MM-DD HH:mm")]})})]}),(0,A.jsx)(h.Z,{title:"原文内容",placement:"right",width:800,height:"100%",onClose:function(){return b(!1)},open:y,children:H&&(0,A.jsxs)("div",{style:{height:"calc(100vh - 108px)",padding:"0"},children:[(0,A.jsx)(R.Z.Title,{level:4,style:{padding:"0 16px 16px"},children:H.source_name||"文档标题"}),q?(0,A.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"80%"},children:(0,A.jsx)(P.Z,{tip:"文档加载中..."})}):(0,A.jsxs)("div",{style:{height:"calc(100% - 60px)",overflow:"auto"},children:["pdf"===_&&(0,A.jsx)("iframe",{src:"/api/documents/view?path=".concat(encodeURIComponent(H.source_name)),style:{width:"100%",height:"100%",border:"none"},title:"PDF文档查看器"}),("docx"===_||"pptx"===_)&&(0,A.jsx)("iframe",{src:"/api/documents/preview?path=".concat(encodeURIComponent(H.source_name)),style:{width:"100%",height:"100%",border:"none"},title:"Office文档查看器"}),"xlsx"===_&&U&&(0,A.jsx)("div",{dangerouslySetInnerHTML:{__html:U},style:{padding:"0 16px"}}),("txt"===_||"md"===_||"html"===_)&&G&&(0,A.jsx)("div",{dangerouslySetInnerHTML:{__html:E.Z.sanitize(W.render(G))},style:{padding:"0 16px"}}),!_&&(0,A.jsx)("div",{style:{textAlign:"center",marginTop:"80px",fontSize:"16px"},children:"该文档暂不支持预览，请下载后查看"})]}),(0,A.jsx)("div",{style:{padding:"16px",borderTop:"1px solid #f0f0f0",marginTop:"16px"},children:(0,A.jsx)("a",{onClick:function(e){e.preventDefault(),J()},style:{cursor:"pointer",color:"#1890ff"},children:"下载原文"})})]})})]})},F=(0,I.kc)((function(e){var t=e.token;return{description:{maxWidth:"720px",lineHeight:"22px"},extra:L()({marginTop:"16px",color:t.colorTextSecondary,lineHeight:"22px","& > em":{marginLeft:"16px",color:t.colorTextDisabled,fontStyle:"normal"}},"@media screen and (max-width: ".concat(t.screenXS,"px)"),{"& > em":{display:"block",marginTop:"8px",marginLeft:"0"}})}})),q=function(e){var t=e.data,n=t.content,r=t.updatedAt,i=t.name,o=t.id,l=F().styles;return(0,A.jsxs)("div",{children:[(0,A.jsx)("div",{className:l.description,children:n}),(0,A.jsxs)("div",{className:l.extra,children:["来源知识库： ",(0,A.jsx)("a",{href:"/api/knowledge/".concat(o),children:i}),(0,A.jsx)("em",{children:Z()(r).format("YYYY-MM-DD HH:mm")})]})]})},B=(0,I.kc)((function(e){var t=e.token;return{description:{maxWidth:"720px",lineHeight:"22px"},extra:L()({marginTop:"16px",color:t.colorTextSecondary,lineHeight:"22px","& > em":{marginLeft:"16px",color:t.colorTextDisabled,fontStyle:"normal"}},"@media screen and (max-width: ".concat(t.screenXS,"px)"),{"& > em":{display:"block",marginTop:"8px",marginLeft:"0"}})}})),O=function(e){var t=e.data,n=t.content,r=t.updatedAt,i=t.name,o=t.id,l=B().styles;return(0,A.jsxs)("div",{children:[(0,A.jsx)("div",{className:l.description,children:n}),(0,A.jsxs)("div",{className:l.extra,children:["来源知识库： ",(0,A.jsx)("a",{href:"/api/knowledge/".concat(o),children:i}),(0,A.jsx)("em",{children:Z()(r).format("YYYY-MM-DD HH:mm")})]})]})},U=(0,I.kc)((function(e){var t=e.token;return{description:{maxWidth:"720px",lineHeight:"22px"},extra:L()({marginTop:"16px",color:t.colorTextSecondary,lineHeight:"22px","& > em":{marginLeft:"16px",color:t.colorTextDisabled,fontStyle:"normal"}},"@media screen and (max-width: ".concat(t.screenXS,"px)"),{"& > em":{display:"block",marginTop:"8px",marginLeft:"0"}})}})),X=function(e){var t=e.data,n=t.content,r=t.updatedAt,i=t.name,o=t.id,l=U().styles;return(0,A.jsxs)("div",{children:[(0,A.jsx)("div",{className:l.description,children:n}),(0,A.jsxs)("div",{className:l.extra,children:["来源知识库： ",(0,A.jsx)("a",{href:"/api/knowledge/".concat(o),children:i}),(0,A.jsx)("em",{children:Z()(r).format("YYYY-MM-DD HH:mm")})]})]})},K=(0,I.kc)((function(e){var t=e.token;return{referenceList:{".ant-list-item:first-child":{paddingTop:"0"}},card:{listItemMetaTitle:{color:t.colorTextHeading},".ant-card-meta-title":{marginBottom:"4px","& > a":{display:"inline-block",maxWidth:"100%",color:t.colorTextHeading}},".ant-card-meta-description":{height:"44px",overflow:"hidden",lineHeight:"22px"},"&:hover":{".ant-card-meta-title > a":{color:t.colorPrimary}}},cardItemContent:{display:"flex",height:"20px",marginTop:"16px",marginBottom:"-4px",lineHeight:"20px","& > span":{flex:"1",color:t.colorTextSecondary,fontSize:"12px"}},avatarList:{flex:"0 1 auto"},cardList:{marginTop:"24px"},coverCardList:{".ant-list .ant-list-item-content-single":{maxWidth:"100%"}},toolbar:{boxShadow:"0 1px 4px rgba(0,0,0,0.1)"},listItem:{transition:"all 0.3s ease"}}})),G=n(11488),V=(0,v.forwardRef)((function(e,t){var n=e.messages,r=K().styles,o=(0,v.useState)([]),y=l()(o,2),j=y[0],b=y[1],k=(0,v.useState)(""),w=l()(k,2),T=w[0],S=w[1],Z=(0,v.useState)(!1),H=l()(Z,2),L=H[0],I=H[1];(0,v.useEffect)((function(){if(n&&n.length>0){var e=(0,G.P)(n);b(e)}else b([])}),[n]);var C=v.useState(!1),D=l()(C,2),_=D[0],E=D[1],M=v.useState("all"),Y=l()(M,2),R=Y[0],P=Y[1],N=(null==j?void 0:j.filter((function(e){var t="all"===R||e.type===R,n=!T||e.messageId===T;return t&&n})))||[],W=v.useRef(null),F=v.useRef(new Map),B=function(e){console.log("scrollToItem--",e);var t=document.getElementById("content-".concat(e));t&&t.scrollIntoView({behavior:"smooth",block:"center"})};return(0,v.useImperativeHandle)(t,(function(){return{scrollToItem:B,updateReferenceList:function(e){b(e)},filterByMessageId:function(e){S(e)},clearFilter:function(){S("")},getFilterMessageId:function(){return T}}})),(0,A.jsxs)("div",{children:[(0,A.jsxs)("div",{style:{position:"sticky",top:0,padding:"8px 12px 8px 12px",zIndex:1,borderBottom:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,A.jsxs)(p.Z,{children:[(0,A.jsx)(u.Z,{title:"展开全部",mouseEnterDelay:2,children:(0,A.jsx)(f.ZP,{type:"text",icon:(0,A.jsx)(a.Z,{}),onClick:function(){return E(!0)}})}),(0,A.jsx)(u.Z,{title:"收起全部",mouseEnterDelay:2,children:(0,A.jsx)(f.ZP,{type:"text",icon:(0,A.jsx)(c.Z,{}),onClick:function(){return E(!1)}})}),(0,A.jsx)(u.Z,{title:"滚动到顶部",mouseEnterDelay:2,children:(0,A.jsx)(f.ZP,{type:"text",icon:(0,A.jsx)(s.Z,{}),onClick:function(){W.current&&W.current.scrollTo({top:0,behavior:"smooth"})}})}),(0,A.jsx)(u.Z,{title:"滚动到底部",mouseEnterDelay:2,children:(0,A.jsx)(f.ZP,{type:"text",icon:(0,A.jsx)(d.Z,{}),onClick:function(){W.current&&W.current.scrollTo({top:W.current.scrollHeight,behavior:"smooth"})}})}),(0,A.jsx)(u.Z,{title:"关联视图",mouseEnterDelay:2,children:(0,A.jsx)(f.ZP,{type:"text",onClick:function(){I(!0)},children:"关联视图"})}),T&&(0,A.jsx)(u.Z,{title:"清除筛选",mouseEnterDelay:2,children:(0,A.jsx)(f.ZP,{type:"primary",icon:(0,A.jsx)(x.Z,{}),onClick:function(){return S("")}})})]}),(0,A.jsxs)(p.Z,{children:[(0,A.jsx)("span",{style:{color:"#666"},children:N.length}),(0,A.jsx)(m.default,{style:{width:120},value:R,onChange:P,options:[{label:"全部",value:"all"},{label:"知识库",value:"knowledge"},{label:"图表数据",value:"chart"},{label:"文本",value:"text"},{label:"文件",value:"file"}]})]})]}),(0,A.jsx)(h.Z,{title:"关联视图",placement:"right",onClose:function(){I(!1)},visible:L,children:"111"}),(0,A.jsx)("div",{ref:W,style:{height:"calc(100vh - 130px)",overflowY:"auto"},children:(0,A.jsx)(g.Z,{size:"large",className:r.referenceList,rowKey:"id",itemLayout:"vertical",dataSource:N,renderItem:function(e){var t;return(0,A.jsxs)(g.Z.Item,{ref:function(t){var n,r;t?F.current.set(null===(n=e.reference)||void 0===n?void 0:n.id,t):F.current.delete(null===(r=e.reference)||void 0===r?void 0:r.id)},children:[(0,A.jsx)(g.Z.Item.Meta,{}),"knowledge"===e.type&&(0,A.jsx)(z,i()(i()({},e.reference),{},{globalExpanded:_})),"chart"===e.type&&(0,A.jsx)(q,i()({},e.reference)),"text"===e.type&&(0,A.jsx)(O,i()({},e.reference)),"file"===e.type&&(0,A.jsx)(X,i()({},e.reference))]},null===(t=e.reference)||void 0===t?void 0:t.id)}})})]})}));V.displayName="Reference";var J=V},11488:function(e,t,n){function r(e){console.log("🚀 ~ extractKnowledgeReferenceListFromAIChat ~ modules-引用:",e);var t=e.find((function(e){return"AI 对话"===e.moduleName&&e.quoteList}));return console.log("🚀 ~ extractKnowledgeReferenceListFromAIChat ~ aiChatModule:",t),t&&t.quoteList?t.quoteList.map((function(e){return{id:e.id,type:"knowledge",reference:e}})):[]}function i(e){if(e){var t=[];return e.forEach((function(e){e.references&&(t=t.concat(e.references.map((function(t){return t.id=e.message_id+"-"+t.id,t.messageId=e.message_id,t}))))})),t}return[]}n.d(t,{P:function(){return i},n:function(){return r}})}}]);