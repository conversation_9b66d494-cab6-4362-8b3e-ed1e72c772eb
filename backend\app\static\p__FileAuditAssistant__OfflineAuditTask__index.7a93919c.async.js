"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2600],{65939:function(e,t,n){n.r(t);var r=n(52677),s=n.n(r),a=n(19632),i=n.n(a),c=n(97857),o=n.n(c),l=n(15009),u=n.n(l),d=n(99289),p=n.n(d),x=n(5574),f=n.n(x),h=n(67294),j=n(26058),g=n(71471),m=n(55102),b=n(11550),v=n(42119),y=n(11941),k=n(8232),Z=n(2453),w=n(4393),_=n(38925),T=n(42075),P=n(83622),S=n(34041),C=n(66309),z=n(2487),B=n(74330),O=n(32983),N=n(17788),E=n(8751),I=n(33914),R=n(15360),F=n(43471),W=n(82826),A=n(97175),D=n(49354),L=n(4161),J=n(45699),G=n(97245),q=n(82061),M=n(66212),K=n(18429),U=n(74842),V=n(47389),H=n(10981),Q=n(96974),X=n(97131),Y=n(88372),$=n(34994),ee=n(74573),te=n(85893),ne=(j.Z.Content,g.Z.Title,g.Z.Text,g.Z.Paragraph,m.Z.TextArea,b.Z.Dragger),re=v.Z.Step,se=y.Z.TabPane,ae={pending_upload:"default",analyzing:"processing",completed:"success",failed:"error"},ie={pending_upload:"待上传文件",analyzing:"分析中",completed:"已完成",failed:"失败"},ce="default",oe="processing",le="success",ue="error";t.default=function(){var e,t=(0,Q.TH)(),n=(0,Q.s0)(),r=(0,h.useState)(null),a=f()(r,2),c=a[0],l=a[1],d=(0,h.useState)([]),x=f()(d,2),j=x[0],de=x[1],pe=k.Z.useForm(),xe=(f()(pe,1)[0],(0,h.useState)("uploaded_at")),fe=f()(xe,2),he=fe[0],je=fe[1],ge=(0,h.useState)("desc"),me=f()(ge,2),be=me[0],ve=me[1],ye=(0,h.useState)([]),ke=f()(ye,2),Ze=ke[0],we=ke[1],_e=(0,h.useState)(!0),Te=f()(_e,2),Pe=(Te[0],Te[1]),Se=(0,h.useState)(""),Ce=f()(Se,2),ze=Ce[0],Be=Ce[1],Oe=(0,h.useState)(""),Ne=f()(Oe,2),Ee=Ne[0],Ie=Ne[1],Re=(0,h.useState)("results"),Fe=f()(Re,2),We=Fe[0],Ae=Fe[1],De=(0,h.useState)(!0),Le=f()(De,2),Je=Le[0],Ge=Le[1],qe=(0,h.useState)(null),Me=f()(qe,2),Ke=Me[0],Ue=Me[1],Ve=(0,h.useState)(!1),He=f()(Ve,2),Qe=He[0],Xe=He[1],Ye=(0,h.useState)(""),$e=f()(Ye,2),et=$e[0],tt=$e[1],nt=(0,h.useState)(""),rt=f()(nt,2),st=rt[0],at=rt[1],it=(0,h.useState)(!1),ct=f()(it,2),ot=ct[0],lt=ct[1],ut=(0,h.useState)([]),dt=f()(ut,2),pt=dt[0],xt=dt[1],ft=(0,h.useState)(!1),ht=f()(ft,2),jt=ht[0],gt=ht[1],mt={name:"file",multiple:!0,progress:{strokeColor:{"0%":"#108ee9","100%":"#87d068"}},maxCount:1e3,fileList:pt,beforeUpload:function(e){return![".pdf",".docx",".doc",".csv",".xls",".xlsx",".json",".txt",".jpg",".jpeg",".png"].some((function(t){return e.name.toLowerCase().endsWith(t)}))&&(Z.ZP.error("仅支持PDF、Word、CSV、Excel、JSON和图片格式的文件"),b.Z.LIST_IGNORE)},onChange:function(e){xt(e.fileList),e.fileList.length>50&&Z.ZP.warning("最多只能上传50个文件")},onDrop:function(e){console.log("拖拽文件",e.dataTransfer.files)}},bt=function(){var e=p()(u()().mark((function e(){var t,n;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,Pe(!0),e.next=4,(0,ee.s6)();case 4:(t=e.sent).success&&t.data?(n=t.data.filter((function(e){return e.is_active})),we(n)):(Z.ZP.error(t.error||"获取任务类型列表失败"),we([])),e.next=13;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("获取任务类型列表失败:",e.t0),Z.ZP.error("获取任务类型列表失败，请检查网络连接"),we([]);case 13:return e.prev=13,Pe(!1),e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[0,8,13,16]])})));return function(){return e.apply(this,arguments)}}(),vt=function(){var e=p()(u()().mark((function e(t){var n;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,Ge(!0),e.next=4,(0,ee.yW)(t);case 4:if(!(n=e.sent).success||!n.data){e.next=17;break}if(l(n.data),Be(n.data.name),Ie(n.data.type),!n.data.files){e.next=13;break}de(n.data.files),e.next=15;break;case 13:return e.next=15,yt(t);case 15:e.next=18;break;case 17:Z.ZP.error(n.error||"获取任务详情失败");case 18:e.next=24;break;case 20:e.prev=20,e.t0=e.catch(0),console.error("获取任务详情失败:",e.t0),Z.ZP.error("获取任务详情失败，请检查网络连接");case 24:return e.prev=24,Ge(!1),e.finish(24);case 27:case"end":return e.stop()}}),e,null,[[0,20,24,27]])})));return function(t){return e.apply(this,arguments)}}(),yt=function(){var e=p()(u()().mark((function e(t){var n;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,ee.Sq)(t);case 3:(n=e.sent).success&&n.data?de(n.data):Z.ZP.error(n.error||"获取任务文件列表失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取任务文件列表失败:",e.t0),Z.ZP.error("获取任务文件列表失败，请检查网络连接");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}(),kt=function(){var e=p()(u()().mark((function e(){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Ke){e.next=3;break}return Z.ZP.warning("无法识别当前任务ID，请刷新页面重试"),e.abrupt("return");case 3:if(0!==j.length){e.next=6;break}return Z.ZP.warning("请先上传文件"),e.abrupt("return");case 6:return e.prev=6,Z.ZP.loading({content:"正在启动任务...",key:"startTask"}),e.next=10,fetch("/api/chat/v1/completions",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat((0,H.bW)())},body:JSON.stringify({taskId:Ke,files:j.map((function(e){return{id:e.id,name:e.name,size:e.size,data_type:e.data_type,status:e.status}}))})});case 10:if(!e.sent.ok){e.next=20;break}return Z.ZP.success({content:"任务已启动，系统正在分析文件...",key:"startTask",icon:(0,te.jsx)(E.Z,{style:{color:"#52c41a"}}),duration:3}),c&&l(o()(o()({},c),{},{status:"analyzing"})),Ae("status"),e.next=17,vt(Ke);case 17:setTimeout(p()(u()().mark((function e(){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,vt(Ke);case 2:case"end":return e.stop()}}),e)}))),5e3),e.next=21;break;case 20:Z.ZP.error({content:"启动任务失败",key:"startTask",duration:3});case 21:e.next=27;break;case 23:e.prev=23,e.t0=e.catch(6),console.error("启动任务失败:",e.t0),Z.ZP.error({content:"启动任务失败，请检查网络连接",key:"startTask",duration:3});case 27:case"end":return e.stop()}}),e,null,[[6,23]])})));return function(){return e.apply(this,arguments)}}(),Zt=function(){var e=p()(u()().mark((function e(){var t;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Ke){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,lt(!0),e.next=6,(0,ee.xJ)(Ke,{name:et,type:st});case 6:(t=e.sent).success?(Z.ZP.success("任务信息更新成功"),c&&l(o()(o()({},c),{},{name:et,type:st})),Be(et),Ie(st),Xe(!1)):Z.ZP.error(t.error||"更新任务信息失败"),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(2),console.error("更新任务信息失败:",e.t0),Z.ZP.error("更新任务信息失败，请检查网络连接");case 14:return e.prev=14,lt(!1),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[2,10,14,17]])})));return function(){return e.apply(this,arguments)}}();(0,h.useEffect)((function(){bt();var e=new URLSearchParams(t.search).get("task");e?(Ue(e),vt(e)):Ge(!1)}),[t]),(0,h.useEffect)((function(){"status"===We&&Ke&&yt(Ke)}),[We,Ke]);var wt,_t,Tt,Pt,St=function(){var e=p()(u()().mark((function e(){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Ke){e.next=3;break}return Z.ZP.warning("无法识别当前任务ID，请刷新页面重试"),e.abrupt("return");case 3:return Z.ZP.loading({content:"正在刷新任务状态...",key:"refreshTask"}),e.prev=4,e.next=7,vt(Ke);case 7:Z.ZP.success({content:"刷新成功",key:"refreshTask"}),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(4),Z.ZP.error({content:"刷新失败，请重试",key:"refreshTask"});case 13:case"end":return e.stop()}}),e,null,[[4,10]])})));return function(){return e.apply(this,arguments)}}(),Ct=function(){var e=p()(u()().mark((function e(){var t,n,r,s;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Ke){e.next=3;break}return Z.ZP.warning("无法识别当前任务ID，请刷新页面重试"),e.abrupt("return");case 3:if(0!==pt.length){e.next=6;break}return Z.ZP.warning("请选择要上传的文件"),e.abrupt("return");case 6:return e.prev=6,gt(!0),(t=new FormData).append("taskId",Ke),pt.forEach((function(e){e.originFileObj&&t.append("files",e.originFileObj)})),n=(0,H.bW)(),e.next=14,fetch("/api/auditTask/upload_task_file",{method:"POST",headers:{Authorization:"Bearer ".concat(n)},body:t});case 14:return r=e.sent,e.next=17,r.json();case 17:if(!(s=e.sent).success){e.next=27;break}if(Z.ZP.success({content:"文件上传成功，请点击页面顶部【开始分析】按钮启动任务",duration:5}),!Ke){e.next=23;break}return e.next=23,vt(Ke);case 23:xt([]),Ae("status"),e.next=28;break;case 27:Z.ZP.error(s.error||"文件上传失败");case 28:e.next=34;break;case 30:e.prev=30,e.t0=e.catch(6),console.error("上传文件过程中发生错误:",e.t0),Z.ZP.error("上传文件失败，请检查网络连接");case 34:return e.prev=34,gt(!1),e.finish(34);case 37:case"end":return e.stop()}}),e,null,[[6,30,34,37]])})));return function(){return e.apply(this,arguments)}}(),zt=function(){var e=p()(u()().mark((function e(t){var n,r;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,c&&"pending_upload"===c.status){e.next=4;break}return Z.ZP.warning("只有在待上传状态下才能删除文件"),e.abrupt("return");case 4:return e.next=6,fetch("/api/auditTask/delete_task_file/".concat(t),{method:"DELETE",headers:{Authorization:"Bearer ".concat((0,H.bW)())}});case 6:return n=e.sent,e.next=9,n.json();case 9:(r=e.sent).success?(de(j.filter((function(e){return e.id!==t}))),Z.ZP.success("文件删除成功"),j.length<=1&&c&&l(o()(o()({},c),{},{file_count:0}))):Z.ZP.error(r.error||"删除文件失败"),e.next=17;break;case 13:e.prev=13,e.t0=e.catch(0),console.error("删除文件时出错:",e.t0),Z.ZP.error("删除文件失败，请检查网络连接");case 17:case"end":return e.stop()}}),e,null,[[0,13]])})));return function(t){return e.apply(this,arguments)}}(),Bt=function(e){return{scene_1:"场景1: 价格一致性检查",scene_2:"场景2: 坐落位置一致性检查",scene_3:"场景3: 房产证号一致性检查",scene_4:"场景4: 建筑面积一致性检查",scene_5:"场景5: 使用年限一致性检查",scene_6:"场景6: 用途一致性检查",scene_7:"场景7: 抵押信息一致性检查",scene_8:"场景8: 租赁信息完整性检查",scene_9:"场景9: 决策文件检查"}[e]||"场景".concat(e.split("_")[1],": 未知检查项")};return(0,te.jsxs)(X._z,{title:(null==c?void 0:c.name)||"任务详情",onBack:function(){return n("/fileAuditAssistant/offlineAuditTaskList")},content:(0,te.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,te.jsxs)(T.Z,{children:[(null==c?void 0:c.status)&&(0,te.jsx)(C.Z,{color:ae[c.status],children:ie[c.status]}),(null==c?void 0:c.type)&&(0,te.jsx)(C.Z,{color:"blue",children:(null===(e=Ze.find((function(e){return e.code===c.type})))||void 0===e?void 0:e.name)||c.type})]}),(0,te.jsx)("div",{style:{fontSize:"12px"},children:null==c?void 0:c.description})]}),header:{backIcon:(0,te.jsx)(W.Z,{})},tabActiveKey:We,onTabChange:Ae,tabList:[{key:"results",tab:(0,te.jsxs)("span",{children:[(0,te.jsx)(E.Z,{}),"审核结果"]})},{key:"status",tab:(0,te.jsxs)("span",{children:[(0,te.jsx)(M.Z,{}),"执行状态"]})},{key:"upload",tab:(0,te.jsxs)("span",{children:[(0,te.jsx)(I.Z,{}),"上传文件"]})}],extra:[(0,te.jsx)(P.ZP,{type:"link",icon:(0,te.jsx)(F.Z,{}),onClick:St,size:"small",children:"刷新状态"},"refresh"),(0,te.jsx)(P.ZP,{type:"link",icon:(0,te.jsx)(U.Z,{}),onClick:kt,disabled:!c||"pending_upload"!==c.status,style:{color:"pending_upload"===(null==c?void 0:c.status)&&j.length>0?"#52c41a":void 0,fontWeight:"pending_upload"===(null==c?void 0:c.status)&&j.length>0?"bold":void 0},title:"启动任务后，系统将开始分析文件内容",size:"small",children:"开始分析"},"start"),(0,te.jsx)(P.ZP,{type:"link",icon:(0,te.jsx)(V.Z,{}),onClick:function(){tt(ze),at(Ee),Xe(!0)},size:"small",children:"编辑任务"},"edit")],children:[Je?(0,te.jsx)(w.Z,{bordered:!1,loading:!0,children:(0,te.jsx)(B.Z,{tip:"加载任务信息...",size:"large",children:(0,te.jsx)("div",{style:{height:"400px",display:"flex",justifyContent:"center",alignItems:"center"},children:"正在加载任务信息..."})})}):(0,te.jsxs)(te.Fragment,{children:[(0,te.jsxs)(Y.f,{children:["results"===We&&function(){if(null==c||!c.result_data||0===c.result_data.length)return(0,te.jsx)(w.Z,{title:(0,te.jsxs)("div",{children:[(0,te.jsx)(E.Z,{style:{color:"#52c41a"}})," 审核结果"]}),style:{borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.05)"},children:(0,te.jsx)(O.Z,{description:"暂无审核结果数据"})});var e=c.result_data[0];return(0,te.jsx)(w.Z,{title:(0,te.jsxs)("div",{children:[(0,te.jsx)(E.Z,{style:{color:"#52c41a"}})," 审核结果"]}),style:{borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.05)"},children:(0,te.jsxs)(y.Z,{defaultActiveKey:"file_info",children:[(0,te.jsx)(se,{tab:"文件信息",children:(0,te.jsx)("div",{className:"result-container",style:{background:"#f9f9f9",padding:20,borderRadius:8},children:Object.entries(e.file_info).map((function(e){var t=f()(e,2),n=t[0],r=t[1];return(0,te.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,te.jsx)(g.Z.Title,{level:5,style:{marginBottom:"8px"},children:n}),r.map((function(e,t){return(0,te.jsx)("div",{style:{backgroundColor:"#fff",border:"1px solid #e8e8e8",borderRadius:"8px",padding:"16px",marginBottom:"8px"},children:(0,te.jsx)("table",{style:{width:"100%",borderCollapse:"collapse"},children:(0,te.jsx)("tbody",{children:Object.entries(e).map((function(t,n){var r=f()(t,2),a=r[0],i=r[1];return(0,te.jsxs)("tr",{style:{borderBottom:n===Object.entries(e).length-1?"none":"1px solid #f0f0f0"},children:[(0,te.jsxs)("td",{style:{padding:"12px 8px",width:"30%",fontWeight:"bold"},children:[a,":"]}),(0,te.jsx)("td",{style:{padding:"12px 8px"},children:"object"===s()(i)?JSON.stringify(i):String(i)})]},a)}))})})},t)}))]},n)}))})},"file_info"),(0,te.jsx)(se,{tab:"审核判断",children:(0,te.jsx)("div",{className:"result-container",style:{background:"#f9f9f9",padding:20,borderRadius:8},children:Object.entries(e.scene_info).map((function(e){var t=f()(e,2),n=t[0],r=t[1];return(0,te.jsxs)("div",{style:{backgroundColor:"#fff",border:"1px solid #e8e8e8",borderRadius:"8px",padding:"16px",marginBottom:"16px"},children:[(0,te.jsx)(g.Z.Title,{level:5,children:Bt(n)}),Object.entries(r).map((function(e){var t=f()(e,2),n=t[0],r=t[1];return n.startsWith("judge")&&"object"===s()(r)&&r&&"value"in r?(0,te.jsxs)("div",{style:{backgroundColor:r.value?"#f6ffed":"#fff2f0",border:"1px solid ".concat(r.value?"#b7eb8f":"#ffccc7"),borderRadius:"4px",padding:"12px",marginBottom:"8px"},children:[r.value?(0,te.jsx)(E.Z,{style:{color:"#52c41a",marginRight:"8px"}}):(0,te.jsx)(K.Z,{style:{color:"#ff4d4f",marginRight:"8px"}}),(0,te.jsx)(g.Z.Text,{children:r.desc})]},n):(0,te.jsxs)("div",{style:{backgroundColor:"#f5f5f5",padding:"12px",borderRadius:"4px",marginBottom:"8px"},children:[(0,te.jsxs)(g.Z.Text,{strong:!0,children:[n,": "]}),(0,te.jsx)(g.Z.Text,{children:"object"===s()(r)?JSON.stringify(r):String(r)})]},n)}))]},n)}))})},"scene_info")]})})}(),"status"===We&&(Tt=function(){if(!c)return 0;switch(c.status){case"pending_upload":default:return 0;case"analyzing":case"failed":return 1;case"completed":return 2}}(),Pt=i()(j).sort((function(e,t){if("status"===he){var n={failed:0,processing:1,completed:2,pending:3},r=n[e.status||"pending"]||4,s=n[t.status||"pending"]||4;return"asc"===be?r-s:s-r}if("size"===he)return"asc"===be?e.size-t.size:t.size-e.size;if("name"===he)return"asc"===be?e.name.localeCompare(t.name):t.name.localeCompare(e.name);if("type"===he)return"asc"===be?(e.data_type||"").localeCompare(t.data_type||""):(t.data_type||"").localeCompare(e.data_type||"");var a=e.uploaded_at||e.uploadTime||"",i=t.uploaded_at||t.uploadTime||"";return"asc"===be?a.localeCompare(i):i.localeCompare(a)})),(0,te.jsxs)("div",{children:[j.length>0&&(0,te.jsxs)(w.Z,{title:(0,te.jsxs)("div",{children:[(0,te.jsx)(R.Z,{})," 已上传文件列表 (",j.length,")"]}),style:{marginBottom:16,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.05)"},extra:(0,te.jsxs)(T.Z,{children:[(0,te.jsx)(P.ZP,{icon:(0,te.jsx)(F.Z,{}),size:"small",onClick:St,title:"刷新任务状态",children:"刷新"}),(0,te.jsx)("span",{children:"排序:"}),(0,te.jsxs)(S.default,{value:he,style:{width:120},onChange:function(e){var t;he===(t=e)?ve("asc"===be?"desc":"asc"):(je(t),ve("desc"))},children:[(0,te.jsx)(S.default.Option,{value:"uploaded_at",children:"上传时间"}),(0,te.jsx)(S.default.Option,{value:"status",children:"处理状态"}),(0,te.jsx)(S.default.Option,{value:"name",children:"文件名"}),(0,te.jsx)(S.default.Option,{value:"size",children:"文件大小"}),(0,te.jsx)(S.default.Option,{value:"type",children:"文件类型"})]}),(0,te.jsx)(P.ZP,{type:"text",icon:"asc"===be?(0,te.jsx)(W.Z,{rotate:90}):(0,te.jsx)(W.Z,{rotate:-90}),onClick:function(){return ve("asc"===be?"desc":"asc")}})]}),children:[j.length>0&&(0,te.jsx)("div",{style:{marginBottom:16,padding:"0 12px"},children:(0,te.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:8},children:(_t={pending:0,processing:0,completed:0,failed:0,unknown:0},j.forEach((function(e){e.status?_t[e.status]=(_t[e.status]||0)+1:_t.unknown++})),(0,te.jsxs)(te.Fragment,{children:[(0,te.jsxs)(C.Z,{color:"default",children:["总文件: ",j.length]}),_t.pending>0&&(0,te.jsxs)(C.Z,{color:ce,children:["待处理: ",_t.pending]}),_t.processing>0&&(0,te.jsxs)(C.Z,{color:oe,children:["处理中: ",_t.processing]}),_t.completed>0&&(0,te.jsxs)(C.Z,{color:le,children:["已完成: ",_t.completed]}),_t.failed>0&&(0,te.jsxs)(C.Z,{color:ue,children:["失败: ",_t.failed]}),_t.unknown>0&&(0,te.jsxs)(C.Z,{children:["未知状态: ",_t.unknown]})]}))})}),(0,te.jsx)(z.Z,{size:"small",dataSource:Pt,renderItem:function(e){var t,n=R.Z,r="#1890ff";if(e.data_type){var s=e.data_type.toLowerCase();s.includes("excel")||s.includes("csv")||s.includes("xls")?(n=A.Z,r="#52c41a"):s.includes("pdf")?(n=D.Z,r="#f5222d"):s.includes("word")||s.includes("doc")?(n=L.Z,r="#1890ff"):s.includes("json")||s.includes("txt")?(n=R.Z,r="#fa8c16"):s.includes("jpg")||s.includes("jpeg")||s.includes("png")?(n=J.Z,r="#722ed1"):(n=G.Z,r="#8c8c8c")}return(0,te.jsx)(z.Z.Item,{actions:["pending_upload"===(null==c?void 0:c.status)&&(0,te.jsx)(P.ZP,{type:"link",icon:(0,te.jsx)(q.Z,{}),danger:!0,size:"small",onClick:function(){return zt(e.id)},children:"移除"})],children:(0,te.jsx)(z.Z.Item.Meta,{avatar:(0,te.jsx)(n,{style:{fontSize:20,color:r}}),title:(0,te.jsxs)(T.Z,{children:[(0,te.jsx)("span",{children:e.name}),e.data_type&&(0,te.jsx)(C.Z,{color:"blue",children:e.data_type})]}),description:(0,te.jsxs)("div",{children:[(0,te.jsx)("div",{style:{display:"flex",justifyContent:"space-between"},children:(0,te.jsx)("span",{children:"大小: ".concat((t=e.size,t<1024?t+" B":t<1048576?(t/1024).toFixed(2)+" KB":t<1073741824?(t/1048576).toFixed(2)+" MB":(t/1073741824).toFixed(2)+" GB"))})}),"processing"===e.status&&(0,te.jsx)("div",{style:{marginTop:8},children:(0,te.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,te.jsx)("span",{style:{marginRight:8},children:"处理中"}),(0,te.jsx)(B.Z,{size:"small"})]})}),"completed"===e.status&&(0,te.jsxs)("div",{style:{marginTop:8,color:"#52c41a"},children:[(0,te.jsx)(E.Z,{style:{marginRight:8}}),"处理完成"]}),"failed"===e.status&&(0,te.jsxs)("div",{style:{marginTop:8,color:"#f5222d"},children:["处理失败: ",e.processing_status||"未知错误"]})]})})})}})]}),(0,te.jsxs)(w.Z,{title:(0,te.jsxs)("div",{children:[(0,te.jsx)(M.Z,{})," 执行状态"]}),style:{borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.05)"},extra:(0,te.jsx)(P.ZP,{icon:(0,te.jsx)(F.Z,{}),size:"small",onClick:St,title:"刷新任务状态",children:"刷新"}),children:["pending_upload"===(null==c?void 0:c.status)&&j.length>0&&(0,te.jsx)(_.Z,{type:"info",message:"文件已上传，等待启动",description:"您的文件已上传成功，请点击页面顶部的【开始分析】按钮启动分析任务。",style:{marginBottom:16},showIcon:!0}),(0,te.jsxs)(v.Z,{progressDot:!0,current:Tt,style:{padding:"0 20px"},children:[(0,te.jsx)(re,{title:"文件上传",description:Tt>=0?"已完成":"等待中"}),(0,te.jsx)(re,{title:"任务处理",description:1===Tt?"进行中":Tt>1?"已完成":"等待中"}),(0,te.jsx)(re,{title:"结果生成",description:2===Tt?"已完成":"等待中"})]})]})]})),"upload"===We&&(wt=Ze.find((function(e){return e.code===Ee})),(0,te.jsx)("div",{children:(0,te.jsxs)(w.Z,{title:(0,te.jsxs)("div",{children:[(0,te.jsx)(I.Z,{})," 文件上传"]}),style:{marginBottom:16,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.05)"},children:[(0,te.jsx)(_.Z,{type:"warning",message:"重要提示",description:(0,te.jsxs)("div",{children:[(0,te.jsx)("p",{children:(0,te.jsx)("strong",{children:"操作步骤："})}),(0,te.jsx)("p",{children:"1. 上传所有需要分析的文件"}),(0,te.jsx)("p",{children:"2. 点击页面顶部的【开始分析】按钮手动启动任务"}),(0,te.jsxs)("p",{children:[(0,te.jsx)("strong",{children:"注意："}),"上传完文件后，需要手动启动分析任务"]})]}),style:{marginBottom:16}}),(0,te.jsx)(_.Z,{type:"info",message:"文件要求",description:(0,te.jsx)("div",{children:null!=wt&&wt.description?(0,te.jsx)("div",{dangerouslySetInnerHTML:{__html:wt.description}}):(0,te.jsxs)(te.Fragment,{children:[(0,te.jsxs)("p",{children:[(0,te.jsx)("strong",{children:"支持格式："}),"PDF、Word、CSV、Excel、JSON、图片等"]}),(0,te.jsxs)("p",{children:[(0,te.jsx)("strong",{children:"命名建议："}),"建议使用有意义的文件名，方便后续识别"]}),(0,te.jsxs)("p",{children:[(0,te.jsx)("strong",{children:"大小限制："}),"单个文件不超过100MB"]})]})}),style:{marginBottom:16}}),(0,te.jsx)($.A,{style:{margin:"auto",marginTop:8,marginBottom:24,width:"100%",paddingBottom:24},name:"basic",layout:"vertical",onFinish:Ct,submitter:{searchConfig:{submitText:"上传文件"},submitButtonProps:{loading:jt,disabled:0===pt.length}},children:(0,te.jsxs)(ne,o()(o()({},mt),{},{style:{margin:"auto",marginTop:8,marginBottom:24,width:"100%",paddingBottom:24},children:[(0,te.jsx)("p",{className:"ant-upload-drag-icon",children:(0,te.jsx)(I.Z,{})}),(0,te.jsx)("p",{className:"ant-upload-text",children:"点击或拖拽文件到此区域上传"}),(0,te.jsx)("p",{className:"ant-upload-hint",children:"支持单个或批量上传。严格禁止上传公司数据或其他禁止的文件。支持数据格式：PDF、Word、CSV、Excel、JSON、图片等"})]}))})]})}))]}),(0,te.jsx)(N.Z,{title:"编辑任务信息",open:Qe,onCancel:function(){return Xe(!1)},footer:[(0,te.jsx)(P.ZP,{onClick:function(){return Xe(!1)},children:"取消"},"cancel"),(0,te.jsx)(P.ZP,{type:"primary",loading:ot,onClick:Zt,children:"保存"},"submit")],children:(0,te.jsxs)(k.Z,{layout:"vertical",children:[(0,te.jsx)(k.Z.Item,{label:"任务名称",required:!0,tooltip:"为当前审核任务起一个名称，便于后续识别",children:(0,te.jsx)(m.Z,{placeholder:"请输入任务名称",value:et,onChange:function(e){return tt(e.target.value)}})}),(0,te.jsx)(k.Z.Item,{label:"任务类型",required:!0,tooltip:"不同的任务类型会使用不同的内容抽取策略",children:(0,te.jsx)(S.default,{placeholder:"请选择任务类型",value:st||void 0,onChange:function(e){return at(e)},style:{width:"100%"},children:Ze.map((function(e){return(0,te.jsxs)(S.default.Option,{value:e.code,children:[e.name,e.description?" (".concat(e.description,")"):""]},e.id)}))})})]})})]}),(0,te.jsx)("style",{children:"\n        .file-audit-tabs .ant-tabs-nav {\n          margin-bottom: 24px;\n        }\n        \n        .task-list-table .ant-table-tbody > tr {\n          cursor: pointer;\n          transition: all 0.3s;\n        }\n        \n        .task-list-table .ant-table-tbody > tr:hover {\n          background-color: rgba(24, 144, 255, 0.05);\n        }\n        \n        .ant-card-head {\n          border-bottom: 1px solid #f0f0f0;\n          padding: 0 16px;\n        }\n        \n        .upload-card .ant-upload-drag {\n          border: 2px dashed #d9d9d9;\n          transition: all 0.3s;\n        }\n        \n        .upload-card .ant-upload-drag:hover {\n          border-color: #1890ff;\n        }\n        \n        .chat-container::-webkit-scrollbar {\n          width: 6px;\n        }\n        \n        .chat-container::-webkit-scrollbar-thumb {\n          background-color: rgba(0, 0, 0, 0.2);\n          border-radius: 3px;\n        }\n        \n        .chat-container::-webkit-scrollbar-track {\n          background-color: transparent;\n        }\n      "})]})}},74573:function(e,t,n){n.d(t,{Ak:function(){return u},Sq:function(){return v},_5:function(){return m},s6:function(){return o},vr:function(){return f},xJ:function(){return j},yW:function(){return p}});var r=n(15009),s=n.n(r),a=n(99289),i=n.n(a),c=n(78158);function o(e){return l.apply(this,arguments)}function l(){return(l=i()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/auditTask/audit-task-types",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function u(e){return d.apply(this,arguments)}function d(){return(d=i()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/auditTask/audit-tasks",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function p(e){return x.apply(this,arguments)}function x(){return(x=i()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/auditTask/audit-tasks/".concat(t),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function f(e){return h.apply(this,arguments)}function h(){return(h=i()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/auditTask/audit-tasks",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function j(e,t){return g.apply(this,arguments)}function g(){return(g=i()(s()().mark((function e(t,n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/auditTask/audit-tasks/".concat(t),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function m(e){return b.apply(this,arguments)}function b(){return(b=i()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/auditTask/audit-tasks/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function v(e){return y.apply(this,arguments)}function y(){return(y=i()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/auditTask/audit-tasks/".concat(t,"/files"),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}}}]);