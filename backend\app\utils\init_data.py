# app/core/init_data.py
from ..models.user import User, Tag, Geographic
from ..db.mongodb import db
from ..utils.logging_config import get_logger
from ..models.group import Group
from datetime import datetime
import hashlib
from app.models.role import Role
from app.role_tree import routes
from ..models.system_app_setting import AppType
from ..models.expert import Expert, ExpertType, ExpertStatus
from app.utils.enums import RerankProvider,EmbeddingProvider,LLMProvider

logger = get_logger(__name__)




async def init_admin():
    # 这里应该有数据库会话来添加和提交 admin_user
    admin_user_data = {
        "id": 1,
        "name": "超级管理员",
        "avatar": "/avatar/01.png",
        "userid": "00000001",
        "signature": "管理员",
        "title": "系统管理员",
        "group_id": 1,  # 新增字段
        "group_name": "管理员组",  # 新增字段
        "tags": [],  # 确保 tags 字段在模型中定义
        "notifyCount": 0,
        "unreadCount": 0,
        "country": "China",
        "address": "",
        "phone": "18513199891",
        "password": hashlib.md5("admin_password".encode()).hexdigest(),
        "is_active": True,
        "is_superuser": True,
        "role_name": "admin",
        "role_id": 1,
        "created_at": datetime.now(),
        "created_by": "system",
    }
    # 检查管理员用户是否已存在
    existing_admin = db["users"].find_one({"phone": "18513199891"})
    if not existing_admin:
        await db["users"].insert_one(admin_user_data)
        logger.info("管理员用户创建成功")
    else:
        logger.info("管理员用户已存在")

async def init_groups():
    groups = [
        Group(id=1, name="管理员组", description="超级管理员组", created_at=datetime.now(), created_by=1, deletable=False),
        Group(id=2, name="默认用户组", description="普通用户组", created_at=datetime.now(), created_by=1, deletable=False),
    ]
    for group in groups:
        existing_group = await db["groups"].find_one({"name": group.name})
        if not existing_group:
            group_data = {
                "id": group.id,
                "name": group.name,
                "description": group.description,
                "created_at": group.created_at,
                "created_by": group.created_by,
                "deletable": group.deletable,
            }
            await db["groups"].insert_one(group_data)
            logger.info(f"组 {group.name} 创建成功")
        else:
            logger.info(f"组 {group.name} 已存在")


async def init_roles():
    # 获取所有权限
    all_access = {}
    def extract_permissions(routes):
        for route in routes:
            if 'access' in route:
                all_access[route['access']] = True
            if 'routes' in route:
                extract_permissions(route['routes'])
    
    extract_permissions(routes)

    roles = [
        {
            "id": 1,
            "name": "admin",
            "description": "拥有系统所有权限的超级管理员角色",
            "created_at": datetime.now(),
            "created_by": 1,
            "deletable": False,
            "access": all_access  # 赋予所有权限
        },
        {
            "id": 2,
            "name": "user",
            "description": "普通用户角色",
            "created_at": datetime.now(),
            "created_by": 1,
            "deletable": False,
            "access": {}  # 普通用户没有默认权限
        }
    ]
    
    for role in roles:
        existing_role = await db["roles"].find_one({"id": role["id"]})
        if not existing_role:
            await db["roles"].insert_one(role)
            logger.info(f"角色 {role['name']} 创建成功")
        else:
            logger.info(f"角色 {role['name']} 已存在")

async def init_systemApp():
    system_app_data = {
        "id": 1,
        "name": "合规问答",
        "app_info": "complianceQA",
        "type": AppType.KNOWLEDGE_QA.value,  # 使用枚举
        "service_url": "https://example.com/api",
        "token_key": "example_token_key",
        "token_price": "0.01",
        "created_at": datetime.now()
    }

    existing_app = await db["system_app_settings"].find_one({"app_info": "complianceQA"})
    if not existing_app:
        await db["system_app_settings"].insert_one(system_app_data)
        logger.info("系统应用 '合规问答' 创建成功")
    else:
        logger.info("系统应用 '合规问答' 已存在")

async def init_llms():
    llm_data ={
        "name" : "开源初始模型",
        "m_name" : "Qwen1.5-14B-Chat",
        "is_default" : True,
        "description" : "千问14B",
        "temperature" : 0.7,
        "max_tokens" : 4096,
        "top_p" : 1.0,
        "frequency_penalty" : 0.0,
        "presence_penalty" : 0.0,
        "service_url" : "http://api.roardata.cn/v1",
        "api_key" : "sk-6e94RWvIwV5TmX3M846529EeCf2b4d9b86A2566a4d4bA242",
        "provider" : LLMProvider.LOCAL,
        "id" : 1,
        "created_at" :datetime.now(),
        "created_by" : 1,
        "is_active" : True
    }

    existing_app = await db["llms"].find_one({"id": 1})
    if not existing_app:
        await db["llms"].insert_one(llm_data)
        logger.info("初始开源模型服务创建成功，但是需要调整")
    else:
        logger.info("初始开源模型服务已存在")

async def init_system_settings():
    """初始化系统设置"""
    existing_settings = await db.system_settings.find_one({})
    if not existing_settings:
        default_settings = {
            "logo": '/static/logo.png',
            "title": "金鹏大模型平台",
            "navTheme": "light",
            "colorPrimary": "#722ED1",
            "features": {
                "enable_coe": True,  # 是否启用 COE
                "coe_config": {
                    "default_confidence_threshold": 0.7,  # COE 默认置信度阈值
                    "enable_fallback": True,  # 是否启用兜底专家
                    "enable_concurrent": False  # 是否启用并发处理
                }
            }
        }
        await db.system_settings.insert_one(default_settings)
        print("系统设置初始化成功")
    else:
        print("系统设置已存在")



async def init_embeddings():
    embeddings ={
    "id" : 1,
    "name" : "bge-m3-pai",
    "embedding_name" :"bge-m3-pai",
    "vector_size" :300,
    "distance_metric" :"cosine",
    "service_url" : "http://api.roardata.cn/v1",
    "api_key" : "sk-6e94RWvIwV5TmX3M846529EeCf2b4d9b86A2566a4d4bA242",
    "provider" : EmbeddingProvider.LOCAL,
    "created_at" :datetime.now(),
    "created_by" : 1,
    "is_active" : True
    }

    existing_app = await db["embeddings"].find_one({"id": 1})
    if not existing_app:
        await db["embeddings"].insert_one(embeddings)
        logger.info("初始开源模型向量创建成功，但是需要调整")
    else:
        logger.info("初始开源模型向量已存在")

async def init_reranks():
    reranks ={
        "id" : 1,
        "name" : "bge-m3-pai",
        "reRank_model" :"bge-m3-pai",
        "service_url" : "http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/reranker_base_finetune/api/v1/rerank",
        "api_key" : "OTEzZWQ2OTc4YTBlZWY2MDBjYjc5ZGNjMDViYzg1NmIyNThkODg5MQ==",
        "provider" : RerankProvider.LOCAL,
        "created_at" :datetime.now(),
        "created_by" : 1,
        "is_active" : True
    }

    existing_rerank = await db["reranks"].find_one({"id": 1})
    if not existing_rerank:
        await db["reranks"].insert_one(reranks)
        logger.info("初始开源模型重排序创建成功，但是需要调整")
    else:
        logger.info("初始开源模型重排序已存在")


async def init_general_expert():
    """初始化基础通用专家"""
    general_expert_data = {
        "name": "通用问答专家",
        "description": "基础的通用问答专家，可以处理一般性的查询和对话",
        "expert_type": ExpertType.GENERAL.value,
        "version": "1.0.0",
        
        # LLM配置，使用系统默认的LLM
        "llm_config": {
            "name": "开源初始模型",
            "m_name": "Qwen1.5-14B-Chat",
            "service_url": "http://api.roardata.cn/v1",
            "api_key": "sk-6e94RWvIwV5TmX3M846529EeCf2b4d9b86A2566a4d4bA242",
            "temperature": 0.7,
            "max_tokens": 4096,
            "top_p": 1.0
        },
        
        # 提示词模板
        "prompt_templates": {
            "default": "你是一个专业、友好的AI助手，请尽可能准确地回答用户的问题。\n\n用户问题：{query}",
            "clarification": "我需要更多信息来回答这个问题。具体来说，请告诉我：\n{questions}"
        },
        
        # 基础能力
        "capabilities": [
            "通用对话",
            "问题解答",
            "信息咨询"
        ],
        
        # 支持的语言
        "supported_languages": ["zh", "en"],
        
        # 性能参数
        "max_context_length": 4096,
        "response_timeout": 30,
        "confidence_threshold": 0.1,  # 通用专家的置信度阈值较低
        
        # 状态信息
        "status": ExpertStatus.ACTIVE.value,
        "is_public": True,
        
        # 创建信息
        "created_by": 1  # 系统创建
    }
    
    # 检查专家是否已存在
    existing_expert = await db["experts"].find_one({"name": general_expert_data["name"]})
    if not existing_expert:
        await db["experts"].insert_one(general_expert_data)
        logger.info("通用问答专家创建成功")
    else:
        logger.info("通用问答专家已存在")

async def init_all():
    """初始化所有基础数据"""
    await init_admin()
    await init_groups()
    await init_roles()
    await init_systemApp()
    await init_llms()
    await init_system_settings()
    await init_embeddings()
    await init_reranks()
    await init_general_expert()