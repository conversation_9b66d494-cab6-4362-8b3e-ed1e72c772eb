"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2119],{42119:function(t,i,e){e.d(i,{Z:function(){return F}});var n=e(67294),o=e(64894),r=e(62208),a=e(93967),l=e.n(a),c=e(87462),s=e(1413),d=e(4942),m=e(91),p=e(15105),g=["className","prefixCls","style","active","status","iconPrefix","icon","wrapperStyle","stepNumber","disabled","description","title","subTitle","progressDot","stepIcon","tailContent","icons","stepIndex","onStepClick","onClick","render"];function $(t){return"string"==typeof t}var u=function(t){var i,e=t.className,o=t.prefixCls,r=t.style,a=t.active,u=t.status,h=t.iconPrefix,b=t.icon,f=(t.wrapperStyle,t.stepNumber),S=t.disabled,v=t.description,C=t.title,I=t.subTitle,y=t.progressDot,w=t.stepIcon,x=t.tailContent,z=t.icons,q=t.stepIndex,k=t.onStepClick,T=t.onClick,H=t.render,E=(0,m.Z)(t,g),O={};!!k&&!S&&(O.role="button",O.tabIndex=0,O.onClick=function(t){null==T||T(t),k(q)},O.onKeyDown=function(t){var i=t.which;i!==p.Z.ENTER&&i!==p.Z.SPACE||k(q)});var W,N,j,M,X=u||"wait",D=l()("".concat(o,"-item"),"".concat(o,"-item-").concat(X),e,(i={},(0,d.Z)(i,"".concat(o,"-item-custom"),b),(0,d.Z)(i,"".concat(o,"-item-active"),a),(0,d.Z)(i,"".concat(o,"-item-disabled"),!0===S),i)),B=(0,s.Z)({},r),Z=n.createElement("div",(0,c.Z)({},E,{className:D,style:B}),n.createElement("div",(0,c.Z)({onClick:T},O,{className:"".concat(o,"-item-container")}),n.createElement("div",{className:"".concat(o,"-item-tail")},x),n.createElement("div",{className:"".concat(o,"-item-icon")},(j=l()("".concat(o,"-icon"),"".concat(h,"icon"),(W={},(0,d.Z)(W,"".concat(h,"icon-").concat(b),b&&$(b)),(0,d.Z)(W,"".concat(h,"icon-check"),!b&&"finish"===u&&(z&&!z.finish||!z)),(0,d.Z)(W,"".concat(h,"icon-cross"),!b&&"error"===u&&(z&&!z.error||!z)),W)),M=n.createElement("span",{className:"".concat(o,"-icon-dot")}),N=y?"function"==typeof y?n.createElement("span",{className:"".concat(o,"-icon")},y(M,{index:f-1,status:u,title:C,description:v})):n.createElement("span",{className:"".concat(o,"-icon")},M):b&&!$(b)?n.createElement("span",{className:"".concat(o,"-icon")},b):z&&z.finish&&"finish"===u?n.createElement("span",{className:"".concat(o,"-icon")},z.finish):z&&z.error&&"error"===u?n.createElement("span",{className:"".concat(o,"-icon")},z.error):b||"finish"===u||"error"===u?n.createElement("span",{className:j}):n.createElement("span",{className:"".concat(o,"-icon")},f),w&&(N=w({index:f-1,status:u,title:C,description:v,node:N})),N)),n.createElement("div",{className:"".concat(o,"-item-content")},n.createElement("div",{className:"".concat(o,"-item-title")},C,I&&n.createElement("div",{title:"string"==typeof I?I:void 0,className:"".concat(o,"-item-subtitle")},I)),v&&n.createElement("div",{className:"".concat(o,"-item-description")},v))));return H&&(Z=H(Z)||null),Z},h=["prefixCls","style","className","children","direction","type","labelPlacement","iconPrefix","status","size","current","progressDot","stepIcon","initial","icons","onChange","itemRender","items"];function b(t){var i,e=t.prefixCls,o=void 0===e?"rc-steps":e,r=t.style,a=void 0===r?{}:r,p=t.className,g=(t.children,t.direction),$=void 0===g?"horizontal":g,b=t.type,f=void 0===b?"default":b,S=t.labelPlacement,v=void 0===S?"horizontal":S,C=t.iconPrefix,I=void 0===C?"rc":C,y=t.status,w=void 0===y?"process":y,x=t.size,z=t.current,q=void 0===z?0:z,k=t.progressDot,T=void 0!==k&&k,H=t.stepIcon,E=t.initial,O=void 0===E?0:E,W=t.icons,N=t.onChange,j=t.itemRender,M=t.items,X=void 0===M?[]:M,D=(0,m.Z)(t,h),B="navigation"===f,Z="inline"===f,P=Z||T,A=Z?"horizontal":$,L=Z?void 0:x,R=P?"vertical":v,G=l()(o,"".concat(o,"-").concat(A),p,(i={},(0,d.Z)(i,"".concat(o,"-").concat(L),L),(0,d.Z)(i,"".concat(o,"-label-").concat(R),"horizontal"===A),(0,d.Z)(i,"".concat(o,"-dot"),!!P),(0,d.Z)(i,"".concat(o,"-navigation"),B),(0,d.Z)(i,"".concat(o,"-inline"),Z),i)),F=function(t){N&&q!==t&&N(t)};return n.createElement("div",(0,c.Z)({className:G,style:a},D),X.filter((function(t){return t})).map((function(t,i){var e=(0,s.Z)({},t),r=O+i;return"error"===w&&i===q-1&&(e.className="".concat(o,"-next-error")),e.status||(e.status=r===q?w:r<q?"finish":"wait"),Z&&(e.icon=void 0,e.subTitle=void 0),!e.render&&j&&(e.render=function(t){return j(e,t)}),n.createElement(u,(0,c.Z)({},e,{active:r===q,stepNumber:r+1,stepIndex:r,key:r,prefixCls:o,iconPrefix:I,wrapperStyle:a,progressDot:P,stepIcon:H,icons:W,onStepClick:N&&F}))})))}b.Step=u;var f=b,S=e(53124),v=e(98675),C=e(25378),I=e(38703),y=e(83062),w=e(11568),x=e(14747),z=e(83559),q=e(83262);var k=t=>{const{componentCls:i,customIconTop:e,customIconSize:n,customIconFontSize:o}=t;return{[`${i}-item-custom`]:{[`> ${i}-item-container > ${i}-item-icon`]:{height:"auto",background:"none",border:0,[`> ${i}-icon`]:{top:e,width:n,height:n,fontSize:o,lineHeight:(0,w.bf)(n)}}},[`&:not(${i}-vertical)`]:{[`${i}-item-custom`]:{[`${i}-item-icon`]:{width:"auto",background:"none"}}}}};var T=t=>{const{componentCls:i}=t;return{[`${i}-horizontal`]:{[`${`${i}-item`}-tail`]:{transform:"translateY(-50%)"}}}};var H=t=>{const{componentCls:i,inlineDotSize:e,inlineTitleColor:n,inlineTailColor:o}=t,r=t.calc(t.paddingXS).add(t.lineWidth).equal(),a={[`${i}-item-container ${i}-item-content ${i}-item-title`]:{color:n}};return{[`&${i}-inline`]:{width:"auto",display:"inline-flex",[`${i}-item`]:{flex:"none","&-container":{padding:`${(0,w.bf)(r)} ${(0,w.bf)(t.paddingXXS)} 0`,margin:`0 ${(0,w.bf)(t.calc(t.marginXXS).div(2).equal())}`,borderRadius:t.borderRadiusSM,cursor:"pointer",transition:`background-color ${t.motionDurationMid}`,"&:hover":{background:t.controlItemBgHover},"&[role='button']:hover":{opacity:1}},"&-icon":{width:e,height:e,marginInlineStart:`calc(50% - ${(0,w.bf)(t.calc(e).div(2).equal())})`,[`> ${i}-icon`]:{top:0},[`${i}-icon-dot`]:{borderRadius:t.calc(t.fontSizeSM).div(4).equal(),"&::after":{display:"none"}}},"&-content":{width:"auto",marginTop:t.calc(t.marginXS).sub(t.lineWidth).equal()},"&-title":{color:n,fontSize:t.fontSizeSM,lineHeight:t.lineHeightSM,fontWeight:"normal",marginBottom:t.calc(t.marginXXS).div(2).equal()},"&-description":{display:"none"},"&-tail":{marginInlineStart:0,top:t.calc(e).div(2).add(r).equal(),transform:"translateY(-50%)","&:after":{width:"100%",height:t.lineWidth,borderRadius:0,marginInlineStart:0,background:o}},[`&:first-child ${i}-item-tail`]:{width:"50%",marginInlineStart:"50%"},[`&:last-child ${i}-item-tail`]:{display:"block",width:"50%"},"&-wait":Object.assign({[`${i}-item-icon ${i}-icon ${i}-icon-dot`]:{backgroundColor:t.colorBorderBg,border:`${(0,w.bf)(t.lineWidth)} ${t.lineType} ${o}`}},a),"&-finish":Object.assign({[`${i}-item-tail::after`]:{backgroundColor:o},[`${i}-item-icon ${i}-icon ${i}-icon-dot`]:{backgroundColor:o,border:`${(0,w.bf)(t.lineWidth)} ${t.lineType} ${o}`}},a),"&-error":a,"&-active, &-process":Object.assign({[`${i}-item-icon`]:{width:e,height:e,marginInlineStart:`calc(50% - ${(0,w.bf)(t.calc(e).div(2).equal())})`,top:0}},a),[`&:not(${i}-item-active) > ${i}-item-container[role='button']:hover`]:{[`${i}-item-title`]:{color:n}}}}}};var E=t=>{const{componentCls:i,iconSize:e,lineHeight:n,iconSizeSM:o}=t;return{[`&${i}-label-vertical`]:{[`${i}-item`]:{overflow:"visible","&-tail":{marginInlineStart:t.calc(e).div(2).add(t.controlHeightLG).equal(),padding:`0 ${(0,w.bf)(t.paddingLG)}`},"&-content":{display:"block",width:t.calc(e).div(2).add(t.controlHeightLG).mul(2).equal(),marginTop:t.marginSM,textAlign:"center"},"&-icon":{display:"inline-block",marginInlineStart:t.controlHeightLG},"&-title":{paddingInlineEnd:0,paddingInlineStart:0,"&::after":{display:"none"}},"&-subtitle":{display:"block",marginBottom:t.marginXXS,marginInlineStart:0,lineHeight:n}},[`&${i}-small:not(${i}-dot)`]:{[`${i}-item`]:{"&-icon":{marginInlineStart:t.calc(e).sub(o).div(2).add(t.controlHeightLG).equal()}}}}}};var O=t=>{const{componentCls:i,navContentMaxWidth:e,navArrowColor:n,stepsNavActiveColor:o,motionDurationSlow:r}=t;return{[`&${i}-navigation`]:{paddingTop:t.paddingSM,[`&${i}-small`]:{[`${i}-item`]:{"&-container":{marginInlineStart:t.calc(t.marginSM).mul(-1).equal()}}},[`${i}-item`]:{overflow:"visible",textAlign:"center","&-container":{display:"inline-block",height:"100%",marginInlineStart:t.calc(t.margin).mul(-1).equal(),paddingBottom:t.paddingSM,textAlign:"start",transition:`opacity ${r}`,[`${i}-item-content`]:{maxWidth:e},[`${i}-item-title`]:Object.assign(Object.assign({maxWidth:"100%",paddingInlineEnd:0},x.vS),{"&::after":{display:"none"}})},[`&:not(${i}-item-active)`]:{[`${i}-item-container[role='button']`]:{cursor:"pointer","&:hover":{opacity:.85}}},"&:last-child":{flex:1,"&::after":{display:"none"}},"&::after":{position:"absolute",top:`calc(50% - ${(0,w.bf)(t.calc(t.paddingSM).div(2).equal())})`,insetInlineStart:"100%",display:"inline-block",width:t.fontSizeIcon,height:t.fontSizeIcon,borderTop:`${(0,w.bf)(t.lineWidth)} ${t.lineType} ${n}`,borderBottom:"none",borderInlineStart:"none",borderInlineEnd:`${(0,w.bf)(t.lineWidth)} ${t.lineType} ${n}`,transform:"translateY(-50%) translateX(-50%) rotate(45deg)",content:'""'},"&::before":{position:"absolute",bottom:0,insetInlineStart:"50%",display:"inline-block",width:0,height:t.lineWidthBold,backgroundColor:o,transition:`width ${r}, inset-inline-start ${r}`,transitionTimingFunction:"ease-out",content:'""'}},[`${i}-item${i}-item-active::before`]:{insetInlineStart:0,width:"100%"}},[`&${i}-navigation${i}-vertical`]:{[`> ${i}-item`]:{marginInlineEnd:0,"&::before":{display:"none"},[`&${i}-item-active::before`]:{top:0,insetInlineEnd:0,insetInlineStart:"unset",display:"block",width:t.calc(t.lineWidth).mul(3).equal(),height:`calc(100% - ${(0,w.bf)(t.marginLG)})`},"&::after":{position:"relative",insetInlineStart:"50%",display:"block",width:t.calc(t.controlHeight).mul(.25).equal(),height:t.calc(t.controlHeight).mul(.25).equal(),marginBottom:t.marginXS,textAlign:"center",transform:"translateY(-50%) translateX(-50%) rotate(135deg)"},"&:last-child":{"&::after":{display:"none"}},[`> ${i}-item-container > ${i}-item-tail`]:{visibility:"hidden"}}},[`&${i}-navigation${i}-horizontal`]:{[`> ${i}-item > ${i}-item-container > ${i}-item-tail`]:{visibility:"hidden"}}}};var W=t=>{const{antCls:i,componentCls:e,iconSize:n,iconSizeSM:o,processIconColor:r,marginXXS:a,lineWidthBold:l,lineWidth:c,paddingXXS:s}=t,d=t.calc(n).add(t.calc(l).mul(4).equal()).equal(),m=t.calc(o).add(t.calc(t.lineWidth).mul(4).equal()).equal();return{[`&${e}-with-progress`]:{[`${e}-item`]:{paddingTop:s,[`&-process ${e}-item-container ${e}-item-icon ${e}-icon`]:{color:r}},[`&${e}-vertical > ${e}-item `]:{paddingInlineStart:s,[`> ${e}-item-container > ${e}-item-tail`]:{top:a,insetInlineStart:t.calc(n).div(2).sub(c).add(s).equal()}},[`&, &${e}-small`]:{[`&${e}-horizontal ${e}-item:first-child`]:{paddingBottom:s,paddingInlineStart:s}},[`&${e}-small${e}-vertical > ${e}-item > ${e}-item-container > ${e}-item-tail`]:{insetInlineStart:t.calc(o).div(2).sub(c).add(s).equal()},[`&${e}-label-vertical ${e}-item ${e}-item-tail`]:{top:t.calc(n).div(2).add(s).equal()},[`${e}-item-icon`]:{position:"relative",[`${i}-progress`]:{position:"absolute",insetInlineStart:"50%",top:"50%",transform:"translate(-50%, -50%)","&-inner":{width:`${(0,w.bf)(d)} !important`,height:`${(0,w.bf)(d)} !important`}}},[`&${e}-small`]:{[`&${e}-label-vertical ${e}-item ${e}-item-tail`]:{top:t.calc(o).div(2).add(s).equal()},[`${e}-item-icon ${i}-progress-inner`]:{width:`${(0,w.bf)(m)} !important`,height:`${(0,w.bf)(m)} !important`}}}}};var N=t=>{const{componentCls:i,descriptionMaxWidth:e,lineHeight:n,dotCurrentSize:o,dotSize:r,motionDurationSlow:a}=t;return{[`&${i}-dot, &${i}-dot${i}-small`]:{[`${i}-item`]:{"&-title":{lineHeight:n},"&-tail":{top:t.calc(t.dotSize).sub(t.calc(t.lineWidth).mul(3).equal()).div(2).equal(),width:"100%",marginTop:0,marginBottom:0,marginInline:`${(0,w.bf)(t.calc(e).div(2).equal())} 0`,padding:0,"&::after":{width:`calc(100% - ${(0,w.bf)(t.calc(t.marginSM).mul(2).equal())})`,height:t.calc(t.lineWidth).mul(3).equal(),marginInlineStart:t.marginSM}},"&-icon":{width:r,height:r,marginInlineStart:t.calc(t.descriptionMaxWidth).sub(r).div(2).equal(),paddingInlineEnd:0,lineHeight:(0,w.bf)(r),background:"transparent",border:0,[`${i}-icon-dot`]:{position:"relative",float:"left",width:"100%",height:"100%",borderRadius:100,transition:`all ${a}`,"&::after":{position:"absolute",top:t.calc(t.marginSM).mul(-1).equal(),insetInlineStart:t.calc(r).sub(t.calc(t.controlHeightLG).mul(1.5).equal()).div(2).equal(),width:t.calc(t.controlHeightLG).mul(1.5).equal(),height:t.controlHeight,background:"transparent",content:'""'}}},"&-content":{width:e},[`&-process ${i}-item-icon`]:{position:"relative",top:t.calc(r).sub(o).div(2).equal(),width:o,height:o,lineHeight:(0,w.bf)(o),background:"none",marginInlineStart:t.calc(t.descriptionMaxWidth).sub(o).div(2).equal()},[`&-process ${i}-icon`]:{[`&:first-child ${i}-icon-dot`]:{insetInlineStart:0}}}},[`&${i}-vertical${i}-dot`]:{[`${i}-item-icon`]:{marginTop:t.calc(t.controlHeight).sub(r).div(2).equal(),marginInlineStart:0,background:"none"},[`${i}-item-process ${i}-item-icon`]:{marginTop:t.calc(t.controlHeight).sub(o).div(2).equal(),top:0,insetInlineStart:t.calc(r).sub(o).div(2).equal(),marginInlineStart:0},[`${i}-item > ${i}-item-container > ${i}-item-tail`]:{top:t.calc(t.controlHeight).sub(r).div(2).equal(),insetInlineStart:0,margin:0,padding:`${(0,w.bf)(t.calc(r).add(t.paddingXS).equal())} 0 ${(0,w.bf)(t.paddingXS)}`,"&::after":{marginInlineStart:t.calc(r).sub(t.lineWidth).div(2).equal()}},[`&${i}-small`]:{[`${i}-item-icon`]:{marginTop:t.calc(t.controlHeightSM).sub(r).div(2).equal()},[`${i}-item-process ${i}-item-icon`]:{marginTop:t.calc(t.controlHeightSM).sub(o).div(2).equal()},[`${i}-item > ${i}-item-container > ${i}-item-tail`]:{top:t.calc(t.controlHeightSM).sub(r).div(2).equal()}},[`${i}-item:first-child ${i}-icon-dot`]:{insetInlineStart:0},[`${i}-item-content`]:{width:"inherit"}}}};var j=t=>{const{componentCls:i}=t;return{[`&${i}-rtl`]:{direction:"rtl",[`${i}-item`]:{"&-subtitle":{float:"left"}},[`&${i}-navigation`]:{[`${i}-item::after`]:{transform:"rotate(-45deg)"}},[`&${i}-vertical`]:{[`> ${i}-item`]:{"&::after":{transform:"rotate(225deg)"},[`${i}-item-icon`]:{float:"right"}}},[`&${i}-dot`]:{[`${i}-item-icon ${i}-icon-dot, &${i}-small ${i}-item-icon ${i}-icon-dot`]:{float:"right"}}}}};var M=t=>{const{componentCls:i,iconSizeSM:e,fontSizeSM:n,fontSize:o,colorTextDescription:r}=t;return{[`&${i}-small`]:{[`&${i}-horizontal:not(${i}-label-vertical) ${i}-item`]:{paddingInlineStart:t.paddingSM,"&:first-child":{paddingInlineStart:0}},[`${i}-item-icon`]:{width:e,height:e,marginTop:0,marginBottom:0,marginInline:`0 ${(0,w.bf)(t.marginXS)}`,fontSize:n,lineHeight:(0,w.bf)(e),textAlign:"center",borderRadius:e},[`${i}-item-title`]:{paddingInlineEnd:t.paddingSM,fontSize:o,lineHeight:(0,w.bf)(e),"&::after":{top:t.calc(e).div(2).equal()}},[`${i}-item-description`]:{color:r,fontSize:o},[`${i}-item-tail`]:{top:t.calc(e).div(2).sub(t.paddingXXS).equal()},[`${i}-item-custom ${i}-item-icon`]:{width:"inherit",height:"inherit",lineHeight:"inherit",background:"none",border:0,borderRadius:0,[`> ${i}-icon`]:{fontSize:e,lineHeight:(0,w.bf)(e),transform:"none"}}}}};var X=t=>{const{componentCls:i,iconSizeSM:e,iconSize:n}=t;return{[`&${i}-vertical`]:{display:"flex",flexDirection:"column",[`> ${i}-item`]:{display:"block",flex:"1 0 auto",paddingInlineStart:0,overflow:"visible",[`${i}-item-icon`]:{float:"left",marginInlineEnd:t.margin},[`${i}-item-content`]:{display:"block",minHeight:t.calc(t.controlHeight).mul(1.5).equal(),overflow:"hidden"},[`${i}-item-title`]:{lineHeight:(0,w.bf)(n)},[`${i}-item-description`]:{paddingBottom:t.paddingSM}},[`> ${i}-item > ${i}-item-container > ${i}-item-tail`]:{position:"absolute",top:0,insetInlineStart:t.calc(n).div(2).sub(t.lineWidth).equal(),width:t.lineWidth,height:"100%",padding:`${(0,w.bf)(t.calc(t.marginXXS).mul(1.5).add(n).equal())} 0 ${(0,w.bf)(t.calc(t.marginXXS).mul(1.5).equal())}`,"&::after":{width:t.lineWidth,height:"100%"}},[`> ${i}-item:not(:last-child) > ${i}-item-container > ${i}-item-tail`]:{display:"block"},[` > ${i}-item > ${i}-item-container > ${i}-item-content > ${i}-item-title`]:{"&::after":{display:"none"}},[`&${i}-small ${i}-item-container`]:{[`${i}-item-tail`]:{position:"absolute",top:0,insetInlineStart:t.calc(e).div(2).sub(t.lineWidth).equal(),padding:`${(0,w.bf)(t.calc(t.marginXXS).mul(1.5).add(e).equal())} 0 ${(0,w.bf)(t.calc(t.marginXXS).mul(1.5).equal())}`},[`${i}-item-title`]:{lineHeight:(0,w.bf)(e)}}}}};const D=(t,i)=>{const e=`${i.componentCls}-item`,n=`${t}IconColor`,o=`${t}TitleColor`,r=`${t}DescriptionColor`,a=`${t}TailColor`,l=`${t}IconBorderColor`,c=`${t}DotColor`;return{[`${e}-${t} ${e}-icon`]:{backgroundColor:i[`${t}IconBgColor`],borderColor:i[l],[`> ${i.componentCls}-icon`]:{color:i[n],[`${i.componentCls}-icon-dot`]:{background:i[c]}}},[`${e}-${t}${e}-custom ${e}-icon`]:{[`> ${i.componentCls}-icon`]:{color:i[c]}},[`${e}-${t} > ${e}-container > ${e}-content > ${e}-title`]:{color:i[o],"&::after":{backgroundColor:i[a]}},[`${e}-${t} > ${e}-container > ${e}-content > ${e}-description`]:{color:i[r]},[`${e}-${t} > ${e}-container > ${e}-tail::after`]:{backgroundColor:i[a]}}},B=t=>{const{componentCls:i,motionDurationSlow:e}=t,n=`${i}-item`,o=`${n}-icon`;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({[n]:{position:"relative",display:"inline-block",flex:1,overflow:"hidden",verticalAlign:"top","&:last-child":{flex:"none",[`> ${n}-container > ${n}-tail, > ${n}-container >  ${n}-content > ${n}-title::after`]:{display:"none"}}},[`${n}-container`]:{outline:"none","&:focus-visible":{[o]:Object.assign({},(0,x.oN)(t))}},[`${o}, ${n}-content`]:{display:"inline-block",verticalAlign:"top"},[o]:{width:t.iconSize,height:t.iconSize,marginTop:0,marginBottom:0,marginInlineStart:0,marginInlineEnd:t.marginXS,fontSize:t.iconFontSize,fontFamily:t.fontFamily,lineHeight:(0,w.bf)(t.iconSize),textAlign:"center",borderRadius:t.iconSize,border:`${(0,w.bf)(t.lineWidth)} ${t.lineType} transparent`,transition:`background-color ${e}, border-color ${e}`,[`${i}-icon`]:{position:"relative",top:t.iconTop,color:t.colorPrimary,lineHeight:1}},[`${n}-tail`]:{position:"absolute",top:t.calc(t.iconSize).div(2).equal(),insetInlineStart:0,width:"100%","&::after":{display:"inline-block",width:"100%",height:t.lineWidth,background:t.colorSplit,borderRadius:t.lineWidth,transition:`background ${e}`,content:'""'}},[`${n}-title`]:{position:"relative",display:"inline-block",paddingInlineEnd:t.padding,color:t.colorText,fontSize:t.fontSizeLG,lineHeight:(0,w.bf)(t.titleLineHeight),"&::after":{position:"absolute",top:t.calc(t.titleLineHeight).div(2).equal(),insetInlineStart:"100%",display:"block",width:9999,height:t.lineWidth,background:t.processTailColor,content:'""'}},[`${n}-subtitle`]:{display:"inline",marginInlineStart:t.marginXS,color:t.colorTextDescription,fontWeight:"normal",fontSize:t.fontSize},[`${n}-description`]:{color:t.colorTextDescription,fontSize:t.fontSize}},D("wait",t)),D("process",t)),{[`${n}-process > ${n}-container > ${n}-title`]:{fontWeight:t.fontWeightStrong}}),D("finish",t)),D("error",t)),{[`${n}${i}-next-error > ${i}-item-title::after`]:{background:t.colorError},[`${n}-disabled`]:{cursor:"not-allowed"}})},Z=t=>{const{componentCls:i,motionDurationSlow:e}=t;return{[`& ${i}-item`]:{[`&:not(${i}-item-active)`]:{[`& > ${i}-item-container[role='button']`]:{cursor:"pointer",[`${i}-item`]:{[`&-title, &-subtitle, &-description, &-icon ${i}-icon`]:{transition:`color ${e}`}},"&:hover":{[`${i}-item`]:{"&-title, &-subtitle, &-description":{color:t.colorPrimary}}}},[`&:not(${i}-item-process)`]:{[`& > ${i}-item-container[role='button']:hover`]:{[`${i}-item`]:{"&-icon":{borderColor:t.colorPrimary,[`${i}-icon`]:{color:t.colorPrimary}}}}}}},[`&${i}-horizontal:not(${i}-label-vertical)`]:{[`${i}-item`]:{paddingInlineStart:t.padding,whiteSpace:"nowrap","&:first-child":{paddingInlineStart:0},[`&:last-child ${i}-item-title`]:{paddingInlineEnd:0},"&-tail":{display:"none"},"&-description":{maxWidth:t.descriptionMaxWidth,whiteSpace:"normal"}}}}},P=t=>{const{componentCls:i}=t;return{[i]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,x.Wf)(t)),{display:"flex",width:"100%",fontSize:0,textAlign:"initial"}),B(t)),Z(t)),k(t)),M(t)),X(t)),T(t)),E(t)),N(t)),O(t)),j(t)),W(t)),H(t))}};var A=(0,z.I$)("Steps",(t=>{const{colorTextDisabled:i,controlHeightLG:e,colorTextLightSolid:n,colorText:o,colorPrimary:r,colorTextDescription:a,colorTextQuaternary:l,colorError:c,colorBorderSecondary:s,colorSplit:d}=t,m=(0,q.IX)(t,{processIconColor:n,processTitleColor:o,processDescriptionColor:o,processIconBgColor:r,processIconBorderColor:r,processDotColor:r,processTailColor:d,waitTitleColor:a,waitDescriptionColor:a,waitTailColor:d,waitDotColor:i,finishIconColor:r,finishTitleColor:o,finishDescriptionColor:a,finishTailColor:r,finishDotColor:r,errorIconColor:n,errorTitleColor:c,errorDescriptionColor:c,errorTailColor:d,errorIconBgColor:c,errorIconBorderColor:c,errorDotColor:c,stepsNavActiveColor:r,stepsProgressSize:e,inlineDotSize:6,inlineTitleColor:l,inlineTailColor:s});return[P(m)]}),(t=>({titleLineHeight:t.controlHeight,customIconSize:t.controlHeight,customIconTop:0,customIconFontSize:t.controlHeightSM,iconSize:t.controlHeight,iconTop:-.5,iconFontSize:t.fontSize,iconSizeSM:t.fontSizeHeading3,dotSize:t.controlHeight/4,dotCurrentSize:t.controlHeightLG/4,navArrowColor:t.colorTextDisabled,navContentMaxWidth:"unset",descriptionMaxWidth:140,waitIconColor:t.wireframe?t.colorTextDisabled:t.colorTextLabel,waitIconBgColor:t.wireframe?t.colorBgContainer:t.colorFillContent,waitIconBorderColor:t.wireframe?t.colorTextDisabled:"transparent",finishIconBgColor:t.wireframe?t.colorBgContainer:t.controlItemBgActive,finishIconBorderColor:t.wireframe?t.colorPrimary:t.controlItemBgActive}))),L=e(50344);var R=function(t,i){var e={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&i.indexOf(n)<0&&(e[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(t);o<n.length;o++)i.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(e[n[o]]=t[n[o]])}return e};const G=t=>{const{percent:i,size:e,className:a,rootClassName:c,direction:s,items:d,responsive:m=!0,current:p=0,children:g,style:$}=t,u=R(t,["percent","size","className","rootClassName","direction","items","responsive","current","children","style"]),{xs:h}=(0,C.Z)(m),{getPrefixCls:b,direction:w,className:x,style:z}=(0,S.dj)("steps"),q=n.useMemo((()=>m&&h?"vertical":s),[h,s]),k=(0,v.Z)(e),T=b("steps",t.prefixCls),[H,E,O]=A(T),W="inline"===t.type,N=b("",t.iconPrefix),j=function(t,i){return t||function(t){return t.filter((t=>t))}((0,L.Z)(i).map((t=>{if(n.isValidElement(t)){const{props:i}=t;return Object.assign({},i)}return null})))}(d,g),M=W?void 0:i,X=Object.assign(Object.assign({},z),$),D=l()(x,{[`${T}-rtl`]:"rtl"===w,[`${T}-with-progress`]:void 0!==M},a,c,E,O),B={finish:n.createElement(o.Z,{className:`${T}-finish-icon`}),error:n.createElement(r.Z,{className:`${T}-error-icon`})};return H(n.createElement(f,Object.assign({icons:B},u,{style:X,current:p,size:k,items:j,itemRender:W?(t,i)=>t.description?n.createElement(y.Z,{title:t.description},i):i:void 0,stepIcon:t=>{let{node:i,status:e}=t;if("process"===e&&void 0!==M){const t="small"===k?32:40;return n.createElement("div",{className:`${T}-progress-icon`},n.createElement(I.Z,{type:"circle",percent:M,size:t,strokeWidth:4,format:()=>null}),i)}return i},direction:q,prefixCls:T,iconPrefix:N,className:D})))};G.Step=f.Step;var F=G}}]);