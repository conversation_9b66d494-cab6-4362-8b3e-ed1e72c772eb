"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[309],{51042:function(e,r,n){var t=n(1413),o=n(67294),a=n(42110),c=n(91146),l=function(e,r){return o.createElement(c.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:r,icon:a.Z}))},i=o.forwardRef(l);r.Z=i},34320:function(e,r,n){n.r(r),n.d(r,{default:function(){return E}});n(9783);var t=n(15009),o=n.n(t),a=n(97857),c=n.n(a),l=n(99289),i=n.n(l),s=n(5574),u=n.n(s),d=n(51042),p=n(97131),f=n(12453),h=n(17788),m=n(47019),v=n(2453),b=n(42075),g=n(66309),x=n(83622),y=n(55102),C=n(26412),k=n(67294),Z=n(78158);function w(e){return j.apply(this,arguments)}function j(){return(j=i()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,Z.N)("/api/llms",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function P(e){return $.apply(this,arguments)}function $(){return($=i()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,Z.N)("/api/llms/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function S(e){return O.apply(this,arguments)}function O(){return(O=i()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,Z.N)("/api/llms/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var _=n(85893),I=h.Z.confirm,T=[{id:"1",name:"公告1",m_name:"状态1",created_at:"2023-10-01 10:00:00",is_active:!0,provider:"local"},{id:"2",name:"公告2",m_name:"状态2",created_at:"2023-10-02 11:00:00",is_active:!1,provider:"OpenAI"},{id:"3",name:"公告3",m_name:"状态3",created_at:"2023-10-03 12:00:00",is_active:!0,provider:"DeepSeek"}],E=function(){var e=(0,k.useState)(!1),r=u()(e,2),n=r[0],t=r[1],a=(0,k.useState)(!1),l=u()(a,2),s=l[0],Z=l[1],j=(0,k.useState)(void 0),$=u()(j,2),O=$[0],E=$[1],N=(0,k.useRef)(),B=m.Z.useForm(),z=u()(B,1)[0],F=(0,k.useState)({}),R=u()(F,2),H=(R[0],R[1],function(){var e=i()(o()().mark((function e(r){var n,a;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=v.ZP.loading("正在添加"),e.prev=1,e.next=4,w(c()({},r));case 4:return n(),v.ZP.success("添加模型成功"),t(!1),null===(a=N.current)||void 0===a||a.reload(),z.resetFields(),e.abrupt("return",!0);case 12:return e.prev=12,e.t0=e.catch(1),n(),v.ZP.error("添加失败，请重试"),e.abrupt("return",!1);case 17:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(r){return e.apply(this,arguments)}}()),q=function(){var e=i()(o()().mark((function e(r){var n,a,l;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=null==O?void 0:O.id){e.next=4;break}return v.ZP.error("更新失败，缺少模型 ID"),e.abrupt("return",!1);case 4:return a=v.ZP.loading("正在更新"),e.prev=5,e.next=8,P(c()(c()({},r),{},{id:n}));case 8:return a(),v.ZP.success("更新成功"),t(!1),E(void 0),null===(l=N.current)||void 0===l||l.reload(),z.resetFields(),e.abrupt("return",!0);case 17:return e.prev=17,e.t0=e.catch(5),a(),v.ZP.error("更新失败，请重试"),e.abrupt("return",!1);case 22:case"end":return e.stop()}}),e,null,[[5,17]])})));return function(r){return e.apply(this,arguments)}}(),L=function(){var e=i()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!O){e.next=4;break}return e.abrupt("return",q(r));case 4:return e.abrupt("return",H(r));case 5:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),W=function(){var e=i()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:I({title:"确认删除",content:"你确定要删除这个公告吗？",onOk:function(){var e=i()(o()().mark((function e(){var n,t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=v.ZP.loading("正在删除"),e.prev=1,e.next=4,S(r.id);case 4:return n(),v.ZP.success("删除成功"),null===(t=N.current)||void 0===t||t.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),n(),v.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(){return e.apply(this,arguments)}}(),onCancel:function(){console.log("取消删除")}});case 1:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),D=[{title:"公告内容",dataIndex:"name",valueType:"text",render:function(e,r){return(0,_.jsxs)(b.Z,{children:[r.name,(0,_.jsx)(g.Z,{color:r.is_active?"success":"default",children:r.is_active?"已发布":"未发布"})]})}},{title:"状态",dataIndex:"m_name",valueType:"text"},{title:"发布时间",dataIndex:"created_at",valueType:"dateTime",search:!1},{title:"操作",dataIndex:"option",valueType:"option",width:60,render:function(e,r){return[(0,_.jsx)(x.ZP,{type:"link",style:{width:"50px"},onClick:function(){E(r),z.setFieldsValue(r),t(!0)},children:"编辑"},"edit-".concat(r.id)),(0,_.jsx)(x.ZP,{type:"link",style:{width:"50px"},onClick:function(){var e;I({title:"确认".concat(r.is_active?"取消发布":"发布"),content:"确定要".concat(r.is_active?"取消发布":"发布","该公告吗？"),onOk:(e=i()(o()().mark((function e(){var n,t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=v.ZP.loading("正在更新状态"),e.prev=1,e.next=4,P({id:r.id,is_active:!r.is_active});case 4:n(),v.ZP.success("状态更新成功"),null===(t=N.current)||void 0===t||t.reload(),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(1),n(),v.ZP.error("状态更新失败，请重试");case 13:case"end":return e.stop()}}),e,null,[[1,9]])}))),function(){return e.apply(this,arguments)})})},children:r.is_active?"取消发布":"发布"},"toggle-".concat(r.id)),(0,_.jsx)(x.ZP,{type:"link",style:{width:"50px"},onClick:function(){E(r),Z(!0)},children:"查看"},"view-".concat(r.id)),(0,_.jsx)(x.ZP,{type:"link",danger:!0,style:{width:"50px"},onClick:function(){return W(r)},children:"删除"},"delete-".concat(r.id))]}}];return(0,_.jsxs)(p._z,{children:[(0,_.jsx)(f.Z,{headerTitle:"公告管理",actionRef:N,rowKey:"id",search:{labelWidth:120,defaultCollapsed:!0},options:!1,toolBarRender:function(){return[(0,_.jsxs)(x.ZP,{type:"primary",onClick:function(){E(void 0),z.resetFields(),t(!0)},children:[(0,_.jsx)(d.Z,{})," 新建"]},"primary")]},request:function(){var e=i()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{data:T,success:!0,total:T.length});case 1:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),columns:D}),(0,_.jsx)(h.Z,{title:O?"编辑公告":"新建公告",visible:n,onCancel:function(){t(!1),E(void 0),z.resetFields()},onOk:function(){return z.submit()},destroyOnClose:!0,forceRender:!0,children:(0,_.jsxs)(m.Z,{form:z,layout:"horizontal",onFinish:L,labelCol:{span:6},wrapperCol:{span:18},children:[O&&(0,_.jsx)(m.Z.Item,{name:"id",hidden:!0,children:(0,_.jsx)(y.Z,{})}),(0,_.jsx)(m.Z.Item,{name:"name",label:"公告内容",rules:[{required:!0,message:"请输入公告内容"}],children:(0,_.jsx)(y.Z,{})}),(0,_.jsx)(m.Z.Item,{name:"created_at",label:"发布时间",rules:[{required:!0,message:"请输入发布时间"}],children:(0,_.jsx)(y.Z,{type:"datetime-local"})}),(0,_.jsx)(m.Z.Item,{name:"m_name",label:"状态",rules:[{required:!0,message:"请输入状态"}],children:(0,_.jsx)(y.Z,{})})]})}),O&&(0,_.jsx)(h.Z,{title:"公告详情",visible:s,onCancel:function(){return Z(!1)},footer:null,children:(0,_.jsxs)(C.Z,{column:1,children:[(0,_.jsx)(C.Z.Item,{label:"公告内容",children:O.name}),(0,_.jsx)(C.Z.Item,{label:"状态",children:O.m_name}),(0,_.jsx)(C.Z.Item,{label:"发布时间",children:O.created_at}),(0,_.jsx)(C.Z.Item,{label:"是否发布",children:O.is_active?"已发布":"未发布"})]})})]})}},66309:function(e,r,n){n.d(r,{Z:function(){return _}});var t=n(67294),o=n(93967),a=n.n(o),c=n(98423),l=n(98787),i=n(69760),s=n(96159),u=n(45353),d=n(53124),p=n(11568),f=n(15063),h=n(14747),m=n(83262),v=n(83559);const b=e=>{const{lineWidth:r,fontSizeIcon:n,calc:t}=e,o=e.fontSizeSM;return(0,m.IX)(e,{tagFontSize:o,tagLineHeight:(0,p.bf)(t(e.lineHeightSM).mul(o).equal()),tagIconSize:t(n).sub(t(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},g=e=>({defaultBg:new f.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var x=(0,v.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:n,tagPaddingHorizontal:t,componentCls:o,calc:a}=e,c=a(t).sub(n).equal(),l=a(r).sub(n).equal();return{[o]:Object.assign(Object.assign({},(0,h.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:c,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:c}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(b(e))),g),y=function(e,r){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(n[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(t=Object.getOwnPropertySymbols(e);o<t.length;o++)r.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(n[t[o]]=e[t[o]])}return n};const C=t.forwardRef(((e,r)=>{const{prefixCls:n,style:o,className:c,checked:l,onChange:i,onClick:s}=e,u=y(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:f}=t.useContext(d.E_),h=p("tag",n),[m,v,b]=x(h),g=a()(h,`${h}-checkable`,{[`${h}-checkable-checked`]:l},null==f?void 0:f.className,c,v,b);return m(t.createElement("span",Object.assign({},u,{ref:r,style:Object.assign(Object.assign({},o),null==f?void 0:f.style),className:g,onClick:e=>{null==i||i(!l),null==s||s(e)}})))}));var k=C,Z=n(98719);var w=(0,v.bk)(["Tag","preset"],(e=>(e=>(0,Z.Z)(e,((r,n)=>{let{textColor:t,lightBorderColor:o,lightColor:a,darkColor:c}=n;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:t,background:a,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:c,borderColor:c},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(b(e))),g);const j=(e,r,n)=>{const t="string"!=typeof(o=n)?o:o.charAt(0).toUpperCase()+o.slice(1);var o;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${n}`],background:e[`color${t}Bg`],borderColor:e[`color${t}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var P=(0,v.bk)(["Tag","status"],(e=>{const r=b(e);return[j(r,"success","Success"),j(r,"processing","Info"),j(r,"error","Error"),j(r,"warning","Warning")]}),g),$=function(e,r){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(n[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(t=Object.getOwnPropertySymbols(e);o<t.length;o++)r.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(n[t[o]]=e[t[o]])}return n};const S=t.forwardRef(((e,r)=>{const{prefixCls:n,className:o,rootClassName:p,style:f,children:h,icon:m,color:v,onClose:b,bordered:g=!0,visible:y}=e,C=$(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:k,direction:Z,tag:j}=t.useContext(d.E_),[S,O]=t.useState(!0),_=(0,c.Z)(C,["closeIcon","closable"]);t.useEffect((()=>{void 0!==y&&O(y)}),[y]);const I=(0,l.o2)(v),T=(0,l.yT)(v),E=I||T,N=Object.assign(Object.assign({backgroundColor:v&&!E?v:void 0},null==j?void 0:j.style),f),B=k("tag",n),[z,F,R]=x(B),H=a()(B,null==j?void 0:j.className,{[`${B}-${v}`]:E,[`${B}-has-color`]:v&&!E,[`${B}-hidden`]:!S,[`${B}-rtl`]:"rtl"===Z,[`${B}-borderless`]:!g},o,p,F,R),q=e=>{e.stopPropagation(),null==b||b(e),e.defaultPrevented||O(!1)},[,L]=(0,i.Z)((0,i.w)(e),(0,i.w)(j),{closable:!1,closeIconRender:e=>{const r=t.createElement("span",{className:`${B}-close-icon`,onClick:q},e);return(0,s.wm)(e,r,(e=>({onClick:r=>{var n;null===(n=null==e?void 0:e.onClick)||void 0===n||n.call(e,r),q(r)},className:a()(null==e?void 0:e.className,`${B}-close-icon`)})))}}),W="function"==typeof C.onClick||h&&"a"===h.type,D=m||null,M=D?t.createElement(t.Fragment,null,D,h&&t.createElement("span",null,h)):h,A=t.createElement("span",Object.assign({},_,{ref:r,className:H,style:N}),M,L,I&&t.createElement(w,{key:"preset",prefixCls:B}),T&&t.createElement(P,{key:"status",prefixCls:B}));return z(W?t.createElement(u.Z,{component:"Tag"},A):A)})),O=S;O.CheckableTag=k;var _=O}}]);