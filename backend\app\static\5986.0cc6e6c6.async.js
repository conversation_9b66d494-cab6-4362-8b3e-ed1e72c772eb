"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5986],{47046:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"}},49495:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"}},82061:function(e,t,o){var n=o(1413),r=o(67294),l=o(47046),a=o(91146),i=function(e,t){return r.createElement(a.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:l.Z}))},c=r.forwardRef(i);t.Z=c},69753:function(e,t,o){var n=o(1413),r=o(67294),l=o(49495),a=o(91146),i=function(e,t){return r.createElement(a.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:l.Z}))},c=r.forwardRef(i);t.Z=c},47389:function(e,t,o){var n=o(1413),r=o(67294),l=o(27363),a=o(91146),i=function(e,t){return r.createElement(a.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:l.Z}))},c=r.forwardRef(i);t.Z=c},43008:function(e,t,o){o.d(t,{Z:function(){return c}});var n=o(1413),r=o(67294),l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM544 472c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v108H372c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h108v108c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V644h108c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V472z"}}]},name:"file-add",theme:"outlined"},a=o(91146),i=function(e,t){return r.createElement(a.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:l}))};var c=r.forwardRef(i)},37446:function(e,t,o){o.d(t,{Z:function(){return c}});var n=o(1413),r=o(67294),l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"},a=o(91146),i=function(e,t){return r.createElement(a.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:l}))};var c=r.forwardRef(i)},81643:function(e,t,o){o.d(t,{Z:function(){return n}});const n=e=>e?"function"==typeof e?e():e:null},86738:function(e,t,o){o.d(t,{Z:function(){return k}});var n=o(67294),r=o(29950),l=o(93967),a=o.n(l),i=o(21770),c=o(98423),s=o(53124),d=o(55241),p=o(86743),u=o(81643),g=o(83622),m=o(33671),f=o(10110),b=o(24457),v=o(66330),y=o(83559);var h=(0,y.I$)("Popconfirm",(e=>(e=>{const{componentCls:t,iconCls:o,antCls:n,zIndexPopup:r,colorText:l,colorWarning:a,marginXXS:i,marginXS:c,fontSize:s,fontWeightStrong:d,colorTextHeading:p}=e;return{[t]:{zIndex:r,[`&${n}-popover`]:{fontSize:s},[`${t}-message`]:{marginBottom:c,display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${t}-message-icon ${o}`]:{color:a,fontSize:s,lineHeight:1,marginInlineEnd:c},[`${t}-title`]:{fontWeight:d,color:p,"&:only-child":{fontWeight:"normal"}},[`${t}-description`]:{marginTop:i,color:l}},[`${t}-buttons`]:{textAlign:"end",whiteSpace:"nowrap",button:{marginInlineStart:c}}}}})(e)),(e=>{const{zIndexPopupBase:t}=e;return{zIndexPopup:t+60}}),{resetStyle:!1}),C=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]])}return o};const O=e=>{const{prefixCls:t,okButtonProps:o,cancelButtonProps:l,title:a,description:i,cancelText:c,okText:d,okType:v="primary",icon:y=n.createElement(r.Z,null),showCancel:h=!0,close:C,onConfirm:O,onCancel:x,onPopupClick:$}=e,{getPrefixCls:w}=n.useContext(s.E_),[k]=(0,f.Z)("Popconfirm",b.Z.Popconfirm),j=(0,u.Z)(a),E=(0,u.Z)(i);return n.createElement("div",{className:`${t}-inner-content`,onClick:$},n.createElement("div",{className:`${t}-message`},y&&n.createElement("span",{className:`${t}-message-icon`},y),n.createElement("div",{className:`${t}-message-text`},j&&n.createElement("div",{className:`${t}-title`},j),E&&n.createElement("div",{className:`${t}-description`},E))),n.createElement("div",{className:`${t}-buttons`},h&&n.createElement(g.ZP,Object.assign({onClick:x,size:"small"},l),c||(null==k?void 0:k.cancelText)),n.createElement(p.Z,{buttonProps:Object.assign(Object.assign({size:"small"},(0,m.nx)(v)),o),actionFn:O,close:C,prefixCls:w("btn"),quitOnNullishReturnValue:!0,emitEvent:!0},d||(null==k?void 0:k.okText))))};var x=e=>{const{prefixCls:t,placement:o,className:r,style:l}=e,i=C(e,["prefixCls","placement","className","style"]),{getPrefixCls:c}=n.useContext(s.E_),d=c("popconfirm",t),[p]=h(d);return p(n.createElement(v.ZP,{placement:o,className:a()(d,r),style:l,content:n.createElement(O,Object.assign({prefixCls:d},i))}))},$=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]])}return o};const w=n.forwardRef(((e,t)=>{var o,l;const{prefixCls:p,placement:u="top",trigger:g="click",okType:m="primary",icon:f=n.createElement(r.Z,null),children:b,overlayClassName:v,onOpenChange:y,onVisibleChange:C,overlayStyle:x,styles:w,classNames:k}=e,j=$(e,["prefixCls","placement","trigger","okType","icon","children","overlayClassName","onOpenChange","onVisibleChange","overlayStyle","styles","classNames"]),{getPrefixCls:E,className:P,style:S,classNames:Z,styles:N}=(0,s.dj)("popconfirm"),[z,I]=(0,i.Z)(!1,{value:null!==(o=e.open)&&void 0!==o?o:e.visible,defaultValue:null!==(l=e.defaultOpen)&&void 0!==l?l:e.defaultVisible}),B=(e,t)=>{I(e,!0),null==C||C(e),null==y||y(e,t)},T=E("popconfirm",p),H=a()(T,P,v,Z.root,null==k?void 0:k.root),W=a()(Z.body,null==k?void 0:k.body),[M]=h(T);return M(n.createElement(d.Z,Object.assign({},(0,c.Z)(j,["title"]),{trigger:g,placement:u,onOpenChange:(t,o)=>{const{disabled:n=!1}=e;n||B(t,o)},open:z,ref:t,classNames:{root:H,body:W},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},N.root),S),x),null==w?void 0:w.root),body:Object.assign(Object.assign({},N.body),null==w?void 0:w.body)},content:n.createElement(O,Object.assign({okType:m,icon:f},e,{prefixCls:T,close:e=>{B(!1,e)},onConfirm:t=>{var o;return null===(o=e.onConfirm)||void 0===o?void 0:o.call(void 0,t)},onCancel:t=>{var o;B(!1,t),null===(o=e.onCancel)||void 0===o||o.call(void 0,t)}})),"data-popover-inject":!0}),b))}));w._InternalPanelDoNotUseOrYouWillBeFired=x;var k=w},66330:function(e,t,o){o.d(t,{aV:function(){return p}});var n=o(67294),r=o(93967),l=o.n(r),a=o(92419),i=o(81643),c=o(53124),s=o(20136),d=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]])}return o};const p=e=>{let{title:t,content:o,prefixCls:r}=e;return t||o?n.createElement(n.Fragment,null,t&&n.createElement("div",{className:`${r}-title`},t),o&&n.createElement("div",{className:`${r}-inner-content`},o)):null},u=e=>{const{hashId:t,prefixCls:o,className:r,style:c,placement:s="top",title:d,content:u,children:g}=e,m=(0,i.Z)(d),f=(0,i.Z)(u),b=l()(t,o,`${o}-pure`,`${o}-placement-${s}`,r);return n.createElement("div",{className:b,style:c},n.createElement("div",{className:`${o}-arrow`}),n.createElement(a.G,Object.assign({},e,{className:t,prefixCls:o}),g||n.createElement(p,{prefixCls:o,title:m,content:f})))};t.ZP=e=>{const{prefixCls:t,className:o}=e,r=d(e,["prefixCls","className"]),{getPrefixCls:a}=n.useContext(c.E_),i=a("popover",t),[p,g,m]=(0,s.Z)(i);return p(n.createElement(u,Object.assign({},r,{prefixCls:i,hashId:g,className:l()(o,m)})))}},55241:function(e,t,o){var n=o(67294),r=o(93967),l=o.n(r),a=o(21770),i=o(15105),c=o(81643),s=o(33603),d=o(96159),p=o(83062),u=o(66330),g=o(53124),m=o(20136),f=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]])}return o};const b=n.forwardRef(((e,t)=>{var o,r;const{prefixCls:b,title:v,content:y,overlayClassName:h,placement:C="top",trigger:O="hover",children:x,mouseEnterDelay:$=.1,mouseLeaveDelay:w=.1,onOpenChange:k,overlayStyle:j={},styles:E,classNames:P}=e,S=f(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:Z,className:N,style:z,classNames:I,styles:B}=(0,g.dj)("popover"),T=Z("popover",b),[H,W,M]=(0,m.Z)(T),V=Z(),R=l()(h,W,M,N,I.root,null==P?void 0:P.root),_=l()(I.body,null==P?void 0:P.body),[D,L]=(0,a.Z)(!1,{value:null!==(o=e.open)&&void 0!==o?o:e.visible,defaultValue:null!==(r=e.defaultOpen)&&void 0!==r?r:e.defaultVisible}),F=(e,t)=>{L(e,!0),null==k||k(e,t)},X=(0,c.Z)(v),A=(0,c.Z)(y);return H(n.createElement(p.Z,Object.assign({placement:C,trigger:O,mouseEnterDelay:$,mouseLeaveDelay:w},S,{prefixCls:T,classNames:{root:R,body:_},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},B.root),z),j),null==E?void 0:E.root),body:Object.assign(Object.assign({},B.body),null==E?void 0:E.body)},ref:t,open:D,onOpenChange:e=>{F(e)},overlay:X||A?n.createElement(u.aV,{prefixCls:T,title:X,content:A}):null,transitionName:(0,s.m)(V,"zoom-big",S.transitionName),"data-popover-inject":!0}),(0,d.Tm)(x,{onKeyDown:e=>{var t,o;n.isValidElement(x)&&(null===(o=null==x?void 0:(t=x.props).onKeyDown)||void 0===o||o.call(t,e)),(e=>{e.keyCode===i.Z.ESC&&F(!1,e)})(e)}})))}));b._InternalPanelDoNotUseOrYouWillBeFired=u.ZP,t.Z=b},20136:function(e,t,o){var n=o(14747),r=o(50438),l=o(97414),a=o(79511),i=o(8796),c=o(83559),s=o(83262);const d=e=>{const{componentCls:t,popoverColor:o,titleMinWidth:r,fontWeightStrong:a,innerPadding:i,boxShadowSecondary:c,colorTextHeading:s,borderRadiusLG:d,zIndexPopup:p,titleMarginBottom:u,colorBgElevated:g,popoverBg:m,titleBorderBottom:f,innerContentPadding:b,titlePadding:v}=e;return[{[t]:Object.assign(Object.assign({},(0,n.Wf)(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:p,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":g,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${t}-content`]:{position:"relative"},[`${t}-inner`]:{backgroundColor:m,backgroundClip:"padding-box",borderRadius:d,boxShadow:c,padding:i},[`${t}-title`]:{minWidth:r,marginBottom:u,color:s,fontWeight:a,borderBottom:f,padding:v},[`${t}-inner-content`]:{color:o,padding:b}})},(0,l.ZP)(e,"var(--antd-arrow-background-color)"),{[`${t}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${t}-content`]:{display:"inline-block"}}}]},p=e=>{const{componentCls:t}=e;return{[t]:i.i.map((o=>{const n=e[`${o}6`];return{[`&${t}-${o}`]:{"--antd-arrow-background-color":n,[`${t}-inner`]:{backgroundColor:n},[`${t}-arrow`]:{background:"transparent"}}}}))}};t.Z=(0,c.I$)("Popover",(e=>{const{colorBgElevated:t,colorText:o}=e,n=(0,s.IX)(e,{popoverBg:t,popoverColor:o});return[d(n),p(n),(0,r._y)(n,"zoom-big")]}),(e=>{const{lineWidth:t,controlHeight:o,fontHeight:n,padding:r,wireframe:i,zIndexPopupBase:c,borderRadiusLG:s,marginXS:d,lineType:p,colorSplit:u,paddingSM:g}=e,m=o-n,f=m/2,b=m/2-t,v=r;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:c+30},(0,a.w)(e)),(0,l.wZ)({contentRadius:s,limitVerticalRadius:!0})),{innerPadding:i?0:12,titleMarginBottom:i?0:d,titlePadding:i?`${f}px ${v}px ${b}px`:0,titleBorderBottom:i?`${t}px ${p} ${u}`:"none",innerContentPadding:i?`${g}px ${v}px`:0})}),{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]})},66309:function(e,t,o){o.d(t,{Z:function(){return Z}});var n=o(67294),r=o(93967),l=o.n(r),a=o(98423),i=o(98787),c=o(69760),s=o(96159),d=o(45353),p=o(53124),u=o(11568),g=o(15063),m=o(14747),f=o(83262),b=o(83559);const v=e=>{const{lineWidth:t,fontSizeIcon:o,calc:n}=e,r=e.fontSizeSM;return(0,f.IX)(e,{tagFontSize:r,tagLineHeight:(0,u.bf)(n(e.lineHeightSM).mul(r).equal()),tagIconSize:n(o).sub(n(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},y=e=>({defaultBg:new g.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var h=(0,b.I$)("Tag",(e=>(e=>{const{paddingXXS:t,lineWidth:o,tagPaddingHorizontal:n,componentCls:r,calc:l}=e,a=l(n).sub(o).equal(),i=l(t).sub(o).equal();return{[r]:Object.assign(Object.assign({},(0,m.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:a,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,u.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${r}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${r}-close-icon`]:{marginInlineStart:i,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${r}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${r}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:a}}),[`${r}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(v(e))),y),C=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]])}return o};const O=n.forwardRef(((e,t)=>{const{prefixCls:o,style:r,className:a,checked:i,onChange:c,onClick:s}=e,d=C(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:u,tag:g}=n.useContext(p.E_),m=u("tag",o),[f,b,v]=h(m),y=l()(m,`${m}-checkable`,{[`${m}-checkable-checked`]:i},null==g?void 0:g.className,a,b,v);return f(n.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},r),null==g?void 0:g.style),className:y,onClick:e=>{null==c||c(!i),null==s||s(e)}})))}));var x=O,$=o(98719);var w=(0,b.bk)(["Tag","preset"],(e=>(e=>(0,$.Z)(e,((t,o)=>{let{textColor:n,lightBorderColor:r,lightColor:l,darkColor:a}=o;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:n,background:l,borderColor:r,"&-inverse":{color:e.colorTextLightSolid,background:a,borderColor:a},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(v(e))),y);const k=(e,t,o)=>{const n="string"!=typeof(r=o)?r:r.charAt(0).toUpperCase()+r.slice(1);var r;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${o}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var j=(0,b.bk)(["Tag","status"],(e=>{const t=v(e);return[k(t,"success","Success"),k(t,"processing","Info"),k(t,"error","Error"),k(t,"warning","Warning")]}),y),E=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]])}return o};const P=n.forwardRef(((e,t)=>{const{prefixCls:o,className:r,rootClassName:u,style:g,children:m,icon:f,color:b,onClose:v,bordered:y=!0,visible:C}=e,O=E(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:x,direction:$,tag:k}=n.useContext(p.E_),[P,S]=n.useState(!0),Z=(0,a.Z)(O,["closeIcon","closable"]);n.useEffect((()=>{void 0!==C&&S(C)}),[C]);const N=(0,i.o2)(b),z=(0,i.yT)(b),I=N||z,B=Object.assign(Object.assign({backgroundColor:b&&!I?b:void 0},null==k?void 0:k.style),g),T=x("tag",o),[H,W,M]=h(T),V=l()(T,null==k?void 0:k.className,{[`${T}-${b}`]:I,[`${T}-has-color`]:b&&!I,[`${T}-hidden`]:!P,[`${T}-rtl`]:"rtl"===$,[`${T}-borderless`]:!y},r,u,W,M),R=e=>{e.stopPropagation(),null==v||v(e),e.defaultPrevented||S(!1)},[,_]=(0,c.Z)((0,c.w)(e),(0,c.w)(k),{closable:!1,closeIconRender:e=>{const t=n.createElement("span",{className:`${T}-close-icon`,onClick:R},e);return(0,s.wm)(e,t,(e=>({onClick:t=>{var o;null===(o=null==e?void 0:e.onClick)||void 0===o||o.call(e,t),R(t)},className:l()(null==e?void 0:e.className,`${T}-close-icon`)})))}}),D="function"==typeof O.onClick||m&&"a"===m.type,L=f||null,F=L?n.createElement(n.Fragment,null,L,m&&n.createElement("span",null,m)):m,X=n.createElement("span",Object.assign({},Z,{ref:t,className:V,style:B}),F,_,N&&n.createElement(w,{key:"preset",prefixCls:T}),z&&n.createElement(j,{key:"status",prefixCls:T}));return H(D?n.createElement(d.Z,{component:"Tag"},X):X)})),S=P;S.CheckableTag=x;var Z=S}}]);