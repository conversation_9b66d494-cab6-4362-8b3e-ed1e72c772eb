"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4216],{36027:function(e,r,t){t.d(r,{Z:function(){return l}});var n=t(1413),s=t(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M917.7 148.8l-42.4-42.4c-1.6-1.6-3.6-2.3-5.7-2.3s-4.1.8-5.7 2.3l-76.1 76.1a199.27 199.27 0 00-112.1-34.3c-51.2 0-102.4 19.5-141.5 58.6L432.3 308.7a8.03 8.03 0 000 11.3L704 591.7c1.6 1.6 3.6 2.3 5.7 2.3 2 0 4.1-.8 5.7-2.3l101.9-101.9c68.9-69 77-175.7 24.3-253.5l76.1-76.1c3.1-3.2 3.1-8.3 0-11.4zM769.1 441.7l-59.4 59.4-186.8-186.8 59.4-59.4c24.9-24.9 58.1-38.7 93.4-38.7 35.3 0 68.4 13.7 93.4 38.7 24.9 24.9 38.7 58.1 38.7 93.4 0 35.3-13.8 68.4-38.7 93.4zm-190.2 105a8.03 8.03 0 00-11.3 0L501 613.3 410.7 523l66.7-66.7c3.1-3.1 3.1-8.2 0-11.3L441 408.6a8.03 8.03 0 00-11.3 0L363 475.3l-43-43a7.85 7.85 0 00-5.7-2.3c-2 0-4.1.8-5.7 2.3L206.8 534.2c-68.9 69-77 175.7-24.3 253.5l-76.1 76.1a8.03 8.03 0 000 11.3l42.4 42.4c1.6 1.6 3.6 2.3 5.7 2.3s4.1-.8 5.7-2.3l76.1-76.1c33.7 22.9 72.9 34.3 112.1 34.3 51.2 0 102.4-19.5 141.5-58.6l101.9-101.9c3.1-3.1 3.1-8.2 0-11.3l-43-43 66.7-66.7c3.1-3.1 3.1-8.2 0-11.3l-36.6-36.2zM441.7 769.1a131.32 131.32 0 01-93.4 38.7c-35.3 0-68.4-13.7-93.4-38.7a131.32 131.32 0 01-38.7-93.4c0-35.3 13.7-68.4 38.7-93.4l59.4-59.4 186.8 186.8-59.4 59.4z"}}]},name:"api",theme:"outlined"},a=t(91146),o=function(e,r){return s.createElement(a.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:c}))};var l=s.forwardRef(o)},23684:function(e,r,t){t.r(r),t.d(r,{default:function(){return U}});var n=t(97857),s=t.n(n),c=t(15009),a=t.n(c),o=t(99289),l=t.n(o),i=t(5574),d=t.n(i),u=t(67294),h=t(71471),p=t(2453),g=t(66309),m=t(55102),v=t(74330),f=t(4393),x=t(32983),j=t(85265),b=t(42075),_=t(83622),C=t(26412),y=t(96074),k=t(36027),N=t(1413),S={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M516 673c0 4.4 3.4 8 7.5 8h185c4.1 0 7.5-3.6 7.5-8v-48c0-4.4-3.4-8-7.5-8h-185c-4.1 0-7.5 3.6-7.5 8v48zm-194.9 6.1l192-161c3.8-3.2 3.8-9.1 0-12.3l-192-160.9A7.95 7.95 0 00308 351v62.7c0 2.4 1 4.6 2.9 6.1L420.7 512l-109.8 92.2a8.1 8.1 0 00-2.9 6.1V673c0 6.8 7.9 10.5 13.1 6.1zM880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"code",theme:"outlined"},Z=t(91146),w=function(e,r){return u.createElement(Z.Z,(0,N.Z)((0,N.Z)({},e),{},{ref:r,icon:S}))};var I=u.forwardRef(w),T={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M848 359.3H627.7L825.8 109c4.1-5.3.4-13-6.3-13H436c-2.8 0-5.5 1.5-6.9 4L170 547.5c-3.1 5.3.7 12 6.9 12h174.4l-89.4 357.6c-1.9 7.8 7.5 13.3 13.3 7.7L853.5 373c5.2-4.9 1.7-13.7-5.5-13.7zM378.2 732.5l60.3-241H281.1l189.6-327.4h224.6L487 427.4h211L378.2 732.5z"}}]},name:"thunderbolt",theme:"outlined"},$=function(e,r){return u.createElement(Z.Z,(0,N.Z)((0,N.Z)({},e),{},{ref:r,icon:T}))};var P=u.forwardRef($),O=t(40110),z={container:"container___yLDgR",logo:"logo___u855o",mainContent:"mainContent___euHkQ",sidebar:"sidebar___hX61U",sidebarHeader:"sidebarHeader___FCn5V",categories:"categories___JN52A",categoryItem:"categoryItem___zi2NU",active:"active___F76dr",count:"count___Qb3RW",content:"content___uKUra",loadingContainer:"loadingContainer___AZRDI",serviceFilters:"serviceFilters___YvdzA",servicesList:"servicesList___gXJ6j",servicesGrid:"servicesGrid___LByDp",serviceCard:"serviceCard___gmTCv",hostedServiceCard:"hostedServiceCard___P_K2o",localServiceCard:"localServiceCard___EvrtS",serviceCardHeader:"serviceCardHeader___TStc4",serviceInfo:"serviceInfo___rlYlK",serviceLogo:"serviceLogo___eYxCa",logoPlaceholder:"logoPlaceholder___zqakV",serviceTitle:"serviceTitle___trthV",servicePath:"servicePath___xG6pN",serviceDescription:"serviceDescription___Qh8Oa",serviceFooter:"serviceFooter___xrmTT",hostedTagColor:"hostedTagColor___XsqO1",localTagColor:"localTagColor___KkfpB",serviceDetail:"serviceDetail___FG0RH",configItem:"configItem___Z6S5J",footer:"footer___jjQBt"},H=t(78404),L=t(78158);function E(e){return B.apply(this,arguments)}function B(){return(B=l()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,L.N)("/api/mcp-services",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function M(e){return F.apply(this,arguments)}function F(){return(F=l()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,L.N)("/api/system-mcp-services",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function D(e){return R.apply(this,arguments)}function R(){return(R=l()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,L.N)("/api/mcp-services/".concat(r),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var A=t(85893),G=h.Z.Title,X=h.Z.Text,q=h.Z.Paragraph,U=function(){var e=(0,u.useState)("all"),r=d()(e,2),t=r[0],n=r[1],c=(0,u.useState)(!1),o=d()(c,2),i=o[0],h=o[1],N=(0,u.useState)([]),S=d()(N,2),Z=S[0],w=S[1],T=(0,u.useState)({current:1,pageSize:12,total:0}),$=d()(T,2),L=$[0],B=$[1],F=(0,u.useState)({current:1,pageSize:12}),R=d()(F,2),U=R[0],V=R[1],W=(0,u.useState)(""),Q=d()(W,2),J=Q[0],K=Q[1],Y=(0,u.useState)([]),ee=d()(Y,2),re=ee[0],te=ee[1],ne=(0,u.useState)(!1),se=d()(ne,2),ce=se[0],ae=se[1],oe=(0,u.useState)(null),le=d()(oe,2),ie=le[0],de=le[1],ue=(0,u.useState)(!1),he=d()(ue,2),pe=he[0],ge=he[1],me=(0,H.kH)(),ve=function(){var e=l()(a()().mark((function e(){var r,t;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,E({current:1,pageSize:1e3});case 3:(r=e.sent).success&&(t=Array.from(new Set(r.data.flatMap((function(e){return e.tags||[]})))),te(t)),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取标签失败:",e.t0);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}(),fe=function(){var e=l()(a()().mark((function e(){var r;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(h(!0),e.prev=1,"system"!==t){e.next=8;break}return e.next=5,M(U);case 5:r=e.sent,e.next=11;break;case 8:return e.next=10,E(U);case 10:r=e.sent;case 11:r.success?(w(r.data||[]),B({current:r.current||1,pageSize:r.pageSize||12,total:r.total||0})):p.ZP.error("获取服务列表失败"),e.next=18;break;case 14:e.prev=14,e.t0=e.catch(1),console.error("获取服务列表失败:",e.t0),p.ZP.error("获取服务列表失败");case 18:return e.prev=18,h(!1),e.finish(18);case 21:case"end":return e.stop()}}),e,null,[[1,14,18,21]])})));return function(){return e.apply(this,arguments)}}();(0,u.useEffect)((function(){fe(),ve()}),[]),(0,u.useEffect)((function(){fe()}),[U,t]);var xe=function(e){n(e),V(s()(s()({},U),{},{tag:"all"===e?void 0:e,current:1}))},je=function(){var e=l()(a()().mark((function e(r){var t;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,de(r),ae(!0),ge(!0),e.next=6,D(r.id);case 6:(t=e.sent).success&&t.data&&de(t.data),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(0),console.error("获取服务详情失败:",e.t0),p.ZP.error("获取服务详情失败");case 14:return e.prev=14,ge(!1),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[0,10,14,17]])})));return function(r){return e.apply(this,arguments)}}(),be=function(e){return"all"===e?L.total:"system"===e?Z.filter((function(e){return e.is_system})).length:Z.filter((function(r){return r.tags&&r.tags.includes(e)})).length},_e=function(e){switch(e){case"streamableHttp":default:return(0,A.jsx)(k.Z,{});case"stdio":return(0,A.jsx)(I,{});case"sse":return(0,A.jsx)(P,{})}},Ce=function(e){switch(e){case"streamableHttp":return"HTTP";case"stdio":return"STDIO";case"sse":return"SSE";default:return e}},ye=function(e){if(!e)return"-";try{return(0,A.jsx)(q,{copyable:!0,className:z.jsonObject,children:JSON.stringify(e,null,2)})}catch(e){return"-"}},ke=function(e){return e&&0!==e.length?e.map((function(e,r){return(0,A.jsx)(g.Z,{children:e},r)})):"-"};return(0,A.jsxs)("div",{className:z.container,children:[(0,A.jsx)("div",{className:z.header,children:(0,A.jsxs)("div",{className:z.logo,children:[(0,A.jsx)("img",{src:null==me?void 0:me.logo,alt:"系统Logo"}),(0,A.jsxs)("div",{children:[(0,A.jsx)(G,{level:4,style:{margin:0},children:"MCP 广场"}),(0,A.jsx)(X,{type:"secondary",children:"聚合优质MCP资源，拓展模型智能边界"})]})]})}),(0,A.jsxs)("div",{className:z.mainContent,children:[(0,A.jsxs)("div",{className:z.sidebar,children:[(0,A.jsx)("div",{className:z.sidebarHeader,children:(0,A.jsx)(G,{level:5,children:"MCP 服务"})}),(0,A.jsx)(m.Z,{placeholder:"搜索MCP服务（共".concat(L.total,"个）"),prefix:(0,A.jsx)(O.Z,{}),style:{marginBottom:16},value:J,onChange:function(e){return r=e.target.value,K(r),void V(s()(s()({},U),{},{name:r||void 0,current:1}));var r},allowClear:!0}),(0,A.jsxs)("div",{className:z.categories,children:[(0,A.jsxs)("div",{className:"".concat(z.categoryItem," ").concat("all"===t?z.active:""),onClick:function(){return xe("all")},children:[(0,A.jsx)("span",{children:"全部"}),(0,A.jsx)("span",{className:z.count,children:L.total})]},"all"),(0,A.jsxs)("div",{className:"".concat(z.categoryItem," ").concat("system"===t?z.active:""),onClick:function(){return xe("system")},children:[(0,A.jsx)("span",{children:"系统服务"}),(0,A.jsx)("span",{className:z.count,children:be("system")})]},"system"),re.map((function(e){return(0,A.jsxs)("div",{className:"".concat(z.categoryItem," ").concat(t===e?z.active:""),onClick:function(){return xe(e)},children:[(0,A.jsx)("span",{children:e}),(0,A.jsx)("span",{className:z.count,children:be(e)})]},e)}))]})]}),(0,A.jsx)("div",{className:z.content,children:i?(0,A.jsx)("div",{className:z.loadingContainer,children:(0,A.jsx)(v.Z,{size:"large",tip:"加载中..."})}):Z.length>0?(0,A.jsx)("div",{className:z.servicesList,children:(0,A.jsx)("div",{className:z.servicesGrid,children:Z.map((function(e){return(0,A.jsxs)(f.Z,{hoverable:!0,className:"".concat(z.serviceCard," ").concat(e.is_system?z.hostedServiceCard:z.localServiceCard),onClick:function(){return je(e)},children:[(0,A.jsx)("div",{className:z.serviceCardHeader,children:(0,A.jsxs)("div",{className:z.serviceInfo,children:[(0,A.jsx)("div",{className:"".concat(z.serviceLogo," ").concat(e.is_system?z.hostedTagColor:z.localTagColor),children:(0,A.jsx)("span",{className:z.logoPlaceholder,children:_e(e.type)})}),(0,A.jsxs)("div",{children:[(0,A.jsx)("div",{className:z.serviceTitle,children:e.name}),(0,A.jsx)("div",{className:z.servicePath,children:Ce(e.type)})]})]})}),(0,A.jsx)("div",{className:z.serviceDescription,children:e.description||"暂无描述"}),(0,A.jsx)("div",{className:z.serviceFooter,children:(0,A.jsxs)("div",{children:[e.tags&&e.tags.slice(0,2).map((function(e){return(0,A.jsx)(g.Z,{children:e},e)})),e.tags&&e.tags.length>2&&(0,A.jsxs)(g.Z,{children:["+",e.tags.length-2]})]})})]},e.id)}))})}):(0,A.jsx)(x.Z,{description:"暂无服务"})})]}),(0,A.jsx)("div",{className:z.footer,children:(0,A.jsx)(X,{type:"secondary",children:"MCP服务广场 - 聚合优质MCP资源，拓展模型智能边界"})}),(0,A.jsx)(j.Z,{title:(0,A.jsx)("div",{className:z.drawerTitle,children:ie&&(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("div",{className:"".concat(z.serviceLogo," ").concat(ie.is_system?z.hostedTagColor:z.localTagColor),children:ie&&_e(ie.type)}),(0,A.jsx)("span",{children:ie.name})]})}),placement:"right",width:600,onClose:function(){return ae(!1)},open:ce,footerStyle:{textAlign:"right"},footer:(0,A.jsx)(b.Z,{children:(0,A.jsx)(_.ZP,{onClick:function(){return ae(!1)},children:"关闭"})}),children:pe?(0,A.jsx)("div",{className:z.loadingContainer,children:(0,A.jsx)(v.Z,{})}):ie&&(0,A.jsxs)("div",{className:z.serviceDetail,children:[(0,A.jsxs)(C.Z,{column:1,bordered:!0,size:"small",children:[(0,A.jsx)(C.Z.Item,{label:"服务名称",children:ie.name}),(0,A.jsx)(C.Z.Item,{label:"描述",children:ie.description||"-"}),(0,A.jsx)(C.Z.Item,{label:"类型",children:(0,A.jsx)(g.Z,{color:"streamableHttp"===ie.type?"blue":"stdio"===ie.type?"green":"orange",children:Ce(ie.type)})}),(0,A.jsx)(C.Z.Item,{label:"状态",children:(0,A.jsx)(g.Z,{color:ie.is_active?"success":"default",children:ie.is_active?"激活":"未激活"})}),(0,A.jsx)(C.Z.Item,{label:"标签",children:ke(ie.tags)}),(0,A.jsxs)(C.Z.Item,{label:"超时时间",children:[ie.timeout,"毫秒"]}),(0,A.jsx)(C.Z.Item,{label:"调用次数",children:ie.usage_count||0}),(0,A.jsx)(C.Z.Item,{label:"创建者",children:ie.user_name||"系统"}),(0,A.jsx)(C.Z.Item,{label:"创建时间",children:new Date(ie.created_at).toLocaleString()})]}),(0,A.jsx)(y.Z,{orientation:"left",children:"配置信息"}),"streamableHttp"===ie.type&&(0,A.jsxs)(A.Fragment,{children:[(0,A.jsxs)("div",{className:z.configItem,children:[(0,A.jsx)(X,{strong:!0,children:"URL:"}),(0,A.jsx)(q,{copyable:!0,style:{marginTop:"4px"},children:ie.url||"-"})]}),(0,A.jsxs)("div",{className:z.configItem,children:[(0,A.jsx)(X,{strong:!0,children:"Headers:"}),ye(ie.headers)]})]}),"stdio"===ie.type&&(0,A.jsxs)(A.Fragment,{children:[(0,A.jsxs)("div",{className:z.configItem,children:[(0,A.jsx)(X,{strong:!0,children:"命令:"}),(0,A.jsx)(q,{copyable:!0,style:{marginTop:"4px"},children:ie.command||"-"})]}),(0,A.jsxs)("div",{className:z.configItem,children:[(0,A.jsx)(X,{strong:!0,children:"参数:"}),ke(ie.args)]}),(0,A.jsxs)("div",{className:z.configItem,children:[(0,A.jsx)(X,{strong:!0,children:"环境变量:"}),ye(ie.env_vars)]})]}),"sse"===ie.type&&(0,A.jsxs)(A.Fragment,{children:[(0,A.jsxs)("div",{className:z.configItem,children:[(0,A.jsx)(X,{strong:!0,children:"URL:"}),(0,A.jsx)(q,{copyable:!0,style:{marginTop:"4px"},children:ie.url||"-"})]}),(0,A.jsxs)("div",{className:z.configItem,children:[(0,A.jsx)(X,{strong:!0,children:"Headers:"}),ye(ie.headers)]})]})]})})]})}},66309:function(e,r,t){t.d(r,{Z:function(){return T}});var n=t(67294),s=t(93967),c=t.n(s),a=t(98423),o=t(98787),l=t(69760),i=t(96159),d=t(45353),u=t(53124),h=t(11568),p=t(15063),g=t(14747),m=t(83262),v=t(83559);const f=e=>{const{lineWidth:r,fontSizeIcon:t,calc:n}=e,s=e.fontSizeSM;return(0,m.IX)(e,{tagFontSize:s,tagLineHeight:(0,h.bf)(n(e.lineHeightSM).mul(s).equal()),tagIconSize:n(t).sub(n(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},x=e=>({defaultBg:new p.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var j=(0,v.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:t,tagPaddingHorizontal:n,componentCls:s,calc:c}=e,a=c(n).sub(t).equal(),o=c(r).sub(t).equal();return{[s]:Object.assign(Object.assign({},(0,g.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:a,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,h.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${s}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${s}-close-icon`]:{marginInlineStart:o,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${s}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${s}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:a}}),[`${s}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(f(e))),x),b=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(n=Object.getOwnPropertySymbols(e);s<n.length;s++)r.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(t[n[s]]=e[n[s]])}return t};const _=n.forwardRef(((e,r)=>{const{prefixCls:t,style:s,className:a,checked:o,onChange:l,onClick:i}=e,d=b(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:h,tag:p}=n.useContext(u.E_),g=h("tag",t),[m,v,f]=j(g),x=c()(g,`${g}-checkable`,{[`${g}-checkable-checked`]:o},null==p?void 0:p.className,a,v,f);return m(n.createElement("span",Object.assign({},d,{ref:r,style:Object.assign(Object.assign({},s),null==p?void 0:p.style),className:x,onClick:e=>{null==l||l(!o),null==i||i(e)}})))}));var C=_,y=t(98719);var k=(0,v.bk)(["Tag","preset"],(e=>(e=>(0,y.Z)(e,((r,t)=>{let{textColor:n,lightBorderColor:s,lightColor:c,darkColor:a}=t;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:n,background:c,borderColor:s,"&-inverse":{color:e.colorTextLightSolid,background:a,borderColor:a},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(f(e))),x);const N=(e,r,t)=>{const n="string"!=typeof(s=t)?s:s.charAt(0).toUpperCase()+s.slice(1);var s;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${t}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var S=(0,v.bk)(["Tag","status"],(e=>{const r=f(e);return[N(r,"success","Success"),N(r,"processing","Info"),N(r,"error","Error"),N(r,"warning","Warning")]}),x),Z=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(n=Object.getOwnPropertySymbols(e);s<n.length;s++)r.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(t[n[s]]=e[n[s]])}return t};const w=n.forwardRef(((e,r)=>{const{prefixCls:t,className:s,rootClassName:h,style:p,children:g,icon:m,color:v,onClose:f,bordered:x=!0,visible:b}=e,_=Z(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:C,direction:y,tag:N}=n.useContext(u.E_),[w,I]=n.useState(!0),T=(0,a.Z)(_,["closeIcon","closable"]);n.useEffect((()=>{void 0!==b&&I(b)}),[b]);const $=(0,o.o2)(v),P=(0,o.yT)(v),O=$||P,z=Object.assign(Object.assign({backgroundColor:v&&!O?v:void 0},null==N?void 0:N.style),p),H=C("tag",t),[L,E,B]=j(H),M=c()(H,null==N?void 0:N.className,{[`${H}-${v}`]:O,[`${H}-has-color`]:v&&!O,[`${H}-hidden`]:!w,[`${H}-rtl`]:"rtl"===y,[`${H}-borderless`]:!x},s,h,E,B),F=e=>{e.stopPropagation(),null==f||f(e),e.defaultPrevented||I(!1)},[,D]=(0,l.Z)((0,l.w)(e),(0,l.w)(N),{closable:!1,closeIconRender:e=>{const r=n.createElement("span",{className:`${H}-close-icon`,onClick:F},e);return(0,i.wm)(e,r,(e=>({onClick:r=>{var t;null===(t=null==e?void 0:e.onClick)||void 0===t||t.call(e,r),F(r)},className:c()(null==e?void 0:e.className,`${H}-close-icon`)})))}}),R="function"==typeof _.onClick||g&&"a"===g.type,A=m||null,G=A?n.createElement(n.Fragment,null,A,g&&n.createElement("span",null,g)):g,X=n.createElement("span",Object.assign({},T,{ref:r,className:M,style:z}),G,D,$&&n.createElement(k,{key:"preset",prefixCls:H}),P&&n.createElement(S,{key:"status",prefixCls:H}));return L(R?n.createElement(d.Z,{component:"Tag"},X):X)})),I=w;I.CheckableTag=C;var T=I}}]);