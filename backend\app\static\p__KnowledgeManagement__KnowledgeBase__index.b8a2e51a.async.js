(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3455],{47046:function(e,n){"use strict";n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"}},82061:function(e,n,t){"use strict";var r=t(1413),i=t(67294),a=t(47046),o=t(91146),c=function(e,n){return i.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:a.Z}))},s=i.forwardRef(c);n.Z=s},37446:function(e,n,t){"use strict";t.d(n,{Z:function(){return s}});var r=t(1413),i=t(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"},o=t(91146),c=function(e,n){return i.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:a}))};var s=i.forwardRef(c)},51042:function(e,n,t){"use strict";var r=t(1413),i=t(67294),a=t(42110),o=t(91146),c=function(e,n){return i.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:n,icon:a.Z}))},s=i.forwardRef(c);n.Z=s},3940:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return O}});var r=t(15009),i=t.n(r),a=t(97857),o=t.n(a),c=t(99289),s=t.n(c),d=t(5574),l=t.n(d),u=t(51042),p=t(43425),x=t(82061),f=t(37446),h=t(90389),g=t(97131),m=t(71471),v=t(34041),b=t(2453),y=t(83622),w=t(2487),j=t(4393),k=t(85418),S=t(17788),C=t(55102),Z=t(72269),_=t(67294),R=t(49185),I=t(58258),z=t(9783),B=t.n(z),P=t(24444),T=(0,P.kc)((function(e){var n=e.token;return{card:{borderRadius:"8px",background:"linear-gradient(135deg, #e8f4fe, #ede7fd)",overflow:"hidden",".ant-card-meta-title":{marginBottom:"12px",fontSize:"16px",fontWeight:"bold","& > a":{display:"inline-block",maxWidth:"100%",color:n.colorTextHeading}},".ant-card-body":{padding:"16px"},".ant-card-body:hover":{".ant-card-meta-title > a":{color:n.colorPrimary}},position:"relative"},item:{height:"40px",color:n.colorPrimary,fontSize:"14px",marginBottom:"0"},cardList:{".ant-list .ant-list-item-content-single":{maxWidth:"100%"},marginTop:"16px"},extraImg:B()({width:"155px",marginTop:"-20px",textAlign:"center",img:{width:"100%"}},"@media screen and (max-width: ".concat(n.screenMD,"px)"),{display:"none"}),newButton:{width:"100%",height:"238px",color:n.colorTextSecondary,backgroundColor:n.colorBgContainer,borderColor:n.colorBorder,borderRadius:"8px",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",fontSize:"16px","&:hover":{borderColor:n.colorPrimary,color:n.colorPrimary}},cardAvatar:{width:"48px",height:"48px",borderRadius:"48px"},cardDescription:{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis",wordBreak:"break-all"},pageHeaderContent:B()({position:"relative"},"@media screen and (max-width: ".concat(n.screenSM,"px)"),{paddingBottom:"30px"}),contentLink:B()(B()({marginTop:"16px",a:{marginRight:"32px",img:{width:"24px"}},img:{marginRight:"8px",verticalAlign:"middle"}},"@media screen and (max-width: ".concat(n.screenLG,"px)"),{a:{marginRight:"16px"}}),"@media screen and (max-width: ".concat(n.screenSM,"px)"),{position:"absolute",bottom:"-4px",left:"0",width:"1000px",a:{marginRight:"16px"},img:{marginRight:"4px"}}),cardLabel:{display:"inline-block",padding:"2px 8px",borderRadius:"4px",fontSize:"12px",marginRight:"8px",backgroundColor:n.colorBgTextHover},cardStats:{marginTop:"8px",display:"flex",justifyContent:"space-between",color:n.colorTextSecondary,fontSize:"12px"},avatarContainer:{marginBottom:"8px",display:"flex",alignItems:"center"},avatarIcon:{width:"40px",height:"40px",borderRadius:"8px",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:n.colorPrimaryBg,color:n.colorPrimary,fontSize:"20px",marginRight:"10px"},moreButton:{position:"absolute",top:"12px",right:"12px",color:n.colorTextSecondary,cursor:"pointer",width:"32px",height:"32px",display:"flex",justifyContent:"center",alignItems:"center",borderRadius:"4px",zIndex:10,"&:hover":{backgroundColor:n.colorBgTextHover}},tableListOperator:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px",padding:"16px 24px",backgroundColor:"#fff",borderRadius:"8px"},rightContent:{display:"flex",alignItems:"center"},searchInput:{".ant-input":{borderRadius:"4px"},".ant-input-search-button":{borderRadius:"0 4px 4px 0"}},contentLinks:{marginTop:"16px",a:{display:"inline-flex",alignItems:"center",img:{width:"24px",marginRight:"8px"}}}}})),N=(0,P.kc)((function(){return{".knowledge-base-dropdown":{".ant-dropdown-menu":{padding:"8px 0",borderRadius:"8px"},".ant-dropdown-menu-item":{padding:"10px 16px",margin:"0","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.04)"}}}}})),E=T,L=t(96974),M=t(79554),D=t(64576),W=t(85893),H=m.Z.Paragraph,A=v.default.Option,O=function(){var e=E().styles;N();var n=(0,_.useState)(!1),t=l()(n,2),r=t[0],a=t[1],c=(0,_.useState)([]),d=l()(c,2),m=d[0],z=d[1],B=(0,_.useState)(!1),P=l()(B,2),T=P[0],O=P[1],G=(0,_.useState)(""),V=l()(G,2),F=V[0],K=V[1],Y=(0,_.useState)(""),q=l()(Y,2),J=q[0],Q=q[1],U=(0,_.useState)(void 0),X=l()(U,2),$=X[0],ee=X[1],ne=(0,_.useState)(void 0),te=l()(ne,2),re=te[0],ie=te[1],ae=(0,_.useState)({current:1,pageSize:12,total:0}),oe=l()(ae,2),ce=oe[0],se=oe[1],de=(0,_.useState)([]),le=l()(de,2),ue=le[0],pe=le[1],xe=(0,_.useState)(!0),fe=l()(xe,2),he=fe[0],ge=fe[1],me=(0,_.useState)(!1),ve=l()(me,2),be=ve[0],ye=ve[1],we=(0,_.useState)(!1),je=l()(we,2),ke=je[0],Se=je[1],Ce=(0,L.s0)(),Ze=(0,_.useState)(!1),_e=l()(Ze,2),Re=_e[0],Ie=_e[1],ze=function(){var e=s()(i()().mark((function e(){var n,t,r,c;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a(!0),e.prev=1,e.next=4,(0,R.ws)({current:ce.current,pageSize:ce.pageSize});case 4:(n=e.sent).success&&(t=n.data.filter((function(e){return!1===e.is_conversation_kb})),r=t.map((function(e){return{id:e.id,name:e.name,description:e.description,created_at:e.created_at,file_count:e.file_count,index_count:e.chunk_count,chunk_count:e.chunk_count}})),z(r),c=Math.ceil(t.length*n.total/n.data.length),se(o()(o()({},ce),{},{total:c}))),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(1),b.ZP.error("获取知识库列表失败");case 11:return e.prev=11,a(!1),e.finish(11);case 14:case"end":return e.stop()}}),e,null,[[1,8,11,14]])})));return function(){return e.apply(this,arguments)}}(),Be=function(){var e=s()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(F.trim()){e.next=3;break}return b.ZP.error("知识库名称不能为空"),e.abrupt("return");case 3:if($){e.next=6;break}return b.ZP.error("请选择向量模型"),e.abrupt("return");case 6:return a(!0),e.prev=7,e.next=10,(0,R.D9)({name:F.trim(),description:J.trim(),embedding_model:$,embedding_model_id:null==re?void 0:re.toString(),basic_index:he,graph_index:be,semantic_index:ke});case 10:b.ZP.success("知识库创建成功"),O(!1),K(""),Q(""),ze(),e.next=20;break;case 17:e.prev=17,e.t0=e.catch(7),b.ZP.error((null===e.t0||void 0===e.t0?void 0:e.t0.message)||"创建失败，请稍后重试");case 20:return e.prev=20,a(!1),e.finish(20);case 23:case"end":return e.stop()}}),e,null,[[7,17,20,23]])})));return function(){return e.apply(this,arguments)}}(),Pe=function(e){Ce("/knowledgeManagement/knowledgeInfo?id=".concat(e))};(0,_.useEffect)((function(){var e=function(){var e=s()(i()().mark((function e(){var n,t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,I.DY)();case 3:(n=e.sent).success&&(t=n.data.map((function(e){return{label:e.name,value:e.embedding_name,id:e.id}})),r=t.map((function(e){return o()(o()({},e),{},{id:Number(e.id)})})),pe(r),r.length>0&&(ee(r[0].value),ie(r[0].id))),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取向量模型列表失败:",e.t0);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);(0,_.useEffect)((function(){ze()}),[ce.current,ce.pageSize]);var Te=function(){var e=s()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,R.eV)(n);case 3:e.sent&&(b.ZP.success("删除成功"),ze()),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("删除失败:",e.t0),b.ZP.error("删除失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(n){return e.apply(this,arguments)}}();return(0,W.jsxs)(W.Fragment,{children:[(0,W.jsxs)(g._z,{children:[(0,W.jsx)("div",{style:{marginBottom:"24px",display:"flex",justifyContent:"flex-end",backgroundColor:"#fff="},children:(0,W.jsx)(y.ZP,{type:"primary",icon:(0,W.jsx)(u.Z,{}),onClick:function(){return O(!0)},children:"新建知识库"})}),(0,W.jsx)("div",{className:e.cardList,children:(0,W.jsx)(w.Z,{rowKey:"id",loading:r,grid:{gutter:24,xs:1,sm:2,md:2,lg:3,xl:4,xxl:4},dataSource:m,pagination:o()(o()({},ce),{},{onChange:function(e){se(o()(o()({},ce),{},{current:e}))}}),renderItem:function(n){var t=[{key:"settings",label:(0,W.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,W.jsx)(p.Z,{style:{marginRight:8}}),(0,W.jsx)("span",{children:"设置"})]}),onClick:function(e){e.domEvent.stopPropagation(),Pe(n.id)}},{key:"delete",label:(0,W.jsxs)("div",{style:{display:"flex",alignItems:"center",color:"#ff4d4f",cursor:"pointer"},onClick:function(){Te(n.id)},children:[(0,W.jsx)(x.Z,{style:{marginRight:8,color:"#ff4d4f"}}),(0,W.jsx)("span",{children:"删除"})]})}];return(0,W.jsx)(w.Z.Item,{children:(0,W.jsxs)(j.Z,{hoverable:!0,className:e.card,onClick:function(){return Pe(n.id)},bodyStyle:{paddingRight:"40px"},children:[(0,W.jsx)("div",{className:e.moreButton,onClick:function(e){e.stopPropagation()},children:(0,W.jsx)(k.Z,{menu:{items:t},trigger:["click"],placement:"bottomRight",getPopupContainer:function(e){return e.parentNode},overlayStyle:{minWidth:"120px",boxShadow:"0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)",borderRadius:"8px"},overlayClassName:"knowledge-base-dropdown",children:(0,W.jsx)(f.Z,{})})}),(0,W.jsxs)("div",{className:e.avatarContainer,children:[(0,W.jsx)("div",{className:e.avatarIcon,children:(0,W.jsx)(h.Z,{})}),(0,W.jsx)("span",{style:{fontWeight:"bold",fontSize:"16px"},children:n.name})]}),(0,W.jsx)(j.Z.Meta,{title:null,description:(0,W.jsx)(H,{className:e.item,ellipsis:{rows:2},children:n.description||"暂无描述"})}),(0,W.jsxs)("div",{className:e.cardStats,children:[(0,W.jsxs)("span",{children:["文件数量：",n.file_count||0]}),(0,W.jsxs)("span",{children:["知识点数量：",n.index_count||0]})]})]})},n.id)}})})]}),(0,W.jsxs)(S.Z,{title:"新建知识库",open:T,onOk:Be,onCancel:function(){O(!1),K(""),Q("")},confirmLoading:r,okButtonProps:{disabled:!F.trim()},children:[(0,W.jsxs)("div",{style:{marginBottom:16},children:[(0,W.jsx)(C.Z,{placeholder:"请输入知识库名称",value:F,onChange:function(e){return K(e.target.value)},maxLength:50,showCount:!0,status:F.trim()?"":"error"}),!F.trim()&&(0,W.jsx)("div",{style:{color:"#ff4d4f",fontSize:12,marginTop:4},children:"请输入知识库名称"})]}),(0,W.jsxs)("div",{style:{marginBottom:20,display:"flex",alignItems:"center"},children:[(0,W.jsx)(C.Z.TextArea,{placeholder:"请输入知识库描述（选填）",value:J,onChange:function(e){return Q(e.target.value)},rows:4,maxLength:200,showCount:!0,style:{flex:1}}),(0,W.jsx)(y.ZP,{type:"text",icon:(0,W.jsx)(D.Z,{style:{fontSize:20}}),onClick:function(){return Ie(!0)},style:{marginLeft:8},title:"AI辅助生成描述"})]}),(0,W.jsx)("div",{style:{marginBottom:20},children:(0,W.jsx)(v.default,{placeholder:"选择向量模型",value:$,onChange:function(e){var n=ue.find((function(n){return n.value===e}));ee(e),ie(null==n?void 0:n.id)},style:{width:"100%"},allowClear:!1,showSearch:!1,children:ue.map((function(e){return(0,W.jsx)(A,{value:e.value,children:e.label},e.id)}))})}),(0,W.jsx)("div",{style:{marginBottom:20},children:(0,W.jsxs)("div",{className:"index-settings-container",style:{background:"#f9f9f9",borderRadius:"8px",padding:"16px",border:"1px solid #f0f0f0"},children:[(0,W.jsxs)("div",{style:{marginBottom:16,fontWeight:"bold",fontSize:"15px",borderBottom:"1px solid #f0f0f0",paddingBottom:"8px",display:"flex",alignItems:"center"},children:[(0,W.jsx)(p.Z,{style:{marginRight:8}}),"索引设置"]}),(0,W.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:12},children:[(0,W.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 12px",background:"#fff",borderRadius:"6px",boxShadow:"0 1px 2px rgba(0,0,0,0.03)"},children:[(0,W.jsxs)("div",{children:[(0,W.jsx)("div",{style:{fontWeight:"500"},children:"基础索引"}),(0,W.jsx)("div",{style:{fontSize:"12px",color:"#888",marginTop:"4px"},children:"用于基本的知识检索"})]}),(0,W.jsx)(Z.Z,{defaultChecked:!0,checked:he,onChange:function(e){return ge(e)}})]}),(0,W.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 12px",background:"#fff",borderRadius:"6px",boxShadow:"0 1px 2px rgba(0,0,0,0.03)"},children:[(0,W.jsxs)("div",{children:[(0,W.jsx)("div",{style:{fontWeight:"500"},children:"Graph索引"}),(0,W.jsx)("div",{style:{fontSize:"12px",color:"#888",marginTop:"4px"},children:"用于知识关联和图谱展示"})]}),(0,W.jsx)(Z.Z,{checked:be,onChange:function(e){return ye(e)}})]}),(0,W.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 12px",background:"#fff",borderRadius:"6px",boxShadow:"0 1px 2px rgba(0,0,0,0.03)"},children:[(0,W.jsxs)("div",{children:[(0,W.jsx)("div",{style:{fontWeight:"500"},children:"语义索引"}),(0,W.jsx)("div",{style:{fontSize:"12px",color:"#888",marginTop:"4px"},children:"用于增强语义理解能力"})]}),(0,W.jsx)(Z.Z,{checked:ke,onChange:function(e){return Se(e)}})]})]})]})})]}),(0,W.jsx)(M.Z,{visible:Re,onCancel:function(){return Ie(!1)},sceneDescription:"知识库描述",app_info:"knowledge_base",onSelectPrompt:function(e){Q(e),Ie(!1)}})]})}},49185:function(e,n,t){"use strict";t.d(n,{D9:function(){return s},IV:function(){return p},eV:function(){return f},ws:function(){return l}});var r=t(15009),i=t.n(r),a=t(99289),o=t.n(a),c=t(78158);function s(e){return d.apply(this,arguments)}function d(){return(d=o()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge_base",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function l(e){return u.apply(this,arguments)}function u(){return(u=o()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/my_knowledge_bases",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function p(e){return x.apply(this,arguments)}function x(){return(x=o()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge_bases/".concat(n),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function f(e){return h.apply(this,arguments)}function h(){return(h=o()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge_bases/".concat(n),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},64599:function(e,n,t){var r=t(96263);e.exports=function(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=r(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var i=0,a=function(){};return{s:a,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,c=!0,s=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return c=e.done,e},e:function(e){s=!0,o=e},f:function(){try{c||null==t.return||t.return()}finally{if(s)throw o}}}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);