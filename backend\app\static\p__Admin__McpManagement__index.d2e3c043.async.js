(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3e3],{51042:function(e,r,t){"use strict";var n=t(1413),a=t(67294),s=t(42110),o=t(91146),i=function(e,r){return a.createElement(o.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:s.Z}))},c=a.forwardRef(i);r.Z=c},61415:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return ce}});var n=t(13769),a=t.n(n),s=t(9783),o=t.n(s),i=t(15009),c=t.n(i),l=t(99289),u=t.n(l),d=t(97857),p=t.n(d),h=t(5574),f=t.n(h),x=t(67294),m=t(97131),y=t(12453),v=t(71471),g=t(55102),b=t(17788),j=t(8232),k=t(2453),C=t(66309),Z=t(42075),P=t(83622),S=t(34041),w=t(67839),O=t(11941),T=t(13457),I=t(72269),N=t(96074),_=t(51042),E=t(64576),$=t(85175),z=t(78158);function B(e){return F.apply(this,arguments)}function F(){return(F=u()(c()().mark((function e(r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,z.N)("/api/mcp-services",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function M(e){return R.apply(this,arguments)}function R(){return(R=u()(c()().mark((function e(r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,z.N)("/api/user-mcp-services",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function L(e){return H.apply(this,arguments)}function H(){return(H=u()(c()().mark((function e(r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,z.N)("/api/system-mcp-services",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function J(e){return A.apply(this,arguments)}function A(){return(A=u()(c()().mark((function e(r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,z.N)("/api/mcp-services/".concat(r),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function q(e){return U.apply(this,arguments)}function U(){return(U=u()(c()().mark((function e(r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,z.N)("/api/mcp-services",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function V(e,r){return W.apply(this,arguments)}function W(){return(W=u()(c()().mark((function e(r,t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,z.N)("/api/mcp-services/".concat(r),{method:"PUT",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function D(e){return G.apply(this,arguments)}function G(){return(G=u()(c()().mark((function e(r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,z.N)("/api/mcp-services/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function K(e){return X.apply(this,arguments)}function X(){return(X=u()(c()().mark((function e(r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,z.N)("/api/mcp-services/".concat(r,"/copy"),{method:"POST"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var Y=[{label:"HTTP流式服务",value:"streamableHttp",icon:"🌐"},{label:"stdio",value:"stdio",icon:"💻"},{label:"sse",value:"sse",icon:"📡"}],Q=["搜索工具","浏览器自动化","文件处理","数据分析","金融工具","内容生成","知识管理","开发工具","通信工具","数据库工具"],ee=t(79554),re=t(85893),te=["current","pageSize","is_system"],ne=v.Z.Title,ae=v.Z.Text,se=v.Z.Paragraph,oe=g.Z.TextArea,ie=b.Z.confirm,ce=function(){var e,r=(0,x.useRef)(),t=j.Z.useForm(),n=f()(t,1)[0],s=(0,x.useState)(!1),i=f()(s,2),l=i[0],d=i[1],h=(0,x.useState)(null),v=f()(h,2),z=v[0],F=v[1],R=(0,x.useState)(!1),H=f()(R,2),A=H[0],U=H[1],W=(0,x.useState)(null),G=f()(W,2),X=G[0],ce=G[1],le=(0,x.useState)(!1),ue=f()(le,2),de=ue[0],pe=ue[1],he=(0,x.useState)("streamableHttp"),fe=f()(he,2),xe=fe[0],me=fe[1],ye=(0,x.useState)(!1),ve=f()(ye,2),ge=ve[0],be=ve[1],je=function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"内容";if(!e)return k.ZP.info("请先输入".concat(r)),!1;try{var t=JSON.parse(e),n=Object.entries(t).every((function(e){var r=f()(e,2),t=r[0],n=r[1];return"string"==typeof t&&("string"==typeof n||null==n)}));return n?(k.ZP.success("".concat(r,"格式正确！")),!0):(k.ZP.error("".concat(r,"格式不正确，所有键和值都必须是字符串")),!1)}catch(e){return k.ZP.error("".concat(r,"格式不正确，应为有效的JSON对象")),!1}},ke=function(e){F(e||null),e?(n.setFieldsValue(p()(p()({},e),{},{name:e.name,description:e.description,type:e.type,url:e.url,command:e.command,args:e.args,headers:e.headers?JSON.stringify(e.headers):void 0,env_vars:e.env_vars?JSON.stringify(e.env_vars):void 0,tags:e.tags||[],timeout:e.timeout||60,is_active:e.is_active,transport:e.transport,key:e.key})),me(e.type)):(n.resetFields(),n.setFieldsValue({type:"streamableHttp",tags:[],timeout:60,is_active:!0}),me("streamableHttp")),d(!0)},Ce=function(){var e=u()(c()().mark((function e(r){var t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,J(r.id);case 3:(t=e.sent).success&&t.data&&(ce(t.data),U(!0)),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取服务详情失败",e.t0),k.ZP.error("获取服务详情失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(r){return e.apply(this,arguments)}}(),Ze=function(){var e=u()(c()().mark((function e(){var t,a,s,o,i,l;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,n.validateFields();case 3:if(t=e.sent,pe(!0),!(a=p()({},t)).headers||"string"!=typeof a.headers){e.next=21;break}if(e.prev=7,s=JSON.parse(a.headers),Object.entries(s).every((function(e){var r=f()(e,2),t=r[0],n=r[1];return"string"==typeof t&&("string"==typeof n||null==n)}))){e.next=13;break}return k.ZP.error("请求头格式不正确，所有键和值都必须是字符串"),pe(!1),e.abrupt("return");case 13:a.headers=s,e.next=21;break;case 16:return e.prev=16,e.t0=e.catch(7),k.ZP.error('请求头格式不正确，应为有效的JSON对象，例如: {"Content-Type": "application/json"}'),pe(!1),e.abrupt("return");case 21:if(!a.env_vars||"string"!=typeof a.env_vars){e.next=36;break}if(e.prev=22,o=JSON.parse(a.env_vars),Object.entries(o).every((function(e){var r=f()(e,2),t=r[0],n=r[1];return"string"==typeof t&&("string"==typeof n||null==n)}))){e.next=28;break}return k.ZP.error("环境变量格式不正确，所有键和值都必须是字符串"),pe(!1),e.abrupt("return");case 28:a.env_vars=o,e.next=36;break;case 31:return e.prev=31,e.t1=e.catch(22),k.ZP.error('环境变量格式不正确，应为有效的JSON对象，例如: {"API_KEY": "your-api-key"}'),pe(!1),e.abrupt("return");case 36:if(!z){e.next=42;break}return e.next=39,V(z.id,a);case 39:i=e.sent,e.next=45;break;case 42:return e.next=44,q(a);case 44:i=e.sent;case 45:i.success?(k.ZP.success("".concat(z?"更新":"创建","MCP服务成功")),d(!1),n.resetFields(),null===(l=r.current)||void 0===l||l.reload()):k.ZP.error(i.message||"".concat(z?"更新":"创建","MCP服务失败")),e.next=52;break;case 48:e.prev=48,e.t2=e.catch(0),console.error("表单验证或提交失败:",e.t2),k.ZP.error("".concat(z?"更新":"创建","MCP服务失败"));case 52:return e.prev=52,pe(!1),e.finish(52);case 55:case"end":return e.stop()}}),e,null,[[0,48,52,55],[7,16],[22,31]])})));return function(){return e.apply(this,arguments)}}(),Pe=function(){var e=u()(c()().mark((function e(t){var n,a;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,pe(!0),e.next=4,K(t);case 4:(n=e.sent).success?(k.ZP.success("复制MCP服务成功，新服务名为 "+n.data.name),null===(a=r.current)||void 0===a||a.reload()):k.ZP.error("复制MCP服务失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("复制失败:",e.t0),k.ZP.error("复制MCP服务失败");case 12:return e.prev=12,pe(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})));return function(r){return e.apply(this,arguments)}}(),Se=function(){var e=u()(c()().mark((function e(t,n){var a,s;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,pe(!0),e.next=4,V(t,{is_active:!n});case 4:(a=e.sent).success?(k.ZP.success("".concat(n?"禁用":"启用","MCP服务成功")),null===(s=r.current)||void 0===s||s.reload()):k.ZP.error(a.message||"".concat(n?"禁用":"启用","MCP服务失败")),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("状态更新失败:",e.t0),k.ZP.error("".concat(n?"禁用":"启用","MCP服务失败"));case 12:return e.prev=12,pe(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})));return function(r,t){return e.apply(this,arguments)}}(),we=[{title:"服务名称",dataIndex:"name",valueType:"text",render:function(e,r){return(0,re.jsx)("a",{onClick:function(){return Ce(r)},children:r.name})}},{title:"服务类型",dataIndex:"type",valueType:"select",width:120,render:function(e,r){var t=Y.find((function(e){return e.value===r.type}));return(0,re.jsx)(C.Z,{color:"blue",children:t?"".concat(t.icon," ").concat(t.label):r.type})},valueEnum:Y.reduce((function(e,r){return p()(p()({},e),{},o()({},r.value,{text:r.label}))}),{})},{title:"标签",dataIndex:"tags",valueType:"text",render:function(e,r){return(0,re.jsx)(Z.Z,{children:r.tags&&r.tags.map((function(e){return(0,re.jsx)(C.Z,{color:"green",children:e},e)}))})}},{title:"创建者",dataIndex:"user_name",valueType:"text",width:100,fieldProps:{placeholder:"请输入创建者"}},{title:"状态",dataIndex:"is_active",valueType:"select",width:80,render:function(e,r){return(0,re.jsx)(C.Z,{color:r.is_active?"success":"error",children:r.is_active?"已启用":"已禁用"})},valueEnum:{true:{text:"已启用",status:"Success"},false:{text:"已禁用",status:"Error"}}},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",sorter:!0,width:160,hideInSearch:!0},{title:"操作",dataIndex:"option",valueType:"option",fixed:"right",width:200,render:function(e,t){return(0,re.jsxs)(Z.Z,{size:"small",children:[(0,re.jsx)(P.ZP,{type:"link",size:"small",onClick:function(){return ke(t)},children:"编辑"},"edit"),(0,re.jsx)(P.ZP,{type:"link",size:"small",onClick:function(){return Pe(t.id)},children:"复制"},"copy"),(0,re.jsx)(P.ZP,{type:"link",size:"small",danger:t.is_active,onClick:function(){return Se(t.id,t.is_active)},children:t.is_active?"禁用":"启用"},"status"),(0,re.jsx)(P.ZP,{type:"link",size:"small",danger:!0,onClick:function(){return e=t.id,void ie({title:"确认删除服务",content:"您确定要删除这个MCP服务吗？此操作无法撤销。",okText:"确定删除",cancelText:"取消",okButtonProps:{danger:!0},onOk:(n=u()(c()().mark((function t(){var n,a;return c()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,pe(!0),t.next=4,D(e);case 4:(n=t.sent).success?(k.ZP.success("删除MCP服务成功"),null===(a=r.current)||void 0===a||a.reload()):k.ZP.error(n.message||"删除MCP服务失败"),t.next=12;break;case 8:t.prev=8,t.t0=t.catch(0),console.error("删除失败:",t.t0),k.ZP.error("删除MCP服务失败");case 12:return t.prev=12,pe(!1),t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[0,8,12,15]])}))),function(){return n.apply(this,arguments)})});var e,n},children:"删除"},"delete")]})}}];return(0,re.jsxs)(m._z,{children:[(0,re.jsx)(y.Z,{headerTitle:"MCP服务管理",actionRef:r,rowKey:"id",search:{labelWidth:"auto",defaultCollapsed:!1},toolBarRender:function(){return[(0,re.jsx)(P.ZP,{type:"primary",onClick:function(){return ke()},icon:(0,re.jsx)(_.Z,{}),children:"新建MCP服务"},"create")]},request:u()(c()().mark((function e(){var r,t,n,s,o,i,l=arguments;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=l.length>0&&void 0!==l[0]?l[0]:{},t=r.current,n=r.pageSize,s=r.is_system,o=a()(r,te),e.prev=2,void 0!==s){e.next=9;break}return e.next=6,B(p()({current:t,pageSize:n},o));case 6:i=e.sent,e.next=18;break;case 9:if("true"!==s){e.next=15;break}return e.next=12,L(p()({current:t,pageSize:n},o));case 12:i=e.sent,e.next=18;break;case 15:return e.next=17,M(p()({current:t,pageSize:n},o));case 17:i=e.sent;case 18:return e.abrupt("return",{data:i.data||[],success:i.success,total:i.total||0});case 21:return e.prev=21,e.t0=e.catch(2),console.error("获取MCP服务失败:",e.t0),k.ZP.error("获取MCP服务列表失败"),e.abrupt("return",{data:[],success:!1,total:0});case 26:case"end":return e.stop()}}),e,null,[[2,21]])}))),columns:we,rowSelection:{selections:[w.Z.SELECTION_ALL,w.Z.SELECTION_INVERT]},pagination:{showQuickJumper:!0,showSizeChanger:!0}}),(0,re.jsx)(b.Z,{title:z?"编辑MCP服务":"新建MCP服务",open:l,onCancel:function(){return d(!1)},footer:[(0,re.jsx)(P.ZP,{onClick:function(){return d(!1)},children:"取消"},"cancel"),(0,re.jsx)(P.ZP,{type:"primary",onClick:Ze,loading:de,children:"确定"},"submit")],width:700,okButtonProps:{loading:de},destroyOnClose:!0,children:(0,re.jsxs)(j.Z,{form:n,layout:"vertical",initialValues:{type:"streamableHttp",tags:[],timeout:60,is_active:!0},children:[(0,re.jsx)(j.Z.Item,{name:"name",label:"服务名称",rules:[{required:!0,message:"请输入服务名称"}],children:(0,re.jsx)(g.Z,{placeholder:"请输入服务名称"})}),(0,re.jsx)(j.Z.Item,{name:"description",label:"服务描述",children:(0,re.jsxs)(g.Z.Group,{compact:!0,children:[(0,re.jsx)(oe,{placeholder:"请输入服务描述",rows:3,style:{resize:"none",width:"calc(100% - 32px)"}}),(0,re.jsx)(P.ZP,{icon:(0,re.jsx)(E.Z,{}),onClick:function(){return be(!0)},title:"提示词助手"})]})}),(0,re.jsx)(j.Z.Item,{name:"type",label:"服务类型",rules:[{required:!0,message:"请选择服务类型"}],children:(0,re.jsx)(O.Z,{activeKey:xe,onChange:function(e){me(e),n.setFieldsValue({type:e})},items:Y.map((function(e){return{label:(0,re.jsxs)("span",{children:[e.icon," ",e.label]}),key:e.value}}))})}),function(){switch(xe){case"streamableHttp":return(0,re.jsxs)(re.Fragment,{children:[(0,re.jsx)(j.Z.Item,{name:"url",label:"服务URL",rules:[{required:!0,message:"请输入服务URL"}],children:(0,re.jsx)(g.Z,{placeholder:"请输入服务URL，例如：https://api.example.com/v1/chat"})}),(0,re.jsx)(j.Z.Item,{name:"headers",label:"请求头",help:"JSON格式，例如：{'Authorization': 'Bearer token', 'Content-Type': 'application/json'}",extra:(0,re.jsx)("div",{style:{marginTop:"8px"},children:(0,re.jsx)(P.ZP,{type:"link",style:{padding:0},onClick:function(){return je(n.getFieldValue("headers"),"请求头")},children:"验证JSON格式"})}),children:(0,re.jsx)(oe,{placeholder:"请输入请求头，JSON格式",rows:4,style:{resize:"none"}})}),(0,re.jsx)(j.Z.Item,{name:"transport",label:"传输协议",initialValue:"http",children:(0,re.jsx)(S.default,{options:[{label:"HTTP",value:"http"},{label:"WebSocket",value:"websocket"}],placeholder:"请选择传输协议"})})]});case"stdio":return(0,re.jsxs)(re.Fragment,{children:[(0,re.jsx)(j.Z.Item,{name:"command",label:"命令",rules:[{required:!0,message:"请输入命令"}],children:(0,re.jsx)(g.Z,{placeholder:"请输入命令，例如：python"})}),(0,re.jsx)(j.Z.Item,{name:"args",label:"命令参数",children:(0,re.jsx)(S.default,{mode:"tags",placeholder:"请输入命令参数，例如：-m script.py",tokenSeparators:[" "]})}),(0,re.jsx)(j.Z.Item,{name:"key",label:"环境变量",help:"格式为：KEY=VALUE",children:(0,re.jsx)(S.default,{mode:"tags",placeholder:"请输入环境变量，例如：API_KEY=abc123",tokenSeparators:[" "]})})]});case"sse":return(0,re.jsxs)(re.Fragment,{children:[(0,re.jsx)(j.Z.Item,{name:"url",label:"服务URL",rules:[{required:!0,message:"请输入服务URL"}],children:(0,re.jsx)(g.Z,{placeholder:"请输入服务URL，例如：https://api.example.com/v1/events"})}),(0,re.jsx)(j.Z.Item,{name:"headers",label:"请求头",help:"JSON格式，例如：{'Authorization': 'Bearer token', 'Content-Type': 'application/json'}",extra:(0,re.jsx)("div",{style:{marginTop:"8px"},children:(0,re.jsx)(P.ZP,{type:"link",style:{padding:0},onClick:function(){return je(n.getFieldValue("headers"),"请求头")},children:"验证JSON格式"})}),children:(0,re.jsx)(oe,{placeholder:"请输入请求头，JSON格式",rows:4,style:{resize:"none"}})})]});default:return null}}(),(0,re.jsx)(j.Z.Item,{name:"tags",label:"标签",help:"用于分类和筛选服务",children:(0,re.jsx)(S.default,{mode:"tags",options:Q.map((function(e){return{label:e,value:e}})),placeholder:"请选择或输入标签"})}),(0,re.jsx)(j.Z.Item,{name:"timeout",label:"超时设置(秒)",rules:[{required:!0,message:"请输入超时设置"}],children:(0,re.jsx)(T.Z,{min:1,max:300,placeholder:"请输入超时设置",style:{width:"100%"}})}),(0,re.jsx)("div",{style:{display:"flex",gap:"16px"},children:(0,re.jsx)(j.Z.Item,{name:"is_active",label:"是否启用",valuePropName:"checked",children:(0,re.jsx)(I.Z,{checkedChildren:"启用",unCheckedChildren:"禁用",defaultChecked:!0})})})]})}),X&&(0,re.jsxs)(b.Z,{title:(0,re.jsx)(ne,{level:5,style:{margin:0},children:X.name}),open:A,onCancel:function(){U(!1),ce(null)},width:700,footer:[(0,re.jsx)(P.ZP,{icon:(0,re.jsx)($.Z,{}),onClick:function(){return Pe(X.id)},children:"复制服务"},"copy"),(0,re.jsx)(P.ZP,{type:"primary",onClick:function(){U(!1),ce(null)},children:"关闭"},"close")],children:[(0,re.jsx)("div",{style:{marginBottom:"20px"},children:(0,re.jsxs)("div",{style:{display:"flex",flexWrap:"wrap",gap:"16px",marginBottom:"16px"},children:[(0,re.jsxs)("div",{children:[(0,re.jsx)(ae,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"服务类型"}),(0,re.jsx)("div",{children:(e=Y.find((function(e){return e.value===X.type})),(0,re.jsx)(C.Z,{color:"blue",children:e?"".concat(e.icon," ").concat(e.label):X.type}))})]}),(0,re.jsxs)("div",{children:[(0,re.jsx)(ae,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"标签"}),(0,re.jsx)("div",{children:X.tags&&X.tags.map((function(e){return(0,re.jsx)(C.Z,{color:"green",children:e},e)}))})]}),(0,re.jsxs)("div",{children:[(0,re.jsx)(ae,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"状态"}),(0,re.jsx)(C.Z,{color:X.is_active?"success":"error",children:X.is_active?"已启用":"已禁用"})]}),(0,re.jsxs)("div",{children:[(0,re.jsx)(ae,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"超时设置"}),(0,re.jsxs)("span",{children:[X.timeout,"秒"]})]}),(0,re.jsxs)("div",{children:[(0,re.jsx)(ae,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"调用次数"}),(0,re.jsx)("span",{children:X.usage_count})]})]})}),(0,re.jsx)(N.Z,{orientation:"left",children:"服务描述"}),(0,re.jsx)(se,{style:{marginBottom:"20px"},children:X.description||"暂无描述"}),"streamableHttp"===X.type&&(0,re.jsxs)(re.Fragment,{children:[(0,re.jsx)(N.Z,{orientation:"left",children:"服务URL"}),(0,re.jsx)(se,{copyable:!0,style:{wordBreak:"break-all"},children:X.url}),X.headers&&Object.keys(X.headers).length>0&&(0,re.jsxs)(re.Fragment,{children:[(0,re.jsx)(N.Z,{orientation:"left",children:"请求头"}),(0,re.jsx)("div",{style:{backgroundColor:"#f5f5f5",padding:"10px",borderRadius:"4px"},children:(0,re.jsx)("pre",{style:{margin:0},children:JSON.stringify(X.headers,null,2)})})]})]}),"stdio"===X.type&&(0,re.jsxs)(re.Fragment,{children:[(0,re.jsx)(N.Z,{orientation:"left",children:"命令"}),(0,re.jsx)(se,{copyable:!0,children:X.command}),X.args&&X.args.length>0&&(0,re.jsxs)(re.Fragment,{children:[(0,re.jsx)(N.Z,{orientation:"left",children:"命令参数"}),(0,re.jsx)("div",{style:{backgroundColor:"#f5f5f5",padding:"10px",borderRadius:"4px"},children:(0,re.jsx)("pre",{style:{margin:0},children:X.args.join(" ")})})]}),X.key&&X.key.length>0&&(0,re.jsxs)(re.Fragment,{children:[(0,re.jsx)(N.Z,{orientation:"left",children:"环境变量"}),(0,re.jsx)("div",{style:{backgroundColor:"#f5f5f5",padding:"10px",borderRadius:"4px"},children:(0,re.jsx)("pre",{style:{margin:0},children:X.key.join("\n")})})]})]}),"sse"===X.type&&(0,re.jsxs)(re.Fragment,{children:[(0,re.jsx)(N.Z,{orientation:"left",children:"服务URL"}),(0,re.jsx)(se,{copyable:!0,style:{wordBreak:"break-all"},children:X.url}),X.headers&&Object.keys(X.headers).length>0&&(0,re.jsxs)(re.Fragment,{children:[(0,re.jsx)(N.Z,{orientation:"left",children:"请求头"}),(0,re.jsx)("div",{style:{backgroundColor:"#f5f5f5",padding:"10px",borderRadius:"4px"},children:(0,re.jsx)("pre",{style:{margin:0},children:JSON.stringify(X.headers,null,2)})})]})]}),(0,re.jsxs)("div",{style:{marginTop:20,paddingTop:16,borderTop:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between"},children:[(0,re.jsx)("div",{children:(0,re.jsxs)(ae,{type:"secondary",style:{fontSize:"12px"},children:["创建时间: ",new Date(X.created_at).toLocaleString()]})}),(0,re.jsxs)(ae,{type:"secondary",style:{fontSize:"12px"},children:["创建者: ",X.user_name||"系统"]})]})]}),(0,re.jsx)(ee.Z,{open:ge,onClose:function(){return be(!1)},onSelect:function(e){n.setFieldsValue({description:e}),be(!1)}})]})}},66309:function(e,r,t){"use strict";t.d(r,{Z:function(){return I}});var n=t(67294),a=t(93967),s=t.n(a),o=t(98423),i=t(98787),c=t(69760),l=t(96159),u=t(45353),d=t(53124),p=t(11568),h=t(15063),f=t(14747),x=t(83262),m=t(83559);const y=e=>{const{lineWidth:r,fontSizeIcon:t,calc:n}=e,a=e.fontSizeSM;return(0,x.IX)(e,{tagFontSize:a,tagLineHeight:(0,p.bf)(n(e.lineHeightSM).mul(a).equal()),tagIconSize:n(t).sub(n(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},v=e=>({defaultBg:new h.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var g=(0,m.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:t,tagPaddingHorizontal:n,componentCls:a,calc:s}=e,o=s(n).sub(t).equal(),i=s(r).sub(t).equal();return{[a]:Object.assign(Object.assign({},(0,f.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:o,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${a}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${a}-close-icon`]:{marginInlineStart:i,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${a}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${a}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:o}}),[`${a}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(y(e))),v),b=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)r.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(t[n[a]]=e[n[a]])}return t};const j=n.forwardRef(((e,r)=>{const{prefixCls:t,style:a,className:o,checked:i,onChange:c,onClick:l}=e,u=b(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:h}=n.useContext(d.E_),f=p("tag",t),[x,m,y]=g(f),v=s()(f,`${f}-checkable`,{[`${f}-checkable-checked`]:i},null==h?void 0:h.className,o,m,y);return x(n.createElement("span",Object.assign({},u,{ref:r,style:Object.assign(Object.assign({},a),null==h?void 0:h.style),className:v,onClick:e=>{null==c||c(!i),null==l||l(e)}})))}));var k=j,C=t(98719);var Z=(0,m.bk)(["Tag","preset"],(e=>(e=>(0,C.Z)(e,((r,t)=>{let{textColor:n,lightBorderColor:a,lightColor:s,darkColor:o}=t;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:n,background:s,borderColor:a,"&-inverse":{color:e.colorTextLightSolid,background:o,borderColor:o},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(y(e))),v);const P=(e,r,t)=>{const n="string"!=typeof(a=t)?a:a.charAt(0).toUpperCase()+a.slice(1);var a;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${t}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var S=(0,m.bk)(["Tag","status"],(e=>{const r=y(e);return[P(r,"success","Success"),P(r,"processing","Info"),P(r,"error","Error"),P(r,"warning","Warning")]}),v),w=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)r.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(t[n[a]]=e[n[a]])}return t};const O=n.forwardRef(((e,r)=>{const{prefixCls:t,className:a,rootClassName:p,style:h,children:f,icon:x,color:m,onClose:y,bordered:v=!0,visible:b}=e,j=w(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:k,direction:C,tag:P}=n.useContext(d.E_),[O,T]=n.useState(!0),I=(0,o.Z)(j,["closeIcon","closable"]);n.useEffect((()=>{void 0!==b&&T(b)}),[b]);const N=(0,i.o2)(m),_=(0,i.yT)(m),E=N||_,$=Object.assign(Object.assign({backgroundColor:m&&!E?m:void 0},null==P?void 0:P.style),h),z=k("tag",t),[B,F,M]=g(z),R=s()(z,null==P?void 0:P.className,{[`${z}-${m}`]:E,[`${z}-has-color`]:m&&!E,[`${z}-hidden`]:!O,[`${z}-rtl`]:"rtl"===C,[`${z}-borderless`]:!v},a,p,F,M),L=e=>{e.stopPropagation(),null==y||y(e),e.defaultPrevented||T(!1)},[,H]=(0,c.Z)((0,c.w)(e),(0,c.w)(P),{closable:!1,closeIconRender:e=>{const r=n.createElement("span",{className:`${z}-close-icon`,onClick:L},e);return(0,l.wm)(e,r,(e=>({onClick:r=>{var t;null===(t=null==e?void 0:e.onClick)||void 0===t||t.call(e,r),L(r)},className:s()(null==e?void 0:e.className,`${z}-close-icon`)})))}}),J="function"==typeof j.onClick||f&&"a"===f.type,A=x||null,q=A?n.createElement(n.Fragment,null,A,f&&n.createElement("span",null,f)):f,U=n.createElement("span",Object.assign({},I,{ref:r,className:R,style:$}),q,H,N&&n.createElement(Z,{key:"preset",prefixCls:z}),_&&n.createElement(S,{key:"status",prefixCls:z}));return B(J?n.createElement(u.Z,{component:"Tag"},U):U)})),T=O;T.CheckableTag=k;var I=T},64599:function(e,r,t){var n=t(96263);e.exports=function(e,r){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=n(e))||r&&e&&"number"==typeof e.length){t&&(e=t);var a=0,s=function(){};return{s:s,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,c=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return i=e.done,e},e:function(e){c=!0,o=e},f:function(){try{i||null==t.return||t.return()}finally{if(c)throw o}}}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);