"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9139],{50675:function(e,r,n){var t=n(1413),o=n(67294),a=n(72961),l=n(91146),i=function(e,r){return o.createElement(l.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:r,icon:a.Z}))},s=o.forwardRef(i);r.Z=s},8913:function(e,r,n){var t=n(1413),o=n(67294),a=n(1085),l=n(91146),i=function(e,r){return o.createElement(l.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:r,icon:a.Z}))},s=o.forwardRef(i);r.Z=s},51042:function(e,r,n){var t=n(1413),o=n(67294),a=n(42110),l=n(91146),i=function(e,r){return o.createElement(l.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:r,icon:a.Z}))},s=o.forwardRef(i);r.Z=s},41742:function(e,r,n){n.r(r),n.d(r,{default:function(){return z}});var t=n(15009),o=n.n(t),a=n(97857),l=n.n(a),i=n(99289),s=n.n(i),c=n(5574),u=n.n(c),d=n(67294),p=n(97131),f=n(12453),m=n(8232),h=n(2453),g=n(66309),b=n(83622),v=n(17788),x=n(55102),y=n(34041),Z=n(50675),C=n(8913),k=n(51042),j=n(78158);function w(e){return I.apply(this,arguments)}function I(){return(I=s()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,j.N)("/api/embeddings",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function P(e){return $.apply(this,arguments)}function $(){return($=s()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,j.N)("/api/embeddings",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function O(e){return S.apply(this,arguments)}function S(){return(S=s()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,j.N)("/api/embeddings/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function T(e){return _.apply(this,arguments)}function _(){return(_=s()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,j.N)("/api/embeddings/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var E=n(85893),z=function(){var e=(0,d.useState)(!1),r=u()(e,2),n=r[0],t=r[1],a=(0,d.useState)(!1),i=u()(a,2),c=i[0],j=i[1],I=(0,d.useState)(void 0),$=u()(I,2),S=$[0],_=$[1],z=(0,d.useRef)(),N=m.Z.useForm(),q=u()(N,1)[0],R=function(){var e=s()(o()().mark((function e(r){var n,a;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=h.ZP.loading("正在添加"),e.prev=1,console.log(r),e.next=5,P(l()({},r));case 5:return n(),h.ZP.success("添加成功"),t(!1),null===(a=z.current)||void 0===a||a.reload(),q.resetFields(),e.abrupt("return",!0);case 13:return e.prev=13,e.t0=e.catch(1),n(),h.ZP.error("添加失败，请重试"),e.abrupt("return",!1);case 18:case"end":return e.stop()}}),e,null,[[1,13]])})));return function(r){return e.apply(this,arguments)}}(),B=function(){var e=s()(o()().mark((function e(r){var n,t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r.id){e.next=3;break}return h.ZP.error("更新失败，缺少嵌入 ID"),e.abrupt("return",!1);case 3:return n=h.ZP.loading("正在更新"),e.prev=4,e.next=7,O(r);case 7:return n(),h.ZP.success("更新成功"),j(!1),_(void 0),null===(t=z.current)||void 0===t||t.reload(),q.resetFields(),e.abrupt("return",!0);case 16:return e.prev=16,e.t0=e.catch(4),n(),h.ZP.error("更新失败，请重试"),e.abrupt("return",!1);case 21:case"end":return e.stop()}}),e,null,[[4,16]])})));return function(r){return e.apply(this,arguments)}}(),F=function(){var e=s()(o()().mark((function e(r){var n,t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=h.ZP.loading("正在删除"),e.prev=1,e.next=4,T(r.id);case 4:return n(),h.ZP.success("删除成功"),null===(t=z.current)||void 0===t||t.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),n(),h.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(r){return e.apply(this,arguments)}}(),A=[{title:"ID",dataIndex:"id",valueType:"digit"},{title:"名称",dataIndex:"name",valueType:"text"},{title:"模型名称",dataIndex:"embedding_name",valueType:"text"},{title:"接口类型",dataIndex:"provider",valueType:"text"},{title:"状态",dataIndex:"is_active",valueType:"boolean",render:function(e){return(0,E.jsx)(g.Z,{color:e?"success":"default",icon:e?(0,E.jsx)(Z.Z,{}):(0,E.jsx)(C.Z,{}),style:{fontWeight:"bold",padding:"0 8px",borderRadius:"12px",display:"flex",alignItems:"center",gap:"4px"},children:e?"已激活":"未激活"})},search:!1},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",search:!1},{title:"操作",dataIndex:"option",valueType:"option",render:function(e,r){return[(0,E.jsx)(b.ZP,{type:"link",onClick:function(){j(!0),_(r)},children:"编辑"},"edit-".concat(r.id)),(0,E.jsx)(b.ZP,{type:r.is_active?"default":"primary",danger:r.is_active,icon:r.is_active?(0,E.jsx)(C.Z,{}):(0,E.jsx)(Z.Z,{}),size:"small",style:{width:"70px",borderRadius:"15px",fontWeight:"bold",marginRight:"8px"},onClick:function(){var e;v.Z.confirm({title:"确认".concat(r.is_active?"停用":"激活"),content:"确定要".concat(r.is_active?"停用":"激活","该向量模型吗？"),onOk:(e=s()(o()().mark((function e(){var n,t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=h.ZP.loading("正在更新状态"),e.prev=1,e.next=4,O({id:r.id,is_active:!r.is_active});case 4:n(),h.ZP.success("状态更新成功"),null===(t=z.current)||void 0===t||t.reload(),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(1),n(),h.ZP.error("状态更新失败，请重试");case 13:case"end":return e.stop()}}),e,null,[[1,9]])}))),function(){return e.apply(this,arguments)})})},children:r.is_active?"停用":"激活"},"toggle-".concat(r.id)),(0,E.jsx)(b.ZP,{type:"link",danger:!0,onClick:function(){return F(r)},children:"删除"},"delete-".concat(r.id))]}}];return(0,E.jsxs)(p._z,{children:[(0,E.jsx)(f.Z,{headerTitle:"嵌入管理",actionRef:z,rowKey:"id",search:{labelWidth:120,defaultCollapsed:!0},toolBarRender:function(){return[(0,E.jsxs)(b.ZP,{type:"primary",onClick:function(){t(!0)},children:[(0,E.jsx)(k.Z,{})," 新建"]},"primary")]},request:function(){var e=s()(o()().mark((function e(r){var n;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,w(l()({current:r.current,pageSize:r.pageSize},r));case 2:return n=e.sent,e.abrupt("return",{data:n.data,success:n.success,total:n.total});case 4:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),columns:A}),(0,E.jsx)(v.Z,{visible:n,title:"新建嵌入",onCancel:function(){return t(!1)},onOk:function(){return q.submit()},children:(0,E.jsxs)(m.Z,{form:q,layout:"horizontal",onFinish:R,initialValues:{provider:"openai"},labelCol:{span:6},wrapperCol:{span:18},children:[(0,E.jsx)(m.Z.Item,{name:"name",label:"名称",rules:[{required:!0,message:"请输入名称"}],children:(0,E.jsx)(x.Z,{})}),(0,E.jsx)(m.Z.Item,{name:"embedding_name",label:"模型名称",rules:[{required:!0,message:"请输入模型名称"}],children:(0,E.jsx)(x.Z,{})}),(0,E.jsx)(m.Z.Item,{name:"vector_size",label:"向量大小",initialValue:300,rules:[{required:!0,message:"请输入向量大小"}],children:(0,E.jsx)(x.Z,{type:"number"})}),(0,E.jsx)(m.Z.Item,{name:"service_url",label:"服务地址",rules:[{required:!0,message:"请输入服务地址"},{pattern:/^(http|https):\/\/[^ "]+$/,message:"请输入有效的 URL，必须以 http 或 https 开头"}],children:(0,E.jsx)(x.Z,{})}),(0,E.jsx)(m.Z.Item,{name:"api_key",label:"API Key",rules:[{required:!0,message:"请输入 API Key"}],children:(0,E.jsx)(x.Z,{})}),(0,E.jsx)(m.Z.Item,{name:"provider",label:"接口标准",rules:[{required:!0,message:"请选择接口调用标准"}],children:(0,E.jsx)(y.default,{children:(0,E.jsx)(y.default.Option,{value:"openai",children:"OpenAI"})})})]})}),S&&(0,E.jsx)(v.Z,{visible:c,title:"更新嵌入",onCancel:function(){return j(!1)},onOk:function(){return q.submit()},children:(0,E.jsxs)(m.Z,{form:q,layout:"horizontal",initialValues:S,onFinish:B,labelCol:{span:6},wrapperCol:{span:18},children:[(0,E.jsx)(m.Z.Item,{name:"id",hidden:!0,children:(0,E.jsx)(x.Z,{})}),(0,E.jsx)(m.Z.Item,{name:"name",label:"名称",rules:[{required:!0,message:"请输入名称"}],children:(0,E.jsx)(x.Z,{})}),(0,E.jsx)(m.Z.Item,{name:"embedding_name",label:"模型名称",rules:[{required:!0,message:"请输入模型名称"}],children:(0,E.jsx)(x.Z,{})}),(0,E.jsx)(m.Z.Item,{name:"service_url",label:"服务地址",rules:[{required:!0,message:"请输入服务地址"},{pattern:/^(http|https):\/\/[^ "]+$/,message:"请输入有效的 URL，必须以 http 或 https 开头"}],children:(0,E.jsx)(x.Z,{})}),(0,E.jsx)(m.Z.Item,{name:"api_key",label:"API Key",rules:[{required:!0,message:"请输入 API Key"}],children:(0,E.jsx)(x.Z,{})}),(0,E.jsx)(m.Z.Item,{name:"provider",label:"接口标准",rules:[{required:!0,message:"请选择接口调用标准"}],children:(0,E.jsx)(y.default,{children:(0,E.jsx)(y.default.Option,{value:"openai",children:"OpenAI"})})})]})})]})}},66309:function(e,r,n){n.d(r,{Z:function(){return S}});var t=n(67294),o=n(93967),a=n.n(o),l=n(98423),i=n(98787),s=n(69760),c=n(96159),u=n(45353),d=n(53124),p=n(11568),f=n(15063),m=n(14747),h=n(83262),g=n(83559);const b=e=>{const{lineWidth:r,fontSizeIcon:n,calc:t}=e,o=e.fontSizeSM;return(0,h.IX)(e,{tagFontSize:o,tagLineHeight:(0,p.bf)(t(e.lineHeightSM).mul(o).equal()),tagIconSize:t(n).sub(t(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},v=e=>({defaultBg:new f.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var x=(0,g.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:n,tagPaddingHorizontal:t,componentCls:o,calc:a}=e,l=a(t).sub(n).equal(),i=a(r).sub(n).equal();return{[o]:Object.assign(Object.assign({},(0,m.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:i,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:l}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(b(e))),v),y=function(e,r){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(n[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(t=Object.getOwnPropertySymbols(e);o<t.length;o++)r.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(n[t[o]]=e[t[o]])}return n};const Z=t.forwardRef(((e,r)=>{const{prefixCls:n,style:o,className:l,checked:i,onChange:s,onClick:c}=e,u=y(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:f}=t.useContext(d.E_),m=p("tag",n),[h,g,b]=x(m),v=a()(m,`${m}-checkable`,{[`${m}-checkable-checked`]:i},null==f?void 0:f.className,l,g,b);return h(t.createElement("span",Object.assign({},u,{ref:r,style:Object.assign(Object.assign({},o),null==f?void 0:f.style),className:v,onClick:e=>{null==s||s(!i),null==c||c(e)}})))}));var C=Z,k=n(98719);var j=(0,g.bk)(["Tag","preset"],(e=>(e=>(0,k.Z)(e,((r,n)=>{let{textColor:t,lightBorderColor:o,lightColor:a,darkColor:l}=n;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:t,background:a,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:l,borderColor:l},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(b(e))),v);const w=(e,r,n)=>{const t="string"!=typeof(o=n)?o:o.charAt(0).toUpperCase()+o.slice(1);var o;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${n}`],background:e[`color${t}Bg`],borderColor:e[`color${t}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var I=(0,g.bk)(["Tag","status"],(e=>{const r=b(e);return[w(r,"success","Success"),w(r,"processing","Info"),w(r,"error","Error"),w(r,"warning","Warning")]}),v),P=function(e,r){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(n[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(t=Object.getOwnPropertySymbols(e);o<t.length;o++)r.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(n[t[o]]=e[t[o]])}return n};const $=t.forwardRef(((e,r)=>{const{prefixCls:n,className:o,rootClassName:p,style:f,children:m,icon:h,color:g,onClose:b,bordered:v=!0,visible:y}=e,Z=P(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:C,direction:k,tag:w}=t.useContext(d.E_),[$,O]=t.useState(!0),S=(0,l.Z)(Z,["closeIcon","closable"]);t.useEffect((()=>{void 0!==y&&O(y)}),[y]);const T=(0,i.o2)(g),_=(0,i.yT)(g),E=T||_,z=Object.assign(Object.assign({backgroundColor:g&&!E?g:void 0},null==w?void 0:w.style),f),N=C("tag",n),[q,R,B]=x(N),F=a()(N,null==w?void 0:w.className,{[`${N}-${g}`]:E,[`${N}-has-color`]:g&&!E,[`${N}-hidden`]:!$,[`${N}-rtl`]:"rtl"===k,[`${N}-borderless`]:!v},o,p,R,B),A=e=>{e.stopPropagation(),null==b||b(e),e.defaultPrevented||O(!1)},[,H]=(0,s.Z)((0,s.w)(e),(0,s.w)(w),{closable:!1,closeIconRender:e=>{const r=t.createElement("span",{className:`${N}-close-icon`,onClick:A},e);return(0,c.wm)(e,r,(e=>({onClick:r=>{var n;null===(n=null==e?void 0:e.onClick)||void 0===n||n.call(e,r),A(r)},className:a()(null==e?void 0:e.className,`${N}-close-icon`)})))}}),L="function"==typeof Z.onClick||m&&"a"===m.type,W=h||null,D=W?t.createElement(t.Fragment,null,W,m&&t.createElement("span",null,m)):m,K=t.createElement("span",Object.assign({},S,{ref:r,className:F,style:z}),D,H,T&&t.createElement(j,{key:"preset",prefixCls:N}),_&&t.createElement(I,{key:"status",prefixCls:N}));return q(L?t.createElement(u.Z,{component:"Tag"},K):K)})),O=$;O.CheckableTag=C;var S=O}}]);