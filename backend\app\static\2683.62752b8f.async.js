(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2683],{94470:function(e){"use strict";var n=Object.prototype.hasOwnProperty,t=Object.prototype.toString,r=Object.defineProperty,o=Object.getOwnPropertyDescriptor,i=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===t.call(e)},l=function(e){if(!e||"[object Object]"!==t.call(e))return!1;var r,o=n.call(e,"constructor"),i=e.constructor&&e.constructor.prototype&&n.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!o&&!i)return!1;for(r in e);return void 0===r||n.call(e,r)},s=function(e,n){r&&"__proto__"===n.name?r(e,n.name,{enumerable:!0,configurable:!0,value:n.newValue,writable:!0}):e[n.name]=n.newValue},a=function(e,t){if("__proto__"===t){if(!n.call(e,t))return;if(o)return o(e,t).value}return e[t]};e.exports=function e(){var n,t,r,o,u,c,f=arguments[0],p=1,d=arguments.length,h=!1;for("boolean"==typeof f&&(h=f,f=arguments[1]||{},p=2),(null==f||"object"!=typeof f&&"function"!=typeof f)&&(f={});p<d;++p)if(null!=(n=arguments[p]))for(t in n)r=a(f,t),f!==(o=a(n,t))&&(h&&o&&(l(o)||(u=i(o)))?(u?(u=!1,c=r&&i(r)?r:[]):c=r&&l(r)?r:{},s(f,{name:t,newValue:e(h,c,o)})):void 0!==o&&s(f,{name:t,newValue:o}));return f}},18139:function(e){var n=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,t=/\n/g,r=/^\s*/,o=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,i=/^:\s*/,l=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,s=/^[;\s]*/,a=/^\s+|\s+$/g,u="";function c(e){return e?e.replace(a,u):u}e.exports=function(e,a){if("string"!=typeof e)throw new TypeError("First argument must be a string");if(!e)return[];a=a||{};var f=1,p=1;function d(e){var n=e.match(t);n&&(f+=n.length);var r=e.lastIndexOf("\n");p=~r?e.length-r:p+e.length}function h(){var e={line:f,column:p};return function(n){return n.position=new m(e),v(),n}}function m(e){this.start=e,this.end={line:f,column:p},this.source=a.source}m.prototype.content=e;var g=[];function y(n){var t=new Error(a.source+":"+f+":"+p+": "+n);if(t.reason=n,t.filename=a.source,t.line=f,t.column=p,t.source=e,!a.silent)throw t;g.push(t)}function x(n){var t=n.exec(e);if(t){var r=t[0];return d(r),e=e.slice(r.length),t}}function v(){x(r)}function k(e){var n;for(e=e||[];n=b();)!1!==n&&e.push(n);return e}function b(){var n=h();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var t=2;u!=e.charAt(t)&&("*"!=e.charAt(t)||"/"!=e.charAt(t+1));)++t;if(t+=2,u===e.charAt(t-1))return y("End of comment missing");var r=e.slice(2,t-2);return p+=2,d(r),e=e.slice(t),p+=2,n({type:"comment",comment:r})}}function w(){var e=h(),t=x(o);if(t){if(b(),!x(i))return y("property missing ':'");var r=x(l),a=e({type:"declaration",property:c(t[0].replace(n,u)),value:r?c(r[0].replace(n,u)):u});return x(s),a}}return v(),function(){var e,n=[];for(k(n);e=w();)!1!==e&&(n.push(e),k(n));return n}()}},5174:function(e,n,t){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(e,n){var t=null;if(!e||"string"!=typeof e)return t;var r=(0,o.default)(e),i="function"==typeof n;return r.forEach((function(e){if("declaration"===e.type){var r=e.property,o=e.value;i?n(r,o,e):o&&((t=t||{})[r]=o)}})),t};var o=r(t(18139))},24345:function(e,n,t){"use strict";function r(){}function o(){}t.d(n,{ok:function(){return r},t1:function(){return o}})},27962:function(e,n,t){"use strict";t.d(n,{B:function(){return o}});const r={};function o(e,n){const t=n||r;return i(e,"boolean"!=typeof t.includeImageAlt||t.includeImageAlt,"boolean"!=typeof t.includeHtml||t.includeHtml)}function i(e,n,t){if(function(e){return Boolean(e&&"object"==typeof e)}(e)){if("value"in e)return"html"!==e.type||t?e.value:"";if(n&&"alt"in e&&e.alt)return e.alt;if("children"in e)return l(e.children,n,t)}return Array.isArray(e)?l(e,n,t):""}function l(e,n,t){const r=[];let o=-1;for(;++o<e.length;)r[o]=i(e[o],n,t);return r.join("")}},23402:function(e,n,t){"use strict";t.d(n,{w:function(){return i}});var r=t(42761),o=t(15459);const i={partial:!0,tokenize:function(e,n,t){return function(n){return(0,o.xz)(n)?(0,r.f)(e,i,"linePrefix")(n):i(n)};function i(e){return null===e||(0,o.Ch)(e)?n(e):t(e)}}}},42761:function(e,n,t){"use strict";t.d(n,{f:function(){return o}});var r=t(15459);function o(e,n,t,o){const i=o?o-1:Number.POSITIVE_INFINITY;let l=0;return function(o){if((0,r.xz)(o))return e.enter(t),s(o);return n(o)};function s(o){return(0,r.xz)(o)&&l++<i?(e.consume(o),s):(e.exit(t),n(o))}}},15459:function(e,n,t){"use strict";t.d(n,{AF:function(){return a},Av:function(){return l},B8:function(){return h},Ch:function(){return c},H$:function(){return o},Xh:function(){return d},jv:function(){return r},n9:function(){return i},pY:function(){return s},sR:function(){return u},xz:function(){return p},z3:function(){return f}});const r=m(/[A-Za-z]/),o=m(/[\dA-Za-z]/),i=m(/[#-'*+\--9=?A-Z^-~]/);function l(e){return null!==e&&(e<32||127===e)}const s=m(/\d/),a=m(/[\dA-Fa-f]/),u=m(/[!-/:-@[-`{-~]/);function c(e){return null!==e&&e<-2}function f(e){return null!==e&&(e<0||32===e)}function p(e){return-2===e||-1===e||32===e}const d=m(/\p{P}|\p{S}/u),h=m(/\s/);function m(e){return function(n){return null!==n&&n>-1&&e.test(String.fromCharCode(n))}}},62888:function(e,n,t){"use strict";function r(e,n,t,r){const o=e.length;let i,l=0;if(n=n<0?-n>o?0:o+n:n>o?o:n,t=t>0?t:0,r.length<1e4)i=Array.from(r),i.unshift(n,t),e.splice(...i);else for(t&&e.splice(n,t);l<r.length;)i=r.slice(l,l+1e4),i.unshift(n,0),e.splice(...i),l+=1e4,n+=1e4}function o(e,n){return e.length>0?(r(e,e.length,0,n),e):n}t.d(n,{V:function(){return o},d:function(){return r}})},62987:function(e,n,t){"use strict";t.d(n,{r:function(){return o}});var r=t(15459);function o(e){return null===e||(0,r.z3)(e)||(0,r.B8)(e)?1:(0,r.Xh)(e)?2:void 0}},4663:function(e,n,t){"use strict";t.d(n,{W:function(){return i}});var r=t(62888);const o={}.hasOwnProperty;function i(e){const n={};let t=-1;for(;++t<e.length;)l(n,e[t]);return n}function l(e,n){let t;for(t in n){const r=(o.call(e,t)?e[t]:void 0)||(e[t]={}),i=n[t];let l;if(i)for(l in i){o.call(r,l)||(r[l]=[]);const e=i[l];s(r[l],Array.isArray(e)?e:e?[e]:[])}}}function s(e,n){let t=-1;const o=[];for(;++t<n.length;)("after"===n[t].add?e:o).push(n[t]);(0,r.d)(e,0,0,o)}},11098:function(e,n,t){"use strict";function r(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}t.d(n,{d:function(){return r}})},63233:function(e,n,t){"use strict";function r(e,n,t){const r=[];let o=-1;for(;++o<e.length;){const i=e[o].resolveAll;i&&!r.includes(i)&&(n=i(n,t),r.push(i))}return n}t.d(n,{C:function(){return r}})},22683:function(e,n,t){"use strict";t.d(n,{UG:function(){return tr}});var r={};t.r(r),t.d(r,{boolean:function(){return y},booleanish:function(){return x},commaOrSpaceSeparated:function(){return S},commaSeparated:function(){return w},number:function(){return k},overloadedBoolean:function(){return v},spaceSeparated:function(){return b}});var o={};t.r(o),t.d(o,{attentionMarkers:function(){return Bn},contentInitial:function(){return Mn},disable:function(){return Hn},document:function(){return On},flow:function(){return Fn},flowInitial:function(){return Nn},insideSpan:function(){return jn},string:function(){return Rn},text:function(){return _n}});var i=t(24345);const l=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,s=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,a={};function u(e,n){return((n||a).jsx?s:l).test(e)}const c=/[ \t\n\f\r]/g;function f(e){return""===e.replace(c,"")}class p{constructor(e,n,t){this.property=e,this.normal=n,t&&(this.space=t)}}function d(e,n){const t={},r={};let o=-1;for(;++o<e.length;)Object.assign(t,e[o].property),Object.assign(r,e[o].normal);return new p(t,r,n)}function h(e){return e.toLowerCase()}p.prototype.property={},p.prototype.normal={},p.prototype.space=null;class m{constructor(e,n){this.property=e,this.attribute=n}}m.prototype.space=null,m.prototype.boolean=!1,m.prototype.booleanish=!1,m.prototype.overloadedBoolean=!1,m.prototype.number=!1,m.prototype.commaSeparated=!1,m.prototype.spaceSeparated=!1,m.prototype.commaOrSpaceSeparated=!1,m.prototype.mustUseProperty=!1,m.prototype.defined=!1;let g=0;const y=C(),x=C(),v=C(),k=C(),b=C(),w=C(),S=C();function C(){return 2**++g}const I=Object.keys(r);class E extends m{constructor(e,n,t,o){let i=-1;if(super(e,n),P(this,"space",o),"number"==typeof t)for(;++i<I.length;){const e=I[i];P(this,I[i],(t&r[e])===r[e])}}}function P(e,n,t){t&&(e[n]=t)}E.prototype.defined=!0;const T={}.hasOwnProperty;function z(e){const n={},t={};let r;for(r in e.properties)if(T.call(e.properties,r)){const o=e.properties[r],i=new E(r,e.transform(e.attributes||{},r),o,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(i.mustUseProperty=!0),n[r]=i,t[h(r)]=r,t[h(i.attribute)]=r}return new p(n,t,e.space)}const A=z({space:"xlink",transform(e,n){return"xlink:"+n.slice(5).toLowerCase()},properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}}),D=z({space:"xml",transform(e,n){return"xml:"+n.slice(3).toLowerCase()},properties:{xmlLang:null,xmlBase:null,xmlSpace:null}});function L(e,n){return n in e?e[n]:n}function O(e,n){return L(e,n.toLowerCase())}const M=z({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:O,properties:{xmlns:null,xmlnsXLink:null}}),N=z({transform(e,n){return"role"===n?n:"aria-"+n.slice(4).toLowerCase()},properties:{ariaActiveDescendant:null,ariaAtomic:x,ariaAutoComplete:null,ariaBusy:x,ariaChecked:x,ariaColCount:k,ariaColIndex:k,ariaColSpan:k,ariaControls:b,ariaCurrent:null,ariaDescribedBy:b,ariaDetails:null,ariaDisabled:x,ariaDropEffect:b,ariaErrorMessage:null,ariaExpanded:x,ariaFlowTo:b,ariaGrabbed:x,ariaHasPopup:null,ariaHidden:x,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:b,ariaLevel:k,ariaLive:null,ariaModal:x,ariaMultiLine:x,ariaMultiSelectable:x,ariaOrientation:null,ariaOwns:b,ariaPlaceholder:null,ariaPosInSet:k,ariaPressed:x,ariaReadOnly:x,ariaRelevant:null,ariaRequired:x,ariaRoleDescription:b,ariaRowCount:k,ariaRowIndex:k,ariaRowSpan:k,ariaSelected:x,ariaSetSize:k,ariaSort:null,ariaValueMax:k,ariaValueMin:k,ariaValueNow:k,ariaValueText:null,role:null}}),F=z({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:O,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:w,acceptCharset:b,accessKey:b,action:null,allow:null,allowFullScreen:y,allowPaymentRequest:y,allowUserMedia:y,alt:null,as:null,async:y,autoCapitalize:null,autoComplete:b,autoFocus:y,autoPlay:y,blocking:b,capture:null,charSet:null,checked:y,cite:null,className:b,cols:k,colSpan:null,content:null,contentEditable:x,controls:y,controlsList:b,coords:k|w,crossOrigin:null,data:null,dateTime:null,decoding:null,default:y,defer:y,dir:null,dirName:null,disabled:y,download:v,draggable:x,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:y,formTarget:null,headers:b,height:k,hidden:y,high:k,href:null,hrefLang:null,htmlFor:b,httpEquiv:b,id:null,imageSizes:null,imageSrcSet:null,inert:y,inputMode:null,integrity:null,is:null,isMap:y,itemId:null,itemProp:b,itemRef:b,itemScope:y,itemType:b,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:y,low:k,manifest:null,max:null,maxLength:k,media:null,method:null,min:null,minLength:k,multiple:y,muted:y,name:null,nonce:null,noModule:y,noValidate:y,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:y,optimum:k,pattern:null,ping:b,placeholder:null,playsInline:y,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:y,referrerPolicy:null,rel:b,required:y,reversed:y,rows:k,rowSpan:k,sandbox:b,scope:null,scoped:y,seamless:y,selected:y,shadowRootClonable:y,shadowRootDelegatesFocus:y,shadowRootMode:null,shape:null,size:k,sizes:null,slot:null,span:k,spellCheck:x,src:null,srcDoc:null,srcLang:null,srcSet:null,start:k,step:null,style:null,tabIndex:k,target:null,title:null,translate:null,type:null,typeMustMatch:y,useMap:null,value:x,width:k,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:b,axis:null,background:null,bgColor:null,border:k,borderColor:null,bottomMargin:k,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:y,declare:y,event:null,face:null,frame:null,frameBorder:null,hSpace:k,leftMargin:k,link:null,longDesc:null,lowSrc:null,marginHeight:k,marginWidth:k,noResize:y,noHref:y,noShade:y,noWrap:y,object:null,profile:null,prompt:null,rev:null,rightMargin:k,rules:null,scheme:null,scrolling:x,standby:null,summary:null,text:null,topMargin:k,valueType:null,version:null,vAlign:null,vLink:null,vSpace:k,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:y,disableRemotePlayback:y,prefix:null,property:null,results:k,security:null,unselectable:null}}),R=z({space:"svg",attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},transform:L,properties:{about:S,accentHeight:k,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:k,amplitude:k,arabicForm:null,ascent:k,attributeName:null,attributeType:null,azimuth:k,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:k,by:null,calcMode:null,capHeight:k,className:b,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:k,diffuseConstant:k,direction:null,display:null,dur:null,divisor:k,dominantBaseline:null,download:y,dx:null,dy:null,edgeMode:null,editable:null,elevation:k,enableBackground:null,end:null,event:null,exponent:k,externalResourcesRequired:null,fill:null,fillOpacity:k,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:w,g2:w,glyphName:w,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:k,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:k,horizOriginX:k,horizOriginY:k,id:null,ideographic:k,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:k,k:k,k1:k,k2:k,k3:k,k4:k,kernelMatrix:S,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:k,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:k,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:k,overlineThickness:k,paintOrder:null,panose1:null,path:null,pathLength:k,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:b,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:k,pointsAtY:k,pointsAtZ:k,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:S,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:S,rev:S,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:S,requiredFeatures:S,requiredFonts:S,requiredFormats:S,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:k,specularExponent:k,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:k,strikethroughThickness:k,string:null,stroke:null,strokeDashArray:S,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:k,strokeOpacity:k,strokeWidth:null,style:null,surfaceScale:k,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:S,tabIndex:k,tableValues:null,target:null,targetX:k,targetY:k,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:S,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:k,underlineThickness:k,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:k,values:null,vAlphabetic:k,vMathematical:k,vectorEffect:null,vHanging:k,vIdeographic:k,version:null,vertAdvY:k,vertOriginX:k,vertOriginY:k,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:k,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null}}),_=d([D,A,M,N,F],"html"),j=d([D,A,M,N,R],"svg"),B=/^data[-\w.:]+$/i,H=/-[a-z]/g,V=/[A-Z]/g;function U(e){return"-"+e.toLowerCase()}function q(e){return e.charAt(1).toUpperCase()}const $={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var Y=t(5174),W=Y.default||Y;const K=X("end"),Q=X("start");function X(e){return function(n){const t=n&&n.position&&n.position[e]||{};if("number"==typeof t.line&&t.line>0&&"number"==typeof t.column&&t.column>0)return{line:t.line,column:t.column,offset:"number"==typeof t.offset&&t.offset>-1?t.offset:void 0}}}function J(e){return e&&"object"==typeof e?"position"in e||"type"in e?G(e.position):"start"in e||"end"in e?G(e):"line"in e||"column"in e?Z(e):"":""}function Z(e){return ee(e&&e.line)+":"+ee(e&&e.column)}function G(e){return Z(e&&e.start)+"-"+Z(e&&e.end)}function ee(e){return e&&"number"==typeof e?e:1}class ne extends Error{constructor(e,n,t){super(),"string"==typeof n&&(t=n,n=void 0);let r="",o={},i=!1;if(n&&(o="line"in n&&"column"in n||"start"in n&&"end"in n?{place:n}:"type"in n?{ancestors:[n],place:n.position}:{...n}),"string"==typeof e?r=e:!o.cause&&e&&(i=!0,r=e.message,o.cause=e),!o.ruleId&&!o.source&&"string"==typeof t){const e=t.indexOf(":");-1===e?o.ruleId=t:(o.source=t.slice(0,e),o.ruleId=t.slice(e+1))}if(!o.place&&o.ancestors&&o.ancestors){const e=o.ancestors[o.ancestors.length-1];e&&(o.place=e.position)}const l=o.place&&"start"in o.place?o.place.start:o.place;this.ancestors=o.ancestors||void 0,this.cause=o.cause||void 0,this.column=l?l.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=l?l.line:void 0,this.name=J(o.place)||"1:1",this.place=o.place||void 0,this.reason=this.message,this.ruleId=o.ruleId||void 0,this.source=o.source||void 0,this.stack=i&&o.cause&&"string"==typeof o.cause.stack?o.cause.stack:"",this.actual,this.expected,this.note,this.url}}ne.prototype.file="",ne.prototype.name="",ne.prototype.reason="",ne.prototype.message="",ne.prototype.stack="",ne.prototype.column=void 0,ne.prototype.line=void 0,ne.prototype.ancestors=void 0,ne.prototype.cause=void 0,ne.prototype.fatal=void 0,ne.prototype.place=void 0,ne.prototype.ruleId=void 0,ne.prototype.source=void 0;const te={}.hasOwnProperty,re=new Map,oe=/[A-Z]/g,ie=/-([a-z])/g,le=new Set(["table","tbody","thead","tfoot","tr"]),se=new Set(["td","th"]),ae="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function ue(e,n){if(!n||void 0===n.Fragment)throw new TypeError("Expected `Fragment` in options");const t=n.filePath||void 0;let r;if(n.development){if("function"!=typeof n.jsxDEV)throw new TypeError("Expected `jsxDEV` in options when `development: true`");r=function(e,n){return t;function t(t,r,o,i){const l=Array.isArray(o.children),s=Q(t);return n(r,o,i,l,{columnNumber:s?s.column-1:void 0,fileName:e,lineNumber:s?s.line:void 0},void 0)}}(t,n.jsxDEV)}else{if("function"!=typeof n.jsx)throw new TypeError("Expected `jsx` in production options");if("function"!=typeof n.jsxs)throw new TypeError("Expected `jsxs` in production options");r=function(e,n,t){return r;function r(e,r,o,i){const l=Array.isArray(o.children)?t:n;return i?l(r,o,i):l(r,o)}}(0,n.jsx,n.jsxs)}const o={Fragment:n.Fragment,ancestors:[],components:n.components||{},create:r,elementAttributeNameCase:n.elementAttributeNameCase||"react",evaluater:n.createEvaluater?n.createEvaluater():void 0,filePath:t,ignoreInvalidStyle:n.ignoreInvalidStyle||!1,passKeys:!1!==n.passKeys,passNode:n.passNode||!1,schema:"svg"===n.space?j:_,stylePropertyNameCase:n.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==n.tableCellAlignToStyle},i=ce(o,e,void 0);return i&&"string"!=typeof i?i:o.create(e,o.Fragment,{children:i||void 0},void 0)}function ce(e,n,t){return"element"===n.type?function(e,n,t){const r=e.schema;let o=r;"svg"===n.tagName.toLowerCase()&&"html"===r.space&&(o=j,e.schema=o);e.ancestors.push(n);const i=me(e,n.tagName,!1),l=function(e,n){const t={};let r,o;for(o in n.properties)if("children"!==o&&te.call(n.properties,o)){const i=he(e,o,n.properties[o]);if(i){const[o,l]=i;e.tableCellAlignToStyle&&"align"===o&&"string"==typeof l&&se.has(n.tagName)?r=l:t[o]=l}}if(r){(t.style||(t.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=r}return t}(e,n);let s=de(e,n);le.has(n.tagName)&&(s=s.filter((function(e){return"string"!=typeof e||!("object"==typeof(n=e)?"text"===n.type&&f(n.value):f(n));var n})));return fe(e,l,i,n),pe(l,s),e.ancestors.pop(),e.schema=r,e.create(n,i,l,t)}(e,n,t):"mdxFlowExpression"===n.type||"mdxTextExpression"===n.type?function(e,n){if(n.data&&n.data.estree&&e.evaluater){const t=n.data.estree.body[0];return(0,i.ok)("ExpressionStatement"===t.type),e.evaluater.evaluateExpression(t.expression)}ge(e,n.position)}(e,n):"mdxJsxFlowElement"===n.type||"mdxJsxTextElement"===n.type?function(e,n,t){const r=e.schema;let o=r;"svg"===n.name&&"html"===r.space&&(o=j,e.schema=o);e.ancestors.push(n);const l=null===n.name?e.Fragment:me(e,n.name,!0),s=function(e,n){const t={};for(const r of n.attributes)if("mdxJsxExpressionAttribute"===r.type)if(r.data&&r.data.estree&&e.evaluater){const n=r.data.estree.body[0];(0,i.ok)("ExpressionStatement"===n.type);const o=n.expression;(0,i.ok)("ObjectExpression"===o.type);const l=o.properties[0];(0,i.ok)("SpreadElement"===l.type),Object.assign(t,e.evaluater.evaluateExpression(l.argument))}else ge(e,n.position);else{const o=r.name;let l;if(r.value&&"object"==typeof r.value)if(r.value.data&&r.value.data.estree&&e.evaluater){const n=r.value.data.estree.body[0];(0,i.ok)("ExpressionStatement"===n.type),l=e.evaluater.evaluateExpression(n.expression)}else ge(e,n.position);else l=null===r.value||r.value;t[o]=l}return t}(e,n),a=de(e,n);return fe(e,s,l,n),pe(s,a),e.ancestors.pop(),e.schema=r,e.create(n,l,s,t)}(e,n,t):"mdxjsEsm"===n.type?function(e,n){if(n.data&&n.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(n.data.estree);ge(e,n.position)}(e,n):"root"===n.type?function(e,n,t){const r={};return pe(r,de(e,n)),e.create(n,e.Fragment,r,t)}(e,n,t):"text"===n.type?function(e,n){return n.value}(0,n):void 0}function fe(e,n,t,r){"string"!=typeof t&&t!==e.Fragment&&e.passNode&&(n.node=r)}function pe(e,n){if(n.length>0){const t=n.length>1?n:n[0];t&&(e.children=t)}}function de(e,n){const t=[];let r=-1;const o=e.passKeys?new Map:re;for(;++r<n.children.length;){const i=n.children[r];let l;if(e.passKeys){const e="element"===i.type?i.tagName:"mdxJsxFlowElement"===i.type||"mdxJsxTextElement"===i.type?i.name:void 0;if(e){const n=o.get(e)||0;l=e+"-"+n,o.set(e,n+1)}}const s=ce(e,i,l);void 0!==s&&t.push(s)}return t}function he(e,n,t){const r=function(e,n){const t=h(n);let r=n,o=m;if(t in e.normal)return e.property[e.normal[t]];if(t.length>4&&"data"===t.slice(0,4)&&B.test(n)){if("-"===n.charAt(4)){const e=n.slice(5).replace(H,q);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{const e=n.slice(4);if(!H.test(e)){let t=e.replace(V,U);"-"!==t.charAt(0)&&(t="-"+t),n="data"+t}}o=E}return new o(r,n)}(e.schema,n);if(!(null==t||"number"==typeof t&&Number.isNaN(t))){if(Array.isArray(t)&&(t=r.commaSeparated?function(e,n){const t=n||{};return(""===e[e.length-1]?[...e,""]:e).join((t.padRight?" ":"")+","+(!1===t.padLeft?"":" ")).trim()}(t):t.join(" ").trim()),"style"===r.property){let n="object"==typeof t?t:function(e,n){const t={};try{W(n,r)}catch(n){if(!e.ignoreInvalidStyle){const t=n,r=new ne("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:t,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw r.file=e.filePath||void 0,r.url=ae+"#cannot-parse-style-attribute",r}}return t;function r(e,n){let r=e;"--"!==r.slice(0,2)&&("-ms-"===r.slice(0,4)&&(r="ms-"+r.slice(4)),r=r.replace(ie,xe)),t[r]=n}}(e,String(t));return"css"===e.stylePropertyNameCase&&(n=function(e){const n={};let t;for(t in e)te.call(e,t)&&(n[ye(t)]=e[t]);return n}(n)),["style",n]}return["react"===e.elementAttributeNameCase&&r.space?$[r.property]||r.property:r.attribute,t]}}function me(e,n,t){let r;if(t)if(n.includes(".")){const e=n.split(".");let t,o=-1;for(;++o<e.length;){const n=u(e[o])?{type:"Identifier",name:e[o]}:{type:"Literal",value:e[o]};t=t?{type:"MemberExpression",object:t,property:n,computed:Boolean(o&&"Literal"===n.type),optional:!1}:n}(0,i.ok)(t,"always a result"),r=t}else r=u(n)&&!/^[a-z]/.test(n)?{type:"Identifier",name:n}:{type:"Literal",value:n};else r={type:"Literal",value:n};if("Literal"===r.type){const n=r.value;return te.call(e.components,n)?e.components[n]:n}if(e.evaluater)return e.evaluater.evaluateExpression(r);ge(e)}function ge(e,n){const t=new ne("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:n,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=ae+"#cannot-handle-mdx-estrees-without-createevaluater",t}function ye(e){let n=e.replace(oe,ve);return"ms-"===n.slice(0,3)&&(n="-"+n),n}function xe(e,n){return n.toUpperCase()}function ve(e){return"-"+e.toLowerCase()}const ke={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var be=t(85893),we=(t(67294),t(27962)),Se=t(62888);class Ce{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,n){const t=null==n?Number.POSITIVE_INFINITY:n;return t<this.left.length?this.left.slice(e,t):e>this.left.length?this.right.slice(this.right.length-t+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-t+this.left.length).reverse())}splice(e,n,t){const r=n||0;this.setCursor(Math.trunc(e));const o=this.right.splice(this.right.length-r,Number.POSITIVE_INFINITY);return t&&Ie(this.left,t),o.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),Ie(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),Ie(this.right,e.reverse())}setCursor(e){if(!(e===this.left.length||e>this.left.length&&0===this.right.length||e<0&&0===this.left.length))if(e<this.left.length){const n=this.left.splice(e,Number.POSITIVE_INFINITY);Ie(this.right,n.reverse())}else{const n=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);Ie(this.left,n.reverse())}}}function Ie(e,n){let t=0;if(n.length<1e4)e.push(...n);else for(;t<n.length;)e.push(...n.slice(t,t+1e4)),t+=1e4}function Ee(e){const n={};let t,r,o,i,l,s,a,u=-1;const c=new Ce(e);for(;++u<c.length;){for(;u in n;)u=n[u];if(t=c.get(u),u&&"chunkFlow"===t[1].type&&"listItemPrefix"===c.get(u-1)[1].type&&(s=t[1]._tokenizer.events,o=0,o<s.length&&"lineEndingBlank"===s[o][1].type&&(o+=2),o<s.length&&"content"===s[o][1].type))for(;++o<s.length&&"content"!==s[o][1].type;)"chunkText"===s[o][1].type&&(s[o][1]._isInFirstContentOfListItem=!0,o++);if("enter"===t[0])t[1].contentType&&(Object.assign(n,Pe(c,u)),u=n[u],a=!0);else if(t[1]._container){for(o=u,r=void 0;o--;)if(i=c.get(o),"lineEnding"===i[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(r&&(c.get(r)[1].type="lineEndingBlank"),i[1].type="lineEnding",r=o);else if("linePrefix"!==i[1].type)break;r&&(t[1].end={...c.get(r)[1].start},l=c.slice(r,u),l.unshift(t),c.splice(r,u-r+1,l))}}return(0,Se.d)(e,0,Number.POSITIVE_INFINITY,c.slice(0)),!a}function Pe(e,n){const t=e.get(n)[1],r=e.get(n)[2];let o=n-1;const i=[],l=t._tokenizer||r.parser[t.contentType](t.start),s=l.events,a=[],u={};let c,f,p=-1,d=t,h=0,m=0;const g=[m];for(;d;){for(;e.get(++o)[1]!==d;);i.push(o),d._tokenizer||(c=r.sliceStream(d),d.next||c.push(null),f&&l.defineSkip(d.start),d._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=!0),l.write(c),d._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=void 0)),f=d,d=d.next}for(d=t;++p<s.length;)"exit"===s[p][0]&&"enter"===s[p-1][0]&&s[p][1].type===s[p-1][1].type&&s[p][1].start.line!==s[p][1].end.line&&(m=p+1,g.push(m),d._tokenizer=void 0,d.previous=void 0,d=d.next);for(l.events=[],d?(d._tokenizer=void 0,d.previous=void 0):g.pop(),p=g.length;p--;){const n=s.slice(g[p],g[p+1]),t=i.pop();a.push([t,t+n.length-1]),e.splice(t,2,n)}for(a.reverse(),p=-1;++p<a.length;)u[h+a[p][0]]=h+a[p][1],h+=a[p][1]-a[p][0]-1;return u}var Te=t(4663),ze=t(42761),Ae=t(15459);const De={tokenize:function(e){const n=e.attempt(this.parser.constructs.contentInitial,(function(t){if(null===t)return void e.consume(t);return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,ze.f)(e,n,"linePrefix")}),(function(n){return e.enter("paragraph"),r(n)}));let t;return n;function r(n){const r=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=r),t=r,o(n)}function o(n){return null===n?(e.exit("chunkText"),e.exit("paragraph"),void e.consume(n)):(0,Ae.Ch)(n)?(e.consume(n),e.exit("chunkText"),r):(e.consume(n),o)}}};const Le={tokenize:function(e){const n=this,t=[];let r,o,i,l=0;return s;function s(r){if(l<t.length){const o=t[l];return n.containerState=o[1],e.attempt(o[0].continuation,a,u)(r)}return u(r)}function a(e){if(l++,n.containerState._closeFlow){n.containerState._closeFlow=void 0,r&&x();const t=n.events.length;let o,i=t;for(;i--;)if("exit"===n.events[i][0]&&"chunkFlow"===n.events[i][1].type){o=n.events[i][1].end;break}y(l);let s=t;for(;s<n.events.length;)n.events[s][1].end={...o},s++;return(0,Se.d)(n.events,i+1,0,n.events.slice(t)),n.events.length=s,u(e)}return s(e)}function u(o){if(l===t.length){if(!r)return p(o);if(r.currentConstruct&&r.currentConstruct.concrete)return h(o);n.interrupt=Boolean(r.currentConstruct&&!r._gfmTableDynamicInterruptHack)}return n.containerState={},e.check(Oe,c,f)(o)}function c(e){return r&&x(),y(l),p(e)}function f(e){return n.parser.lazy[n.now().line]=l!==t.length,i=n.now().offset,h(e)}function p(t){return n.containerState={},e.attempt(Oe,d,h)(t)}function d(e){return l++,t.push([n.currentConstruct,n.containerState]),p(e)}function h(t){return null===t?(r&&x(),y(0),void e.consume(t)):(r=r||n.parser.flow(n.now()),e.enter("chunkFlow",{_tokenizer:r,contentType:"flow",previous:o}),m(t))}function m(t){return null===t?(g(e.exit("chunkFlow"),!0),y(0),void e.consume(t)):(0,Ae.Ch)(t)?(e.consume(t),g(e.exit("chunkFlow")),l=0,n.interrupt=void 0,s):(e.consume(t),m)}function g(e,t){const s=n.sliceStream(e);if(t&&s.push(null),e.previous=o,o&&(o.next=e),o=e,r.defineSkip(e.start),r.write(s),n.parser.lazy[e.start.line]){let e=r.events.length;for(;e--;)if(r.events[e][1].start.offset<i&&(!r.events[e][1].end||r.events[e][1].end.offset>i))return;const t=n.events.length;let o,s,a=t;for(;a--;)if("exit"===n.events[a][0]&&"chunkFlow"===n.events[a][1].type){if(o){s=n.events[a][1].end;break}o=!0}for(y(l),e=t;e<n.events.length;)n.events[e][1].end={...s},e++;(0,Se.d)(n.events,a+1,0,n.events.slice(t)),n.events.length=e}}function y(r){let o=t.length;for(;o-- >r;){const r=t[o];n.containerState=r[1],r[0].exit.call(n,e)}t.length=r}function x(){r.write([null]),o=void 0,r=void 0,n.containerState._closeFlow=void 0}}},Oe={tokenize:function(e,n,t){return(0,ze.f)(e,e.attempt(this.parser.constructs.document,n,t),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};var Me=t(23402);const Ne={resolve:function(e){return Ee(e),e},tokenize:function(e,n){let t;return function(n){return e.enter("content"),t=e.enter("chunkContent",{contentType:"content"}),r(n)};function r(n){return null===n?o(n):(0,Ae.Ch)(n)?e.check(Fe,i,o)(n):(e.consume(n),r)}function o(t){return e.exit("chunkContent"),e.exit("content"),n(t)}function i(n){return e.consume(n),e.exit("chunkContent"),t.next=e.enter("chunkContent",{contentType:"content",previous:t}),t=t.next,r}}},Fe={partial:!0,tokenize:function(e,n,t){const r=this;return function(n){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),(0,ze.f)(e,o,"linePrefix")};function o(o){if(null===o||(0,Ae.Ch)(o))return t(o);const i=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?n(o):e.interrupt(r.parser.constructs.flow,t,n)(o)}}};const Re={tokenize:function(e){const n=this,t=e.attempt(Me.w,(function(r){if(null===r)return void e.consume(r);return e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),n.currentConstruct=void 0,t}),e.attempt(this.parser.constructs.flowInitial,r,(0,ze.f)(e,e.attempt(this.parser.constructs.flow,r,e.attempt(Ne,r)),"linePrefix")));return t;function r(r){if(null!==r)return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),n.currentConstruct=void 0,t;e.consume(r)}}};const _e={resolveAll:Ve()},je=He("string"),Be=He("text");function He(e){return{resolveAll:Ve("text"===e?Ue:void 0),tokenize:function(n){const t=this,r=this.parser.constructs[e],o=n.attempt(r,i,l);return i;function i(e){return a(e)?o(e):l(e)}function l(e){if(null!==e)return n.enter("data"),n.consume(e),s;n.consume(e)}function s(e){return a(e)?(n.exit("data"),o(e)):(n.consume(e),s)}function a(e){if(null===e)return!0;const n=r[e];let o=-1;if(n)for(;++o<n.length;){const e=n[o];if(!e.previous||e.previous.call(t,t.previous))return!0}return!1}}}}function Ve(e){return function(n,t){let r,o=-1;for(;++o<=n.length;)void 0===r?n[o]&&"data"===n[o][1].type&&(r=o,o++):n[o]&&"data"===n[o][1].type||(o!==r+2&&(n[r][1].end=n[o-1][1].end,n.splice(r+2,o-r-2),o=r+2),r=void 0);return e?e(n,t):n}}function Ue(e,n){let t=0;for(;++t<=e.length;)if((t===e.length||"lineEnding"===e[t][1].type)&&"data"===e[t-1][1].type){const r=e[t-1][1],o=n.sliceStream(r);let i,l=o.length,s=-1,a=0;for(;l--;){const e=o[l];if("string"==typeof e){for(s=e.length;32===e.charCodeAt(s-1);)a++,s--;if(s)break;s=-1}else if(-2===e)i=!0,a++;else if(-1!==e){l++;break}}if(a){const o={type:t===e.length||i||a<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:l?s:r.start._bufferIndex+s,_index:r.start._index+l,line:r.end.line,column:r.end.column-a,offset:r.end.offset-a},end:{...r.end}};r.end={...o.start},r.start.offset===r.end.offset?Object.assign(r,o):(e.splice(t,0,["enter",o,n],["exit",o,n]),t+=2)}t++}return e}const qe={name:"thematicBreak",tokenize:function(e,n,t){let r,o=0;return function(n){return e.enter("thematicBreak"),function(e){return r=e,i(e)}(n)};function i(i){return i===r?(e.enter("thematicBreakSequence"),l(i)):o>=3&&(null===i||(0,Ae.Ch)(i))?(e.exit("thematicBreak"),n(i)):t(i)}function l(n){return n===r?(e.consume(n),o++,l):(e.exit("thematicBreakSequence"),(0,Ae.xz)(n)?(0,ze.f)(e,i,"whitespace")(n):i(n))}}};const $e={continuation:{tokenize:function(e,n,t){const r=this;return r.containerState._closeFlow=void 0,e.check(Me.w,(function(t){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,(0,ze.f)(e,n,"listItemIndent",r.containerState.size+1)(t)}),(function(t){if(r.containerState.furtherBlankLines||!(0,Ae.xz)(t))return r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,o(t);return r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(We,n,o)(t)}));function o(o){return r.containerState._closeFlow=!0,r.interrupt=void 0,(0,ze.f)(e,e.attempt($e,n,t),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(o)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,n,t){const r=this,o=r.events[r.events.length-1];let i=o&&"linePrefix"===o[1].type?o[2].sliceSerialize(o[1],!0).length:0,l=0;return function(n){const o=r.containerState.type||(42===n||43===n||45===n?"listUnordered":"listOrdered");if("listUnordered"===o?!r.containerState.marker||n===r.containerState.marker:(0,Ae.pY)(n)){if(r.containerState.type||(r.containerState.type=o,e.enter(o,{_container:!0})),"listUnordered"===o)return e.enter("listItemPrefix"),42===n||45===n?e.check(qe,t,a)(n):a(n);if(!r.interrupt||49===n)return e.enter("listItemPrefix"),e.enter("listItemValue"),s(n)}return t(n)};function s(n){return(0,Ae.pY)(n)&&++l<10?(e.consume(n),s):(!r.interrupt||l<2)&&(r.containerState.marker?n===r.containerState.marker:41===n||46===n)?(e.exit("listItemValue"),a(n)):t(n)}function a(n){return e.enter("listItemMarker"),e.consume(n),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||n,e.check(Me.w,r.interrupt?t:u,e.attempt(Ye,f,c))}function u(e){return r.containerState.initialBlankLine=!0,i++,f(e)}function c(n){return(0,Ae.xz)(n)?(e.enter("listItemPrefixWhitespace"),e.consume(n),e.exit("listItemPrefixWhitespace"),f):t(n)}function f(t){return r.containerState.size=i+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,n(t)}}},Ye={partial:!0,tokenize:function(e,n,t){const r=this;return(0,ze.f)(e,(function(e){const o=r.events[r.events.length-1];return!(0,Ae.xz)(e)&&o&&"listItemPrefixWhitespace"===o[1].type?n(e):t(e)}),"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},We={partial:!0,tokenize:function(e,n,t){const r=this;return(0,ze.f)(e,(function(e){const o=r.events[r.events.length-1];return o&&"listItemIndent"===o[1].type&&o[2].sliceSerialize(o[1],!0).length===r.containerState.size?n(e):t(e)}),"listItemIndent",r.containerState.size+1)}};const Ke={continuation:{tokenize:function(e,n,t){const r=this;return function(n){if((0,Ae.xz)(n))return(0,ze.f)(e,o,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(n);return o(n)};function o(r){return e.attempt(Ke,n,t)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,n,t){const r=this;return function(n){if(62===n){const t=r.containerState;return t.open||(e.enter("blockQuote",{_container:!0}),t.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(n),e.exit("blockQuoteMarker"),o}return t(n)};function o(t){return(0,Ae.xz)(t)?(e.enter("blockQuotePrefixWhitespace"),e.consume(t),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),n):(e.exit("blockQuotePrefix"),n(t))}}};function Qe(e,n,t,r,o,i,l,s,a){const u=a||Number.POSITIVE_INFINITY;let c=0;return function(n){if(60===n)return e.enter(r),e.enter(o),e.enter(i),e.consume(n),e.exit(i),f;if(null===n||32===n||41===n||(0,Ae.Av)(n))return t(n);return e.enter(r),e.enter(l),e.enter(s),e.enter("chunkString",{contentType:"string"}),h(n)};function f(t){return 62===t?(e.enter(i),e.consume(t),e.exit(i),e.exit(o),e.exit(r),n):(e.enter(s),e.enter("chunkString",{contentType:"string"}),p(t))}function p(n){return 62===n?(e.exit("chunkString"),e.exit(s),f(n)):null===n||60===n||(0,Ae.Ch)(n)?t(n):(e.consume(n),92===n?d:p)}function d(n){return 60===n||62===n||92===n?(e.consume(n),p):p(n)}function h(o){return c||null!==o&&41!==o&&!(0,Ae.z3)(o)?c<u&&40===o?(e.consume(o),c++,h):41===o?(e.consume(o),c--,h):null===o||32===o||40===o||(0,Ae.Av)(o)?t(o):(e.consume(o),92===o?m:h):(e.exit("chunkString"),e.exit(s),e.exit(l),e.exit(r),n(o))}function m(n){return 40===n||41===n||92===n?(e.consume(n),h):h(n)}}function Xe(e,n,t,r,o,i){const l=this;let s,a=0;return function(n){return e.enter(r),e.enter(o),e.consume(n),e.exit(o),e.enter(i),u};function u(f){return a>999||null===f||91===f||93===f&&!s||94===f&&!a&&"_hiddenFootnoteSupport"in l.parser.constructs?t(f):93===f?(e.exit(i),e.enter(o),e.consume(f),e.exit(o),e.exit(r),n):(0,Ae.Ch)(f)?(e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),u):(e.enter("chunkString",{contentType:"string"}),c(f))}function c(n){return null===n||91===n||93===n||(0,Ae.Ch)(n)||a++>999?(e.exit("chunkString"),u(n)):(e.consume(n),s||(s=!(0,Ae.xz)(n)),92===n?f:c)}function f(n){return 91===n||92===n||93===n?(e.consume(n),a++,c):c(n)}}function Je(e,n,t,r,o,i){let l;return function(n){if(34===n||39===n||40===n)return e.enter(r),e.enter(o),e.consume(n),e.exit(o),l=40===n?41:n,s;return t(n)};function s(t){return t===l?(e.enter(o),e.consume(t),e.exit(o),e.exit(r),n):(e.enter(i),a(t))}function a(n){return n===l?(e.exit(i),s(l)):null===n?t(n):(0,Ae.Ch)(n)?(e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),(0,ze.f)(e,a,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),u(n))}function u(n){return n===l||null===n||(0,Ae.Ch)(n)?(e.exit("chunkString"),a(n)):(e.consume(n),92===n?c:u)}function c(n){return n===l||92===n?(e.consume(n),u):u(n)}}function Ze(e,n){let t;return function r(o){if((0,Ae.Ch)(o))return e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),t=!0,r;if((0,Ae.xz)(o))return(0,ze.f)(e,r,t?"linePrefix":"lineSuffix")(o);return n(o)}}var Ge=t(11098);const en={name:"definition",tokenize:function(e,n,t){const r=this;let o;return function(n){return e.enter("definition"),function(n){return Xe.call(r,e,i,t,"definitionLabel","definitionLabelMarker","definitionLabelString")(n)}(n)};function i(n){return o=(0,Ge.d)(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),58===n?(e.enter("definitionMarker"),e.consume(n),e.exit("definitionMarker"),l):t(n)}function l(n){return(0,Ae.z3)(n)?Ze(e,s)(n):s(n)}function s(n){return Qe(e,a,t,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(n)}function a(n){return e.attempt(nn,u,u)(n)}function u(n){return(0,Ae.xz)(n)?(0,ze.f)(e,c,"whitespace")(n):c(n)}function c(i){return null===i||(0,Ae.Ch)(i)?(e.exit("definition"),r.parser.defined.push(o),n(i)):t(i)}}},nn={partial:!0,tokenize:function(e,n,t){return function(n){return(0,Ae.z3)(n)?Ze(e,r)(n):t(n)};function r(n){return Je(e,o,t,"definitionTitle","definitionTitleMarker","definitionTitleString")(n)}function o(n){return(0,Ae.xz)(n)?(0,ze.f)(e,i,"whitespace")(n):i(n)}function i(e){return null===e||(0,Ae.Ch)(e)?n(e):t(e)}}};const tn={name:"codeIndented",tokenize:function(e,n,t){const r=this;return function(n){return e.enter("codeIndented"),(0,ze.f)(e,o,"linePrefix",5)(n)};function o(e){const n=r.events[r.events.length-1];return n&&"linePrefix"===n[1].type&&n[2].sliceSerialize(n[1],!0).length>=4?i(e):t(e)}function i(n){return null===n?s(n):(0,Ae.Ch)(n)?e.attempt(rn,i,s)(n):(e.enter("codeFlowValue"),l(n))}function l(n){return null===n||(0,Ae.Ch)(n)?(e.exit("codeFlowValue"),i(n)):(e.consume(n),l)}function s(t){return e.exit("codeIndented"),n(t)}}},rn={partial:!0,tokenize:function(e,n,t){const r=this;return o;function o(n){return r.parser.lazy[r.now().line]?t(n):(0,Ae.Ch)(n)?(e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),o):(0,ze.f)(e,i,"linePrefix",5)(n)}function i(e){const i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?n(e):(0,Ae.Ch)(e)?o(e):t(e)}}};const on={name:"headingAtx",resolve:function(e,n){let t,r,o=e.length-2,i=3;"whitespace"===e[i][1].type&&(i+=2);o-2>i&&"whitespace"===e[o][1].type&&(o-=2);"atxHeadingSequence"===e[o][1].type&&(i===o-1||o-4>i&&"whitespace"===e[o-2][1].type)&&(o-=i+1===o?2:4);o>i&&(t={type:"atxHeadingText",start:e[i][1].start,end:e[o][1].end},r={type:"chunkText",start:e[i][1].start,end:e[o][1].end,contentType:"text"},(0,Se.d)(e,i,o-i+1,[["enter",t,n],["enter",r,n],["exit",r,n],["exit",t,n]]));return e},tokenize:function(e,n,t){let r=0;return function(n){return e.enter("atxHeading"),function(n){return e.enter("atxHeadingSequence"),o(n)}(n)};function o(n){return 35===n&&r++<6?(e.consume(n),o):null===n||(0,Ae.z3)(n)?(e.exit("atxHeadingSequence"),i(n)):t(n)}function i(t){return 35===t?(e.enter("atxHeadingSequence"),l(t)):null===t||(0,Ae.Ch)(t)?(e.exit("atxHeading"),n(t)):(0,Ae.xz)(t)?(0,ze.f)(e,i,"whitespace")(t):(e.enter("atxHeadingText"),s(t))}function l(n){return 35===n?(e.consume(n),l):(e.exit("atxHeadingSequence"),i(n))}function s(n){return null===n||35===n||(0,Ae.z3)(n)?(e.exit("atxHeadingText"),i(n)):(e.consume(n),s)}}};const ln={name:"setextUnderline",resolveTo:function(e,n){let t,r,o,i=e.length;for(;i--;)if("enter"===e[i][0]){if("content"===e[i][1].type){t=i;break}"paragraph"===e[i][1].type&&(r=i)}else"content"===e[i][1].type&&e.splice(i,1),o||"definition"!==e[i][1].type||(o=i);const l={type:"setextHeading",start:{...e[r][1].start},end:{...e[e.length-1][1].end}};e[r][1].type="setextHeadingText",o?(e.splice(r,0,["enter",l,n]),e.splice(o+1,0,["exit",e[t][1],n]),e[t][1].end={...e[o][1].end}):e[t][1]=l;return e.push(["exit",l,n]),e},tokenize:function(e,n,t){const r=this;let o;return function(n){let l,s=r.events.length;for(;s--;)if("lineEnding"!==r.events[s][1].type&&"linePrefix"!==r.events[s][1].type&&"content"!==r.events[s][1].type){l="paragraph"===r.events[s][1].type;break}if(!r.parser.lazy[r.now().line]&&(r.interrupt||l))return e.enter("setextHeadingLine"),o=n,function(n){return e.enter("setextHeadingLineSequence"),i(n)}(n);return t(n)};function i(n){return n===o?(e.consume(n),i):(e.exit("setextHeadingLineSequence"),(0,Ae.xz)(n)?(0,ze.f)(e,l,"lineSuffix")(n):l(n))}function l(r){return null===r||(0,Ae.Ch)(r)?(e.exit("setextHeadingLine"),n(r)):t(r)}}};const sn=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],an=["pre","script","style","textarea"],un={concrete:!0,name:"htmlFlow",resolveTo:function(e){let n=e.length;for(;n--&&("enter"!==e[n][0]||"htmlFlow"!==e[n][1].type););n>1&&"linePrefix"===e[n-2][1].type&&(e[n][1].start=e[n-2][1].start,e[n+1][1].start=e[n-2][1].start,e.splice(n-2,2));return e},tokenize:function(e,n,t){const r=this;let o,i,l,s,a;return function(n){return function(n){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(n),u}(n)};function u(s){return 33===s?(e.consume(s),c):47===s?(e.consume(s),i=!0,d):63===s?(e.consume(s),o=3,r.interrupt?n:M):(0,Ae.jv)(s)?(e.consume(s),l=String.fromCharCode(s),h):t(s)}function c(i){return 45===i?(e.consume(i),o=2,f):91===i?(e.consume(i),o=5,s=0,p):(0,Ae.jv)(i)?(e.consume(i),o=4,r.interrupt?n:M):t(i)}function f(o){return 45===o?(e.consume(o),r.interrupt?n:M):t(o)}function p(o){const i="CDATA[";return o===i.charCodeAt(s++)?(e.consume(o),s===i.length?r.interrupt?n:E:p):t(o)}function d(n){return(0,Ae.jv)(n)?(e.consume(n),l=String.fromCharCode(n),h):t(n)}function h(s){if(null===s||47===s||62===s||(0,Ae.z3)(s)){const a=47===s,u=l.toLowerCase();return a||i||!an.includes(u)?sn.includes(l.toLowerCase())?(o=6,a?(e.consume(s),m):r.interrupt?n(s):E(s)):(o=7,r.interrupt&&!r.parser.lazy[r.now().line]?t(s):i?g(s):y(s)):(o=1,r.interrupt?n(s):E(s))}return 45===s||(0,Ae.H$)(s)?(e.consume(s),l+=String.fromCharCode(s),h):t(s)}function m(o){return 62===o?(e.consume(o),r.interrupt?n:E):t(o)}function g(n){return(0,Ae.xz)(n)?(e.consume(n),g):C(n)}function y(n){return 47===n?(e.consume(n),C):58===n||95===n||(0,Ae.jv)(n)?(e.consume(n),x):(0,Ae.xz)(n)?(e.consume(n),y):C(n)}function x(n){return 45===n||46===n||58===n||95===n||(0,Ae.H$)(n)?(e.consume(n),x):v(n)}function v(n){return 61===n?(e.consume(n),k):(0,Ae.xz)(n)?(e.consume(n),v):y(n)}function k(n){return null===n||60===n||61===n||62===n||96===n?t(n):34===n||39===n?(e.consume(n),a=n,b):(0,Ae.xz)(n)?(e.consume(n),k):w(n)}function b(n){return n===a?(e.consume(n),a=null,S):null===n||(0,Ae.Ch)(n)?t(n):(e.consume(n),b)}function w(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||(0,Ae.z3)(n)?v(n):(e.consume(n),w)}function S(e){return 47===e||62===e||(0,Ae.xz)(e)?y(e):t(e)}function C(n){return 62===n?(e.consume(n),I):t(n)}function I(n){return null===n||(0,Ae.Ch)(n)?E(n):(0,Ae.xz)(n)?(e.consume(n),I):t(n)}function E(n){return 45===n&&2===o?(e.consume(n),A):60===n&&1===o?(e.consume(n),D):62===n&&4===o?(e.consume(n),N):63===n&&3===o?(e.consume(n),M):93===n&&5===o?(e.consume(n),O):!(0,Ae.Ch)(n)||6!==o&&7!==o?null===n||(0,Ae.Ch)(n)?(e.exit("htmlFlowData"),P(n)):(e.consume(n),E):(e.exit("htmlFlowData"),e.check(cn,F,P)(n))}function P(n){return e.check(fn,T,F)(n)}function T(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),z}function z(n){return null===n||(0,Ae.Ch)(n)?P(n):(e.enter("htmlFlowData"),E(n))}function A(n){return 45===n?(e.consume(n),M):E(n)}function D(n){return 47===n?(e.consume(n),l="",L):E(n)}function L(n){if(62===n){const t=l.toLowerCase();return an.includes(t)?(e.consume(n),N):E(n)}return(0,Ae.jv)(n)&&l.length<8?(e.consume(n),l+=String.fromCharCode(n),L):E(n)}function O(n){return 93===n?(e.consume(n),M):E(n)}function M(n){return 62===n?(e.consume(n),N):45===n&&2===o?(e.consume(n),M):E(n)}function N(n){return null===n||(0,Ae.Ch)(n)?(e.exit("htmlFlowData"),F(n)):(e.consume(n),N)}function F(t){return e.exit("htmlFlow"),n(t)}}},cn={partial:!0,tokenize:function(e,n,t){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(Me.w,n,t)}}},fn={partial:!0,tokenize:function(e,n,t){const r=this;return function(n){if((0,Ae.Ch)(n))return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),o;return t(n)};function o(e){return r.parser.lazy[r.now().line]?t(e):n(e)}}};const pn={partial:!0,tokenize:function(e,n,t){const r=this;return function(n){if(null===n)return t(n);return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),o};function o(e){return r.parser.lazy[r.now().line]?t(e):n(e)}}},dn={concrete:!0,name:"codeFenced",tokenize:function(e,n,t){const r=this,o={partial:!0,tokenize:function(e,n,t){let o=0;return l;function l(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),a}function a(n){return e.enter("codeFencedFence"),(0,Ae.xz)(n)?(0,ze.f)(e,u,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(n):u(n)}function u(n){return n===i?(e.enter("codeFencedFenceSequence"),c(n)):t(n)}function c(n){return n===i?(o++,e.consume(n),c):o>=s?(e.exit("codeFencedFenceSequence"),(0,Ae.xz)(n)?(0,ze.f)(e,f,"whitespace")(n):f(n)):t(n)}function f(r){return null===r||(0,Ae.Ch)(r)?(e.exit("codeFencedFence"),n(r)):t(r)}}};let i,l=0,s=0;return function(n){return function(n){const t=r.events[r.events.length-1];return l=t&&"linePrefix"===t[1].type?t[2].sliceSerialize(t[1],!0).length:0,i=n,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),a(n)}(n)};function a(n){return n===i?(s++,e.consume(n),a):s<3?t(n):(e.exit("codeFencedFenceSequence"),(0,Ae.xz)(n)?(0,ze.f)(e,u,"whitespace")(n):u(n))}function u(t){return null===t||(0,Ae.Ch)(t)?(e.exit("codeFencedFence"),r.interrupt?n(t):e.check(pn,d,x)(t)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),c(t))}function c(n){return null===n||(0,Ae.Ch)(n)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),u(n)):(0,Ae.xz)(n)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),(0,ze.f)(e,f,"whitespace")(n)):96===n&&n===i?t(n):(e.consume(n),c)}function f(n){return null===n||(0,Ae.Ch)(n)?u(n):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),p(n))}function p(n){return null===n||(0,Ae.Ch)(n)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),u(n)):96===n&&n===i?t(n):(e.consume(n),p)}function d(n){return e.attempt(o,x,h)(n)}function h(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),m}function m(n){return l>0&&(0,Ae.xz)(n)?(0,ze.f)(e,g,"linePrefix",l+1)(n):g(n)}function g(n){return null===n||(0,Ae.Ch)(n)?e.check(pn,d,x)(n):(e.enter("codeFlowValue"),y(n))}function y(n){return null===n||(0,Ae.Ch)(n)?(e.exit("codeFlowValue"),g(n)):(e.consume(n),y)}function x(t){return e.exit("codeFenced"),n(t)}}};const hn=document.createElement("i");function mn(e){const n="&"+e+";";hn.innerHTML=n;const t=hn.textContent;return(59!==t.charCodeAt(t.length-1)||"semi"===e)&&(t!==n&&t)}const gn={name:"characterReference",tokenize:function(e,n,t){const r=this;let o,i,l=0;return function(n){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(n),e.exit("characterReferenceMarker"),s};function s(n){return 35===n?(e.enter("characterReferenceMarkerNumeric"),e.consume(n),e.exit("characterReferenceMarkerNumeric"),a):(e.enter("characterReferenceValue"),o=31,i=Ae.H$,u(n))}function a(n){return 88===n||120===n?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(n),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),o=6,i=Ae.AF,u):(e.enter("characterReferenceValue"),o=7,i=Ae.pY,u(n))}function u(s){if(59===s&&l){const o=e.exit("characterReferenceValue");return i!==Ae.H$||mn(r.sliceSerialize(o))?(e.enter("characterReferenceMarker"),e.consume(s),e.exit("characterReferenceMarker"),e.exit("characterReference"),n):t(s)}return i(s)&&l++<o?(e.consume(s),u):t(s)}}};const yn={name:"characterEscape",tokenize:function(e,n,t){return function(n){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(n),e.exit("escapeMarker"),r};function r(r){return(0,Ae.sR)(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),n):t(r)}}};const xn={name:"lineEnding",tokenize:function(e,n){return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,ze.f)(e,n,"linePrefix")}}};var vn=t(63233);const kn={name:"labelEnd",resolveAll:function(e){let n=-1;const t=[];for(;++n<e.length;){const r=e[n][1];if(t.push(e[n]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){const e="labelImage"===r.type?4:2;r.type="data",n+=e}}e.length!==t.length&&(0,Se.d)(e,0,e.length,t);return e},resolveTo:function(e,n){let t,r,o,i,l=e.length,s=0;for(;l--;)if(t=e[l][1],r){if("link"===t.type||"labelLink"===t.type&&t._inactive)break;"enter"===e[l][0]&&"labelLink"===t.type&&(t._inactive=!0)}else if(o){if("enter"===e[l][0]&&("labelImage"===t.type||"labelLink"===t.type)&&!t._balanced&&(r=l,"labelLink"!==t.type)){s=2;break}}else"labelEnd"===t.type&&(o=l);const a={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},u={type:"label",start:{...e[r][1].start},end:{...e[o][1].end}},c={type:"labelText",start:{...e[r+s+2][1].end},end:{...e[o-2][1].start}};return i=[["enter",a,n],["enter",u,n]],i=(0,Se.V)(i,e.slice(r+1,r+s+3)),i=(0,Se.V)(i,[["enter",c,n]]),i=(0,Se.V)(i,(0,vn.C)(n.parser.constructs.insideSpan.null,e.slice(r+s+4,o-3),n)),i=(0,Se.V)(i,[["exit",c,n],e[o-2],e[o-1],["exit",u,n]]),i=(0,Se.V)(i,e.slice(o+1)),i=(0,Se.V)(i,[["exit",a,n]]),(0,Se.d)(e,r,e.length,i),e},tokenize:function(e,n,t){const r=this;let o,i,l=r.events.length;for(;l--;)if(("labelImage"===r.events[l][1].type||"labelLink"===r.events[l][1].type)&&!r.events[l][1]._balanced){o=r.events[l][1];break}return function(n){if(!o)return t(n);if(o._inactive)return c(n);return i=r.parser.defined.includes((0,Ge.d)(r.sliceSerialize({start:o.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(n),e.exit("labelMarker"),e.exit("labelEnd"),s};function s(n){return 40===n?e.attempt(bn,u,i?u:c)(n):91===n?e.attempt(wn,u,i?a:c)(n):i?u(n):c(n)}function a(n){return e.attempt(Sn,u,c)(n)}function u(e){return n(e)}function c(e){return o._balanced=!0,t(e)}}},bn={tokenize:function(e,n,t){return function(n){return e.enter("resource"),e.enter("resourceMarker"),e.consume(n),e.exit("resourceMarker"),r};function r(n){return(0,Ae.z3)(n)?Ze(e,o)(n):o(n)}function o(n){return 41===n?u(n):Qe(e,i,l,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(n)}function i(n){return(0,Ae.z3)(n)?Ze(e,s)(n):u(n)}function l(e){return t(e)}function s(n){return 34===n||39===n||40===n?Je(e,a,t,"resourceTitle","resourceTitleMarker","resourceTitleString")(n):u(n)}function a(n){return(0,Ae.z3)(n)?Ze(e,u)(n):u(n)}function u(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),n):t(r)}}},wn={tokenize:function(e,n,t){const r=this;return function(n){return Xe.call(r,e,o,i,"reference","referenceMarker","referenceString")(n)};function o(e){return r.parser.defined.includes((0,Ge.d)(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?n(e):t(e)}function i(e){return t(e)}}},Sn={tokenize:function(e,n,t){return function(n){return e.enter("reference"),e.enter("referenceMarker"),e.consume(n),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),n):t(r)}}};const Cn={name:"labelStartImage",resolveAll:kn.resolveAll,tokenize:function(e,n,t){const r=this;return function(n){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(n),e.exit("labelImageMarker"),o};function o(n){return 91===n?(e.enter("labelMarker"),e.consume(n),e.exit("labelMarker"),e.exit("labelImage"),i):t(n)}function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?t(e):n(e)}}};var In=t(62987);const En={name:"attention",resolveAll:function(e,n){let t,r,o,i,l,s,a,u,c=-1;for(;++c<e.length;)if("enter"===e[c][0]&&"attentionSequence"===e[c][1].type&&e[c][1]._close)for(t=c;t--;)if("exit"===e[t][0]&&"attentionSequence"===e[t][1].type&&e[t][1]._open&&n.sliceSerialize(e[t][1]).charCodeAt(0)===n.sliceSerialize(e[c][1]).charCodeAt(0)){if((e[t][1]._close||e[c][1]._open)&&(e[c][1].end.offset-e[c][1].start.offset)%3&&!((e[t][1].end.offset-e[t][1].start.offset+e[c][1].end.offset-e[c][1].start.offset)%3))continue;s=e[t][1].end.offset-e[t][1].start.offset>1&&e[c][1].end.offset-e[c][1].start.offset>1?2:1;const f={...e[t][1].end},p={...e[c][1].start};Pn(f,-s),Pn(p,s),i={type:s>1?"strongSequence":"emphasisSequence",start:f,end:{...e[t][1].end}},l={type:s>1?"strongSequence":"emphasisSequence",start:{...e[c][1].start},end:p},o={type:s>1?"strongText":"emphasisText",start:{...e[t][1].end},end:{...e[c][1].start}},r={type:s>1?"strong":"emphasis",start:{...i.start},end:{...l.end}},e[t][1].end={...i.start},e[c][1].start={...l.end},a=[],e[t][1].end.offset-e[t][1].start.offset&&(a=(0,Se.V)(a,[["enter",e[t][1],n],["exit",e[t][1],n]])),a=(0,Se.V)(a,[["enter",r,n],["enter",i,n],["exit",i,n],["enter",o,n]]),a=(0,Se.V)(a,(0,vn.C)(n.parser.constructs.insideSpan.null,e.slice(t+1,c),n)),a=(0,Se.V)(a,[["exit",o,n],["enter",l,n],["exit",l,n],["exit",r,n]]),e[c][1].end.offset-e[c][1].start.offset?(u=2,a=(0,Se.V)(a,[["enter",e[c][1],n],["exit",e[c][1],n]])):u=0,(0,Se.d)(e,t-1,c-t+3,a),c=t+a.length-u-2;break}c=-1;for(;++c<e.length;)"attentionSequence"===e[c][1].type&&(e[c][1].type="data");return e},tokenize:function(e,n){const t=this.parser.constructs.attentionMarkers.null,r=this.previous,o=(0,In.r)(r);let i;return function(n){return i=n,e.enter("attentionSequence"),l(n)};function l(s){if(s===i)return e.consume(s),l;const a=e.exit("attentionSequence"),u=(0,In.r)(s),c=!u||2===u&&o||t.includes(s),f=!o||2===o&&u||t.includes(r);return a._open=Boolean(42===i?c:c&&(o||!f)),a._close=Boolean(42===i?f:f&&(u||!c)),n(s)}}};function Pn(e,n){e.column+=n,e.offset+=n,e._bufferIndex+=n}const Tn={name:"autolink",tokenize:function(e,n,t){let r=0;return function(n){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(n),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),o};function o(n){return(0,Ae.jv)(n)?(e.consume(n),i):64===n?t(n):a(n)}function i(e){return 43===e||45===e||46===e||(0,Ae.H$)(e)?(r=1,l(e)):a(e)}function l(n){return 58===n?(e.consume(n),r=0,s):(43===n||45===n||46===n||(0,Ae.H$)(n))&&r++<32?(e.consume(n),l):(r=0,a(n))}function s(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),n):null===r||32===r||60===r||(0,Ae.Av)(r)?t(r):(e.consume(r),s)}function a(n){return 64===n?(e.consume(n),u):(0,Ae.n9)(n)?(e.consume(n),a):t(n)}function u(e){return(0,Ae.H$)(e)?c(e):t(e)}function c(t){return 46===t?(e.consume(t),r=0,u):62===t?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.exit("autolink"),n):f(t)}function f(n){if((45===n||(0,Ae.H$)(n))&&r++<63){const t=45===n?f:c;return e.consume(n),t}return t(n)}}};const zn={name:"htmlText",tokenize:function(e,n,t){const r=this;let o,i,l;return function(n){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(n),s};function s(n){return 33===n?(e.consume(n),a):47===n?(e.consume(n),k):63===n?(e.consume(n),x):(0,Ae.jv)(n)?(e.consume(n),S):t(n)}function a(n){return 45===n?(e.consume(n),u):91===n?(e.consume(n),i=0,d):(0,Ae.jv)(n)?(e.consume(n),y):t(n)}function u(n){return 45===n?(e.consume(n),p):t(n)}function c(n){return null===n?t(n):45===n?(e.consume(n),f):(0,Ae.Ch)(n)?(l=c,L(n)):(e.consume(n),c)}function f(n){return 45===n?(e.consume(n),p):c(n)}function p(e){return 62===e?D(e):45===e?f(e):c(e)}function d(n){const r="CDATA[";return n===r.charCodeAt(i++)?(e.consume(n),i===r.length?h:d):t(n)}function h(n){return null===n?t(n):93===n?(e.consume(n),m):(0,Ae.Ch)(n)?(l=h,L(n)):(e.consume(n),h)}function m(n){return 93===n?(e.consume(n),g):h(n)}function g(n){return 62===n?D(n):93===n?(e.consume(n),g):h(n)}function y(n){return null===n||62===n?D(n):(0,Ae.Ch)(n)?(l=y,L(n)):(e.consume(n),y)}function x(n){return null===n?t(n):63===n?(e.consume(n),v):(0,Ae.Ch)(n)?(l=x,L(n)):(e.consume(n),x)}function v(e){return 62===e?D(e):x(e)}function k(n){return(0,Ae.jv)(n)?(e.consume(n),b):t(n)}function b(n){return 45===n||(0,Ae.H$)(n)?(e.consume(n),b):w(n)}function w(n){return(0,Ae.Ch)(n)?(l=w,L(n)):(0,Ae.xz)(n)?(e.consume(n),w):D(n)}function S(n){return 45===n||(0,Ae.H$)(n)?(e.consume(n),S):47===n||62===n||(0,Ae.z3)(n)?C(n):t(n)}function C(n){return 47===n?(e.consume(n),D):58===n||95===n||(0,Ae.jv)(n)?(e.consume(n),I):(0,Ae.Ch)(n)?(l=C,L(n)):(0,Ae.xz)(n)?(e.consume(n),C):D(n)}function I(n){return 45===n||46===n||58===n||95===n||(0,Ae.H$)(n)?(e.consume(n),I):E(n)}function E(n){return 61===n?(e.consume(n),P):(0,Ae.Ch)(n)?(l=E,L(n)):(0,Ae.xz)(n)?(e.consume(n),E):C(n)}function P(n){return null===n||60===n||61===n||62===n||96===n?t(n):34===n||39===n?(e.consume(n),o=n,T):(0,Ae.Ch)(n)?(l=P,L(n)):(0,Ae.xz)(n)?(e.consume(n),P):(e.consume(n),z)}function T(n){return n===o?(e.consume(n),o=void 0,A):null===n?t(n):(0,Ae.Ch)(n)?(l=T,L(n)):(e.consume(n),T)}function z(n){return null===n||34===n||39===n||60===n||61===n||96===n?t(n):47===n||62===n||(0,Ae.z3)(n)?C(n):(e.consume(n),z)}function A(e){return 47===e||62===e||(0,Ae.z3)(e)?C(e):t(e)}function D(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),n):t(r)}function L(n){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),O}function O(n){return(0,Ae.xz)(n)?(0,ze.f)(e,M,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(n):M(n)}function M(n){return e.enter("htmlTextData"),l(n)}}};const An={name:"labelStartLink",resolveAll:kn.resolveAll,tokenize:function(e,n,t){const r=this;return function(n){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(n),e.exit("labelMarker"),e.exit("labelLink"),o};function o(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?t(e):n(e)}}};const Dn={name:"hardBreakEscape",tokenize:function(e,n,t){return function(n){return e.enter("hardBreakEscape"),e.consume(n),r};function r(r){return(0,Ae.Ch)(r)?(e.exit("hardBreakEscape"),n(r)):t(r)}}};const Ln={name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let n,t,r=e.length-4,o=3;if(!("lineEnding"!==e[o][1].type&&"space"!==e[o][1].type||"lineEnding"!==e[r][1].type&&"space"!==e[r][1].type))for(n=o;++n<r;)if("codeTextData"===e[n][1].type){e[o][1].type="codeTextPadding",e[r][1].type="codeTextPadding",o+=2,r-=2;break}n=o-1,r++;for(;++n<=r;)void 0===t?n!==r&&"lineEnding"!==e[n][1].type&&(t=n):n!==r&&"lineEnding"!==e[n][1].type||(e[t][1].type="codeTextData",n!==t+2&&(e[t][1].end=e[n-1][1].end,e.splice(t+2,n-t-2),r-=n-t-2,n=t+2),t=void 0);return e},tokenize:function(e,n,t){let r,o,i=0;return function(n){return e.enter("codeText"),e.enter("codeTextSequence"),l(n)};function l(n){return 96===n?(e.consume(n),i++,l):(e.exit("codeTextSequence"),s(n))}function s(n){return null===n?t(n):32===n?(e.enter("space"),e.consume(n),e.exit("space"),s):96===n?(o=e.enter("codeTextSequence"),r=0,u(n)):(0,Ae.Ch)(n)?(e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),s):(e.enter("codeTextData"),a(n))}function a(n){return null===n||32===n||96===n||(0,Ae.Ch)(n)?(e.exit("codeTextData"),s(n)):(e.consume(n),a)}function u(t){return 96===t?(e.consume(t),r++,u):r===i?(e.exit("codeTextSequence"),e.exit("codeText"),n(t)):(o.type="codeTextData",a(t))}}};const On={42:$e,43:$e,45:$e,48:$e,49:$e,50:$e,51:$e,52:$e,53:$e,54:$e,55:$e,56:$e,57:$e,62:Ke},Mn={91:en},Nn={[-2]:tn,[-1]:tn,32:tn},Fn={35:on,42:qe,45:[ln,qe],60:un,61:ln,95:qe,96:dn,126:dn},Rn={38:gn,92:yn},_n={[-5]:xn,[-4]:xn,[-3]:xn,33:Cn,38:gn,42:En,60:[Tn,zn],91:An,92:[Dn,yn],93:kn,95:En,96:Ln},jn={null:[En,_e]},Bn={null:[42,95]},Hn={null:[]};function Vn(e,n,t){let r={_bufferIndex:-1,_index:0,line:t&&t.line||1,column:t&&t.column||1,offset:t&&t.offset||0};const o={},i=[];let l=[],s=[],a=!0;const u={attempt:x((function(e,n){v(e,n.from)})),check:x(y),consume:function(e){(0,Ae.Ch)(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,k()):-1!==e&&(r.column++,r.offset++);r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===l[r._index].length&&(r._bufferIndex=-1,r._index++));c.previous=e,a=!0},enter:function(e,n){const t=n||{};return t.type=e,t.start=h(),c.events.push(["enter",t,c]),s.push(t),t},exit:function(e){const n=s.pop();return n.end=h(),c.events.push(["exit",n,c]),n},interrupt:x(y,{interrupt:!0})},c={code:null,containerState:{},defineSkip:function(e){o[e.line]=e.column,k()},events:[],now:h,parser:e,previous:null,sliceSerialize:function(e,n){return function(e,n){let t=-1;const r=[];let o;for(;++t<e.length;){const i=e[t];let l;if("string"==typeof i)l=i;else switch(i){case-5:l="\r";break;case-4:l="\n";break;case-3:l="\r\n";break;case-2:l=n?" ":"\t";break;case-1:if(!n&&o)continue;l=" ";break;default:l=String.fromCharCode(i)}o=-2===i,r.push(l)}return r.join("")}(d(e),n)},sliceStream:d,write:function(e){if(l=(0,Se.V)(l,e),m(),null!==l[l.length-1])return[];return v(n,0),c.events=(0,vn.C)(i,c.events,c),c.events}};let f,p=n.tokenize.call(c,u);return n.resolveAll&&i.push(n),c;function d(e){return function(e,n){const t=n.start._index,r=n.start._bufferIndex,o=n.end._index,i=n.end._bufferIndex;let l;if(t===o)l=[e[t].slice(r,i)];else{if(l=e.slice(t,o),r>-1){const e=l[0];"string"==typeof e?l[0]=e.slice(r):l.shift()}i>0&&l.push(e[o].slice(0,i))}return l}(l,e)}function h(){const{_bufferIndex:e,_index:n,line:t,column:o,offset:i}=r;return{_bufferIndex:e,_index:n,line:t,column:o,offset:i}}function m(){let e;for(;r._index<l.length;){const n=l[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;)g(n.charCodeAt(r._bufferIndex));else g(n)}}function g(e){a=void 0,f=e,p=p(e)}function y(e,n){n.restore()}function x(e,n){return function(t,o,i){let l,f,p,d;return Array.isArray(t)?m(t):"tokenize"in t?m([t]):function(e){return n;function n(n){const t=null!==n&&e[n],r=null!==n&&e.null;return m([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(r)?r:r?[r]:[]])(n)}}(t);function m(e){return l=e,f=0,0===e.length?i:g(e[f])}function g(e){return function(t){d=function(){const e=h(),n=c.previous,t=c.currentConstruct,o=c.events.length,i=Array.from(s);return{from:o,restore:l};function l(){r=e,c.previous=n,c.currentConstruct=t,c.events.length=o,s=i,k()}}(),p=e,e.partial||(c.currentConstruct=e);if(e.name&&c.parser.constructs.disable.null.includes(e.name))return x(t);return e.tokenize.call(n?Object.assign(Object.create(c),n):c,u,y,x)(t)}}function y(n){return a=!0,e(p,d),o}function x(e){return a=!0,d.restore(),++f<l.length?g(l[f]):i}}}function v(e,n){e.resolveAll&&!i.includes(e)&&i.push(e),e.resolve&&(0,Se.d)(c.events,n,c.events.length-n,e.resolve(c.events.slice(n),c)),e.resolveTo&&(c.events=e.resolveTo(c.events,c))}function k(){r.line in o&&r.column<2&&(r.column=o[r.line],r.offset+=o[r.line]-1)}}const Un=/[\0\t\n\r]/g;function qn(e,n){const t=Number.parseInt(e,n);return t<9||11===t||t>13&&t<32||t>126&&t<160||t>55295&&t<57344||t>64975&&t<65008||65535==(65535&t)||65534==(65535&t)||t>1114111?"�":String.fromCodePoint(t)}const $n=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function Yn(e,n,t){if(n)return n;if(35===t.charCodeAt(0)){const e=t.charCodeAt(1),n=120===e||88===e;return qn(t.slice(n?2:1),n?16:10)}return mn(t)||e}const Wn={}.hasOwnProperty;function Kn(e,n,t){return"string"!=typeof n&&(t=n,n=void 0),function(e){const n={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:i(te),autolinkProtocol:I,autolinkEmail:I,atxHeading:i(Z),blockQuote:i(Y),characterEscape:I,characterReference:I,codeFenced:i(W),codeFencedFenceInfo:l,codeFencedFenceMeta:l,codeIndented:i(W,l),codeText:i(K,l),codeTextData:I,data:I,codeFlowValue:I,definition:i(Q),definitionDestinationString:l,definitionLabelString:l,definitionTitleString:l,emphasis:i(X),hardBreakEscape:i(G),hardBreakTrailing:i(G),htmlFlow:i(ee,l),htmlFlowData:I,htmlText:i(ee,l),htmlTextData:I,image:i(ne),label:l,link:i(te),listItem:i(oe),listItemValue:p,listOrdered:i(re,f),listUnordered:i(re),paragraph:i(ie),reference:j,referenceString:l,resourceDestinationString:l,resourceTitleString:l,setextHeading:i(Z),strong:i(le),thematicBreak:i(ae)},exit:{atxHeading:a(),atxHeadingSequence:b,autolink:a(),autolinkEmail:$,autolinkProtocol:q,blockQuote:a(),characterEscapeValue:E,characterReferenceMarkerHexadecimal:H,characterReferenceMarkerNumeric:H,characterReferenceValue:V,characterReference:U,codeFenced:a(g),codeFencedFence:m,codeFencedFenceInfo:d,codeFencedFenceMeta:h,codeFlowValue:E,codeIndented:a(y),codeText:a(D),codeTextData:E,data:E,definition:a(),definitionDestinationString:k,definitionLabelString:x,definitionTitleString:v,emphasis:a(),hardBreakEscape:a(T),hardBreakTrailing:a(T),htmlFlow:a(z),htmlFlowData:E,htmlText:a(A),htmlTextData:E,image:a(O),label:N,labelText:M,lineEnding:P,link:a(L),listItem:a(),listOrdered:a(),listUnordered:a(),paragraph:a(),referenceString:B,resourceDestinationString:F,resourceTitleString:R,resource:_,setextHeading:a(C),setextHeadingLineSequence:S,setextHeadingText:w,strong:a(),thematicBreak:a()}};Xn(n,(e||{}).mdastExtensions||[]);const t={};return r;function r(e){let r={type:"root",children:[]};const i={stack:[r],tokenStack:[],config:n,enter:s,exit:u,buffer:l,resume:c,data:t},a=[];let f=-1;for(;++f<e.length;)if("listOrdered"===e[f][1].type||"listUnordered"===e[f][1].type)if("enter"===e[f][0])a.push(f);else{f=o(e,a.pop(),f)}for(f=-1;++f<e.length;){const t=n[e[f][0]];Wn.call(t,e[f][1].type)&&t[e[f][1].type].call(Object.assign({sliceSerialize:e[f][2].sliceSerialize},i),e[f][1])}if(i.tokenStack.length>0){const e=i.tokenStack[i.tokenStack.length-1];(e[1]||Zn).call(i,void 0,e[0])}for(r.position={start:Qn(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:Qn(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},f=-1;++f<n.transforms.length;)r=n.transforms[f](r)||r;return r}function o(e,n,t){let r,o,i,l,s=n-1,a=-1,u=!1;for(;++s<=t;){const n=e[s];switch(n[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===n[0]?a++:a--,l=void 0;break;case"lineEndingBlank":"enter"===n[0]&&(!r||l||a||i||(i=s),l=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:l=void 0}if(!a&&"enter"===n[0]&&"listItemPrefix"===n[1].type||-1===a&&"exit"===n[0]&&("listUnordered"===n[1].type||"listOrdered"===n[1].type)){if(r){let l=s;for(o=void 0;l--;){const n=e[l];if("lineEnding"===n[1].type||"lineEndingBlank"===n[1].type){if("exit"===n[0])continue;o&&(e[o][1].type="lineEndingBlank",u=!0),n[1].type="lineEnding",o=l}else if("linePrefix"!==n[1].type&&"blockQuotePrefix"!==n[1].type&&"blockQuotePrefixWhitespace"!==n[1].type&&"blockQuoteMarker"!==n[1].type&&"listItemIndent"!==n[1].type)break}i&&(!o||i<o)&&(r._spread=!0),r.end=Object.assign({},o?e[o][1].start:n[1].end),e.splice(o||s,0,["exit",r,n[2]]),s++,t++}if("listItemPrefix"===n[1].type){const o={type:"listItem",_spread:!1,start:Object.assign({},n[1].start),end:void 0};r=o,e.splice(s,0,["enter",o,n[2]]),s++,t++,i=void 0,l=!0}}}return e[n][1]._spread=u,t}function i(e,n){return t;function t(t){s.call(this,e(t),t),n&&n.call(this,t)}}function l(){this.stack.push({type:"fragment",children:[]})}function s(e,n,t){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([n,t||void 0]),e.position={start:Qn(n.start),end:void 0}}function a(e){return n;function n(n){e&&e.call(this,n),u.call(this,n)}}function u(e,n){const t=this.stack.pop(),r=this.tokenStack.pop();if(!r)throw new Error("Cannot close `"+e.type+"` ("+J({start:e.start,end:e.end})+"): it’s not open");if(r[0].type!==e.type)if(n)n.call(this,e,r[0]);else{(r[1]||Zn).call(this,e,r[0])}t.position.end=Qn(e.end)}function c(){return(0,we.B)(this.stack.pop())}function f(){this.data.expectingFirstListItemValue=!0}function p(e){if(this.data.expectingFirstListItemValue){this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0}}function d(){const e=this.resume();this.stack[this.stack.length-1].lang=e}function h(){const e=this.resume();this.stack[this.stack.length-1].meta=e}function m(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function g(){const e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function y(){const e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}function x(e){const n=this.resume(),t=this.stack[this.stack.length-1];t.label=n,t.identifier=(0,Ge.d)(this.sliceSerialize(e)).toLowerCase()}function v(){const e=this.resume();this.stack[this.stack.length-1].title=e}function k(){const e=this.resume();this.stack[this.stack.length-1].url=e}function b(e){const n=this.stack[this.stack.length-1];if(!n.depth){const t=this.sliceSerialize(e).length;n.depth=t}}function w(){this.data.setextHeadingSlurpLineEnding=!0}function S(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2}function C(){this.data.setextHeadingSlurpLineEnding=void 0}function I(e){const n=this.stack[this.stack.length-1].children;let t=n[n.length-1];t&&"text"===t.type||(t=se(),t.position={start:Qn(e.start),end:void 0},n.push(t)),this.stack.push(t)}function E(e){const n=this.stack.pop();n.value+=this.sliceSerialize(e),n.position.end=Qn(e.end)}function P(e){const t=this.stack[this.stack.length-1];if(this.data.atHardBreak){return t.children[t.children.length-1].position.end=Qn(e.end),void(this.data.atHardBreak=void 0)}!this.data.setextHeadingSlurpLineEnding&&n.canContainEols.includes(t.type)&&(I.call(this,e),E.call(this,e))}function T(){this.data.atHardBreak=!0}function z(){const e=this.resume();this.stack[this.stack.length-1].value=e}function A(){const e=this.resume();this.stack[this.stack.length-1].value=e}function D(){const e=this.resume();this.stack[this.stack.length-1].value=e}function L(){const e=this.stack[this.stack.length-1];if(this.data.inReference){const n=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=n,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}function O(){const e=this.stack[this.stack.length-1];if(this.data.inReference){const n=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=n,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}function M(e){const n=this.sliceSerialize(e),t=this.stack[this.stack.length-2];t.label=function(e){return e.replace($n,Yn)}(n),t.identifier=(0,Ge.d)(n).toLowerCase()}function N(){const e=this.stack[this.stack.length-1],n=this.resume(),t=this.stack[this.stack.length-1];if(this.data.inReference=!0,"link"===t.type){const n=e.children;t.children=n}else t.alt=n}function F(){const e=this.resume();this.stack[this.stack.length-1].url=e}function R(){const e=this.resume();this.stack[this.stack.length-1].title=e}function _(){this.data.inReference=void 0}function j(){this.data.referenceType="collapsed"}function B(e){const n=this.resume(),t=this.stack[this.stack.length-1];t.label=n,t.identifier=(0,Ge.d)(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"}function H(e){this.data.characterReferenceType=e.type}function V(e){const n=this.sliceSerialize(e),t=this.data.characterReferenceType;let r;if(t)r=qn(n,"characterReferenceMarkerNumeric"===t?10:16),this.data.characterReferenceType=void 0;else{r=mn(n)}this.stack[this.stack.length-1].value+=r}function U(e){this.stack.pop().position.end=Qn(e.end)}function q(e){E.call(this,e);this.stack[this.stack.length-1].url=this.sliceSerialize(e)}function $(e){E.call(this,e);this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)}function Y(){return{type:"blockquote",children:[]}}function W(){return{type:"code",lang:null,meta:null,value:""}}function K(){return{type:"inlineCode",value:""}}function Q(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function X(){return{type:"emphasis",children:[]}}function Z(){return{type:"heading",depth:0,children:[]}}function G(){return{type:"break"}}function ee(){return{type:"html",value:""}}function ne(){return{type:"image",title:null,url:"",alt:null}}function te(){return{type:"link",title:null,url:"",children:[]}}function re(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}function oe(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}function ie(){return{type:"paragraph",children:[]}}function le(){return{type:"strong",children:[]}}function se(){return{type:"text",value:""}}function ae(){return{type:"thematicBreak"}}}(t)(function(e){for(;!Ee(e););return e}(function(e){const n=e||{},t={constructs:(0,Te.W)([o,...n.extensions||[]]),content:r(De),defined:[],document:r(Le),flow:r(Re),lazy:{},string:r(je),text:r(Be)};return t;function r(e){return function(n){return Vn(t,e,n)}}}(t).document().write(function(){let e,n=1,t="",r=!0;return function(o,i,l){const s=[];let a,u,c,f,p;for(o=t+("string"==typeof o?o.toString():new TextDecoder(i||void 0).decode(o)),c=0,t="",r&&(65279===o.charCodeAt(0)&&c++,r=void 0);c<o.length;){if(Un.lastIndex=c,a=Un.exec(o),f=a&&void 0!==a.index?a.index:o.length,p=o.charCodeAt(f),!a){t=o.slice(c);break}if(10===p&&c===f&&e)s.push(-3),e=void 0;else switch(e&&(s.push(-5),e=void 0),c<f&&(s.push(o.slice(c,f)),n+=f-c),p){case 0:s.push(65533),n++;break;case 9:for(u=4*Math.ceil(n/4),s.push(-2);n++<u;)s.push(-1);break;case 10:s.push(-4),n=1;break;default:e=!0,n=1}c=f+1}return l&&(e&&s.push(-5),t&&s.push(t),s.push(null)),s}}()(e,n,!0))))}function Qn(e){return{line:e.line,column:e.column,offset:e.offset}}function Xn(e,n){let t=-1;for(;++t<n.length;){const r=n[t];Array.isArray(r)?Xn(e,r):Jn(e,r)}}function Jn(e,n){let t;for(t in n)if(Wn.call(n,t))switch(t){case"canContainEols":{const r=n[t];r&&e[t].push(...r);break}case"transforms":{const r=n[t];r&&e[t].push(...r);break}case"enter":case"exit":{const r=n[t];r&&Object.assign(e[t],r);break}}}function Zn(e,n){throw e?new Error("Cannot close `"+e.type+"` ("+J({start:e.start,end:e.end})+"): a different token (`"+n.type+"`, "+J({start:n.start,end:n.end})+") is open"):new Error("Cannot close document, a token (`"+n.type+"`, "+J({start:n.start,end:n.end})+") is still open")}function Gn(e){const n=this;n.parser=function(t){return Kn(t,{...n.data("settings"),...e,extensions:n.data("micromarkExtensions")||[],mdastExtensions:n.data("fromMarkdownExtensions")||[]})}}const et="object"==typeof self?self:globalThis,nt=e=>((e,n)=>{const t=(n,t)=>(e.set(t,n),n),r=o=>{if(e.has(o))return e.get(o);const[i,l]=n[o];switch(i){case 0:case-1:return t(l,o);case 1:{const e=t([],o);for(const n of l)e.push(r(n));return e}case 2:{const e=t({},o);for(const[n,t]of l)e[r(n)]=r(t);return e}case 3:return t(new Date(l),o);case 4:{const{source:e,flags:n}=l;return t(new RegExp(e,n),o)}case 5:{const e=t(new Map,o);for(const[n,t]of l)e.set(r(n),r(t));return e}case 6:{const e=t(new Set,o);for(const n of l)e.add(r(n));return e}case 7:{const{name:e,message:n}=l;return t(new et[e](n),o)}case 8:return t(BigInt(l),o);case"BigInt":return t(Object(BigInt(l)),o);case"ArrayBuffer":return t(new Uint8Array(l).buffer,l);case"DataView":{const{buffer:e}=new Uint8Array(l);return t(new DataView(e),l)}}return t(new et[i](l),o)};return r})(new Map,e)(0),tt="",{toString:rt}={},{keys:ot}=Object,it=e=>{const n=typeof e;if("object"!==n||!e)return[0,n];const t=rt.call(e).slice(8,-1);switch(t){case"Array":return[1,tt];case"Object":return[2,tt];case"Date":return[3,tt];case"RegExp":return[4,tt];case"Map":return[5,tt];case"Set":return[6,tt];case"DataView":return[1,t]}return t.includes("Array")?[1,t]:t.includes("Error")?[7,t]:[2,t]},lt=([e,n])=>0===e&&("function"===n||"symbol"===n),st=(e,{json:n,lossy:t}={})=>{const r=[];return((e,n,t,r)=>{const o=(e,n)=>{const o=r.push(e)-1;return t.set(n,o),o},i=r=>{if(t.has(r))return t.get(r);let[l,s]=it(r);switch(l){case 0:{let n=r;switch(s){case"bigint":l=8,n=r.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+s);n=null;break;case"undefined":return o([-1],r)}return o([l,n],r)}case 1:{if(s){let e=r;return"DataView"===s?e=new Uint8Array(r.buffer):"ArrayBuffer"===s&&(e=new Uint8Array(r)),o([s,[...e]],r)}const e=[],n=o([l,e],r);for(const n of r)e.push(i(n));return n}case 2:{if(s)switch(s){case"BigInt":return o([s,r.toString()],r);case"Boolean":case"Number":case"String":return o([s,r.valueOf()],r)}if(n&&"toJSON"in r)return i(r.toJSON());const t=[],a=o([l,t],r);for(const n of ot(r))!e&&lt(it(r[n]))||t.push([i(n),i(r[n])]);return a}case 3:return o([l,r.toISOString()],r);case 4:{const{source:e,flags:n}=r;return o([l,{source:e,flags:n}],r)}case 5:{const n=[],t=o([l,n],r);for(const[t,o]of r)(e||!lt(it(t))&&!lt(it(o)))&&n.push([i(t),i(o)]);return t}case 6:{const n=[],t=o([l,n],r);for(const t of r)!e&&lt(it(t))||n.push(i(t));return t}}const{message:a}=r;return o([l,{name:s,message:a}],r)};return i})(!(n||t),!!n,new Map,r)(e),r};var at="function"==typeof structuredClone?(e,n)=>n&&("json"in n||"lossy"in n)?nt(st(e,n)):structuredClone(e):(e,n)=>nt(st(e,n));function ut(e){const n=[];let t=-1,r=0,o=0;for(;++t<e.length;){const i=e.charCodeAt(t);let l="";if(37===i&&(0,Ae.H$)(e.charCodeAt(t+1))&&(0,Ae.H$)(e.charCodeAt(t+2)))o=2;else if(i<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(i))||(l=String.fromCharCode(i));else if(i>55295&&i<57344){const n=e.charCodeAt(t+1);i<56320&&n>56319&&n<57344?(l=String.fromCharCode(i,n),o=1):l="�"}else l=String.fromCharCode(i);l&&(n.push(e.slice(r,t),encodeURIComponent(l)),r=t+o+1,l=""),o&&(t+=o,o=0)}return n.join("")+e.slice(r)}function ct(e,n){const t=[{type:"text",value:"↩"}];return n>1&&t.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(n)}]}),t}function ft(e,n){return"Back to reference "+(e+1)+(n>1?"-"+n:"")}var pt=t(21623);function dt(e,n){const t=n.referenceType;let r="]";if("collapsed"===t?r+="[]":"full"===t&&(r+="["+(n.label||n.identifier)+"]"),"imageReference"===n.type)return[{type:"text",value:"!["+n.alt+r}];const o=e.all(n),i=o[0];i&&"text"===i.type?i.value="["+i.value:o.unshift({type:"text",value:"["});const l=o[o.length-1];return l&&"text"===l.type?l.value+=r:o.push({type:"text",value:r}),o}function ht(e){const n=e.spread;return null==n?e.children.length>1:n}function mt(e){const n=String(e),t=/\r?\n|\r/g;let r=t.exec(n),o=0;const i=[];for(;r;)i.push(gt(n.slice(o,r.index),o>0,!0),r[0]),o=r.index+r[0].length,r=t.exec(n);return i.push(gt(n.slice(o),o>0,!1)),i.join("")}function gt(e,n,t){let r=0,o=e.length;if(n){let n=e.codePointAt(r);for(;9===n||32===n;)r++,n=e.codePointAt(r)}if(t){let n=e.codePointAt(o-1);for(;9===n||32===n;)o--,n=e.codePointAt(o-1)}return o>r?e.slice(r,o):""}const yt={blockquote:function(e,n){const t={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(n),!0)};return e.patch(n,t),e.applyData(n,t)},break:function(e,n){const t={type:"element",tagName:"br",properties:{},children:[]};return e.patch(n,t),[e.applyData(n,t),{type:"text",value:"\n"}]},code:function(e,n){const t=n.value?n.value+"\n":"",r={};n.lang&&(r.className=["language-"+n.lang]);let o={type:"element",tagName:"code",properties:r,children:[{type:"text",value:t}]};return n.meta&&(o.data={meta:n.meta}),e.patch(n,o),o=e.applyData(n,o),o={type:"element",tagName:"pre",properties:{},children:[o]},e.patch(n,o),o},delete:function(e,n){const t={type:"element",tagName:"del",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)},emphasis:function(e,n){const t={type:"element",tagName:"em",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)},footnoteReference:function(e,n){const t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",r=String(n.identifier).toUpperCase(),o=ut(r.toLowerCase()),i=e.footnoteOrder.indexOf(r);let l,s=e.footnoteCounts.get(r);void 0===s?(s=0,e.footnoteOrder.push(r),l=e.footnoteOrder.length):l=i+1,s+=1,e.footnoteCounts.set(r,s);const a={type:"element",tagName:"a",properties:{href:"#"+t+"fn-"+o,id:t+"fnref-"+o+(s>1?"-"+s:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(l)}]};e.patch(n,a);const u={type:"element",tagName:"sup",properties:{},children:[a]};return e.patch(n,u),e.applyData(n,u)},heading:function(e,n){const t={type:"element",tagName:"h"+n.depth,properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)},html:function(e,n){if(e.options.allowDangerousHtml){const t={type:"raw",value:n.value};return e.patch(n,t),e.applyData(n,t)}},imageReference:function(e,n){const t=String(n.identifier).toUpperCase(),r=e.definitionById.get(t);if(!r)return dt(e,n);const o={src:ut(r.url||""),alt:n.alt};null!==r.title&&void 0!==r.title&&(o.title=r.title);const i={type:"element",tagName:"img",properties:o,children:[]};return e.patch(n,i),e.applyData(n,i)},image:function(e,n){const t={src:ut(n.url)};null!==n.alt&&void 0!==n.alt&&(t.alt=n.alt),null!==n.title&&void 0!==n.title&&(t.title=n.title);const r={type:"element",tagName:"img",properties:t,children:[]};return e.patch(n,r),e.applyData(n,r)},inlineCode:function(e,n){const t={type:"text",value:n.value.replace(/\r?\n|\r/g," ")};e.patch(n,t);const r={type:"element",tagName:"code",properties:{},children:[t]};return e.patch(n,r),e.applyData(n,r)},linkReference:function(e,n){const t=String(n.identifier).toUpperCase(),r=e.definitionById.get(t);if(!r)return dt(e,n);const o={href:ut(r.url||"")};null!==r.title&&void 0!==r.title&&(o.title=r.title);const i={type:"element",tagName:"a",properties:o,children:e.all(n)};return e.patch(n,i),e.applyData(n,i)},link:function(e,n){const t={href:ut(n.url)};null!==n.title&&void 0!==n.title&&(t.title=n.title);const r={type:"element",tagName:"a",properties:t,children:e.all(n)};return e.patch(n,r),e.applyData(n,r)},listItem:function(e,n,t){const r=e.all(n),o=t?function(e){let n=!1;if("list"===e.type){n=e.spread||!1;const t=e.children;let r=-1;for(;!n&&++r<t.length;)n=ht(t[r])}return n}(t):ht(n),i={},l=[];if("boolean"==typeof n.checked){const e=r[0];let t;e&&"element"===e.type&&"p"===e.tagName?t=e:(t={type:"element",tagName:"p",properties:{},children:[]},r.unshift(t)),t.children.length>0&&t.children.unshift({type:"text",value:" "}),t.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:n.checked,disabled:!0},children:[]}),i.className=["task-list-item"]}let s=-1;for(;++s<r.length;){const e=r[s];(o||0!==s||"element"!==e.type||"p"!==e.tagName)&&l.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||o?l.push(e):l.push(...e.children)}const a=r[r.length-1];a&&(o||"element"!==a.type||"p"!==a.tagName)&&l.push({type:"text",value:"\n"});const u={type:"element",tagName:"li",properties:i,children:l};return e.patch(n,u),e.applyData(n,u)},list:function(e,n){const t={},r=e.all(n);let o=-1;for("number"==typeof n.start&&1!==n.start&&(t.start=n.start);++o<r.length;){const e=r[o];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){t.className=["contains-task-list"];break}}const i={type:"element",tagName:n.ordered?"ol":"ul",properties:t,children:e.wrap(r,!0)};return e.patch(n,i),e.applyData(n,i)},paragraph:function(e,n){const t={type:"element",tagName:"p",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)},root:function(e,n){const t={type:"root",children:e.wrap(e.all(n))};return e.patch(n,t),e.applyData(n,t)},strong:function(e,n){const t={type:"element",tagName:"strong",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)},table:function(e,n){const t=e.all(n),r=t.shift(),o=[];if(r){const t={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(n.children[0],t),o.push(t)}if(t.length>0){const r={type:"element",tagName:"tbody",properties:{},children:e.wrap(t,!0)},i=Q(n.children[1]),l=K(n.children[n.children.length-1]);i&&l&&(r.position={start:i,end:l}),o.push(r)}const i={type:"element",tagName:"table",properties:{},children:e.wrap(o,!0)};return e.patch(n,i),e.applyData(n,i)},tableCell:function(e,n){const t={type:"element",tagName:"td",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)},tableRow:function(e,n,t){const r=t?t.children:void 0,o=0===(r?r.indexOf(n):1)?"th":"td",i=t&&"table"===t.type?t.align:void 0,l=i?i.length:n.children.length;let s=-1;const a=[];for(;++s<l;){const t=n.children[s],r={},l=i?i[s]:void 0;l&&(r.align=l);let u={type:"element",tagName:o,properties:r,children:[]};t&&(u.children=e.all(t),e.patch(t,u),u=e.applyData(t,u)),a.push(u)}const u={type:"element",tagName:"tr",properties:{},children:e.wrap(a,!0)};return e.patch(n,u),e.applyData(n,u)},text:function(e,n){const t={type:"text",value:mt(String(n.value))};return e.patch(n,t),e.applyData(n,t)},thematicBreak:function(e,n){const t={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(n,t),e.applyData(n,t)},toml:xt,yaml:xt,definition:xt,footnoteDefinition:xt};function xt(){}const vt={}.hasOwnProperty,kt={};function bt(e,n){e.position&&(n.position=function(e){const n=Q(e),t=K(e);if(n&&t)return{start:n,end:t}}(e))}function wt(e,n){let t=n;if(e&&e.data){const n=e.data.hName,r=e.data.hChildren,o=e.data.hProperties;if("string"==typeof n)if("element"===t.type)t.tagName=n;else{t={type:"element",tagName:n,properties:{},children:"children"in t?t.children:[t]}}"element"===t.type&&o&&Object.assign(t.properties,at(o)),"children"in t&&t.children&&null!=r&&(t.children=r)}return t}function St(e,n){const t=n.data||{},r=!("value"in n)||vt.call(t,"hProperties")||vt.call(t,"hChildren")?{type:"element",tagName:"div",properties:{},children:e.all(n)}:{type:"text",value:n.value};return e.patch(n,r),e.applyData(n,r)}function Ct(e,n){const t=[];let r=-1;for(n&&t.push({type:"text",value:"\n"});++r<e.length;)r&&t.push({type:"text",value:"\n"}),t.push(e[r]);return n&&e.length>0&&t.push({type:"text",value:"\n"}),t}function It(e){let n=0,t=e.charCodeAt(n);for(;9===t||32===t;)n++,t=e.charCodeAt(n);return e.slice(n)}function Et(e,n){const t=function(e,n){const t=n||kt,r=new Map,o=new Map,i=new Map,l={...yt,...t.handlers},s={all:function(e){const n=[];if("children"in e){const t=e.children;let r=-1;for(;++r<t.length;){const o=s.one(t[r],e);if(o){if(r&&"break"===t[r-1].type&&(Array.isArray(o)||"text"!==o.type||(o.value=It(o.value)),!Array.isArray(o)&&"element"===o.type)){const e=o.children[0];e&&"text"===e.type&&(e.value=It(e.value))}Array.isArray(o)?n.push(...o):n.push(o)}}}return n},applyData:wt,definitionById:r,footnoteById:o,footnoteCounts:i,footnoteOrder:[],handlers:l,one:function(e,n){const t=e.type,r=s.handlers[t];if(vt.call(s.handlers,t)&&r)return r(s,e,n);if(s.options.passThrough&&s.options.passThrough.includes(t)){if("children"in e){const{children:n,...t}=e,r=at(t);return r.children=s.all(e),r}return at(e)}return(s.options.unknownHandler||St)(s,e,n)},options:t,patch:bt,wrap:Ct};return(0,pt.Vn)(e,(function(e){if("definition"===e.type||"footnoteDefinition"===e.type){const n="definition"===e.type?r:o,t=String(e.identifier).toUpperCase();n.has(t)||n.set(t,e)}})),s}(e,n),r=t.one(e,void 0),o=function(e){const n="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",t=e.options.footnoteBackContent||ct,r=e.options.footnoteBackLabel||ft,o=e.options.footnoteLabel||"Footnotes",i=e.options.footnoteLabelTagName||"h2",l=e.options.footnoteLabelProperties||{className:["sr-only"]},s=[];let a=-1;for(;++a<e.footnoteOrder.length;){const o=e.footnoteById.get(e.footnoteOrder[a]);if(!o)continue;const i=e.all(o),l=String(o.identifier).toUpperCase(),u=ut(l.toLowerCase());let c=0;const f=[],p=e.footnoteCounts.get(l);for(;void 0!==p&&++c<=p;){f.length>0&&f.push({type:"text",value:" "});let e="string"==typeof t?t:t(a,c);"string"==typeof e&&(e={type:"text",value:e}),f.push({type:"element",tagName:"a",properties:{href:"#"+n+"fnref-"+u+(c>1?"-"+c:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(a,c),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}const d=i[i.length-1];if(d&&"element"===d.type&&"p"===d.tagName){const e=d.children[d.children.length-1];e&&"text"===e.type?e.value+=" ":d.children.push({type:"text",value:" "}),d.children.push(...f)}else i.push(...f);const h={type:"element",tagName:"li",properties:{id:n+"fn-"+u},children:e.wrap(i,!0)};e.patch(o,h),s.push(h)}if(0!==s.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:i,properties:{...at(l),id:"footnote-label"},children:[{type:"text",value:o}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(s,!0)},{type:"text",value:"\n"}]}}(t),l=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return o&&((0,i.ok)("children"in l),l.children.push({type:"text",value:"\n"},o)),l}function Pt(e,n){return e&&"run"in e?async function(t,r){const o=Et(t,{file:r,...n});await e.run(o,r)}:function(t,r){return Et(t,{file:r,...e||n})}}function Tt(e){if(e)throw e}var zt=t(94470);function At(e){if("object"!=typeof e||null===e)return!1;const n=Object.getPrototypeOf(e);return!(null!==n&&n!==Object.prototype&&null!==Object.getPrototypeOf(n)||Symbol.toStringTag in e||Symbol.iterator in e)}function Dt(){const e=[],n={run:function(...n){let t=-1;const r=n.pop();if("function"!=typeof r)throw new TypeError("Expected function as last argument, not "+r);!function o(i,...l){const s=e[++t];let a=-1;if(i)r(i);else{for(;++a<n.length;)null!==l[a]&&void 0!==l[a]||(l[a]=n[a]);n=l,s?function(e,n){let t;return r;function r(...n){const r=e.length>n.length;let l;r&&n.push(o);try{l=e.apply(this,n)}catch(e){if(r&&t)throw e;return o(e)}r||(l&&l.then&&"function"==typeof l.then?l.then(i,o):l instanceof Error?o(l):i(l))}function o(e,...r){t||(t=!0,n(e,...r))}function i(e){o(null,e)}}(s,o)(...l):r(null,...l)}}(null,...n)},use:function(t){if("function"!=typeof t)throw new TypeError("Expected `middelware` to be a function, not "+t);return e.push(t),n}};return n}const Lt={basename:function(e,n){if(void 0!==n&&"string"!=typeof n)throw new TypeError('"ext" argument must be a string');Ot(e);let t,r=0,o=-1,i=e.length;if(void 0===n||0===n.length||n.length>e.length){for(;i--;)if(47===e.codePointAt(i)){if(t){r=i+1;break}}else o<0&&(t=!0,o=i+1);return o<0?"":e.slice(r,o)}if(n===e)return"";let l=-1,s=n.length-1;for(;i--;)if(47===e.codePointAt(i)){if(t){r=i+1;break}}else l<0&&(t=!0,l=i+1),s>-1&&(e.codePointAt(i)===n.codePointAt(s--)?s<0&&(o=i):(s=-1,o=l));r===o?o=l:o<0&&(o=e.length);return e.slice(r,o)},dirname:function(e){if(Ot(e),0===e.length)return".";let n,t=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(n){t=r;break}}else n||(n=!0);return t<0?47===e.codePointAt(0)?"/":".":1===t&&47===e.codePointAt(0)?"//":e.slice(0,t)},extname:function(e){Ot(e);let n,t=e.length,r=-1,o=0,i=-1,l=0;for(;t--;){const s=e.codePointAt(t);if(47!==s)r<0&&(n=!0,r=t+1),46===s?i<0?i=t:1!==l&&(l=1):i>-1&&(l=-1);else if(n){o=t+1;break}}if(i<0||r<0||0===l||1===l&&i===r-1&&i===o+1)return"";return e.slice(i,r)},join:function(...e){let n,t=-1;for(;++t<e.length;)Ot(e[t]),e[t]&&(n=void 0===n?e[t]:n+"/"+e[t]);return void 0===n?".":function(e){Ot(e);const n=47===e.codePointAt(0);let t=function(e,n){let t,r,o="",i=0,l=-1,s=0,a=-1;for(;++a<=e.length;){if(a<e.length)t=e.codePointAt(a);else{if(47===t)break;t=47}if(47===t){if(l===a-1||1===s);else if(l!==a-1&&2===s){if(o.length<2||2!==i||46!==o.codePointAt(o.length-1)||46!==o.codePointAt(o.length-2))if(o.length>2){if(r=o.lastIndexOf("/"),r!==o.length-1){r<0?(o="",i=0):(o=o.slice(0,r),i=o.length-1-o.lastIndexOf("/")),l=a,s=0;continue}}else if(o.length>0){o="",i=0,l=a,s=0;continue}n&&(o=o.length>0?o+"/..":"..",i=2)}else o.length>0?o+="/"+e.slice(l+1,a):o=e.slice(l+1,a),i=a-l-1;l=a,s=0}else 46===t&&s>-1?s++:s=-1}return o}(e,!n);0!==t.length||n||(t=".");t.length>0&&47===e.codePointAt(e.length-1)&&(t+="/");return n?"/"+t:t}(n)},sep:"/"};function Ot(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const Mt={cwd:function(){return"/"}};function Nt(e){return Boolean(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}function Ft(e){if("string"==typeof e)e=new URL(e);else if(!Nt(e)){const n=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw n.code="ERR_INVALID_ARG_TYPE",n}if("file:"!==e.protocol){const e=new TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){const e=new TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}const n=e.pathname;let t=-1;for(;++t<n.length;)if(37===n.codePointAt(t)&&50===n.codePointAt(t+1)){const e=n.codePointAt(t+2);if(70===e||102===e){const e=new TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(n)}(e)}const Rt=["history","path","basename","stem","extname","dirname"];class _t{constructor(e){let n;n=e?Nt(e)?{path:e}:"string"==typeof e||function(e){return Boolean(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in n?"":Mt.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let t,r=-1;for(;++r<Rt.length;){const e=Rt[r];e in n&&void 0!==n[e]&&null!==n[e]&&(this[e]="history"===e?[...n[e]]:n[e])}for(t in n)Rt.includes(t)||(this[t]=n[t])}get basename(){return"string"==typeof this.path?Lt.basename(this.path):void 0}set basename(e){Bt(e,"basename"),jt(e,"basename"),this.path=Lt.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?Lt.dirname(this.path):void 0}set dirname(e){Ht(this.basename,"dirname"),this.path=Lt.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?Lt.extname(this.path):void 0}set extname(e){if(jt(e,"extname"),Ht(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw new Error("`extname` must start with `.`");if(e.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=Lt.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){Nt(e)&&(e=Ft(e)),Bt(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?Lt.basename(this.path,this.extname):void 0}set stem(e){Bt(e,"stem"),jt(e,"stem"),this.path=Lt.join(this.dirname||"",e+(this.extname||""))}fail(e,n,t){const r=this.message(e,n,t);throw r.fatal=!0,r}info(e,n,t){const r=this.message(e,n,t);return r.fatal=void 0,r}message(e,n,t){const r=new ne(e,n,t);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){if(void 0===this.value)return"";if("string"==typeof this.value)return this.value;return new TextDecoder(e||void 0).decode(this.value)}}function jt(e,n){if(e&&e.includes(Lt.sep))throw new Error("`"+n+"` cannot be a path: did not expect `"+Lt.sep+"`")}function Bt(e,n){if(!e)throw new Error("`"+n+"` cannot be empty")}function Ht(e,n){if(!e)throw new Error("Setting `"+n+"` requires `path` to be set too")}const Vt=function(e){const n=this.constructor.prototype,t=n[e],r=function(){return t.apply(r,arguments)};return Object.setPrototypeOf(r,n),r},Ut={}.hasOwnProperty;class qt extends Vt{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=Dt()}copy(){const e=new qt;let n=-1;for(;++n<this.attachers.length;){const t=this.attachers[n];e.use(...t)}return e.data(zt(!0,{},this.namespace)),e}data(e,n){return"string"==typeof e?2===arguments.length?(Kt("data",this.frozen),this.namespace[e]=n,this):Ut.call(this.namespace,e)&&this.namespace[e]||void 0:e?(Kt("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;const e=this;for(;++this.freezeIndex<this.attachers.length;){const[n,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);const r=n.call(e,...t);"function"==typeof r&&this.transformers.use(r)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();const n=Jt(e),t=this.parser||this.Parser;return Yt("parse",t),t(String(n),n)}process(e,n){const t=this;return this.freeze(),Yt("process",this.parser||this.Parser),Wt("process",this.compiler||this.Compiler),n?r(void 0,n):new Promise(r);function r(r,o){const l=Jt(e),s=t.parse(l);function a(e,t){e||!t?o(e):r?r(t):((0,i.ok)(n,"`done` is defined if `resolve` is not"),n(void 0,t))}t.run(s,l,(function(e,n,r){if(e||!n||!r)return a(e);const o=n,i=t.stringify(o,r);var l;"string"==typeof(l=i)||function(e){return Boolean(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(l)?r.value=i:r.result=i,a(e,r)}))}}processSync(e){let n,t=!1;return this.freeze(),Yt("processSync",this.parser||this.Parser),Wt("processSync",this.compiler||this.Compiler),this.process(e,(function(e,r){t=!0,Tt(e),n=r})),Xt("processSync","process",t),(0,i.ok)(n,"we either bailed on an error or have a tree"),n}run(e,n,t){Qt(e),this.freeze();const r=this.transformers;return t||"function"!=typeof n||(t=n,n=void 0),t?o(void 0,t):new Promise(o);function o(o,l){(0,i.ok)("function"!=typeof n,"`file` can’t be a `done` anymore, we checked");const s=Jt(n);r.run(e,s,(function(n,r,s){const a=r||e;n?l(n):o?o(a):((0,i.ok)(t,"`done` is defined if `resolve` is not"),t(void 0,a,s))}))}}runSync(e,n){let t,r=!1;return this.run(e,n,(function(e,n){Tt(e),t=n,r=!0})),Xt("runSync","run",r),(0,i.ok)(t,"we either bailed on an error or have a tree"),t}stringify(e,n){this.freeze();const t=Jt(n),r=this.compiler||this.Compiler;return Wt("stringify",r),Qt(e),r(e,t)}use(e,...n){const t=this.attachers,r=this.namespace;if(Kt("use",this.frozen),null==e);else if("function"==typeof e)s(e,n);else{if("object"!=typeof e)throw new TypeError("Expected usable value, not `"+e+"`");Array.isArray(e)?l(e):i(e)}return this;function o(e){if("function"==typeof e)s(e,[]);else{if("object"!=typeof e)throw new TypeError("Expected usable value, not `"+e+"`");if(Array.isArray(e)){const[n,...t]=e;s(n,t)}else i(e)}}function i(e){if(!("plugins"in e)&&!("settings"in e))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");l(e.plugins),e.settings&&(r.settings=zt(!0,r.settings,e.settings))}function l(e){let n=-1;if(null==e);else{if(!Array.isArray(e))throw new TypeError("Expected a list of plugins, not `"+e+"`");for(;++n<e.length;){o(e[n])}}}function s(e,n){let r=-1,o=-1;for(;++r<t.length;)if(t[r][0]===e){o=r;break}if(-1===o)t.push([e,...n]);else if(n.length>0){let[r,...i]=n;const l=t[o][1];At(l)&&At(r)&&(r=zt(!0,l,r)),t[o]=[e,r,...i]}}}}const $t=(new qt).freeze();function Yt(e,n){if("function"!=typeof n)throw new TypeError("Cannot `"+e+"` without `parser`")}function Wt(e,n){if("function"!=typeof n)throw new TypeError("Cannot `"+e+"` without `compiler`")}function Kt(e,n){if(n)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Qt(e){if(!At(e)||"string"!=typeof e.type)throw new TypeError("Expected node, got `"+e+"`")}function Xt(e,n,t){if(!t)throw new Error("`"+e+"` finished async. Use `"+n+"` instead")}function Jt(e){return function(e){return Boolean(e&&"object"==typeof e&&"message"in e&&"messages"in e)}(e)?e:new _t(e)}const Zt=[],Gt={allowDangerousHtml:!0},er=/^(https?|ircs?|mailto|xmpp)$/i,nr=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function tr(e){const n=rr(e),t=or(e);return ir(n.runSync(n.parse(t),t),e)}function rr(e){const n=e.rehypePlugins||Zt,t=e.remarkPlugins||Zt,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...Gt}:Gt;return $t().use(Gn).use(t).use(Pt,r).use(n)}function or(e){const n=e.children||"",t=new _t;return"string"==typeof n?t.value=n:(0,i.t1)("Unexpected value `"+n+"` for `children` prop, expected `string`"),t}function ir(e,n){const t=n.allowedElements,r=n.allowElement,o=n.components,l=n.disallowedElements,s=n.skipHtml,a=n.unwrapDisallowed,u=n.urlTransform||lr;for(const e of nr)Object.hasOwn(n,e.from)&&(0,i.t1)("Unexpected `"+e.from+"` prop, "+(e.to?"use `"+e.to+"` instead":"remove it")+" (see <https://github.com/remarkjs/react-markdown/blob/main/changelog.md#"+e.id+"> for more info)");return t&&l&&(0,i.t1)("Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other"),n.className&&(e={type:"element",tagName:"div",properties:{className:n.className},children:"root"===e.type?e.children:[e]}),(0,pt.Vn)(e,(function(e,n,o){if("raw"===e.type&&o&&"number"==typeof n)return s?o.children.splice(n,1):o.children[n]={type:"text",value:e.value},n;if("element"===e.type){let n;for(n in ke)if(Object.hasOwn(ke,n)&&Object.hasOwn(e.properties,n)){const t=e.properties[n],r=ke[n];(null===r||r.includes(e.tagName))&&(e.properties[n]=u(String(t||""),n,e))}}if("element"===e.type){let i=t?!t.includes(e.tagName):!!l&&l.includes(e.tagName);if(!i&&r&&"number"==typeof n&&(i=!r(e,n,o)),i&&o&&"number"==typeof n)return a&&e.children?o.children.splice(n,1,...e.children):o.children.splice(n,1),n}})),ue(e,{Fragment:be.Fragment,components:o,ignoreInvalidStyle:!0,jsx:be.jsx,jsxs:be.jsxs,passKeys:!0,passNode:!0})}function lr(e){const n=e.indexOf(":"),t=e.indexOf("?"),r=e.indexOf("#"),o=e.indexOf("/");return-1===n||-1!==o&&n>o||-1!==t&&n>t||-1!==r&&n>r||er.test(e.slice(0,n))?e:""}},96093:function(e,n,t){"use strict";t.d(n,{O:function(){return r}});const r=function(e){if(null==e)return i;if("function"==typeof e)return o(e);if("object"==typeof e)return Array.isArray(e)?function(e){const n=[];let t=-1;for(;++t<e.length;)n[t]=r(e[t]);return o(i);function i(...e){let t=-1;for(;++t<n.length;)if(n[t].apply(this,e))return!0;return!1}}(e):function(e){const n=e;return o(t);function t(t){const r=t;let o;for(o in e)if(r[o]!==n[o])return!1;return!0}}(e);if("string"==typeof e)return function(e){return o(n);function n(n){return n&&n.type===e}}(e);throw new Error("Expected function, string, or object as test")};function o(e){return function(n,t,r){return Boolean(l(n)&&e.call(this,n,"number"==typeof t?t:void 0,r||void 0))}}function i(){return!0}function l(e){return null!==e&&"object"==typeof e&&"type"in e}},88718:function(e,n,t){"use strict";t.d(n,{BK:function(){return i},S4:function(){return l}});var r=t(96093);const o=[],i=!1;function l(e,n,t,l){let s;"function"==typeof n&&"function"!=typeof t?(l=t,t=n):s=n;const a=(0,r.O)(s),u=l?-1:1;!function e(r,s,c){const f=r&&"object"==typeof r?r:{};if("string"==typeof f.type){const e="string"==typeof f.tagName?f.tagName:"string"==typeof f.name?f.name:void 0;Object.defineProperty(p,"name",{value:"node ("+r.type+(e?"<"+e+">":"")+")"})}return p;function p(){let f,p,d,h=o;if((!n||a(r,s,c[c.length-1]||void 0))&&(h=function(e){if(Array.isArray(e))return e;if("number"==typeof e)return[true,e];return null==e?o:[e]}(t(r,c)),h[0]===i))return h;if("children"in r&&r.children){const n=r;if(n.children&&"skip"!==h[0])for(p=(l?n.children.length:-1)+u,d=c.concat(n);p>-1&&p<n.children.length;){const t=n.children[p];if(f=e(t,p,d)(),f[0]===i)return f;p="number"==typeof f[1]?f[1]:p+u}}return h}}(e,void 0,[])()}},21623:function(e,n,t){"use strict";t.d(n,{Vn:function(){return o}});var r=t(88718);function o(e,n,t,o){let i,l,s;"function"==typeof n&&"function"!=typeof t?(l=void 0,s=n,i=t):(l=n,s=t,i=o),(0,r.S4)(e,l,(function(e,n){const t=n[n.length-1],r=t?t.children.indexOf(e):void 0;return s(e,r,t)}),i)}}}]);