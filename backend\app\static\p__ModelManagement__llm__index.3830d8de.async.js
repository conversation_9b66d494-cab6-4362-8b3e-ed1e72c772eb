"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1614],{50675:function(e,r,t){var n=t(1413),a=t(67294),l=t(72961),s=t(91146),o=function(e,r){return a.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:l.Z}))},i=a.forwardRef(o);r.Z=i},8913:function(e,r,t){var n=t(1413),a=t(67294),l=t(1085),s=t(91146),o=function(e,r){return a.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:l.Z}))},i=a.forwardRef(o);r.Z=i},51042:function(e,r,t){var n=t(1413),a=t(67294),l=t(42110),s=t(91146),o=function(e,r){return a.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:l.Z}))},i=a.forwardRef(o);r.Z=i},6647:function(e,r,t){t.r(r),t.d(r,{default:function(){return V}});var n=t(9783),a=t.n(n),l=t(15009),s=t.n(l),o=t(97857),i=t.n(o),c=t(99289),u=t.n(c),d=t(5574),p=t.n(d),m=t(50675),h=t(8913),f=t(1413),x=t(67294),v={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M704 446H320c-4.4 0-8 3.6-8 8v402c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8V454c0-4.4-3.6-8-8-8zm-328 64h272v117H376V510zm272 290H376V683h272v117z"}},{tag:"path",attrs:{d:"M424 748a32 32 0 1064 0 32 32 0 10-64 0zm0-178a32 32 0 1064 0 32 32 0 10-64 0z"}},{tag:"path",attrs:{d:"M811.4 368.9C765.6 248 648.9 162 512.2 162S258.8 247.9 213 368.8C126.9 391.5 63.5 470.2 64 563.6 64.6 668 145.6 752.9 247.6 762c4.7.4 8.7-3.3 8.7-8v-60.4c0-4-3-7.4-7-7.9-27-3.4-52.5-15.2-72.1-34.5-24-23.5-37.2-55.1-37.2-88.6 0-28 9.1-54.4 26.2-76.4 16.7-21.4 40.2-36.9 66.1-43.7l37.9-10 13.9-36.7c8.6-22.8 20.6-44.2 35.7-63.5 14.9-19.2 32.6-36 52.4-50 41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.3c19.9 14 37.5 30.8 52.4 50 15.1 19.3 27.1 40.7 35.7 63.5l13.8 36.6 37.8 10c54.2 14.4 92.1 63.7 92.1 120 0 33.6-13.2 65.1-37.2 88.6-19.5 19.2-44.9 31.1-71.9 34.5-4 .5-6.9 3.9-6.9 7.9V754c0 4.7 4.1 8.4 8.8 8 101.7-9.2 182.5-94 183.2-198.2.6-93.4-62.7-172.1-148.6-194.9z"}}]},name:"cloud-server",theme:"outlined"},b=t(91146),g=function(e,r){return x.createElement(b.Z,(0,f.Z)((0,f.Z)({},e),{},{ref:r,icon:v}))};var Z=x.forwardRef(g),j=t(51042),y=t(97131),k=t(12453),I=t(17788),C=t(47019),O=t(2453),P=t(42075),w=t(66309),E=t(83622),_=t(74330),S=t(55102),$=t(34041),A=t(26412),T=t(78158);function M(e){return L.apply(this,arguments)}function L(){return(L=u()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,T.N)("/api/llms",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function N(e){return q.apply(this,arguments)}function q(){return(q=u()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,T.N)("/api/llms",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function z(e){return B.apply(this,arguments)}function B(){return(B=u()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,T.N)("/api/llms/test",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function R(e){return D.apply(this,arguments)}function D(){return(D=u()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,T.N)("/api/llms/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function F(e){return H.apply(this,arguments)}function H(){return(H=u()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,T.N)("/api/llms/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var U=t(77885),K=t(85893),W=I.Z.confirm,V=function(){var e=(0,x.useState)(!1),r=p()(e,2),t=r[0],n=r[1],l=(0,x.useState)(!1),o=p()(l,2),c=o[0],d=o[1],f=(0,x.useState)(!1),v=p()(f,2),b=v[0],g=v[1],T=(0,x.useState)(void 0),L=p()(T,2),q=L[0],B=L[1],D=(0,x.useRef)(),H=C.Z.useForm(),V=p()(H,1)[0],J=(0,x.useState)({}),X=p()(J,2),G=X[0],Q=X[1],Y=function(){var e=u()(s()().mark((function e(r){var t,a;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=O.ZP.loading("正在添加"),e.prev=1,e.next=4,N(i()({},r));case 4:return t(),O.ZP.success("添加模型成功"),n(!1),null===(a=D.current)||void 0===a||a.reload(),V.resetFields(),e.abrupt("return",!0);case 12:return e.prev=12,e.t0=e.catch(1),t(),O.ZP.error("添加失败，请重试"),e.abrupt("return",!1);case 17:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(r){return e.apply(this,arguments)}}(),ee=function(){var e=u()(s()().mark((function e(r){var t,n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.info(r),r.id){e.next=4;break}return O.ZP.error("更新失败，缺少模型 ID"),e.abrupt("return",!1);case 4:return t=O.ZP.loading("正在更新"),e.prev=5,e.next=8,R(r);case 8:return t(),O.ZP.success("更新成功"),d(!1),B(void 0),null===(n=D.current)||void 0===n||n.reload(),V.resetFields(),e.abrupt("return",!0);case 17:return e.prev=17,e.t0=e.catch(5),t(),O.ZP.error("更新失败，请重试"),e.abrupt("return",!1);case 22:case"end":return e.stop()}}),e,null,[[5,17]])})));return function(r){return e.apply(this,arguments)}}(),re=function(){var e=u()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:W({title:"确认删除",content:"你确定要删除这个模型吗？",onOk:function(){var e=u()(s()().mark((function e(){var t,n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=O.ZP.loading("正在删除"),e.prev=1,e.next=4,F(r.id);case 4:return t(),O.ZP.success("删除成功"),null===(n=D.current)||void 0===n||n.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),t(),O.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(){return e.apply(this,arguments)}}(),onCancel:function(){console.log("取消删除")}});case 1:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),te=function(){var e=u()(s()().mark((function e(r){var t,n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return Q((function(e){return i()(i()({},e),{},a()({},r.id,!0))})),t=O.ZP.loading("开始测试"),e.prev=2,e.next=5,z({id:r.id});case 5:"success"===(null==(n=e.sent)?void 0:n.status)?(t(),O.ZP.success("测试成功: ".concat(n.message))):(t(),O.ZP.error("测试失败: ".concat(n.message))),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(2),t(),O.ZP.error("测试失败，请重试");case 13:return e.prev=13,Q((function(e){return i()(i()({},e),{},a()({},r.id,!1))})),e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[2,9,13,16]])})));return function(r){return e.apply(this,arguments)}}(),ne=[{title:"ID",dataIndex:"id",valueType:"digit"},{title:"名称",dataIndex:"name",valueType:"text",render:function(e,r){return(0,K.jsxs)(P.Z,{children:[r.name,(0,K.jsx)(w.Z,{color:r.is_active?"success":"default",icon:r.is_active?(0,K.jsx)(m.Z,{}):(0,K.jsx)(h.Z,{}),style:{fontWeight:"bold",padding:"0 8px",borderRadius:"12px",display:"flex",alignItems:"center",gap:"4px"},children:r.is_active?"已上线":"已下线"}),(0,K.jsx)(w.Z,{color:"blue",icon:(0,K.jsx)(Z,{}),style:{borderRadius:"12px",display:"flex",alignItems:"center",gap:"4px"},children:"local"===r.provider?"本地化":r.provider})]})}},{title:"模型名称",dataIndex:"m_name",valueType:"text"},{title:"最大令牌数",dataIndex:"max_tokens",valueType:"digit",search:!1},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",search:!1},{title:"操作",dataIndex:"option",valueType:"option",width:60,render:function(e,r){return[(0,K.jsx)(E.ZP,{type:"link",style:{width:"50px"},onClick:function(){B(r),d(!0),V.resetFields(),V.setFieldsValue(r)},children:"编辑"},"edit-".concat(r.id)),(0,K.jsx)(E.ZP,{type:r.is_active?"default":"primary",danger:r.is_active,icon:r.is_active?(0,K.jsx)(h.Z,{}):(0,K.jsx)(m.Z,{}),size:"small",style:{width:"70px",borderRadius:"15px",fontWeight:"bold"},onClick:function(){var e;W({title:"确认".concat(r.is_active?"下线":"上线"),content:"确定要".concat(r.is_active?"下线":"上线","该模型吗？"),onOk:(e=u()(s()().mark((function e(){var t,n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=O.ZP.loading("正在更新状态"),e.prev=1,e.next=4,R({id:r.id,is_active:!r.is_active});case 4:t(),O.ZP.success("状态更新成功"),null===(n=D.current)||void 0===n||n.reload(),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(1),t(),O.ZP.error("状态更新失败，请重试");case 13:case"end":return e.stop()}}),e,null,[[1,9]])}))),function(){return e.apply(this,arguments)})})},children:r.is_active?"下线":"上线"},"toggle-".concat(r.id)),(0,K.jsx)(E.ZP,{type:"link",style:{width:"50px"},onClick:function(){B(r),g(!0)},children:"查看"},"view-".concat(r.id)),(0,K.jsx)(E.ZP,{type:"link",danger:!0,style:{width:"50px"},onClick:function(){return re(r)},children:"删除"},"delete-".concat(r.id)),(0,K.jsxs)(E.ZP,{type:"link",style:{width:"50px"},onClick:function(){return te(r)},disabled:G[r.id],children:["测试",G[r.id]&&(0,K.jsx)(_.Z,{size:"small",style:{marginLeft:8}})]},"test-".concat(r.id))]}}];return(0,K.jsxs)(y._z,{children:[(0,K.jsx)(k.Z,{headerTitle:"大语言模型管理",actionRef:D,rowKey:"id",search:{labelWidth:120,defaultCollapsed:!0},toolBarRender:function(){return[(0,K.jsxs)(E.ZP,{type:"primary",onClick:function(){n(!0)},children:[(0,K.jsx)(j.Z,{})," 新建"]},"primary")]},request:function(){var e=u()(s()().mark((function e(r){var t;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,M(i()({current:r.current,pageSize:r.pageSize},r));case 2:return t=e.sent,e.abrupt("return",{data:t.data,success:t.success,total:t.total});case 4:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),columns:ne}),(0,K.jsx)(I.Z,{visible:t,title:"新建模型",onCancel:function(){return n(!1)},onOk:function(){return V.submit()},children:(0,K.jsxs)(C.Z,{form:V,layout:"horizontal",onFinish:Y,initialValues:{temperature:.7,max_tokens:4096,top_p:2,provider:U.M.OPENAI,vector_size:300},labelCol:{span:6},wrapperCol:{span:18},children:[(0,K.jsx)(C.Z.Item,{name:"name",label:"名称",rules:[{required:!0,message:"请输入名称"}],children:(0,K.jsx)(S.Z,{})}),(0,K.jsx)(C.Z.Item,{name:"description",label:"描述",rules:[{required:!0,message:"请输入模型描述"}],children:(0,K.jsx)(S.Z,{})}),(0,K.jsx)(C.Z.Item,{name:"m_name",label:"模型名称",rules:[{required:!0,message:"请输入模型名称"}],children:(0,K.jsx)(S.Z,{})}),(0,K.jsx)(C.Z.Item,{name:"provider",label:"接口标准",rules:[{required:!0,message:"请选择接口调用标准"}],children:(0,K.jsxs)($.default,{onChange:function(e){var r="";switch(e){case U.M.DEEPSEEK:r="https://api.deepseek.com";break;case U.M.DOUBAO:r="https://ark.cn-beijing.volces.com/api/v3";break;case U.M.OPENAI:r="https://api.openai.com";break;case U.M.LOACL:case U.M.OLLAMA:case U.M.JIUTIAN:case U.M.TELEAI:r="http://"}V.setFieldsValue({service_url:r})},children:[(0,K.jsx)($.default.Option,{value:U.M.LOACL,children:"本地化"}),(0,K.jsx)($.default.Option,{value:U.M.OLLAMA,children:"Ollama"}),(0,K.jsx)($.default.Option,{value:U.M.JIUTIAN,children:"移动九天"}),(0,K.jsx)($.default.Option,{value:U.M.DEEPSEEK,children:"DeepSeek"}),(0,K.jsx)($.default.Option,{value:U.M.DOUBAO,children:"豆包"}),(0,K.jsx)($.default.Option,{value:U.M.TELEAI,children:"电信星辰"}),(0,K.jsx)($.default.Option,{value:U.M.OPENAI,children:"OpenAI"})]})}),(0,K.jsx)(C.Z.Item,{name:"service_url",label:"服务地址",extra:"服务地址必须以 /v1 结尾",rules:[{required:!0,message:"请输入服务地址"},{pattern:/^(http|https):\/\/[^ "]+$/,message:"请输入有效的 URL，必须以 http 或 https 开头"},{pattern:/\/v1$/,message:"服务地址必须以 /v1 结尾"}],children:(0,K.jsx)(S.Z,{})}),(0,K.jsx)(C.Z.Item,{name:"api_key",label:"API Key",rules:[{required:!0,message:"请输入 API Key"}],children:(0,K.jsx)(S.Z,{})}),(0,K.jsx)(C.Z.Item,{name:"temperature",label:"默认温度",rules:[{required:!0,message:"请输入温度"}],children:(0,K.jsx)(S.Z,{type:"number"})}),(0,K.jsx)(C.Z.Item,{name:"max_tokens",label:"最大令牌数",rules:[{required:!0,message:"请输入最大令牌数"}],children:(0,K.jsx)(S.Z,{type:"number"})}),(0,K.jsx)(C.Z.Item,{name:"top_p",label:"Top P",rules:[{required:!0,message:"请输入 Top P"}],children:(0,K.jsx)(S.Z,{type:"number"})})]})}),q&&(0,K.jsx)(I.Z,{visible:c,title:"更新模型",onCancel:function(){d(!1),V.resetFields()},onOk:function(){return V.submit()},destroyOnClose:!0,forceRender:!0,children:(0,K.jsxs)(C.Z,{form:V,layout:"horizontal",initialValues:q,onFinish:ee,labelCol:{span:6},wrapperCol:{span:18},children:[(0,K.jsx)(C.Z.Item,{name:"id",hidden:!0,children:(0,K.jsx)(S.Z,{})}),(0,K.jsx)(C.Z.Item,{name:"name",label:"名称",rules:[{required:!0,message:"请输入名称"}],children:(0,K.jsx)(S.Z,{})}),(0,K.jsx)(C.Z.Item,{name:"description",label:"描述",rules:[{required:!1,message:"请输入模型描述"}],children:(0,K.jsx)(S.Z,{})}),(0,K.jsx)(C.Z.Item,{name:"m_name",label:"模型名称",rules:[{required:!0,message:"请输入模型名称"}],children:(0,K.jsx)(S.Z,{})}),(0,K.jsx)(C.Z.Item,{name:"provider",label:"接口标准",rules:[{required:!0,message:"请选择接口调用标准"}],children:(0,K.jsxs)($.default,{children:[(0,K.jsx)($.default.Option,{value:U.M.LOACL,children:"本地化"}),(0,K.jsx)($.default.Option,{value:U.M.OLLAMA,children:"Ollama"}),(0,K.jsx)($.default.Option,{value:U.M.JIUTIAN,children:"移动九天"}),(0,K.jsx)($.default.Option,{value:U.M.DEEPSEEK,children:"DeepSeek"}),(0,K.jsx)($.default.Option,{value:U.M.DOUBAO,children:"豆包"}),(0,K.jsx)($.default.Option,{value:U.M.OPENAI,children:"OpenAI"}),(0,K.jsx)($.default.Option,{value:U.M.TELEAI,children:"电信星辰"})]})}),(0,K.jsx)(C.Z.Item,{name:"service_url",label:"服务地址",extra:"服务地址必须以 /v1 结尾",rules:[{required:!0,message:"请输入服务地址"},{pattern:/^(http|https):\/\/[^ "]+$/,message:"请输入有效的 URL，必须以 http 或 https 开头"},{pattern:/\/v1$/,message:"服务地址必须以 /v1 结尾"}],children:(0,K.jsx)(S.Z,{})}),(0,K.jsx)(C.Z.Item,{name:"api_key",label:"API Key",rules:[{required:!0,message:"请输入 API Key"}],children:(0,K.jsx)(S.Z,{})}),(0,K.jsx)(C.Z.Item,{name:"temperature",label:"默认温度",rules:[{required:!0,message:"请输入温度"}],children:(0,K.jsx)(S.Z,{type:"number"})}),(0,K.jsx)(C.Z.Item,{name:"max_tokens",label:"最大令牌数",rules:[{required:!0,message:"请输入最大令牌数"}],children:(0,K.jsx)(S.Z,{type:"number"})}),(0,K.jsx)(C.Z.Item,{name:"top_p",label:"Top P",rules:[{required:!0,message:"请输入 Top P"}],children:(0,K.jsx)(S.Z,{type:"number"})})]},q.id)}),q&&(0,K.jsx)(I.Z,{visible:b,title:"模型信息",width:800,onCancel:function(){return g(!1)},footer:null,children:(0,K.jsxs)(A.Z,{bordered:!0,column:1,children:[(0,K.jsx)(A.Z.Item,{label:"名称",children:q.name}),(0,K.jsx)(A.Z.Item,{label:"模型名称",children:q.m_name}),(0,K.jsx)(A.Z.Item,{label:"描述",children:q.description}),(0,K.jsx)(A.Z.Item,{label:"服务地址",children:q.service_url}),(0,K.jsx)(A.Z.Item,{label:"接口标准",children:q.provider}),(0,K.jsx)(A.Z.Item,{label:"状态",children:(0,K.jsx)(w.Z,{color:q.is_active?"success":"default",icon:q.is_active?(0,K.jsx)(m.Z,{}):(0,K.jsx)(h.Z,{}),style:{fontWeight:"bold",padding:"0 8px",borderRadius:"12px",display:"flex",alignItems:"center",gap:"4px"},children:q.is_active?"已上线":"已下线"})}),(0,K.jsx)(A.Z.Item,{label:"最大令牌数",children:q.max_tokens}),(0,K.jsx)(A.Z.Item,{label:"创建时间",children:q.created_at}),(0,K.jsx)(A.Z.Item,{label:"API Key",children:q.api_key.slice(0,4)+"****"+q.api_key.slice(-4)}),(0,K.jsx)(A.Z.Item,{label:"默认温度",children:q.temperature}),(0,K.jsx)(A.Z.Item,{label:"Top P",children:q.top_p}),(0,K.jsx)(A.Z.Item,{label:"频率惩罚",children:q.frequency_penalty}),(0,K.jsx)(A.Z.Item,{label:"存在惩罚",children:q.presence_penalty}),(0,K.jsx)(A.Z.Item,{label:"价格",children:q.price})]})})]})}},77885:function(e,r,t){t.d(r,{M:function(){return n}});var n={LOACL:"local",OPENAI:"openai",DEEPSEEK:"deepseek",DOUBAO:"doubao",TELEAI:"teleai",JIUTIAN:"jiutian",OLLAMA:"ollama"}},66309:function(e,r,t){t.d(r,{Z:function(){return _}});var n=t(67294),a=t(93967),l=t.n(a),s=t(98423),o=t(98787),i=t(69760),c=t(96159),u=t(45353),d=t(53124),p=t(11568),m=t(15063),h=t(14747),f=t(83262),x=t(83559);const v=e=>{const{lineWidth:r,fontSizeIcon:t,calc:n}=e,a=e.fontSizeSM;return(0,f.IX)(e,{tagFontSize:a,tagLineHeight:(0,p.bf)(n(e.lineHeightSM).mul(a).equal()),tagIconSize:n(t).sub(n(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},b=e=>({defaultBg:new m.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var g=(0,x.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:t,tagPaddingHorizontal:n,componentCls:a,calc:l}=e,s=l(n).sub(t).equal(),o=l(r).sub(t).equal();return{[a]:Object.assign(Object.assign({},(0,h.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:s,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${a}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${a}-close-icon`]:{marginInlineStart:o,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${a}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${a}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:s}}),[`${a}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(v(e))),b),Z=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)r.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(t[n[a]]=e[n[a]])}return t};const j=n.forwardRef(((e,r)=>{const{prefixCls:t,style:a,className:s,checked:o,onChange:i,onClick:c}=e,u=Z(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:m}=n.useContext(d.E_),h=p("tag",t),[f,x,v]=g(h),b=l()(h,`${h}-checkable`,{[`${h}-checkable-checked`]:o},null==m?void 0:m.className,s,x,v);return f(n.createElement("span",Object.assign({},u,{ref:r,style:Object.assign(Object.assign({},a),null==m?void 0:m.style),className:b,onClick:e=>{null==i||i(!o),null==c||c(e)}})))}));var y=j,k=t(98719);var I=(0,x.bk)(["Tag","preset"],(e=>(e=>(0,k.Z)(e,((r,t)=>{let{textColor:n,lightBorderColor:a,lightColor:l,darkColor:s}=t;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:n,background:l,borderColor:a,"&-inverse":{color:e.colorTextLightSolid,background:s,borderColor:s},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(v(e))),b);const C=(e,r,t)=>{const n="string"!=typeof(a=t)?a:a.charAt(0).toUpperCase()+a.slice(1);var a;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${t}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var O=(0,x.bk)(["Tag","status"],(e=>{const r=v(e);return[C(r,"success","Success"),C(r,"processing","Info"),C(r,"error","Error"),C(r,"warning","Warning")]}),b),P=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)r.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(t[n[a]]=e[n[a]])}return t};const w=n.forwardRef(((e,r)=>{const{prefixCls:t,className:a,rootClassName:p,style:m,children:h,icon:f,color:x,onClose:v,bordered:b=!0,visible:Z}=e,j=P(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:y,direction:k,tag:C}=n.useContext(d.E_),[w,E]=n.useState(!0),_=(0,s.Z)(j,["closeIcon","closable"]);n.useEffect((()=>{void 0!==Z&&E(Z)}),[Z]);const S=(0,o.o2)(x),$=(0,o.yT)(x),A=S||$,T=Object.assign(Object.assign({backgroundColor:x&&!A?x:void 0},null==C?void 0:C.style),m),M=y("tag",t),[L,N,q]=g(M),z=l()(M,null==C?void 0:C.className,{[`${M}-${x}`]:A,[`${M}-has-color`]:x&&!A,[`${M}-hidden`]:!w,[`${M}-rtl`]:"rtl"===k,[`${M}-borderless`]:!b},a,p,N,q),B=e=>{e.stopPropagation(),null==v||v(e),e.defaultPrevented||E(!1)},[,R]=(0,i.Z)((0,i.w)(e),(0,i.w)(C),{closable:!1,closeIconRender:e=>{const r=n.createElement("span",{className:`${M}-close-icon`,onClick:B},e);return(0,c.wm)(e,r,(e=>({onClick:r=>{var t;null===(t=null==e?void 0:e.onClick)||void 0===t||t.call(e,r),B(r)},className:l()(null==e?void 0:e.className,`${M}-close-icon`)})))}}),D="function"==typeof j.onClick||h&&"a"===h.type,F=f||null,H=F?n.createElement(n.Fragment,null,F,h&&n.createElement("span",null,h)):h,U=n.createElement("span",Object.assign({},_,{ref:r,className:z,style:T}),H,R,S&&n.createElement(I,{key:"preset",prefixCls:M}),$&&n.createElement(O,{key:"status",prefixCls:M}));return L(D?n.createElement(u.Z,{component:"Tag"},U):U)})),E=w;E.CheckableTag=y;var _=E}}]);