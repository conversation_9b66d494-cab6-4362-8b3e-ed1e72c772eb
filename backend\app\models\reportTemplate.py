from datetime import datetime
from mongoengine import Document, ObjectIdField, StringField, DateTimeField, ReferenceField, BooleanField, IntField, ListField
from pydantic import BaseModel, Field
from typing import List as PyList, Optional, Any

# MongoEngine 模型保持不变
class ReportTemplate(Document):
    """信贷报告模板基本信息"""
    meta = {'collection': 'report_templates'}
    
    title = StringField(required=True, description='模板标题')
    description = StringField(description='模板描述')
    template_type = StringField(description='模板类型，例如：对公贷款报告、个人贷款报告等')
    tags = ListField(StringField(), description='模板标签，便于分类和筛选')
    file_path = StringField(description='原始模板文件存储路径')
    file_type = StringField(description='模板文件类型，例如DOCX')
    content_template = StringField(description='内容模板')
    user_id = IntField(required=True, description='用户ID')
    user_name = StringField(description='用户名称')
    created_at = DateTimeField(default=datetime.now, description='创建时间')
    updated_at = DateTimeField(default=datetime.now, description='更新时间')
    is_active = BooleanField(default=True, description='是否启用')
    is_published = BooleanField(default=False, description='是否公开')
    template_sections = ListField(ObjectIdField(), description='模板章节结构')
    
    def __str__(self):
        return f"{self.title}"

class TemplateSection(Document):
    """模板章节结构，包含提示词配置"""
    meta = {'collection': 'template_sections'}
    title = StringField(required=True, description='章节标题')
    section_type = StringField(description='章节类型，例如：概述、财务分析、风险评估等')
    tags = ListField(StringField(), description='章节标签，用于标识章节特性')
    prompt_text = StringField(description='章节内容生成提示词')
    data_requirements = StringField(description='章节所需数据要求描述')
    content_template = StringField(description='内容模板')
    created_at = DateTimeField(default=datetime.now, description='创建时间')
    updated_at = DateTimeField(default=datetime.now, description='更新时间')
    child_sections = ListField(ObjectIdField(), description='子章节')
    
    def __str__(self):
        return f"{self.title}"  # 移除了 (Level: {self.level})，因为没有 level 字段

class ReportTask(Document):
    """报告任务"""
    meta = {'collection': 'report_tasks'}
    
    name = StringField(required=True, description='任务名称')
    template = ReferenceField(ReportTemplate, required=True, description='使用的模板')
    customer_id = StringField(required=True, description='调查对象ID（客户ID）')
    customer_name = StringField(description='调查对象名称（客户名称）')
    status = StringField(default='pending', description='任务状态：pending（待上传）、generating（生成中）、review（待审核）、completed（已完成）、failed（失败）')
    created_by = StringField(description='创建人ID')
    created_at = DateTimeField(default=datetime.now, description='创建时间')
    updated_at = DateTimeField(default=datetime.now, description='更新时间')
    completed_at = DateTimeField(description='完成时间')
    
    def __str__(self):
        return f"{self.name} ({self.status})"

# 添加 Pydantic 模型用于 API 响应
class ReportTemplateModel(BaseModel):
    title: str
    description: Optional[str] = None
    template_type: Optional[str] = None
    tags: Optional[PyList[str]] = None  # 使用 PyList 而不是 ListField
    file_path: Optional[str] = None
    file_type: Optional[str] = None
    content_template: Optional[str] = None
    user_id: Optional[int] = None  # 改为可选，由后端填充
    user_name: Optional[str] = None
    created_at: Optional[datetime] = None  # 改为可选，由后端填充
    updated_at: Optional[datetime] = None  # 改为可选，由后端填充
    is_active: bool = True
    is_published: bool = False
    template_sections: Optional[PyList[str]] = None  # 使用 PyList 而不是 ListField

class TemplateSectionModel(BaseModel):
    title: str
    section_type: Optional[str] = None
    tags: Optional[PyList[str]] = None  # 修复这里：使用 PyList 而不是 ListField
    prompt_text: Optional[str] = None
    data_requirements: Optional[str] = None
    content_template: Optional[str] = None
    created_at: Optional[datetime] = None  # 改为可选，由后端填充
    updated_at: Optional[datetime] = None  # 改为可选，由后端填充
    child_sections: Optional[PyList[str]] = None  # 使用 PyList 而不是 ListField

class ReportTaskModel(BaseModel):
    name: str
    template: Any  # 这里使用 Any 因为引用的是另一个模型
    customer_id: str
    customer_name: Optional[str] = None
    status: str = "pending"
    created_by: Optional[str] = None
    created_at: Optional[datetime] = None  # 改为可选，由后端填充
    updated_at: Optional[datetime] = None  # 改为可选，由后端填充
    completed_at: Optional[datetime] = None
