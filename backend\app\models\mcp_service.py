from mongoengine import Document, StringField, IntField, DateTimeField, ListField, BooleanField, ReferenceField, ObjectIdField, DictField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from bson import ObjectId

# MongoEngine 模型
class mcpService(Document):
    meta = {
        'collection': 'mcp_service'
    }
    name = StringField(required=True)
    description = StringField()
    created_at = DateTimeField(default=datetime.now)
    user_id = IntField(required=True)
    user_name = StringField()
    type = StringField()  # streamableHttp, stdio, sse
    is_active = BooleanField(required=True, default=True) # 是否启用
    url = StringField() # 服务地址，用于streamableHttp和sse类型
    transport = StringField() # http, websocket
    args = ListField() # 接口密钥或命令参数
    headers = DictField() # HTTP请求头，用于streamableHttp和sse类型
    command = StringField() # 命令，用于stdio类型
    key = ListField() # 环境变量，用于stdio类型
    tags = ListField() # 服务用途标签分类
    timeout = IntField(default=60) # 超时设置，单位：秒
    usage_count = IntField(required=True, default=0) # 调用量

# Pydantic 基础模型
class MCPServiceBase(BaseModel):
    name: str
    description: Optional[str] = None
    type: str  # streamableHttp, stdio, sse
    is_active: bool = True
    tags: List[str] = Field(default_factory=list)
    timeout: Optional[int] = 60

# StreamableHttp类型服务
class StreamableHttpService(MCPServiceBase):
    url: str
    headers: Dict[str, str] = Field(default_factory=dict)

# 标准输入/输出类型服务
class StdioService(MCPServiceBase):
    command: str
    args: List[str] = Field(default_factory=list)
    env_vars: Dict[str, str] = Field(default_factory=dict)

# 服务器发送事件类型服务
class SSEService(MCPServiceBase):
    url: str
    headers: Optional[Dict[str, str]] = Field(default_factory=dict)

# 创建服务请求模型
class MCPServiceCreate(BaseModel):
    name: str
    description: Optional[str] = None
    type: str  # streamableHttp, stdio, sse
    is_active: bool = True
    url: Optional[str] = None
    command: Optional[str] = None
    args:  Optional[List[str]] = None
    headers: Optional[Dict[str, str]] = None
    env_vars: Optional[Dict[str, str]] = None
    tags: List[str] = Field(default_factory=list)
    timeout: int = 60

# 更新服务请求模型
class MCPServiceUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    type: Optional[str] = None
    is_active: Optional[bool] = None
    url: Optional[str] = None
    command: Optional[str] = None
    args: Optional[List[str]] = None
    headers: Optional[Dict[str, str]] = None
    env_vars: Optional[Dict[str, str]] = None
    tags: Optional[List[str]] = None
    timeout: Optional[int] = None

# 服务响应模型
class MCPServiceResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    type: str
    is_active: bool
    url: Optional[str] = None
    command: Optional[str] = None
    args: List[str] = []
    headers: Dict[str, str] = {}
    env_vars: Dict[str, str] = {}
    tags: List[str] = []
    timeout: int
    usage_count: int
    created_at: datetime
    user_id: int
    user_name: Optional[str] = None

    class Config:
        from_attributes = True
