from fastapi import APIRouter, HTTPException, Depends, Query, Body, Path
from typing import List, Optional, Dict, Any
from ..models.mcp_service import mcpService, MCPServiceCreate, MCPServiceUpdate, MCPServiceResponse
from ..db.mongodb import db
from ..utils.auth import verify_token
from datetime import datetime
from bson import ObjectId
import asyncio
import traceback
import httpx
import subprocess
import json
import shlex
from app.utils.logging_config import setup_logging, get_logger
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

setup_logging()
logger = get_logger(__name__)

router = APIRouter(
    prefix="/api",
    tags=["mcp-services"]
)

class ServiceInvokeRequest(BaseModel):
    """请求调用MCP服务的模型"""
    data: Dict[str, Any]
    stream: bool = False
    timeout: Optional[int] = None

# 获取所有MCP服务列表，支持分页和过滤
@router.get("/mcp-services", response_model=Dict[str, Any])
async def get_mcp_services(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    name: Optional[str] = None,
    type: Optional[str] = None,
    tag: Optional[str] = None,
    is_active: Optional[bool] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    # 基本查询
    query = {}
    
    # 添加额外过滤条件
    if name:
        query["name"] = {"$regex": name, "$options": "i"}
    if type:
        query["type"] = type
    if tag:
        query["tags"] = {"$in": [tag]}
    if is_active is not None:
        query["is_active"] = is_active
    
    # 查询数据库
    services = await db["mcp_service"].find(query).sort(
        "created_at", -1
    ).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 获取总数
    total = await db["mcp_service"].count_documents(query)
    
    # 处理ID格式
    for service in services:
        service["id"] = str(service["_id"])
        del service["_id"]
    
    return {
        "data": services,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 获取个人MCP服务列表，支持分页和过滤
@router.get("/user-mcp-services", response_model=Dict[str, Any])
async def get_user_mcp_services(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    name: Optional[str] = None,
    type: Optional[str] = None,
    tag: Optional[str] = None,
    is_active: Optional[bool] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    # 基本查询: 只查询用户自己创建的服务
    query = {"user_id": current_user["id"]}
    
    # 添加额外过滤条件
    if name:
        query["name"] = {"$regex": name, "$options": "i"}
    if type:
        query["type"] = type
    if tag:
        query["tags"] = {"$in": [tag]}
    if is_active is not None:
        query["is_active"] = is_active
    
    # 查询数据库
    services = await db["mcp_service"].find(query).sort(
        "created_at", -1
    ).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 获取总数
    total = await db["mcp_service"].count_documents(query)
    
    # 处理ID格式
    for service in services:
        service["id"] = str(service["_id"])
        del service["_id"]
    
    return {
        "data": services,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 获取系统MCP服务列表，支持分页和过滤
@router.get("/system-mcp-services", response_model=Dict[str, Any])
async def get_system_mcp_services(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    name: Optional[str] = None,
    type: Optional[str] = None,
    tag: Optional[str] = None,
    is_active: Optional[bool] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    # 基本查询: 只查询系统级别的服务
    query = {"is_system": True}
    
    # 添加额外过滤条件
    if name:
        query["name"] = {"$regex": name, "$options": "i"}
    if type:
        query["type"] = type
    if tag:
        query["tags"] = {"$in": [tag]}
    if is_active is not None:
        query["is_active"] = is_active
    
    # 查询数据库
    services = await db["mcp_service"].find(query).sort(
        "created_at", -1
    ).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 获取总数
    total = await db["mcp_service"].count_documents(query)
    
    # 处理ID格式
    for service in services:
        service["id"] = str(service["_id"])
        del service["_id"]
    
    return {
        "data": services,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 获取MCP服务详情
@router.get("/mcp-services/{service_id}", response_model=Dict[str, Any])
async def get_mcp_service(
    service_id: str,
    current_user: dict = Depends(verify_token)
):
    try:
        service = await db["mcp_service"].find_one({"_id": ObjectId(service_id)})
        if not service:
            raise HTTPException(status_code=404, detail="MCP服务不存在")
        
        # 检查访问权限: 用户只能查看自己的服务或系统服务
        if not service.get("is_system", False) and service.get("user_id") != current_user["id"]:
            raise HTTPException(status_code=403, detail="无权访问此服务")
        
        # 处理ID格式
        service["id"] = str(service["_id"])
        del service["_id"]
        
        return {
            "data": service,
            "success": True
        }
    except Exception as e:
        logger.error(f"获取MCP服务详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取MCP服务详情失败: {str(e)}")

# 创建新MCP服务
@router.post("/mcp-services", response_model=Dict[str, Any])
async def create_mcp_service(
    service: MCPServiceCreate,
    current_user: dict = Depends(verify_token)
):
    try:
        new_service = service.dict()
        new_service.update({
            "user_name": current_user["name"],
            "user_id": current_user["id"],
            "created_at": datetime.now(),
            "is_active": True,
            "is_system": False,
            "usage_count": 0
        })
        
        # 根据服务类型验证必填字段
        service_type = new_service.get("type")
        if service_type in ["streamableHttp", "sse"]:
            if not new_service.get("url"):
                raise HTTPException(status_code=400, detail="URL字段对于HTTP和SSE类型服务是必需的")
        elif service_type == "stdio":
            if not new_service.get("command"):
                raise HTTPException(status_code=400, detail="命令字段对于stdio类型服务是必需的")
                
        # 确保tags字段存在
        if "tags" not in new_service:
            new_service["tags"] = []
        
        result = await db["mcp_service"].insert_one(new_service)
        created_service = await db["mcp_service"].find_one({"_id": result.inserted_id})
        
        # 处理ID格式
        created_service["id"] = str(created_service["_id"])
        del created_service["_id"]
        
        return {
            "data": created_service,
            "success": True
        }
    except Exception as e:
        logger.error(f"创建MCP服务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建MCP服务失败: {str(e)}")

# 更新MCP服务
@router.put("/mcp-services/{service_id}", response_model=Dict[str, Any])
async def update_mcp_service(
    service_id: str,
    service: MCPServiceUpdate,
    current_user: dict = Depends(verify_token)
):
    try:
        # 检查服务是否存在
        existing_service = await db["mcp_service"].find_one({"_id": ObjectId(service_id)})
        if not existing_service:
            raise HTTPException(status_code=404, detail="MCP服务不存在")
        
        # 检查权限: 用户只能更新自己的服务
        if existing_service.get("user_id") != current_user["id"]:
            raise HTTPException(status_code=403, detail="无权更新此服务")
        
        # 只更新非空字段
        update_data = {k: v for k, v in service.dict(exclude_unset=True).items() if v is not None}
        
        result = await db["mcp_service"].update_one(
            {"_id": ObjectId(service_id)},
            {"$set": update_data}
        )
        
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="MCP服务不存在")
        
        updated_service = await db["mcp_service"].find_one({"_id": ObjectId(service_id)})
        updated_service["id"] = str(updated_service["_id"])
        del updated_service["_id"]
        
        return {
            "data": updated_service,
            "success": True
        }
    except Exception as e:
        traceback.print_exc()
        logger.error(f"更新MCP服务失败: {str(e)}")
        return {
            "data": str(e),
            "success": False
        }

# 删除MCP服务
@router.delete("/mcp-services/{service_id}", response_model=Dict[str, Any])
async def delete_mcp_service(
    service_id: str,
    current_user: dict = Depends(verify_token)
):
    try:
        # 检查服务是否存在
        existing_service = await db["mcp_service"].find_one({"_id": ObjectId(service_id)})
        if not existing_service:
            raise HTTPException(status_code=404, detail="MCP服务不存在")
        
        # 检查权限: 用户只能删除自己的服务
        if existing_service.get("user_id") != current_user["id"]:
            raise HTTPException(status_code=403, detail="无权删除此服务")
        
        result = await db["mcp_service"].delete_one({"_id": ObjectId(service_id)})
        
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="MCP服务不存在")
        
        return {
            "id": service_id,
            "success": True
        }
    except Exception as e:
        logger.error(f"删除MCP服务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除MCP服务失败: {str(e)}")

# 复制MCP服务（创建一个副本）
@router.post("/mcp-services/{service_id}/copy", response_model=Dict[str, Any])
async def copy_mcp_service(
    service_id: str,
    current_user: dict = Depends(verify_token)
):
    try:
        # 检查服务是否存在
        existing_service = await db["mcp_service"].find_one({"_id": ObjectId(service_id)})
        if not existing_service:
            raise HTTPException(status_code=404, detail="MCP服务不存在")
        
        # 检查访问权限: 用户只能复制自己的服务或系统服务
        if not existing_service.get("is_system", False) and existing_service.get("user_id") != current_user["id"]:
            raise HTTPException(status_code=403, detail="无权复制此服务")
        
        # 创建新的服务副本
        new_service = dict(existing_service)
        del new_service["_id"]  # 删除原ID
        
        # 更新副本信息
        new_service.update({
            "name": f"{new_service['name']} (副本)",
            "user_name": current_user["name"],
            "user_id": current_user["id"],
            "created_at": datetime.now(),
            "usage_count": 0,
            "is_system": False  # 复制的服务永远不是系统服务
        })
        
        result = await db["mcp_service"].insert_one(new_service)
        created_service = await db["mcp_service"].find_one({"_id": result.inserted_id})
        
        # 处理ID格式
        created_service["id"] = str(created_service["_id"])
        del created_service["_id"]
        
        return {
            "data": created_service,
            "success": True
        }
    except Exception as e:
        logger.error(f"复制MCP服务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"复制MCP服务失败: {str(e)}")

# 调用MCP服务
@router.post("/mcp-services/{service_id}/invoke", response_model=Dict[str, Any])
async def invoke_mcp_service(
    service_id: str,
    request: ServiceInvokeRequest,
    current_user: dict = Depends(verify_token)
):
    try:
        # 检查服务是否存在
        service = await db["mcp_service"].find_one({"_id": ObjectId(service_id)})
        if not service:
            raise HTTPException(status_code=404, detail="MCP服务不存在")
        
        # 检查服务是否启用
        if not service.get("is_active", True):
            raise HTTPException(status_code=400, detail="此服务当前已禁用")
        
        # 更新使用次数
        await db["mcp_service"].update_one(
            {"_id": ObjectId(service_id)},
            {"$inc": {"usage_count": 1}}
        )
        
        # 设置超时时间
        timeout = request.timeout or service.get("timeout", 60)
        
        # 根据服务类型执行不同的调用逻辑
        service_type = service.get("type")
        
        # 1. 处理可流式HTTP服务
        if service_type == "streamableHttp":
            url = service.get("url")
            if not url:
                raise HTTPException(status_code=400, detail="服务URL未配置")
            
            # 准备请求头
            headers = {}
            if service.get("headers"):
                headers.update(service.get("headers"))
            
            # 支持流式和非流式响应
            if request.stream:
                async def stream_http_response():
                    try:
                        async with httpx.AsyncClient() as client:
                            async with client.stream("POST", url, json=request.data, headers=headers, timeout=timeout) as response:
                                async for chunk in response.aiter_bytes():
                                    yield chunk
                    except Exception as e:
                        yield f"event: error\ndata: {json.dumps({'error': str(e)})}\n\n".encode()
                        logger.error(f"流式HTTP请求失败: {str(e)}")
                
                return StreamingResponse(stream_http_response())
            else:
                # 非流式HTTP请求
                async with httpx.AsyncClient() as client:
                    response = await client.post(url, json=request.data, headers=headers, timeout=timeout)
                    return {
                        "data": response.json(),
                        "success": response.status_code == 200
                    }
        
        # 2. 处理标准输入/输出服务
        elif service_type == "stdio":
            command = service.get("command")
            if not command:
                raise HTTPException(status_code=400, detail="服务命令未配置")
            
            # 准备命令参数
            args = service.get("args", [])
            cmd_parts = [command] + args
            
            # 准备环境变量
            env_vars = {}
            if service.get("key"):
                for key in service.get("key", []):
                    if "=" in key:
                        k, v = key.split("=", 1)
                        env_vars[k] = v
            
            # 将请求数据转换为命令行参数或标准输入
            input_data = json.dumps(request.data).encode()
            
            # 执行命令
            try:
                process = await asyncio.create_subprocess_exec(
                    *[shlex.quote(part) for part in cmd_parts],
                    stdin=asyncio.subprocess.PIPE,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    env=env_vars
                )
                
                stdout, stderr = await process.communicate(input=input_data)
                
                if process.returncode != 0:
                    logger.error(f"命令执行失败: {stderr.decode()}")
                    return {
                        "success": False,
                        "error": stderr.decode(),
                        "return_code": process.returncode
                    }
                
                # 尝试解析JSON输出
                try:
                    result = json.loads(stdout.decode())
                    return {
                        "data": result,
                        "success": True
                    }
                except json.JSONDecodeError:
                    return {
                        "data": stdout.decode(),
                        "success": True
                    }
            except Exception as e:
                logger.error(f"执行命令失败: {str(e)}")
                return {
                    "success": False,
                    "error": str(e)
                }
        
        # 3. 处理服务器发送事件服务
        elif service_type == "sse":
            url = service.get("url")
            if not url:
                raise HTTPException(status_code=400, detail="服务URL未配置")
            
            # 准备请求头
            headers = {"Accept": "text/event-stream"}
            if service.get("headers"):
                headers.update(service.get("headers"))
            
            # SSE总是流式的
            async def stream_sse_response():
                try:
                    async with httpx.AsyncClient() as client:
                        async with client.stream("POST", url, json=request.data, headers=headers, timeout=timeout) as response:
                            async for line in response.aiter_lines():
                                if line:
                                    yield f"{line}\n".encode()
                except Exception as e:
                    yield f"event: error\ndata: {json.dumps({'error': str(e)})}\n\n".encode()
                    logger.error(f"SSE请求失败: {str(e)}")
            
            return StreamingResponse(stream_sse_response(), media_type="text/event-stream")
        
        else:
            raise HTTPException(status_code=400, detail=f"不支持的服务类型: {service_type}")
        
    except Exception as e:
        logger.error(f"调用MCP服务失败: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            "success": False,
            "error": str(e)
        }

# 获取所有服务类型
@router.get("/mcp-service-types", response_model=Dict[str, Any])
async def get_service_types():
    return {
        "data": ["streamableHttp", "stdio", "sse"],
        "success": True
    }
