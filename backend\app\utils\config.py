import os
from urllib.parse import quote_plus
from dotenv import load_dotenv
from ..utils.logging_config import get_logger,setup_logging
setup_logging()
logger = get_logger(__name__)


class Settings:
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._initialize()
    
    def _initialize(self):
        logger.info("--==开始加载环境配置==--")
        # 加载环境变量
        env_mode = os.getenv('ENV_MODE', 'production')
        self.system_name = os.getenv('SYSTEM_NAME', None)
        logger.info(f"环境变量: {env_mode}")
        env_file = os.path.join(os.path.dirname(__file__), '..', '..', f'.env.{env_mode}')
        
        if not os.path.exists(env_file):
            logger.warning(f"配置文件 {env_file} 不存在，使用默认 .env 文件")
            env_file = os.path.join(os.path.dirname(__file__), '..', '..', '.env')
        
        logger.info(f"加载配置文件: {env_file}")
        load_dotenv(env_file)
        
        # MongoDB配置
        self.MONGODB_HOST = os.getenv('MONGODB_HOST', 'localhost')
        self.MONGODB_PORT = os.getenv('MONGODB_PORT', '27017')
        self.MONGODB_USER = os.getenv('MONGODB_USER', 'memInterview')
        self.MONGODB_PASSWORD = os.getenv('MONGODB_PASSWORD', 'memInterview@202408')
        self.DATABASE_NAME = os.getenv('DATABASE_NAME', 'roardataAiApp_test')
        self.MONGODB_AUTH_SOURCE = os.getenv('MONGODB_AUTH_SOURCE', 'admin')

        # MinIO配置
        self.MINIO_ENABLE = os.getenv('MINIO_ENABLE', 'false').lower() == 'true'
        self.MINIO_ENDPOINT = os.getenv('MINIO_ENDPOINT', 'localhost:9000')
        self.MINIO_ACCESS_KEY = os.getenv('MINIO_ACCESS_KEY', 'minioadmin')
        self.MINIO_SECRET_KEY = os.getenv('MINIO_SECRET_KEY', 'minioadmin')
        # 将字符串转换为布尔值
        self.MINIO_SECURE = os.getenv('MINIO_SECURE', 'False').lower() == 'true'
        self.MINIO_BUCKET = os.getenv('MINIO_BUCKET', 'mybucket')

        # 文件上传路径
        self.FILE_UPLOAD_PATH = os.getenv('FILE_UPLOAD_PATH', '/data/upload')

        # Elasticsearch配置
        self.ES_HOST = os.getenv('ES_HOST', '[{"host": "**************", "port": 9600},{"host": "**************", "port": 9600}]')
        self.ES_INDEX = os.getenv('ES_INDEX', 'wise_agent_chunk_index')

        # 日志级别
        self.LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')

        # 索引器配置
        self.INDEXER_ENABLED = os.getenv('INDEXER_ENABLED', 'false').lower() == 'true'
        
        # JWT配置
        self.SECRET_KEY = os.getenv('SECRET_KEY', 'wsie-ai-agent')
        self.TOKENS_COUNT_MODEL = os.getenv('TOKENS_COUNT_MODEL', 'gpt-4o-mini')
        # 将字符串转换为整数
        self.ACCESS_TOKEN_EXPIRE_SECONDS = int(os.getenv('ACCESS_TOKEN_EXPIRE_SECONDS', 3600))

        # wiseflow
        self.enable_wiseflow = os.getenv('enable_wiseflow', 'True').lower() == 'true'
        self.wiseflow_url = os.getenv('wiseflow_url', 'http://localhost:7860/api/v1/wiseagentauth')

        # Neo4j配置
        self.GRAPH_RAG=os.getenv('GRAPH_RAG', 'True').lower() == 'true'
        self.NEO4J_URI = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
        self.NEO4J_USERNAME = os.getenv('NEO4J_USERNAME', 'neo4j')
        self.NEO4J_PASSWORD = os.getenv('NEO4J_PASSWORD', 'password')
        self.NEO4J_DATABASE = os.getenv('NEO4J_DATABASE', 'neo4j')

        # 图查询配置
        self.GRAPH_QUERY_MAX_DEPTH = int(os.getenv('GRAPH_QUERY_MAX_DEPTH', '3'))
        self.GRAPH_QUERY_MAX_RESULTS = int(os.getenv('GRAPH_QUERY_MAX_RESULTS', '50'))
        self.GRAPH_QUERY_SIMILARITY_THRESHOLD = float(os.getenv('GRAPH_QUERY_SIMILARITY_THRESHOLD', '0.7'))

        # OpenAI配置
        self.OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
        self.OPENAI_BASE_URL = os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1')
        self.OPENAI_MODEL = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')
        self.OPENAI_TEMPERATURE = float(os.getenv('OPENAI_TEMPERATURE', '0.1'))
        self.OPENAI_MAX_TOKENS = int(os.getenv('OPENAI_MAX_TOKENS', '4000'))


        # MCP配置
        self.MCP_WEBSEARCH_URL = os.getenv('MCP_WEBSEARCH_URL', 'http://47.93.214.158:8004')
        self.MCP_WEBSEARCH_TRANSPORT = os.getenv('MCP_WEBSEARCH_TRANSPORT', 'streamable_http')
        self.MCP_ENABLED = os.getenv('MCP_ENABLED', 'false').lower() == 'true'

        self._initialized = True
        logger.info("环境配置加载完成")
    
    @property
    def MONGODB_URL(self) -> str:
        """动态构建MongoDB连接URL，并处理特殊字符编码"""
        encoded_user = quote_plus(self.MONGODB_USER)
        encoded_password = quote_plus(self.MONGODB_PASSWORD)
        return f"mongodb://{encoded_user}:{encoded_password}@{self.MONGODB_HOST}:{self.MONGODB_PORT}/{self.DATABASE_NAME}?authSource={self.MONGODB_AUTH_SOURCE}"

# 创建全局配置实例
settings = Settings()
