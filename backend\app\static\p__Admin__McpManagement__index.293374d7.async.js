"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3e3],{8751:function(e,r,t){t.d(r,{Z:function(){return i}});var n=t(1413),a=t(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"},o=t(91146),c=function(e,r){return a.createElement(o.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:s}))};var i=a.forwardRef(c)},85175:function(e,r,t){var n=t(1413),a=t(67294),s=t(48820),o=t(91146),c=function(e,r){return a.createElement(o.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:s.Z}))},i=a.forwardRef(c);r.Z=i},82061:function(e,r,t){var n=t(1413),a=t(67294),s=t(47046),o=t(91146),c=function(e,r){return a.createElement(o.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:s.Z}))},i=a.forwardRef(c);r.Z=i},47389:function(e,r,t){var n=t(1413),a=t(67294),s=t(27363),o=t(91146),c=function(e,r){return a.createElement(o.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:s.Z}))},i=a.forwardRef(c);r.Z=i},51042:function(e,r,t){var n=t(1413),a=t(67294),s=t(42110),o=t(91146),c=function(e,r){return a.createElement(o.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:s.Z}))},i=a.forwardRef(c);r.Z=i},87784:function(e,r,t){t.d(r,{Z:function(){return i}});var n=t(1413),a=t(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"},o=t(91146),c=function(e,r){return a.createElement(o.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:s}))};var i=a.forwardRef(c)},61415:function(e,r,t){t.r(r),t.d(r,{default:function(){return ue}});var n=t(13769),a=t.n(n),s=t(9783),o=t.n(s),c=t(15009),i=t.n(c),l=t(99289),u=t.n(l),d=t(97857),p=t.n(d),h=t(5574),f=t.n(h),x=t(67294),m=t(97131),v=t(12453),g=t(71471),y=t(55102),b=t(17788),j=t(47019),k=t(2453),Z=t(66309),C=t(42075),P=t(83622),w=t(34041),S=t(67839),O=t(11941),T=t(13457),I=t(72269),N=t(96074),E=t(47389),_=t(85175),$=t(87784),z=t(8751),B=t(82061),M=t(51042),R=t(78158);function F(e){return L.apply(this,arguments)}function L(){return(L=u()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,R.N)("/api/mcp-services",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function H(e){return J.apply(this,arguments)}function J(){return(J=u()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,R.N)("/api/user-mcp-services",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function q(e){return U.apply(this,arguments)}function U(){return(U=u()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,R.N)("/api/system-mcp-services",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function A(e){return V.apply(this,arguments)}function V(){return(V=u()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,R.N)("/api/mcp-services/".concat(r),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function W(e){return K.apply(this,arguments)}function K(){return(K=u()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,R.N)("/api/mcp-services",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function D(e,r){return G.apply(this,arguments)}function G(){return(G=u()(i()().mark((function e(r,t){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,R.N)("/api/mcp-services/".concat(r),{method:"PUT",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function X(e){return Y.apply(this,arguments)}function Y(){return(Y=u()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,R.N)("/api/mcp-services/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Q(e){return ee.apply(this,arguments)}function ee(){return(ee=u()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,R.N)("/api/mcp-services/".concat(r,"/copy"),{method:"POST"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var re=[{label:"HTTP流式服务",value:"streamableHttp",icon:"🌐"},{label:"stdio",value:"stdio",icon:"💻"},{label:"sse",value:"sse",icon:"📡"}],te=["搜索工具","浏览器自动化","文件处理","数据分析","金融工具","内容生成","知识管理","开发工具","通信工具","数据库工具"],ne=t(85893),ae=["current","pageSize","is_system"],se=g.Z.Title,oe=g.Z.Text,ce=g.Z.Paragraph,ie=y.Z.TextArea,le=b.Z.confirm,ue=function(){var e,r=(0,x.useRef)(),t=j.Z.useForm(),n=f()(t,1)[0],s=(0,x.useState)(!1),c=f()(s,2),l=c[0],d=c[1],h=(0,x.useState)(null),g=f()(h,2),R=g[0],L=g[1],J=(0,x.useState)(!1),U=f()(J,2),V=U[0],K=U[1],G=(0,x.useState)(null),Y=f()(G,2),ee=Y[0],ue=Y[1],de=(0,x.useState)(!1),pe=f()(de,2),he=pe[0],fe=pe[1],xe=(0,x.useState)("streamableHttp"),me=f()(xe,2),ve=me[0],ge=me[1],ye=function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"内容";if(!e)return k.ZP.info("请先输入".concat(r)),!1;try{var t=JSON.parse(e),n=Object.entries(t).every((function(e){var r=f()(e,2),t=r[0],n=r[1];return"string"==typeof t&&("string"==typeof n||null==n)}));return n?(k.ZP.success("".concat(r,"格式正确！")),!0):(k.ZP.error("".concat(r,"格式不正确，所有键和值都必须是字符串")),!1)}catch(e){return k.ZP.error("".concat(r,"格式不正确，应为有效的JSON对象")),!1}},be=function(e){L(e||null),e?(n.setFieldsValue(p()(p()({},e),{},{name:e.name,description:e.description,type:e.type,url:e.url,command:e.command,args:e.args,headers:e.headers?JSON.stringify(e.headers):void 0,env_vars:e.env_vars?JSON.stringify(e.env_vars):void 0,tags:e.tags||[],timeout:e.timeout||60,is_active:e.is_active,transport:e.transport,key:e.key})),ge(e.type)):(n.resetFields(),n.setFieldsValue({type:"streamableHttp",tags:[],timeout:60,is_active:!0}),ge("streamableHttp")),d(!0)},je=function(){var e=u()(i()().mark((function e(r){var t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,A(r.id);case 3:(t=e.sent).success&&t.data&&(ue(t.data),K(!0)),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取服务详情失败",e.t0),k.ZP.error("获取服务详情失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(r){return e.apply(this,arguments)}}(),ke=function(){var e=u()(i()().mark((function e(){var t,a,s,o,c,l;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,n.validateFields();case 3:if(t=e.sent,fe(!0),!(a=p()({},t)).headers||"string"!=typeof a.headers){e.next=21;break}if(e.prev=7,s=JSON.parse(a.headers),Object.entries(s).every((function(e){var r=f()(e,2),t=r[0],n=r[1];return"string"==typeof t&&("string"==typeof n||null==n)}))){e.next=13;break}return k.ZP.error("请求头格式不正确，所有键和值都必须是字符串"),fe(!1),e.abrupt("return");case 13:a.headers=s,e.next=21;break;case 16:return e.prev=16,e.t0=e.catch(7),k.ZP.error('请求头格式不正确，应为有效的JSON对象，例如: {"Content-Type": "application/json"}'),fe(!1),e.abrupt("return");case 21:if(!a.env_vars||"string"!=typeof a.env_vars){e.next=36;break}if(e.prev=22,o=JSON.parse(a.env_vars),Object.entries(o).every((function(e){var r=f()(e,2),t=r[0],n=r[1];return"string"==typeof t&&("string"==typeof n||null==n)}))){e.next=28;break}return k.ZP.error("环境变量格式不正确，所有键和值都必须是字符串"),fe(!1),e.abrupt("return");case 28:a.env_vars=o,e.next=36;break;case 31:return e.prev=31,e.t1=e.catch(22),k.ZP.error('环境变量格式不正确，应为有效的JSON对象，例如: {"API_KEY": "your-api-key"}'),fe(!1),e.abrupt("return");case 36:if(!R){e.next=42;break}return e.next=39,D(R.id,a);case 39:c=e.sent,e.next=45;break;case 42:return e.next=44,W(a);case 44:c=e.sent;case 45:c.success?(k.ZP.success("".concat(R?"更新":"创建","MCP服务成功")),d(!1),n.resetFields(),null===(l=r.current)||void 0===l||l.reload()):k.ZP.error(c.message||"".concat(R?"更新":"创建","MCP服务失败")),e.next=52;break;case 48:e.prev=48,e.t2=e.catch(0),console.error("表单验证或提交失败:",e.t2),k.ZP.error("".concat(R?"更新":"创建","MCP服务失败"));case 52:return e.prev=52,fe(!1),e.finish(52);case 55:case"end":return e.stop()}}),e,null,[[0,48,52,55],[7,16],[22,31]])})));return function(){return e.apply(this,arguments)}}(),Ze=function(){var e=u()(i()().mark((function e(t){var n,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,fe(!0),e.next=4,Q(t);case 4:(n=e.sent).success?(k.ZP.success("复制MCP服务成功"),null===(a=r.current)||void 0===a||a.reload()):k.ZP.error(n.message||"复制MCP服务失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("复制失败:",e.t0),k.ZP.error("复制MCP服务失败");case 12:return e.prev=12,fe(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})));return function(r){return e.apply(this,arguments)}}(),Ce=function(){var e=u()(i()().mark((function e(t,n){var a,s;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,fe(!0),e.next=4,D(t,{is_active:!n});case 4:(a=e.sent).success?(k.ZP.success("".concat(n?"禁用":"启用","MCP服务成功")),null===(s=r.current)||void 0===s||s.reload()):k.ZP.error(a.message||"".concat(n?"禁用":"启用","MCP服务失败")),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("状态更新失败:",e.t0),k.ZP.error("".concat(n?"禁用":"启用","MCP服务失败"));case 12:return e.prev=12,fe(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})));return function(r,t){return e.apply(this,arguments)}}(),Pe=[{title:"服务名称",dataIndex:"name",valueType:"text",render:function(e,r){return(0,ne.jsx)("a",{onClick:function(){return je(r)},children:r.name})}},{title:"服务类型",dataIndex:"type",valueType:"select",width:120,render:function(e,r){var t=re.find((function(e){return e.value===r.type}));return(0,ne.jsx)(Z.Z,{color:"blue",children:t?"".concat(t.icon," ").concat(t.label):r.type})},valueEnum:re.reduce((function(e,r){return p()(p()({},e),{},o()({},r.value,{text:r.label}))}),{})},{title:"标签",dataIndex:"tags",valueType:"text",render:function(e,r){return(0,ne.jsx)(C.Z,{children:r.tags&&r.tags.map((function(e){return(0,ne.jsx)(Z.Z,{color:"green",children:e},e)}))})}},{title:"创建者",dataIndex:"user_name",valueType:"text",width:100,fieldProps:{placeholder:"请输入创建者"}},{title:"状态",dataIndex:"is_active",valueType:"select",width:80,render:function(e,r){return(0,ne.jsx)(Z.Z,{color:r.is_active?"success":"error",children:r.is_active?"已启用":"已禁用"})},valueEnum:{true:{text:"已启用",status:"Success"},false:{text:"已禁用",status:"Error"}}},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",sorter:!0,width:160,hideInSearch:!0},{title:"操作",dataIndex:"option",valueType:"option",width:240,render:function(e,t){return[(0,ne.jsx)(P.ZP,{type:"link",icon:(0,ne.jsx)(E.Z,{}),onClick:function(){return be(t)},children:"编辑"},"edit"),(0,ne.jsx)(P.ZP,{type:"link",icon:(0,ne.jsx)(_.Z,{}),onClick:function(){return Ze(t.id)},children:"复制"},"copy"),(0,ne.jsx)(P.ZP,{type:"link",icon:t.is_active?(0,ne.jsx)($.Z,{}):(0,ne.jsx)(z.Z,{}),danger:t.is_active,onClick:function(){return Ce(t.id,t.is_active)},children:t.is_active?"禁用":"启用"},"status"),(0,ne.jsx)(P.ZP,{type:"link",danger:!0,icon:(0,ne.jsx)(B.Z,{}),onClick:function(){return e=t.id,void le({title:"确认删除服务",content:"您确定要删除这个MCP服务吗？此操作无法撤销。",okText:"确定删除",cancelText:"取消",okButtonProps:{danger:!0},onOk:(n=u()(i()().mark((function t(){var n,a;return i()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,fe(!0),t.next=4,X(e);case 4:(n=t.sent).success?(k.ZP.success("删除MCP服务成功"),null===(a=r.current)||void 0===a||a.reload()):k.ZP.error(n.message||"删除MCP服务失败"),t.next=12;break;case 8:t.prev=8,t.t0=t.catch(0),console.error("删除失败:",t.t0),k.ZP.error("删除MCP服务失败");case 12:return t.prev=12,fe(!1),t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[0,8,12,15]])}))),function(){return n.apply(this,arguments)})});var e,n},children:"删除"},"delete")]}}];return(0,ne.jsxs)(m._z,{children:[(0,ne.jsx)(v.Z,{headerTitle:"MCP服务管理",actionRef:r,rowKey:"id",search:{labelWidth:"auto",defaultCollapsed:!1},toolBarRender:function(){return[(0,ne.jsx)(P.ZP,{type:"primary",onClick:function(){return be()},icon:(0,ne.jsx)(M.Z,{}),children:"新建MCP服务"},"create")]},request:u()(i()().mark((function e(){var r,t,n,s,o,c,l=arguments;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=l.length>0&&void 0!==l[0]?l[0]:{},t=r.current,n=r.pageSize,s=r.is_system,o=a()(r,ae),e.prev=2,void 0!==s){e.next=9;break}return e.next=6,F(p()({current:t,pageSize:n},o));case 6:c=e.sent,e.next=18;break;case 9:if("true"!==s){e.next=15;break}return e.next=12,q(p()({current:t,pageSize:n},o));case 12:c=e.sent,e.next=18;break;case 15:return e.next=17,H(p()({current:t,pageSize:n},o));case 17:c=e.sent;case 18:return e.abrupt("return",{data:c.data||[],success:c.success,total:c.total||0});case 21:return e.prev=21,e.t0=e.catch(2),console.error("获取MCP服务失败:",e.t0),k.ZP.error("获取MCP服务列表失败"),e.abrupt("return",{data:[],success:!1,total:0});case 26:case"end":return e.stop()}}),e,null,[[2,21]])}))),columns:Pe,rowSelection:{selections:[S.Z.SELECTION_ALL,S.Z.SELECTION_INVERT]},pagination:{showQuickJumper:!0,showSizeChanger:!0}}),(0,ne.jsx)(b.Z,{title:R?"编辑MCP服务":"新建MCP服务",open:l,onCancel:function(){return d(!1)},destroyOnClose:!0,footer:[(0,ne.jsx)(P.ZP,{onClick:function(){return d(!1)},children:"取消"},"cancel"),(0,ne.jsx)(P.ZP,{type:"primary",onClick:ke,loading:he,children:"确定"},"submit")],width:700,children:(0,ne.jsxs)(j.Z,{form:n,layout:"vertical",initialValues:{type:"streamableHttp",tags:[],timeout:60,is_active:!0},children:[(0,ne.jsx)(j.Z.Item,{name:"name",label:"服务名称",rules:[{required:!0,message:"请输入服务名称"}],children:(0,ne.jsx)(y.Z,{placeholder:"请输入服务名称"})}),(0,ne.jsx)(j.Z.Item,{name:"description",label:"服务描述",children:(0,ne.jsx)(ie,{placeholder:"请输入服务描述",rows:3,style:{resize:"none"}})}),(0,ne.jsx)(j.Z.Item,{name:"type",label:"服务类型",rules:[{required:!0,message:"请选择服务类型"}],children:(0,ne.jsx)(O.Z,{activeKey:ve,onChange:function(e){ge(e),n.setFieldsValue({type:e})},items:re.map((function(e){return{label:(0,ne.jsxs)("span",{children:[e.icon," ",e.label]}),key:e.value}}))})}),function(){switch(ve){case"streamableHttp":return(0,ne.jsxs)(ne.Fragment,{children:[(0,ne.jsx)(j.Z.Item,{name:"url",label:"服务URL",rules:[{required:!0,message:"请输入服务URL"}],children:(0,ne.jsx)(y.Z,{placeholder:"请输入服务URL，例如：https://api.example.com/v1/chat"})}),(0,ne.jsx)(j.Z.Item,{name:"headers",label:"请求头",help:"JSON格式，例如：{'Authorization': 'Bearer token', 'Content-Type': 'application/json'}",extra:(0,ne.jsx)("div",{style:{marginTop:"8px"},children:(0,ne.jsx)(P.ZP,{type:"link",style:{padding:0},onClick:function(){return ye(n.getFieldValue("headers"),"请求头")},children:"验证JSON格式"})}),children:(0,ne.jsx)(ie,{placeholder:"请输入请求头，JSON格式",rows:4,style:{resize:"none"}})}),(0,ne.jsx)(j.Z.Item,{name:"transport",label:"传输协议",initialValue:"http",children:(0,ne.jsx)(w.default,{options:[{label:"HTTP",value:"http"},{label:"WebSocket",value:"websocket"}],placeholder:"请选择传输协议"})})]});case"stdio":return(0,ne.jsxs)(ne.Fragment,{children:[(0,ne.jsx)(j.Z.Item,{name:"command",label:"命令",rules:[{required:!0,message:"请输入命令"}],children:(0,ne.jsx)(y.Z,{placeholder:"请输入命令，例如：python"})}),(0,ne.jsx)(j.Z.Item,{name:"args",label:"命令参数",children:(0,ne.jsx)(w.default,{mode:"tags",placeholder:"请输入命令参数，例如：-m script.py",tokenSeparators:[" "]})}),(0,ne.jsx)(j.Z.Item,{name:"key",label:"环境变量",help:"格式为：KEY=VALUE",children:(0,ne.jsx)(w.default,{mode:"tags",placeholder:"请输入环境变量，例如：API_KEY=abc123",tokenSeparators:[" "]})})]});case"sse":return(0,ne.jsxs)(ne.Fragment,{children:[(0,ne.jsx)(j.Z.Item,{name:"url",label:"服务URL",rules:[{required:!0,message:"请输入服务URL"}],children:(0,ne.jsx)(y.Z,{placeholder:"请输入服务URL，例如：https://api.example.com/v1/events"})}),(0,ne.jsx)(j.Z.Item,{name:"headers",label:"请求头",help:"JSON格式，例如：{'Authorization': 'Bearer token', 'Content-Type': 'application/json'}",extra:(0,ne.jsx)("div",{style:{marginTop:"8px"},children:(0,ne.jsx)(P.ZP,{type:"link",style:{padding:0},onClick:function(){return ye(n.getFieldValue("headers"),"请求头")},children:"验证JSON格式"})}),children:(0,ne.jsx)(ie,{placeholder:"请输入请求头，JSON格式",rows:4,style:{resize:"none"}})})]});default:return null}}(),(0,ne.jsx)(j.Z.Item,{name:"tags",label:"标签",help:"用于分类和筛选服务",children:(0,ne.jsx)(w.default,{mode:"tags",options:te.map((function(e){return{label:e,value:e}})),placeholder:"请选择或输入标签"})}),(0,ne.jsx)(j.Z.Item,{name:"timeout",label:"超时设置(秒)",rules:[{required:!0,message:"请输入超时设置"}],children:(0,ne.jsx)(T.Z,{min:1,max:300,placeholder:"请输入超时设置",style:{width:"100%"}})}),(0,ne.jsx)("div",{style:{display:"flex",gap:"16px"},children:(0,ne.jsx)(j.Z.Item,{name:"is_active",label:"是否启用",valuePropName:"checked",children:(0,ne.jsx)(I.Z,{checkedChildren:"启用",unCheckedChildren:"禁用",defaultChecked:!0})})})]})}),ee&&(0,ne.jsxs)(b.Z,{title:(0,ne.jsx)(se,{level:5,style:{margin:0},children:ee.name}),open:V,onCancel:function(){K(!1),ue(null)},width:700,footer:[(0,ne.jsx)(P.ZP,{icon:(0,ne.jsx)(_.Z,{}),onClick:function(){return Ze(ee.id)},children:"复制服务"},"copy"),(0,ne.jsx)(P.ZP,{type:"primary",onClick:function(){K(!1),ue(null)},children:"关闭"},"close")],children:[(0,ne.jsx)("div",{style:{marginBottom:"20px"},children:(0,ne.jsxs)("div",{style:{display:"flex",flexWrap:"wrap",gap:"16px",marginBottom:"16px"},children:[(0,ne.jsxs)("div",{children:[(0,ne.jsx)(oe,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"服务类型"}),(0,ne.jsx)("div",{children:(e=re.find((function(e){return e.value===ee.type})),(0,ne.jsx)(Z.Z,{color:"blue",children:e?"".concat(e.icon," ").concat(e.label):ee.type}))})]}),(0,ne.jsxs)("div",{children:[(0,ne.jsx)(oe,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"标签"}),(0,ne.jsx)("div",{children:ee.tags&&ee.tags.map((function(e){return(0,ne.jsx)(Z.Z,{color:"green",children:e},e)}))})]}),(0,ne.jsxs)("div",{children:[(0,ne.jsx)(oe,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"状态"}),(0,ne.jsx)(Z.Z,{color:ee.is_active?"success":"error",children:ee.is_active?"已启用":"已禁用"})]}),(0,ne.jsxs)("div",{children:[(0,ne.jsx)(oe,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"超时设置"}),(0,ne.jsxs)("span",{children:[ee.timeout,"秒"]})]}),(0,ne.jsxs)("div",{children:[(0,ne.jsx)(oe,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"调用次数"}),(0,ne.jsx)("span",{children:ee.usage_count})]})]})}),(0,ne.jsx)(N.Z,{orientation:"left",children:"服务描述"}),(0,ne.jsx)(ce,{style:{marginBottom:"20px"},children:ee.description||"暂无描述"}),"streamableHttp"===ee.type&&(0,ne.jsxs)(ne.Fragment,{children:[(0,ne.jsx)(N.Z,{orientation:"left",children:"服务URL"}),(0,ne.jsx)(ce,{copyable:!0,style:{wordBreak:"break-all"},children:ee.url}),ee.headers&&Object.keys(ee.headers).length>0&&(0,ne.jsxs)(ne.Fragment,{children:[(0,ne.jsx)(N.Z,{orientation:"left",children:"请求头"}),(0,ne.jsx)("div",{style:{backgroundColor:"#f5f5f5",padding:"10px",borderRadius:"4px"},children:(0,ne.jsx)("pre",{style:{margin:0},children:JSON.stringify(ee.headers,null,2)})})]})]}),"stdio"===ee.type&&(0,ne.jsxs)(ne.Fragment,{children:[(0,ne.jsx)(N.Z,{orientation:"left",children:"命令"}),(0,ne.jsx)(ce,{copyable:!0,children:ee.command}),ee.args&&ee.args.length>0&&(0,ne.jsxs)(ne.Fragment,{children:[(0,ne.jsx)(N.Z,{orientation:"left",children:"命令参数"}),(0,ne.jsx)("div",{style:{backgroundColor:"#f5f5f5",padding:"10px",borderRadius:"4px"},children:(0,ne.jsx)("pre",{style:{margin:0},children:ee.args.join(" ")})})]}),ee.key&&ee.key.length>0&&(0,ne.jsxs)(ne.Fragment,{children:[(0,ne.jsx)(N.Z,{orientation:"left",children:"环境变量"}),(0,ne.jsx)("div",{style:{backgroundColor:"#f5f5f5",padding:"10px",borderRadius:"4px"},children:(0,ne.jsx)("pre",{style:{margin:0},children:ee.key.join("\n")})})]})]}),"sse"===ee.type&&(0,ne.jsxs)(ne.Fragment,{children:[(0,ne.jsx)(N.Z,{orientation:"left",children:"服务URL"}),(0,ne.jsx)(ce,{copyable:!0,style:{wordBreak:"break-all"},children:ee.url}),ee.headers&&Object.keys(ee.headers).length>0&&(0,ne.jsxs)(ne.Fragment,{children:[(0,ne.jsx)(N.Z,{orientation:"left",children:"请求头"}),(0,ne.jsx)("div",{style:{backgroundColor:"#f5f5f5",padding:"10px",borderRadius:"4px"},children:(0,ne.jsx)("pre",{style:{margin:0},children:JSON.stringify(ee.headers,null,2)})})]})]}),(0,ne.jsxs)("div",{style:{marginTop:20,paddingTop:16,borderTop:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between"},children:[(0,ne.jsx)("div",{children:(0,ne.jsxs)(oe,{type:"secondary",style:{fontSize:"12px"},children:["创建时间: ",new Date(ee.created_at).toLocaleString()]})}),(0,ne.jsxs)(oe,{type:"secondary",style:{fontSize:"12px"},children:["创建者: ",ee.user_name||"系统"]})]})]})]})}},66309:function(e,r,t){t.d(r,{Z:function(){return I}});var n=t(67294),a=t(93967),s=t.n(a),o=t(98423),c=t(98787),i=t(69760),l=t(96159),u=t(45353),d=t(53124),p=t(11568),h=t(15063),f=t(14747),x=t(83262),m=t(83559);const v=e=>{const{lineWidth:r,fontSizeIcon:t,calc:n}=e,a=e.fontSizeSM;return(0,x.IX)(e,{tagFontSize:a,tagLineHeight:(0,p.bf)(n(e.lineHeightSM).mul(a).equal()),tagIconSize:n(t).sub(n(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},g=e=>({defaultBg:new h.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var y=(0,m.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:t,tagPaddingHorizontal:n,componentCls:a,calc:s}=e,o=s(n).sub(t).equal(),c=s(r).sub(t).equal();return{[a]:Object.assign(Object.assign({},(0,f.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:o,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${a}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${a}-close-icon`]:{marginInlineStart:c,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${a}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${a}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:o}}),[`${a}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(v(e))),g),b=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)r.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(t[n[a]]=e[n[a]])}return t};const j=n.forwardRef(((e,r)=>{const{prefixCls:t,style:a,className:o,checked:c,onChange:i,onClick:l}=e,u=b(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:h}=n.useContext(d.E_),f=p("tag",t),[x,m,v]=y(f),g=s()(f,`${f}-checkable`,{[`${f}-checkable-checked`]:c},null==h?void 0:h.className,o,m,v);return x(n.createElement("span",Object.assign({},u,{ref:r,style:Object.assign(Object.assign({},a),null==h?void 0:h.style),className:g,onClick:e=>{null==i||i(!c),null==l||l(e)}})))}));var k=j,Z=t(98719);var C=(0,m.bk)(["Tag","preset"],(e=>(e=>(0,Z.Z)(e,((r,t)=>{let{textColor:n,lightBorderColor:a,lightColor:s,darkColor:o}=t;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:n,background:s,borderColor:a,"&-inverse":{color:e.colorTextLightSolid,background:o,borderColor:o},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(v(e))),g);const P=(e,r,t)=>{const n="string"!=typeof(a=t)?a:a.charAt(0).toUpperCase()+a.slice(1);var a;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${t}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var w=(0,m.bk)(["Tag","status"],(e=>{const r=v(e);return[P(r,"success","Success"),P(r,"processing","Info"),P(r,"error","Error"),P(r,"warning","Warning")]}),g),S=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)r.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(t[n[a]]=e[n[a]])}return t};const O=n.forwardRef(((e,r)=>{const{prefixCls:t,className:a,rootClassName:p,style:h,children:f,icon:x,color:m,onClose:v,bordered:g=!0,visible:b}=e,j=S(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:k,direction:Z,tag:P}=n.useContext(d.E_),[O,T]=n.useState(!0),I=(0,o.Z)(j,["closeIcon","closable"]);n.useEffect((()=>{void 0!==b&&T(b)}),[b]);const N=(0,c.o2)(m),E=(0,c.yT)(m),_=N||E,$=Object.assign(Object.assign({backgroundColor:m&&!_?m:void 0},null==P?void 0:P.style),h),z=k("tag",t),[B,M,R]=y(z),F=s()(z,null==P?void 0:P.className,{[`${z}-${m}`]:_,[`${z}-has-color`]:m&&!_,[`${z}-hidden`]:!O,[`${z}-rtl`]:"rtl"===Z,[`${z}-borderless`]:!g},a,p,M,R),L=e=>{e.stopPropagation(),null==v||v(e),e.defaultPrevented||T(!1)},[,H]=(0,i.Z)((0,i.w)(e),(0,i.w)(P),{closable:!1,closeIconRender:e=>{const r=n.createElement("span",{className:`${z}-close-icon`,onClick:L},e);return(0,l.wm)(e,r,(e=>({onClick:r=>{var t;null===(t=null==e?void 0:e.onClick)||void 0===t||t.call(e,r),L(r)},className:s()(null==e?void 0:e.className,`${z}-close-icon`)})))}}),J="function"==typeof j.onClick||f&&"a"===f.type,q=x||null,U=q?n.createElement(n.Fragment,null,q,f&&n.createElement("span",null,f)):f,A=n.createElement("span",Object.assign({},I,{ref:r,className:F,style:$}),U,H,N&&n.createElement(C,{key:"preset",prefixCls:z}),E&&n.createElement(w,{key:"status",prefixCls:z}));return B(J?n.createElement(u.Z,{component:"Tag"},A):A)})),T=O;T.CheckableTag=k;var I=T}}]);