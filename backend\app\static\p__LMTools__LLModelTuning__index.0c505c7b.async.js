"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3163],{51042:function(e,t,n){var s=n(1413),a=n(67294),i=n(42110),r=n(91146),l=function(e,t){return a.createElement(r.Z,(0,s.Z)((0,s.Z)({},e),{},{ref:t,icon:i.Z}))},d=a.forwardRef(l);t.Z=d},97710:function(e,t,n){n.r(t),n.d(t,{default:function(){return R}});var s=n(15009),a=n.n(s),i=(n(97857),n(99289)),r=n.n(i),l=n(5574),d=n.n(l),c=n(67294),o=(n(2453),n(83622)),u=n(85265),h=n(11941),x=n(47019),p=n(55102),m=n(34041),j=n(96074),g=n(71230),f=n(15746),Z=n(13457),y=n(17788),b=n(97131),k=n(12453),I=n(51042),w=n(76772);n(78158);var v=n(85893),R=function(){var e=(0,w.useIntl)(),t=(0,c.useRef)(),n=((0,c.useRef)(),(0,c.useState)(!1)),s=d()(n,2),i=(s[0],s[1],(0,c.useState)(!1)),l=d()(i,2),R=l[0],T=l[1],S=(0,c.useState)(null),M=d()(S,2),z=M[0],C=M[1],A=(0,c.useState)(!1),Q=d()(A,2),B=Q[0],q=Q[1],L=[{title:e.formatMessage({id:"pages.tuning.columns.taskId"}),dataIndex:"taskId",valueType:"text"},{title:e.formatMessage({id:"pages.tuning.columns.model"}),dataIndex:"model",valueType:"text"},{title:"数据集",dataIndex:"dataset",valueType:"text"},{title:"消耗 tokens",dataIndex:"tokens",search:!1},{title:"费用",dataIndex:"cost",valueType:"money",search:!1},{title:"状态",dataIndex:"status",valueType:"text"},{title:"时间",dataIndex:"time",valueType:"dateTime",search:!1},{title:e.formatMessage({id:"pages.tuning.columns.actions"}),dataIndex:"option",valueType:"option",render:function(t,n){return[(0,v.jsx)(o.ZP,{type:"link",onClick:function(){return function(e){C(e),T(!0)}(n)},children:e.formatMessage({id:"pages.tuning.button.preview"})},"preview-".concat(n.id)),(0,v.jsx)(o.ZP,{type:"link",children:e.formatMessage({id:"pages.tuning.button.edit"})},"edit-".concat(n.id)),(0,v.jsx)(o.ZP,{type:"link",danger:!0,children:e.formatMessage({id:"pages.tuning.button.delete"})},"delete-".concat(n.id))]}}];return(0,v.jsxs)(b._z,{children:[(0,v.jsx)(k.Z,{headerTitle:e.formatMessage({id:"pages.tuning.list.title"}),actionRef:t,rowKey:"id",search:{labelWidth:120},toolBarRender:function(){return[(0,v.jsxs)(o.ZP,{type:"primary",onClick:function(){return q(!0)},children:[(0,v.jsx)(I.Z,{})," ",e.formatMessage({id:"pages.tuning.button.create"})]},"primary")]},request:function(){var e=r()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{data:[{id:1,taskId:"pzpvb...xhtrmx",model:"Qwen/Qwen2.5-7B-Instruct",dataset:"SiliconCloud数据集",tokens:10866,cost:"¥0.0380",status:"已完成",time:"2025-01-10 10:35:32",epochs:3,batchSize:8,learningRate:1e-4,tunedModel:"ftLoRA/Qwen/Qwen2.5-7B-Instructp99shqc5wj:QwenA3pzpvbernuexesxyxhtrmx",startTime:"2025-01-10 10:35:32",endTime:"2025-01-10 10:38:28"}],success:!0,total:1});case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),columns:L,pagination:{pageSize:10}}),(0,v.jsx)(u.Z,{title:e.formatMessage({id:"pages.tuning.drawer.title"}),width:720,onClose:function(){return q(!1)},open:B,bodyStyle:{paddingBottom:80},children:(0,v.jsx)(h.Z,{defaultActiveKey:"1",children:(0,v.jsx)(h.Z.TabPane,{tab:e.formatMessage({id:"pages.tuning.tabs.parameters"}),children:(0,v.jsxs)(x.Z,{layout:"vertical",hideRequiredMark:!0,initialValues:{learningRate:1e-4,epochs:3,batchSize:8,loraRank:8,loraAlpha:32,loraDropout:.05,maxTokens:32768},children:[(0,v.jsx)(x.Z.Item,{name:"taskName",label:"任务名称",rules:[{required:!0,message:"请输入任务名称"}],children:(0,v.jsx)(p.Z,{placeholder:"请输入任务名称"})}),(0,v.jsx)(x.Z.Item,{name:"model",label:"微调模型",rules:[{required:!0,message:"请选择模型"}],children:(0,v.jsx)(m.default,{placeholder:"请选择模型",children:(0,v.jsx)(m.default.Option,{value:"Qwen/Qwen2.5-7B-Instruct",children:"Qwen/Qwen2.5-7B-Instruct"})})}),(0,v.jsx)(x.Z.Item,{name:"dataset",label:"微调数据集",rules:[{required:!0,message:"请选择数据集"}],children:(0,v.jsx)(m.default,{placeholder:"请选择数据集",children:(0,v.jsx)(m.default.Option,{value:"SiliconCloud数据集",children:"SiliconCloud数据集"})})}),(0,v.jsx)(j.Z,{}),(0,v.jsxs)(g.Z,{gutter:16,children:[(0,v.jsx)(f.Z,{span:12,children:(0,v.jsx)(x.Z.Item,{name:"learningRate",label:"Learning Rate (0, 0.1)",children:(0,v.jsx)(Z.Z,{min:0,max:.1,step:1e-4,style:{width:"100%"}})})}),(0,v.jsx)(f.Z,{span:12,children:(0,v.jsx)(x.Z.Item,{name:"epochs",label:"Number of Epochs [1, 10]",children:(0,v.jsx)(Z.Z,{min:1,max:10,style:{width:"100%"}})})})]}),(0,v.jsxs)(g.Z,{gutter:16,children:[(0,v.jsx)(f.Z,{span:12,children:(0,v.jsx)(x.Z.Item,{name:"batchSize",label:"Batch Size",children:(0,v.jsx)(Z.Z,{min:1,style:{width:"100%"}})})}),(0,v.jsx)(f.Z,{span:12,children:(0,v.jsx)(x.Z.Item,{name:"loraRank",label:"LoRA Rank",children:(0,v.jsx)(Z.Z,{min:1,style:{width:"100%"}})})})]}),(0,v.jsxs)(g.Z,{gutter:16,children:[(0,v.jsx)(f.Z,{span:12,children:(0,v.jsx)(x.Z.Item,{name:"loraAlpha",label:"LoRA Alpha",children:(0,v.jsx)(Z.Z,{min:1,style:{width:"100%"}})})}),(0,v.jsx)(f.Z,{span:12,children:(0,v.jsx)(x.Z.Item,{name:"loraDropout",label:"LoRA Dropout (0, 1.0)",children:(0,v.jsx)(Z.Z,{min:0,max:1,step:.01,style:{width:"100%"}})})})]}),(0,v.jsx)(x.Z.Item,{name:"maxTokens",label:"Max Tokens (0, 32768)",children:(0,v.jsx)(Z.Z,{min:0,max:32768,style:{width:"100%"}})}),(0,v.jsx)(o.ZP,{type:"primary",onClick:function(){q(!1)},style:{marginTop:16},children:"开始微调"})]})},"1")})}),(0,v.jsx)(y.Z,{visible:R,title:"参数预览",onCancel:function(){return T(!1)},footer:null,children:z&&(0,v.jsxs)("div",{style:{lineHeight:"1.6",padding:"10px"},children:[(0,v.jsxs)("p",{children:[(0,v.jsx)("strong",{children:"任务 ID:"})," ",z.taskId]}),(0,v.jsxs)("p",{children:[(0,v.jsx)("strong",{children:"模型:"})," ",z.model]}),(0,v.jsxs)("p",{children:[(0,v.jsx)("strong",{children:"数据集:"})," ",z.dataset]}),(0,v.jsxs)("p",{children:[(0,v.jsx)("strong",{children:"消耗 tokens:"})," ",z.tokens]}),(0,v.jsxs)("p",{children:[(0,v.jsx)("strong",{children:"费用:"})," ",z.cost]}),(0,v.jsxs)("p",{children:[(0,v.jsx)("strong",{children:"状态:"})," ",z.status]}),(0,v.jsxs)("p",{children:[(0,v.jsx)("strong",{children:"时间:"})," ",z.time]}),(0,v.jsxs)("p",{children:[(0,v.jsx)("strong",{children:"Number of Epochs:"})," ",z.epochs]}),(0,v.jsxs)("p",{children:[(0,v.jsx)("strong",{children:"Batch Size:"})," ",z.batchSize]}),(0,v.jsxs)("p",{children:[(0,v.jsx)("strong",{children:"Learning Rate:"})," ",z.learningRate]}),(0,v.jsxs)("p",{children:[(0,v.jsx)("strong",{children:"微调模型:"})," ",z.tunedModel]}),(0,v.jsxs)("p",{children:[(0,v.jsx)("strong",{children:"开始时间:"})," ",z.startTime]}),(0,v.jsxs)("p",{children:[(0,v.jsx)("strong",{children:"结束时间:"})," ",z.endTime]})]})})]})}}}]);