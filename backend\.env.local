# 基础配置
SECRET_KEY=dev-secret-key
ACCESS_TOKEN_EXPIRE_SECONDS=360000

# MongoDB配置
MONGODB_USER=memInterview
MONGODB_PASSWORD=memInterview@202408
DATABASE_NAME=roardataAiApp_test
MONGODB_AUTH_SOURCE=admin
MONGODB_HOST=kgweb.roardata.cn
MONGODB_PORT=37017

# 文件上传配置
FILE_UPLOAD_PATH=./static/uploads

# MinIO配置
MINIO_ENABLE=false
MINIO_ENDPOINT=**************:9002
MINIO_ACCESS_KEY=qFHq6y3pNfboA7YBNynE
MINIO_SECRET_KEY=P5lTULcF2IEtX47mkdmfuDpQVkYJEeL2AKEhvDRr
MINIO_BUCKET=wiseagentfiles

# Elasticsearch配置
ES_HOST=[{"host": "**************", "port": 9600},{"host": "**************", "port": 9600}]
ES_INDEX=wise_agent_chunk_index

# 开发环境特定配置
DEBUG=true
RELOAD=true
LOG_LEVEL=debug

# 索引器配置
INDEXER_ENABLED=false

# Milvus配置
MILVUS_HOST="http://localhost:19530"
MILVUS_COLLECTION_NAME="wise_agent_chunking"
MILVUS_EMBEDDING_FIELD="embedding"

# COE引擎配置
COE_ENGINE_ENABLED=true

# 开发环境特定配置 CRITICAL ERROR WARNING INFO DEBUG
DEBUG=true
RELOAD=true
LOG_LEVEL=INFO

TOKENS_COUNT_MODEL=gpt-4

# wiseflow 配置
enable_wiseflow=True
wiseflow_url="http://localhost:7860/api/v1/wiseagentauth"

# OpenAI 配置
OPENAI_API_KEY=sk-a1a653d74b6d41f6a024d05e65b68865
OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
OPENAI_MODEL=qwen-plus
OPENAI_TEMPERATURE=0.1
OPENAI_MAX_TOKENS=8000

# Neo4j 配置
GRAPH_RAG=False
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password
NEO4J_DATABASE=neo4j

# 图查询配置
GRAPH_QUERY_MAX_DEPTH=3
GRAPH_QUERY_MAX_RESULTS=50
GRAPH_QUERY_SIMILARITY_THRESHOLD=0.7

# MCP配置
MCP_WEBSEARCH_URL=http://*************:8004
MCP_WEBSEARCH_TRANSPORT=sse
MCP_ENABLED=true