"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1941],{42110:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"}},11941:function(e,t,n){n.d(t,{Z:function(){return Re}});var a=n(67294),o=n(62208),r=n(48001),i=n(87462),l=n(42110),c=n(93771),d=function(e,t){return a.createElement(c.Z,(0,i.Z)({},e,{ref:t,icon:l.Z}))};var s=a.forwardRef(d),u=n(93967),f=n.n(u),v=n(4942),b=n(1413),p=n(97685),m=n(71002),h=n(91),g=n(21770),$=n(31131),y=(0,a.createContext)(null),k=n(74902),w=n(9220),x=n(66680),_=n(42550),S=n(75164),C=function(e){var t=e.activeTabOffset,n=e.horizontal,o=e.rtl,r=e.indicator,i=void 0===r?{}:r,l=i.size,c=i.align,d=void 0===c?"center":c,s=(0,a.useState)(),u=(0,p.Z)(s,2),f=u[0],v=u[1],b=(0,a.useRef)(),m=a.useCallback((function(e){return"function"==typeof l?l(e):"number"==typeof l?l:e}),[l]);function h(){S.Z.cancel(b.current)}return(0,a.useEffect)((function(){var e={};if(t)if(n){e.width=m(t.width);var a=o?"right":"left";"start"===d&&(e[a]=t[a]),"center"===d&&(e[a]=t[a]+t.width/2,e.transform=o?"translateX(50%)":"translateX(-50%)"),"end"===d&&(e[a]=t[a]+t.width,e.transform="translateX(-100%)")}else e.height=m(t.height),"start"===d&&(e.top=t.top),"center"===d&&(e.top=t.top+t.height/2,e.transform="translateY(-50%)"),"end"===d&&(e.top=t.top+t.height,e.transform="translateY(-100%)");return h(),b.current=(0,S.Z)((function(){var t=f&&e&&Object.keys(e).every((function(t){var n=e[t],a=f[t];return"number"==typeof n&&"number"==typeof a?Math.round(n)===Math.round(a):n===a}));t||v(e)})),h}),[JSON.stringify(t),n,o,d,m]),{style:f}},E={width:0,height:0,left:0,top:0};function Z(e,t){var n=a.useRef(e),o=a.useState({}),r=(0,p.Z)(o,2)[1];return[n.current,function(e){var a="function"==typeof e?e(n.current):e;a!==n.current&&t(a,n.current),n.current=a,r({})}]}var P=Math.pow(.995,20);var R=n(8410);function I(e){var t=(0,a.useState)(0),n=(0,p.Z)(t,2),o=n[0],r=n[1],i=(0,a.useRef)(0),l=(0,a.useRef)();return l.current=e,(0,R.o)((function(){var e;null===(e=l.current)||void 0===e||e.call(l)}),[o]),function(){i.current===o&&(i.current+=1,r(i.current))}}var M={width:0,height:0,left:0,top:0,right:0};function T(e){var t;return e instanceof Map?(t={},e.forEach((function(e,n){t[n]=e}))):t=e,JSON.stringify(t)}function L(e){return String(e).replace(/"/g,"TABS_DQ")}function O(e,t,n,a){return!(!n||a||!1===e||void 0===e&&(!1===t||null===t))}var z=a.forwardRef((function(e,t){var n=e.prefixCls,o=e.editable,r=e.locale,i=e.style;return o&&!1!==o.showAdd?a.createElement("button",{ref:t,type:"button",className:"".concat(n,"-nav-add"),style:i,"aria-label":(null==r?void 0:r.addAriaLabel)||"Add tab",onClick:function(e){o.onEdit("add",{event:e})}},o.addIcon||"+"):null})),B=z;var N=a.forwardRef((function(e,t){var n,o=e.position,r=e.prefixCls,i=e.extra;if(!i)return null;var l={};return"object"!==(0,m.Z)(i)||a.isValidElement(i)?l.right=i:l=i,"right"===o&&(n=l.right),"left"===o&&(n=l.left),n?a.createElement("div",{className:"".concat(r,"-extra-content"),ref:t},n):null})),D=n(29171),H=n(72512),j=n(15105),G=a.forwardRef((function(e,t){var n=e.prefixCls,o=e.id,r=e.tabs,l=e.locale,c=e.mobile,d=e.more,s=void 0===d?{}:d,u=e.style,b=e.className,m=e.editable,h=e.tabBarGutter,g=e.rtl,$=e.removeAriaLabel,y=e.onTabClick,k=e.getPopupContainer,w=e.popupClassName,x=(0,a.useState)(!1),_=(0,p.Z)(x,2),S=_[0],C=_[1],E=(0,a.useState)(null),Z=(0,p.Z)(E,2),P=Z[0],R=Z[1],I=s.icon,M=void 0===I?"More":I,T="".concat(o,"-more-popup"),L="".concat(n,"-dropdown"),z=null!==P?"".concat(T,"-").concat(P):null,N=null==l?void 0:l.dropdownAriaLabel;var G=a.createElement(H.ZP,{onClick:function(e){var t=e.key,n=e.domEvent;y(t,n),C(!1)},prefixCls:"".concat(L,"-menu"),id:T,tabIndex:-1,role:"listbox","aria-activedescendant":z,selectedKeys:[P],"aria-label":void 0!==N?N:"expanded dropdown"},r.map((function(e){var t=e.closable,n=e.disabled,r=e.closeIcon,i=e.key,l=e.label,c=O(t,r,m,n);return a.createElement(H.sN,{key:i,id:"".concat(T,"-").concat(i),role:"option","aria-controls":o&&"".concat(o,"-panel-").concat(i),disabled:n},a.createElement("span",null,l),c&&a.createElement("button",{type:"button","aria-label":$||"remove",tabIndex:0,className:"".concat(L,"-menu-item-remove"),onClick:function(e){e.stopPropagation(),function(e,t){e.preventDefault(),e.stopPropagation(),m.onEdit("remove",{key:t,event:e})}(e,i)}},r||m.removeIcon||"×"))})));function A(e){for(var t=r.filter((function(e){return!e.disabled})),n=t.findIndex((function(e){return e.key===P}))||0,a=t.length,o=0;o<a;o+=1){var i=t[n=(n+e+a)%a];if(!i.disabled)return void R(i.key)}}(0,a.useEffect)((function(){var e=document.getElementById(z);e&&e.scrollIntoView&&e.scrollIntoView(!1)}),[P]),(0,a.useEffect)((function(){S||R(null)}),[S]);var W=(0,v.Z)({},g?"marginRight":"marginLeft",h);r.length||(W.visibility="hidden",W.order=1);var X=f()((0,v.Z)({},"".concat(L,"-rtl"),g)),K=c?null:a.createElement(D.Z,(0,i.Z)({prefixCls:L,overlay:G,visible:!!r.length&&S,onVisibleChange:C,overlayClassName:f()(X,w),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:k},s),a.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:W,"aria-haspopup":"listbox","aria-controls":T,id:"".concat(o,"-more"),"aria-expanded":S,onKeyDown:function(e){var t=e.which;if(S)switch(t){case j.Z.UP:A(-1),e.preventDefault();break;case j.Z.DOWN:A(1),e.preventDefault();break;case j.Z.ESC:C(!1);break;case j.Z.SPACE:case j.Z.ENTER:null!==P&&y(P,e)}else[j.Z.DOWN,j.Z.SPACE,j.Z.ENTER].includes(t)&&(C(!0),e.preventDefault())}},M));return a.createElement("div",{className:f()("".concat(n,"-nav-operations"),b),style:u,ref:t},K,a.createElement(B,{prefixCls:n,locale:l,editable:m}))})),A=a.memo(G,(function(e,t){return t.tabMoving})),W=function(e){var t=e.prefixCls,n=e.id,o=e.active,r=e.focus,i=e.tab,l=i.key,c=i.label,d=i.disabled,s=i.closeIcon,u=i.icon,b=e.closable,p=e.renderWrapper,m=e.removeAriaLabel,h=e.editable,g=e.onClick,$=e.onFocus,y=e.onBlur,k=e.onKeyDown,w=e.onMouseDown,x=e.onMouseUp,_=e.style,S=e.tabCount,C=e.currentPosition,E="".concat(t,"-tab"),Z=O(b,s,h,d);function P(e){d||g(e)}var R=a.useMemo((function(){return u&&"string"==typeof c?a.createElement("span",null,c):c}),[c,u]),I=a.useRef(null);a.useEffect((function(){r&&I.current&&I.current.focus()}),[r]);var M=a.createElement("div",{key:l,"data-node-key":L(l),className:f()(E,(0,v.Z)((0,v.Z)((0,v.Z)((0,v.Z)({},"".concat(E,"-with-remove"),Z),"".concat(E,"-active"),o),"".concat(E,"-disabled"),d),"".concat(E,"-focus"),r)),style:_,onClick:P},a.createElement("div",{ref:I,role:"tab","aria-selected":o,id:n&&"".concat(n,"-tab-").concat(l),className:"".concat(E,"-btn"),"aria-controls":n&&"".concat(n,"-panel-").concat(l),"aria-disabled":d,tabIndex:d?null:o?0:-1,onClick:function(e){e.stopPropagation(),P(e)},onKeyDown:k,onMouseDown:w,onMouseUp:x,onFocus:$,onBlur:y},r&&a.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(C," of ").concat(S)),u&&a.createElement("span",{className:"".concat(E,"-icon")},u),c&&R),Z&&a.createElement("button",{type:"button",role:"tab","aria-label":m||"remove",tabIndex:o?0:-1,className:"".concat(E,"-remove"),onClick:function(e){var t;e.stopPropagation(),(t=e).preventDefault(),t.stopPropagation(),h.onEdit("remove",{key:l,event:t})}},s||h.removeIcon||"×"));return p?p(M):M},X=function(e){var t=e.current||{},n=t.offsetWidth,a=void 0===n?0:n,o=t.offsetHeight,r=void 0===o?0:o;if(e.current){var i=e.current.getBoundingClientRect(),l=i.width,c=i.height;if(Math.abs(l-a)<1)return[l,c]}return[a,r]},K=function(e,t){return e[t?0:1]},q=a.forwardRef((function(e,t){var n=e.className,o=e.style,r=e.id,l=e.animated,c=e.activeKey,d=e.rtl,s=e.extra,u=e.editable,m=e.locale,h=e.tabPosition,g=e.tabBarGutter,$=e.children,S=e.onTabClick,R=e.onTabScroll,z=e.indicator,D=a.useContext(y),H=D.prefixCls,j=D.tabs,G=(0,a.useRef)(null),q=(0,a.useRef)(null),F=(0,a.useRef)(null),V=(0,a.useRef)(null),Y=(0,a.useRef)(null),U=(0,a.useRef)(null),Q=(0,a.useRef)(null),J="top"===h||"bottom"===h,ee=Z(0,(function(e,t){J&&R&&R({direction:e>t?"left":"right"})})),te=(0,p.Z)(ee,2),ne=te[0],ae=te[1],oe=Z(0,(function(e,t){!J&&R&&R({direction:e>t?"top":"bottom"})})),re=(0,p.Z)(oe,2),ie=re[0],le=re[1],ce=(0,a.useState)([0,0]),de=(0,p.Z)(ce,2),se=de[0],ue=de[1],fe=(0,a.useState)([0,0]),ve=(0,p.Z)(fe,2),be=ve[0],pe=ve[1],me=(0,a.useState)([0,0]),he=(0,p.Z)(me,2),ge=he[0],$e=he[1],ye=(0,a.useState)([0,0]),ke=(0,p.Z)(ye,2),we=ke[0],xe=ke[1],_e=function(e){var t=(0,a.useRef)([]),n=(0,a.useState)({}),o=(0,p.Z)(n,2)[1],r=(0,a.useRef)("function"==typeof e?e():e),i=I((function(){var e=r.current;t.current.forEach((function(t){e=t(e)})),t.current=[],r.current=e,o({})}));return[r.current,function(e){t.current.push(e),i()}]}(new Map),Se=(0,p.Z)(_e,2),Ce=Se[0],Ee=Se[1],Ze=function(e,t,n){return(0,a.useMemo)((function(){for(var n,a=new Map,o=t.get(null===(n=e[0])||void 0===n?void 0:n.key)||E,r=o.left+o.width,i=0;i<e.length;i+=1){var l,c=e[i].key,d=t.get(c);d||(d=t.get(null===(l=e[i-1])||void 0===l?void 0:l.key)||E);var s=a.get(c)||(0,b.Z)({},d);s.right=r-s.left-s.width,a.set(c,s)}return a}),[e.map((function(e){return e.key})).join("_"),t,n])}(j,Ce,be[0]),Pe=K(se,J),Re=K(be,J),Ie=K(ge,J),Me=K(we,J),Te=Math.floor(Pe)<Math.floor(Re+Ie),Le=Te?Pe-Me:Pe-Ie,Oe="".concat(H,"-nav-operations-hidden"),ze=0,Be=0;function Ne(e){return e<ze?ze:e>Be?Be:e}J&&d?(ze=0,Be=Math.max(0,Re-Le)):(ze=Math.min(0,Le-Re),Be=0);var De=(0,a.useRef)(null),He=(0,a.useState)(),je=(0,p.Z)(He,2),Ge=je[0],Ae=je[1];function We(){Ae(Date.now())}function Xe(){De.current&&clearTimeout(De.current)}!function(e,t){var n=(0,a.useState)(),o=(0,p.Z)(n,2),r=o[0],i=o[1],l=(0,a.useState)(0),c=(0,p.Z)(l,2),d=c[0],s=c[1],u=(0,a.useState)(0),f=(0,p.Z)(u,2),v=f[0],b=f[1],m=(0,a.useState)(),h=(0,p.Z)(m,2),g=h[0],$=h[1],y=(0,a.useRef)(),k=(0,a.useRef)(),w=(0,a.useRef)(null);w.current={onTouchStart:function(e){var t=e.touches[0],n=t.screenX,a=t.screenY;i({x:n,y:a}),window.clearInterval(y.current)},onTouchMove:function(e){if(r){var n=e.touches[0],a=n.screenX,o=n.screenY;i({x:a,y:o});var l=a-r.x,c=o-r.y;t(l,c);var u=Date.now();s(u),b(u-d),$({x:l,y:c})}},onTouchEnd:function(){if(r&&(i(null),$(null),g)){var e=g.x/v,n=g.y/v,a=Math.abs(e),o=Math.abs(n);if(Math.max(a,o)<.1)return;var l=e,c=n;y.current=window.setInterval((function(){Math.abs(l)<.01&&Math.abs(c)<.01?window.clearInterval(y.current):t(20*(l*=P),20*(c*=P))}),20)}},onWheel:function(e){var n=e.deltaX,a=e.deltaY,o=0,r=Math.abs(n),i=Math.abs(a);r===i?o="x"===k.current?n:a:r>i?(o=n,k.current="x"):(o=a,k.current="y"),t(-o,-o)&&e.preventDefault()}},a.useEffect((function(){function t(e){w.current.onTouchMove(e)}function n(e){w.current.onTouchEnd(e)}return document.addEventListener("touchmove",t,{passive:!1}),document.addEventListener("touchend",n,{passive:!0}),e.current.addEventListener("touchstart",(function(e){w.current.onTouchStart(e)}),{passive:!0}),e.current.addEventListener("wheel",(function(e){w.current.onWheel(e)}),{passive:!1}),function(){document.removeEventListener("touchmove",t),document.removeEventListener("touchend",n)}}),[])}(V,(function(e,t){function n(e,t){e((function(e){return Ne(e+t)}))}return!!Te&&(J?n(ae,e):n(le,t),Xe(),We(),!0)})),(0,a.useEffect)((function(){return Xe(),Ge&&(De.current=setTimeout((function(){Ae(0)}),100)),Xe}),[Ge]);var Ke=function(e,t,n,o,r,i,l){var c,d,s,u=l.tabs,f=l.tabPosition,v=l.rtl;return["top","bottom"].includes(f)?(c="width",d=v?"right":"left",s=Math.abs(n)):(c="height",d="top",s=-n),(0,a.useMemo)((function(){if(!u.length)return[0,0];for(var n=u.length,a=n,o=0;o<n;o+=1){var r=e.get(u[o].key)||M;if(Math.floor(r[d]+r[c])>Math.floor(s+t)){a=o-1;break}}for(var i=0,l=n-1;l>=0;l-=1)if((e.get(u[l].key)||M)[d]<s){i=l+1;break}return i>=a?[0,0]:[i,a]}),[e,t,o,r,i,s,f,u.map((function(e){return e.key})).join("_"),v])}(Ze,Le,J?ne:ie,Re,Ie,Me,(0,b.Z)((0,b.Z)({},e),{},{tabs:j})),qe=(0,p.Z)(Ke,2),Fe=qe[0],Ve=qe[1],Ye=(0,x.Z)((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=Ze.get(e)||{width:0,height:0,left:0,right:0,top:0};if(J){var n=ne;d?t.right<ne?n=t.right:t.right+t.width>ne+Le&&(n=t.right+t.width-Le):t.left<-ne?n=-t.left:t.left+t.width>-ne+Le&&(n=-(t.left+t.width-Le)),le(0),ae(Ne(n))}else{var a=ie;t.top<-ie?a=-t.top:t.top+t.height>-ie+Le&&(a=-(t.top+t.height-Le)),ae(0),le(Ne(a))}})),Ue=(0,a.useState)(),Qe=(0,p.Z)(Ue,2),Je=Qe[0],et=Qe[1],tt=(0,a.useState)(!1),nt=(0,p.Z)(tt,2),at=nt[0],ot=nt[1],rt=j.filter((function(e){return!e.disabled})).map((function(e){return e.key})),it=function(e){var t=rt.indexOf(Je||c),n=rt.length,a=rt[(t+e+n)%n];et(a)},lt=function(e){var t=e.code,n=d&&J,a=rt[0],o=rt[rt.length-1];switch(t){case"ArrowLeft":J&&it(n?1:-1);break;case"ArrowRight":J&&it(n?-1:1);break;case"ArrowUp":e.preventDefault(),J||it(-1);break;case"ArrowDown":e.preventDefault(),J||it(1);break;case"Home":e.preventDefault(),et(a);break;case"End":e.preventDefault(),et(o);break;case"Enter":case"Space":e.preventDefault(),S(null!=Je?Je:c,e);break;case"Backspace":case"Delete":var r=rt.indexOf(Je),i=j.find((function(e){return e.key===Je}));O(null==i?void 0:i.closable,null==i?void 0:i.closeIcon,u,null==i?void 0:i.disabled)&&(e.preventDefault(),e.stopPropagation(),u.onEdit("remove",{key:Je,event:e}),r===rt.length-1?it(-1):it(1))}},ct={};J?ct[d?"marginRight":"marginLeft"]=g:ct.marginTop=g;var dt=j.map((function(e,t){var n=e.key;return a.createElement(W,{id:r,prefixCls:H,key:n,tab:e,style:0===t?void 0:ct,closable:e.closable,editable:u,active:n===c,focus:n===Je,renderWrapper:$,removeAriaLabel:null==m?void 0:m.removeAriaLabel,tabCount:rt.length,currentPosition:t+1,onClick:function(e){S(n,e)},onKeyDown:lt,onFocus:function(){at||et(n),Ye(n),We(),V.current&&(d||(V.current.scrollLeft=0),V.current.scrollTop=0)},onBlur:function(){et(void 0)},onMouseDown:function(){ot(!0)},onMouseUp:function(){ot(!1)}})})),st=function(){return Ee((function(){var e,t=new Map,n=null===(e=Y.current)||void 0===e?void 0:e.getBoundingClientRect();return j.forEach((function(e){var a,o=e.key,r=null===(a=Y.current)||void 0===a?void 0:a.querySelector('[data-node-key="'.concat(L(o),'"]'));if(r){var i=function(e,t){var n=e.offsetWidth,a=e.offsetHeight,o=e.offsetTop,r=e.offsetLeft,i=e.getBoundingClientRect(),l=i.width,c=i.height,d=i.left,s=i.top;return Math.abs(l-n)<1?[l,c,d-t.left,s-t.top]:[n,a,r,o]}(r,n),l=(0,p.Z)(i,4),c=l[0],d=l[1],s=l[2],u=l[3];t.set(o,{width:c,height:d,left:s,top:u})}})),t}))};(0,a.useEffect)((function(){st()}),[j.map((function(e){return e.key})).join("_")]);var ut=I((function(){var e=X(G),t=X(q),n=X(F);ue([e[0]-t[0]-n[0],e[1]-t[1]-n[1]]);var a=X(Q);$e(a);var o=X(U);xe(o);var r=X(Y);pe([r[0]-a[0],r[1]-a[1]]),st()})),ft=j.slice(0,Fe),vt=j.slice(Ve+1),bt=[].concat((0,k.Z)(ft),(0,k.Z)(vt)),pt=Ze.get(c),mt=C({activeTabOffset:pt,horizontal:J,indicator:z,rtl:d}).style;(0,a.useEffect)((function(){Ye()}),[c,ze,Be,T(pt),T(Ze),J]),(0,a.useEffect)((function(){ut()}),[d]);var ht,gt,$t,yt,kt=!!bt.length,wt="".concat(H,"-nav-wrap");return J?d?(gt=ne>0,ht=ne!==Be):(ht=ne<0,gt=ne!==ze):($t=ie<0,yt=ie!==ze),a.createElement(w.Z,{onResize:ut},a.createElement("div",{ref:(0,_.x1)(t,G),role:"tablist","aria-orientation":J?"horizontal":"vertical",className:f()("".concat(H,"-nav"),n),style:o,onKeyDown:function(){We()}},a.createElement(N,{ref:q,position:"left",extra:s,prefixCls:H}),a.createElement(w.Z,{onResize:ut},a.createElement("div",{className:f()(wt,(0,v.Z)((0,v.Z)((0,v.Z)((0,v.Z)({},"".concat(wt,"-ping-left"),ht),"".concat(wt,"-ping-right"),gt),"".concat(wt,"-ping-top"),$t),"".concat(wt,"-ping-bottom"),yt)),ref:V},a.createElement(w.Z,{onResize:ut},a.createElement("div",{ref:Y,className:"".concat(H,"-nav-list"),style:{transform:"translate(".concat(ne,"px, ").concat(ie,"px)"),transition:Ge?"none":void 0}},dt,a.createElement(B,{ref:Q,prefixCls:H,locale:m,editable:u,style:(0,b.Z)((0,b.Z)({},0===dt.length?void 0:ct),{},{visibility:kt?"hidden":null})}),a.createElement("div",{className:f()("".concat(H,"-ink-bar"),(0,v.Z)({},"".concat(H,"-ink-bar-animated"),l.inkBar)),style:mt}))))),a.createElement(A,(0,i.Z)({},e,{removeAriaLabel:null==m?void 0:m.removeAriaLabel,ref:U,prefixCls:H,tabs:bt,className:!kt&&Oe,tabMoving:!!Ge})),a.createElement(N,{ref:F,position:"right",extra:s,prefixCls:H})))})),F=q,V=a.forwardRef((function(e,t){var n=e.prefixCls,o=e.className,r=e.style,i=e.id,l=e.active,c=e.tabKey,d=e.children;return a.createElement("div",{id:i&&"".concat(i,"-panel-").concat(c),role:"tabpanel",tabIndex:l?0:-1,"aria-labelledby":i&&"".concat(i,"-tab-").concat(c),"aria-hidden":!l,style:r,className:f()(n,l&&"".concat(n,"-active"),o),ref:t},d)}));var Y=V,U=["renderTabBar"],Q=["label","key"];var J=function(e){var t=e.renderTabBar,n=(0,h.Z)(e,U),o=a.useContext(y).tabs;return t?t((0,b.Z)((0,b.Z)({},n),{},{panes:o.map((function(e){var t=e.label,n=e.key,o=(0,h.Z)(e,Q);return a.createElement(Y,(0,i.Z)({tab:t,key:n,tabKey:n},o))}))}),F):a.createElement(F,n)},ee=n(29372),te=["key","forceRender","style","className","destroyInactiveTabPane"],ne=function(e){var t=e.id,n=e.activeKey,o=e.animated,r=e.tabPosition,l=e.destroyInactiveTabPane,c=a.useContext(y),d=c.prefixCls,s=c.tabs,u=o.tabPane,p="".concat(d,"-tabpane");return a.createElement("div",{className:f()("".concat(d,"-content-holder"))},a.createElement("div",{className:f()("".concat(d,"-content"),"".concat(d,"-content-").concat(r),(0,v.Z)({},"".concat(d,"-content-animated"),u))},s.map((function(e){var r=e.key,c=e.forceRender,d=e.style,s=e.className,v=e.destroyInactiveTabPane,m=(0,h.Z)(e,te),g=r===n;return a.createElement(ee.ZP,(0,i.Z)({key:r,visible:g,forceRender:c,removeOnLeave:!(!l&&!v),leavedClassName:"".concat(p,"-hidden")},o.tabPaneMotion),(function(e,n){var o=e.style,l=e.className;return a.createElement(Y,(0,i.Z)({},m,{prefixCls:p,id:t,tabKey:r,animated:u,active:g,style:(0,b.Z)((0,b.Z)({},d),o),className:f()(s,l),ref:n}))}))}))))};n(80334);var ae=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],oe=0,re=a.forwardRef((function(e,t){var n=e.id,o=e.prefixCls,r=void 0===o?"rc-tabs":o,l=e.className,c=e.items,d=e.direction,s=e.activeKey,u=e.defaultActiveKey,k=e.editable,w=e.animated,x=e.tabPosition,_=void 0===x?"top":x,S=e.tabBarGutter,C=e.tabBarStyle,E=e.tabBarExtraContent,Z=e.locale,P=e.more,R=e.destroyInactiveTabPane,I=e.renderTabBar,M=e.onChange,T=e.onTabClick,L=e.onTabScroll,O=e.getPopupContainer,z=e.popupClassName,B=e.indicator,N=(0,h.Z)(e,ae),D=a.useMemo((function(){return(c||[]).filter((function(e){return e&&"object"===(0,m.Z)(e)&&"key"in e}))}),[c]),H="rtl"===d,j=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{inkBar:!0,tabPane:!1};return(e=!1===t?{inkBar:!1,tabPane:!1}:!0===t?{inkBar:!0,tabPane:!1}:(0,b.Z)({inkBar:!0},"object"===(0,m.Z)(t)?t:{})).tabPaneMotion&&void 0===e.tabPane&&(e.tabPane=!0),!e.tabPaneMotion&&e.tabPane&&(e.tabPane=!1),e}(w),G=(0,a.useState)(!1),A=(0,p.Z)(G,2),W=A[0],X=A[1];(0,a.useEffect)((function(){X((0,$.Z)())}),[]);var K=(0,g.Z)((function(){var e;return null===(e=D[0])||void 0===e?void 0:e.key}),{value:s,defaultValue:u}),q=(0,p.Z)(K,2),F=q[0],V=q[1],Y=(0,a.useState)((function(){return D.findIndex((function(e){return e.key===F}))})),U=(0,p.Z)(Y,2),Q=U[0],ee=U[1];(0,a.useEffect)((function(){var e,t=D.findIndex((function(e){return e.key===F}));-1===t&&(t=Math.max(0,Math.min(Q,D.length-1)),V(null===(e=D[t])||void 0===e?void 0:e.key));ee(t)}),[D.map((function(e){return e.key})).join("_"),F,Q]);var te=(0,g.Z)(null,{value:n}),re=(0,p.Z)(te,2),ie=re[0],le=re[1];(0,a.useEffect)((function(){n||(le("rc-tabs-".concat(oe)),oe+=1)}),[]);var ce={id:ie,activeKey:F,animated:j,tabPosition:_,rtl:H,mobile:W},de=(0,b.Z)((0,b.Z)({},ce),{},{editable:k,locale:Z,more:P,tabBarGutter:S,onTabClick:function(e,t){null==T||T(e,t);var n=e!==F;V(e),n&&(null==M||M(e))},onTabScroll:L,extra:E,style:C,panes:null,getPopupContainer:O,popupClassName:z,indicator:B});return a.createElement(y.Provider,{value:{tabs:D,prefixCls:r}},a.createElement("div",(0,i.Z)({ref:t,id:n,className:f()(r,"".concat(r,"-").concat(_),(0,v.Z)((0,v.Z)((0,v.Z)({},"".concat(r,"-mobile"),W),"".concat(r,"-editable"),k),"".concat(r,"-rtl"),H),l)},N),a.createElement(J,(0,i.Z)({},de,{renderTabBar:I})),a.createElement(ne,(0,i.Z)({destroyInactiveTabPane:R},ce,{animated:j}))))}));var ie=re,le=n(53124),ce=n(35792),de=n(98675),se=n(33603);const ue={motionAppear:!1,motionEnter:!0,motionLeave:!0};var fe=n(50344),ve=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n};var be=n(11568),pe=n(14747),me=n(83559),he=n(83262),ge=n(67771);var $e=e=>{const{componentCls:t,motionDurationSlow:n}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${n}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${n}`}}}}},[(0,ge.oN)(e,"slide-up"),(0,ge.oN)(e,"slide-down")]]};const ye=e=>{const{componentCls:t,tabsCardPadding:n,cardBg:a,cardGutter:o,colorBorderSecondary:r,itemSelectedColor:i}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:n,background:a,border:`${(0,be.bf)(e.lineWidth)} ${e.lineType} ${r}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:i,background:e.colorBgContainer},[`${t}-tab-focus`]:Object.assign({},(0,pe.oN)(e,-3)),[`${t}-ink-bar`]:{visibility:"hidden"},[`& ${t}-tab${t}-tab-focus ${t}-tab-btn`]:{outline:"none"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:(0,be.bf)(o)}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${(0,be.bf)(e.borderRadiusLG)} ${(0,be.bf)(e.borderRadiusLG)} 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${(0,be.bf)(e.borderRadiusLG)} ${(0,be.bf)(e.borderRadiusLG)}`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:(0,be.bf)(o)}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,be.bf)(e.borderRadiusLG)} 0 0 ${(0,be.bf)(e.borderRadiusLG)}`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,be.bf)(e.borderRadiusLG)} ${(0,be.bf)(e.borderRadiusLG)} 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},ke=e=>{const{componentCls:t,itemHoverColor:n,dropdownEdgeChildVerticalPadding:a}=e;return{[`${t}-dropdown`]:Object.assign(Object.assign({},(0,pe.Wf)(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${(0,be.bf)(a)} 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":Object.assign(Object.assign({},pe.vS),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${(0,be.bf)(e.paddingXXS)} ${(0,be.bf)(e.paddingSM)}`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},we=e=>{const{componentCls:t,margin:n,colorBorderSecondary:a,horizontalMargin:o,verticalItemPadding:r,verticalItemMargin:i,calc:l}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:o,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${(0,be.bf)(e.lineWidth)} ${e.lineType} ${a}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},\n            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,\n        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:n,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:l(e.controlHeight).mul(1.25).equal(),[`${t}-tab`]:{padding:r,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:i},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:(0,be.bf)(l(e.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:`${(0,be.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:l(e.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:`${(0,be.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},xe=e=>{const{componentCls:t,cardPaddingSM:n,cardPaddingLG:a,cardHeightSM:o,cardHeightLG:r,horizontalItemPaddingSM:i,horizontalItemPaddingLG:l}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:i,fontSize:e.titleFontSizeSM}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:l,fontSize:e.titleFontSizeLG,lineHeight:e.lineHeightLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:n},[`${t}-nav-add`]:{minWidth:o,minHeight:o}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${(0,be.bf)(e.borderRadius)} ${(0,be.bf)(e.borderRadius)}`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${(0,be.bf)(e.borderRadius)} ${(0,be.bf)(e.borderRadius)} 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,be.bf)(e.borderRadius)} ${(0,be.bf)(e.borderRadius)} 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,be.bf)(e.borderRadius)} 0 0 ${(0,be.bf)(e.borderRadius)}`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:a},[`${t}-nav-add`]:{minWidth:r,minHeight:r}}}}}},_e=e=>{const{componentCls:t,itemActiveColor:n,itemHoverColor:a,iconCls:o,tabsHorizontalItemMargin:r,horizontalItemPadding:i,itemSelectedColor:l,itemColor:c}=e,d=`${t}-tab`;return{[d]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:i,fontSize:e.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:c,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:n}},"&-btn":{outline:"none",transition:`all ${e.motionDurationSlow}`,[`${d}-icon:not(:last-child)`]:{marginInlineEnd:e.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:e.calc(e.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},(0,pe.Qy)(e)),"&:hover":{color:a},[`&${d}-active ${d}-btn`]:{color:l,textShadow:e.tabsActiveTextShadow},[`&${d}-focus ${d}-btn`]:Object.assign({},(0,pe.oN)(e)),[`&${d}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${d}-disabled ${d}-btn, &${d}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${d}-remove ${o}`]:{margin:0},[`${o}:not(:last-child)`]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${d} + ${d}`]:{margin:{_skip_check_:!0,value:r}}}},Se=e=>{const{componentCls:t,tabsHorizontalItemMarginRTL:n,iconCls:a,cardGutter:o,calc:r}=e;return{[`${t}-rtl`]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:n},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[a]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:(0,be.bf)(e.marginSM)}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:(0,be.bf)(e.marginXS)},marginLeft:{_skip_check_:!0,value:(0,be.bf)(r(e.marginXXS).mul(-1).equal())},[a]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:o},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},Ce=e=>{const{componentCls:t,tabsCardPadding:n,cardHeight:a,cardGutter:o,itemHoverColor:r,itemActiveColor:i,colorBorderSecondary:l}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,pe.Wf)(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:n,background:"transparent",border:0,color:e.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.calc(e.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:Object.assign({minWidth:a,minHeight:a,marginLeft:{_skip_check_:!0,value:o},background:"transparent",border:`${(0,be.bf)(e.lineWidth)} ${e.lineType} ${l}`,borderRadius:`${(0,be.bf)(e.borderRadiusLG)} ${(0,be.bf)(e.borderRadiusLG)} 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:r},"&:active, &:focus:not(:focus-visible)":{color:i}},(0,pe.Qy)(e,-3))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.inkBarColor,pointerEvents:"none"}}),_e(e)),{[`${t}-content`]:{position:"relative",width:"100%"},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:Object.assign(Object.assign({},(0,pe.Qy)(e)),{"&-hidden":{display:"none"}})}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping']) > ${t}-nav-list`]:{margin:"auto"}}}}}};var Ee=(0,me.I$)("Tabs",(e=>{const t=(0,he.IX)(e,{tabsCardPadding:e.cardPadding,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:`0 0 0 ${(0,be.bf)(e.horizontalItemGutter)}`,tabsHorizontalItemMarginRTL:`0 0 0 ${(0,be.bf)(e.horizontalItemGutter)}`});return[xe(t),Se(t),we(t),ke(t),ye(t),Ce(t),$e(t)]}),(e=>{const{cardHeight:t,cardHeightSM:n,cardHeightLG:a,controlHeight:o,controlHeightLG:r}=e,i=t||r,l=n||o,c=a||r+8;return{zIndexPopup:e.zIndexPopupBase+50,cardBg:e.colorFillAlter,cardHeight:i,cardHeightSM:l,cardHeightLG:c,cardPadding:`${(i-e.fontHeight)/2-e.lineWidth}px ${e.padding}px`,cardPaddingSM:`${(l-e.fontHeight)/2-e.lineWidth}px ${e.paddingXS}px`,cardPaddingLG:`${(c-e.fontHeightLG)/2-e.lineWidth}px ${e.padding}px`,titleFontSize:e.fontSize,titleFontSizeLG:e.fontSizeLG,titleFontSizeSM:e.fontSize,inkBarColor:e.colorPrimary,horizontalMargin:`0 0 ${e.margin}px 0`,horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:`${e.paddingSM}px 0`,horizontalItemPaddingSM:`${e.paddingXS}px 0`,horizontalItemPaddingLG:`${e.padding}px 0`,verticalItemPadding:`${e.paddingXS}px ${e.paddingLG}px`,verticalItemMargin:`${e.margin}px 0 0 0`,itemColor:e.colorText,itemSelectedColor:e.colorPrimary,itemHoverColor:e.colorPrimaryHover,itemActiveColor:e.colorPrimaryActive,cardGutter:e.marginXXS/2}}));var Ze=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n};const Pe=e=>{var t,n,i,l,c,d,u,v,b,p,m;const{type:h,className:g,rootClassName:$,size:y,onEdit:k,hideAdd:w,centered:x,addIcon:_,removeIcon:S,moreIcon:C,more:E,popupClassName:Z,children:P,items:R,animated:I,style:M,indicatorSize:T,indicator:L,destroyInactiveTabPane:O,destroyOnHidden:z}=e,B=Ze(e,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator","destroyInactiveTabPane","destroyOnHidden"]),{prefixCls:N}=B,{direction:D,tabs:H,getPrefixCls:j,getPopupContainer:G}=a.useContext(le.E_),A=j("tabs",N),W=(0,ce.Z)(A),[X,K,q]=Ee(A,W);let F;"editable-card"===h&&(F={onEdit:(e,t)=>{let{key:n,event:a}=t;null==k||k("add"===e?a:n,e)},removeIcon:null!==(t=null!=S?S:null==H?void 0:H.removeIcon)&&void 0!==t?t:a.createElement(o.Z,null),addIcon:(null!=_?_:null==H?void 0:H.addIcon)||a.createElement(s,null),showAdd:!0!==w});const V=j();const Y=(0,de.Z)(y),U=function(e,t){return e?e.map((e=>{var t;const n=null!==(t=e.destroyOnHidden)&&void 0!==t?t:e.destroyInactiveTabPane;return Object.assign(Object.assign({},e),{destroyInactiveTabPane:n})})):function(e){return e.filter((e=>e))}((0,fe.Z)(t).map((e=>{if(a.isValidElement(e)){const{key:t,props:n}=e,a=n||{},{tab:o}=a,r=ve(a,["tab"]);return Object.assign(Object.assign({key:String(t)},r),{label:o})}return null})))}(R,P),Q=function(e){let t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{inkBar:!0,tabPane:!1};return t=!1===n?{inkBar:!1,tabPane:!1}:!0===n?{inkBar:!0,tabPane:!0}:Object.assign({inkBar:!0},"object"==typeof n?n:{}),t.tabPane&&(t.tabPaneMotion=Object.assign(Object.assign({},ue),{motionName:(0,se.m)(e,"switch")})),t}(A,I),J=Object.assign(Object.assign({},null==H?void 0:H.style),M),ee={align:null!==(n=null==L?void 0:L.align)&&void 0!==n?n:null===(i=null==H?void 0:H.indicator)||void 0===i?void 0:i.align,size:null!==(u=null!==(c=null!==(l=null==L?void 0:L.size)&&void 0!==l?l:T)&&void 0!==c?c:null===(d=null==H?void 0:H.indicator)||void 0===d?void 0:d.size)&&void 0!==u?u:null==H?void 0:H.indicatorSize};return X(a.createElement(ie,Object.assign({direction:D,getPopupContainer:G},B,{items:U,className:f()({[`${A}-${Y}`]:Y,[`${A}-card`]:["card","editable-card"].includes(h),[`${A}-editable-card`]:"editable-card"===h,[`${A}-centered`]:x},null==H?void 0:H.className,g,$,K,q,W),popupClassName:f()(Z,K,q,W),style:J,editable:F,more:Object.assign({icon:null!==(m=null!==(p=null!==(b=null===(v=null==H?void 0:H.more)||void 0===v?void 0:v.icon)&&void 0!==b?b:null==H?void 0:H.moreIcon)&&void 0!==p?p:C)&&void 0!==m?m:a.createElement(r.Z,null),transitionName:`${V}-slide-up`},E),prefixCls:A,animated:Q,indicator:ee,destroyInactiveTabPane:null!=z?z:O})))};Pe.TabPane=()=>null;var Re=Pe}}]);