"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3030],{51042:function(e,t,n){var r=n(1413),a=n(67294),u=n(42110),i=n(91146),o=function(e,t){return a.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:u.Z}))},s=a.forwardRef(o);t.Z=s},14480:function(e,t,n){n.r(t),n.d(t,{default:function(){return q}});var r=n(97857),a=n.n(r),u=n(15009),i=n.n(u),o=n(99289),s=n.n(o),c=n(5574),l=n.n(c),p=n(67294),d=n(97131),f=n(12453),h=n(47019),m=n(2453),v=n(83622),x=n(17788),b=n(63960),y=n(34041),w=n(54880),Z=n(51042),k=n(78158);function C(e){return _.apply(this,arguments)}function _(){return(_=s()(i()().mark((function e(t){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,k.N)("/api/authorizations",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function g(e){return j.apply(this,arguments)}function j(){return(j=s()(i()().mark((function e(t){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,k.N)("/api/authorizations",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function O(e,t){return E.apply(this,arguments)}function E(){return(E=s()(i()().mark((function e(t,n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,k.N)("/api/authorizations/".concat(t),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function S(e){return P.apply(this,arguments)}function P(){return(P=s()(i()().mark((function e(t){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,k.N)("/api/authorizations/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function I(){return N.apply(this,arguments)}function N(){return(N=s()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,k.N)("/api/authUsers",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function T(){return z.apply(this,arguments)}function z(){return(z=s()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,k.N)("/api/authEmbModels",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function D(){return M.apply(this,arguments)}function M(){return(M=s()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,k.N)("/api/authLLMModels",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var R=n(27484),Y=n.n(R),U=n(85893),q=function(){var e=(0,p.useState)(!1),t=l()(e,2),n=t[0],r=t[1],u=(0,p.useState)(!1),o=l()(u,2),c=o[0],k=o[1],_=(0,p.useState)(void 0),j=l()(_,2),E=j[0],P=j[1],N=(0,p.useRef)(),z=h.Z.useForm(),M=l()(z,1)[0],R=(0,p.useState)([]),q=l()(R,2),F=q[0],G=q[1],B=(0,p.useState)([]),L=l()(B,2),V=L[0],W=L[1],A=(0,p.useState)("llm"),K=l()(A,2),X=(K[0],K[1]),$=(0,p.useState)(null),H=l()($,2),J=H[0],Q=H[1],ee=(0,p.useState)(null),te=l()(ee,2),ne=te[0],re=te[1];(0,p.useEffect)((function(){var e=function(){var e=s()(i()().mark((function e(){var t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,I();case 3:t=e.sent,G(t.map((function(e){return{label:e.name,value:e.name,id:e.id}}))),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),m.ZP.error("获取用户数据失败");case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);var ae=function(){var e=s()(i()().mark((function e(t){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,"llm"!==t){e.next=7;break}return e.next=4,D();case 4:e.t0=e.sent,e.next=10;break;case 7:return e.next=9,T();case 9:e.t0=e.sent;case 10:n=e.t0,W(n.map((function(e){return{label:e.name,value:e.name,id:e.id}}))),e.next=17;break;case 14:e.prev=14,e.t1=e.catch(0),m.ZP.error("获取模型数据失败");case 17:case"end":return e.stop()}}),e,null,[[0,14]])})));return function(t){return e.apply(this,arguments)}}(),ue=function(){var e=s()(i()().mark((function e(t){var n,u;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(J&&ne){e.next=3;break}return m.ZP.error("请选择用户和模型"),e.abrupt("return",!1);case 3:return n=m.ZP.loading("正在添加"),e.prev=4,e.next=7,g(a()(a()({},t),{},{user_id:J,m_id:ne,expiration_date:t.expiration_date.format("YYYY-MM-DD")}));case 7:return n(),m.ZP.success("添加成功"),r(!1),null===(u=N.current)||void 0===u||u.reload(),M.resetFields(),e.abrupt("return",!0);case 15:return e.prev=15,e.t0=e.catch(4),n(),m.ZP.error("添加失败，请重试"),e.abrupt("return",!1);case 20:case"end":return e.stop()}}),e,null,[[4,15]])})));return function(t){return e.apply(this,arguments)}}(),ie=function(){var e=s()(i()().mark((function e(t){var n,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null!=E&&E.id){e.next=3;break}return m.ZP.error("更新失败，缺少授权 ID"),e.abrupt("return",!1);case 3:return n=m.ZP.loading("正在更新"),e.prev=4,e.next=7,O(E.id,{expiration_date:t.expiration_date.format("YYYY-MM-DD")});case 7:return n(),m.ZP.success("更新成功"),k(!1),P(void 0),null===(r=N.current)||void 0===r||r.reload(),M.resetFields(),e.abrupt("return",!0);case 16:return e.prev=16,e.t0=e.catch(4),n(),m.ZP.error("更新失败，请重试"),e.abrupt("return",!1);case 21:case"end":return e.stop()}}),e,null,[[4,16]])})));return function(t){return e.apply(this,arguments)}}(),oe=function(){var e=s()(i()().mark((function e(t){var n,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=m.ZP.loading("正在删除"),e.prev=1,e.next=4,S(t.id);case 4:return n(),m.ZP.success("删除成功"),null===(r=N.current)||void 0===r||r.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),n(),m.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(t){return e.apply(this,arguments)}}(),se=[{title:"用户名称",dataIndex:"user_name",valueType:"text"},{title:"模型名称",dataIndex:"m_name",valueType:"text"},{title:"授权类型",dataIndex:"authorization_type",valueType:"text"},{title:"到期时间",dataIndex:"expiration_date",valueType:"date"},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",search:!1},{title:"操作",dataIndex:"option",valueType:"option",render:function(e,t){return[(0,U.jsx)(v.ZP,{type:"link",onClick:function(){k(!0),P(t)},children:"编辑"},"edit-".concat(t.id)),(0,U.jsx)(v.ZP,{type:"link",danger:!0,onClick:function(){return oe(t)},children:"删除"},"delete-".concat(t.id))]}}];return(0,U.jsxs)(d._z,{children:[(0,U.jsx)(f.Z,{headerTitle:"模型授权管理",actionRef:N,rowKey:"id",search:{labelWidth:120,defaultCollapsed:!0},toolBarRender:function(){return[(0,U.jsxs)(v.ZP,{type:"primary",onClick:function(){r(!0)},children:[(0,U.jsx)(Z.Z,{})," 新建"]},"primary")]},request:function(){var e=s()(i()().mark((function e(t){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,C(a()({current:t.current,pageSize:t.pageSize},t));case 2:return n=e.sent,e.abrupt("return",{data:n.data,success:n.success,total:n.total});case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),columns:se}),(0,U.jsx)(x.Z,{visible:n,title:"新建授权",onCancel:function(){return r(!1)},onOk:function(){return M.submit()},children:(0,U.jsxs)(h.Z,{form:M,layout:"horizontal",onFinish:ue,labelCol:{span:6},wrapperCol:{span:18},children:[(0,U.jsx)(h.Z.Item,{name:"user_name",label:"用户名称",rules:[{required:!0,message:"请输入用户名称"}],children:(0,U.jsx)(b.Z,{options:F,placeholder:"输入用户名称",onSelect:function(e,t){return Q(t.id)},filterOption:function(e,t){return-1!==t.value.toUpperCase().indexOf(e.toUpperCase())}})}),(0,U.jsx)(h.Z.Item,{name:"authorization_type",label:"授权类型",rules:[{required:!0,message:"请选择授权类型"}],children:(0,U.jsxs)(y.default,{onChange:function(e){X(e),ae(e)},children:[(0,U.jsx)(y.default.Option,{value:"llm",children:"大语言模型"}),(0,U.jsx)(y.default.Option,{value:"embedding",children:"嵌入模型"})]})}),(0,U.jsx)(h.Z.Item,{name:"m_name",label:"模型名称",rules:[{required:!0,message:"请输入模型名称"}],children:(0,U.jsx)(b.Z,{options:V,placeholder:"输入模型名称",onSelect:function(e,t){return re(t.id)},filterOption:function(e,t){return-1!==t.value.toUpperCase().indexOf(e.toUpperCase())}})}),(0,U.jsx)(h.Z.Item,{name:"expiration_date",label:"到期时间",rules:[{required:!0,message:"请选择到期时间"}],children:(0,U.jsx)(w.default,{})})]})}),E&&(0,U.jsx)(x.Z,{visible:c,title:"更新授权",onCancel:function(){return k(!1)},onOk:function(){return M.submit()},children:(0,U.jsx)(h.Z,{form:M,layout:"horizontal",initialValues:{expiration_date:Y()(E.expiration_date)},onFinish:ie,labelCol:{span:6},wrapperCol:{span:18},children:(0,U.jsx)(h.Z.Item,{name:"expiration_date",label:"到期时间",rules:[{required:!0,message:"请选择到期时间"}],children:(0,U.jsx)(w.default,{})})})})]})}},63960:function(e,t,n){n.d(t,{Z:function(){return y}});var r=n(98423),a=n(8745),u=n(34041),i=n(67294),o=n(93967),s=n.n(o),c=n(50344),l=n(87263),p=n(53124);const{Option:d}=u.default;function f(e){return(null==e?void 0:e.type)&&(e.type.isSelectOption||e.type.isSelectOptGroup)}const h=(e,t)=>{var n,a;const{prefixCls:o,className:h,popupClassName:m,dropdownClassName:v,children:x,dataSource:b,dropdownStyle:y,dropdownRender:w,popupRender:Z,onDropdownVisibleChange:k,onOpenChange:C,styles:_,classNames:g}=e,j=(0,c.Z)(x),O=(null===(n=null==_?void 0:_.popup)||void 0===n?void 0:n.root)||y,E=(null===(a=null==g?void 0:g.popup)||void 0===a?void 0:a.root)||m||v,S=Z||w,P=C||k;let I;1===j.length&&i.isValidElement(j[0])&&!f(j[0])&&([I]=j);const N=I?()=>I:void 0;let T;T=j.length&&f(j[0])?x:b?b.map((e=>{if(i.isValidElement(e))return e;switch(typeof e){case"string":return i.createElement(d,{key:e,value:e},e);case"object":{const{value:t}=e;return i.createElement(d,{key:t,value:t},e.text)}default:return}})):[];const{getPrefixCls:z}=i.useContext(p.E_),D=z("select",o),[M]=(0,l.Cn)("SelectLike",null==O?void 0:O.zIndex);return i.createElement(u.default,Object.assign({ref:t,suffixIcon:null},(0,r.Z)(e,["dataSource","dropdownClassName","popupClassName"]),{prefixCls:D,classNames:{popup:{root:E},root:null==g?void 0:g.root},styles:{popup:{root:Object.assign(Object.assign({},O),{zIndex:M})},root:null==_?void 0:_.root},className:s()(`${D}-auto-complete`,h),mode:u.default.SECRET_COMBOBOX_MODE_DO_NOT_USE,popupRender:S,onOpenChange:P,getInputElement:N}),T)};var m=i.forwardRef(h);const{Option:v}=u.default,x=(0,a.Z)(m,"dropdownAlign",(e=>(0,r.Z)(e,["visible"]))),b=m;b.Option=v,b._InternalPanelDoNotUseOrYouWillBeFired=x;var y=b}}]);