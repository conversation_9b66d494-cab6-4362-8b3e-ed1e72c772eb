"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3282],{85673:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"}}]},name:"arrow-up",theme:"outlined"}},47422:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M682 455V311l-76 76v68c-.1 50.7-42 92.1-94 92a95.8 95.8 0 01-52-15l-54 55c29.1 22.4 65.9 36 106 36 93.8 0 170-75.1 170-168z"}},{tag:"path",attrs:{d:"M833 446h-60c-4.4 0-8 3.6-8 8 0 140.3-113.7 254-254 254-63 0-120.7-23-165-61l-54 54a334.01 334.01 0 00179 81v102H326c-13.9 0-24.9 14.3-25 32v36c.1 4.4 2.9 8 6 8h408c3.2 0 6-3.6 6-8v-36c0-17.7-11-32-25-32H547V782c165.3-17.9 294-157.9 294-328 0-4.4-3.6-8-8-8zm13.1-377.7l-43.5-41.9a8 8 0 00-11.2.1l-129 129C634.3 101.2 577 64 511 64c-93.9 0-170 75.3-170 168v224c0 6.7.4 13.3 1.2 19.8l-68 68A252.33 252.33 0 01258 454c-.2-4.4-3.8-8-8-8h-60c-4.4 0-8 3.6-8 8 0 53 12.5 103 34.6 147.4l-137 137a8.03 8.03 0 000 11.3l42.7 42.7c3.1 3.1 8.2 3.1 11.3 0L846.2 79.8l.1-.1c3.1-3.2 3-8.3-.2-11.4zM417 401V232c0-50.6 41.9-92 94-92 46 0 84.1 32.3 92.3 74.7L417 401z"}}]},name:"audio-muted",theme:"outlined"}},62497:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M842 454c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 140.3-113.7 254-254 254S258 594.3 258 454c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 168.7 126.6 307.9 290 327.6V884H326.7c-13.7 0-24.7 14.3-24.7 32v36c0 4.4 2.8 8 6.2 8h407.6c3.4 0 6.2-3.6 6.2-8v-36c0-17.7-11-32-24.7-32H548V782.1c165.3-18 294-158 294-328.1zM512 624c93.9 0 170-75.2 170-168V232c0-92.8-76.1-168-170-168s-170 75.2-170 168v224c0 92.8 76.1 168 170 168zm-94-392c0-50.6 41.9-92 94-92s94 41.4 94 92v224c0 50.6-41.9 92-94 92s-94-41.4-94-92V232z"}}]},name:"audio",theme:"outlined"}},21450:function(e,t,n){n.d(t,{Z:function(){return i}});var r=n(67294);var a=r.createContext({});const o={classNames:{},styles:{},className:"",style:{}};var i=e=>{const t=r.useContext(a);return r.useMemo((()=>({...o,...t[e]})),[t[e]])}},78919:function(e,t,n){n.d(t,{Z:function(){return h}});var r=n(87462),a=n(71471),o=n(93967),i=n.n(o),c=n(67294),l=n(21450),s=n(36158),d=n(11568),f=n(83262),u=n(43495);const m=e=>{const{componentCls:t}=e;return{[t]:{"&, & *":{boxSizing:"border-box"},maxWidth:"100%",[`&${t}-rtl`]:{direction:"rtl"},[`& ${t}-title`]:{marginBlockStart:0,fontWeight:"normal",color:e.colorTextTertiary},[`& ${t}-list`]:{display:"flex",gap:e.paddingSM,overflowX:"scroll","&::-webkit-scrollbar":{display:"none"},listStyle:"none",paddingInlineStart:0,marginBlock:0,alignItems:"stretch","&-wrap":{flexWrap:"wrap"},"&-vertical":{flexDirection:"column",alignItems:"flex-start"}},[`${t}-item`]:{flex:"none",display:"flex",gap:e.paddingXS,height:"auto",paddingBlock:e.paddingSM,paddingInline:e.padding,alignItems:"flex-start",justifyContent:"flex-start",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,transition:["border","background"].map((t=>`${t} ${e.motionDurationSlow}`)).join(","),border:`${(0,d.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`,[`&:not(${t}-item-has-nest)`]:{"&:hover":{cursor:"pointer",background:e.colorFillTertiary},"&:active":{background:e.colorFill}},[`${t}-content`]:{flex:"auto",minWidth:0,display:"flex",gap:e.paddingXXS,flexDirection:"column",alignItems:"flex-start"},[`${t}-icon, ${t}-label, ${t}-desc`]:{margin:0,padding:0,fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start",whiteSpace:"normal"},[`${t}-label`]:{color:e.colorTextHeading,fontWeight:500},[`${t}-label + ${t}-desc`]:{color:e.colorTextTertiary},[`&${t}-item-disabled`]:{pointerEvents:"none",background:e.colorBgContainerDisabled,[`${t}-label, ${t}-desc`]:{color:e.colorTextTertiary}}}}}},p=e=>{const{componentCls:t}=e;return{[t]:{[`${t}-item-has-nest`]:{[`> ${t}-content`]:{[`> ${t}-label`]:{fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}}},[`&${t}-nested`]:{marginTop:e.paddingXS,alignSelf:"stretch",[`${t}-list`]:{alignItems:"stretch"},[`${t}-item`]:{border:0,background:e.colorFillQuaternary}}}}};var g=(0,u.I$)("Prompts",(e=>{const t=(0,f.IX)(e,{});return[m(t),p(t)]}),(()=>({})));const b=e=>{const{prefixCls:t,title:n,className:o,items:d,onItemClick:f,vertical:u,wrap:m,rootClassName:p,styles:h={},classNames:v={},style:y,...x}=e,{getPrefixCls:C,direction:w}=(0,s.Z)(),$=C("prompts",t),E=(0,l.Z)("prompts"),[S,k,N]=g($),Z=i()($,E.className,o,p,k,N,{[`${$}-rtl`]:"rtl"===w}),M=i()(`${$}-list`,E.classNames.list,v.list,{[`${$}-list-wrap`]:m},{[`${$}-list-vertical`]:u});return S(c.createElement("div",(0,r.Z)({},x,{className:Z,style:{...y,...E.style}}),n&&c.createElement(a.Z.Title,{level:5,className:i()(`${$}-title`,E.classNames.title,v.title),style:{...E.styles.title,...h.title}},n),c.createElement("div",{className:M,style:{...E.styles.list,...h.list}},d?.map(((e,t)=>{const n=e.children&&e.children.length>0;return c.createElement("div",{key:e.key||`key_${t}`,style:{...E.styles.item,...h.item},className:i()(`${$}-item`,E.classNames.item,v.item,{[`${$}-item-disabled`]:e.disabled,[`${$}-item-has-nest`]:n}),onClick:()=>{!n&&f&&f({data:e})}},e.icon&&c.createElement("div",{className:`${$}-icon`},e.icon),c.createElement("div",{className:i()(`${$}-content`,E.classNames.itemContent,v.itemContent),style:{...E.styles.itemContent,...h.itemContent}},e.label&&c.createElement("h6",{className:`${$}-label`},e.label),e.description&&c.createElement("p",{className:`${$}-desc`},e.description),n&&c.createElement(b,{className:`${$}-nested`,items:e.children,vertical:!0,onItemClick:f,classNames:{list:v.subList,item:v.subItem},styles:{list:h.subList,item:h.subItem}})))})))))};var h=b},4628:function(e,t,n){n.d(t,{Z:function(){return ie}});var r=n(87462),a=n(55102),o=n(86250),i=n(93967),c=n.n(i),l=n(56790),s=n(64217),d=n(88306),f=n(67294);var u=n(21450),m=n(36158),p=n(89503),g=n(51398),b=function(e,t){return f.createElement(g.Z,(0,r.Z)({},e,{ref:t,icon:p.Z}))};var h=f.forwardRef(b),v=n(83622),y=n(29372);const x=f.createContext({}),C=()=>({height:0}),w=e=>({height:e.scrollHeight});const $=f.createContext(null);function E(e,t){const{className:n,action:a,onClick:o,...i}=e,l=f.useContext($),{prefixCls:s,disabled:d}=l,u=l[a],m=d??i.disabled??l[`${a}Disabled`];return f.createElement(v.ZP,(0,r.Z)({type:"text"},i,{ref:t,onClick:e=>{m||(u&&u(),o&&o(e))},className:c()(s,n,{[`${s}-disabled`]:m})}))}var S=f.forwardRef(E),k={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M899.1 869.6l-53-305.6H864c14.4 0 26-11.6 26-26V346c0-14.4-11.6-26-26-26H618V138c0-14.4-11.6-26-26-26H432c-14.4 0-26 11.6-26 26v182H160c-14.4 0-26 11.6-26 26v192c0 14.4 11.6 26 26 26h17.9l-53 305.6a25.95 25.95 0 0025.6 30.4h723c1.5 0 3-.1 4.4-.4a25.88 25.88 0 0021.2-30zM204 390h272V182h72v208h272v104H204V390zm468 440V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H416V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H202.8l45.1-260H776l45.1 260H672z"}}]},name:"clear",theme:"outlined"},N=function(e,t){return f.createElement(g.Z,(0,r.Z)({},e,{ref:t,icon:k}))};var Z=f.forwardRef(N);function M(e,t){return f.createElement(S,(0,r.Z)({icon:f.createElement(Z,null)},e,{action:"onClear",ref:t}))}var T=f.forwardRef(M);var R=(0,f.memo)((e=>{const{className:t}=e;return f.createElement("svg",{color:"currentColor",viewBox:"0 0 1000 1000",xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",className:t},f.createElement("title",null,"Stop Loading"),f.createElement("rect",{fill:"currentColor",height:"250",rx:"24",ry:"24",width:"250",x:"375",y:"375"}),f.createElement("circle",{cx:"500",cy:"500",fill:"none",r:"450",stroke:"currentColor",strokeWidth:"100",opacity:"0.45"}),f.createElement("circle",{cx:"500",cy:"500",fill:"none",r:"450",stroke:"currentColor",strokeWidth:"100",strokeDasharray:"600 9999999"},f.createElement("animateTransform",{attributeName:"transform",dur:"1s",from:"0 500 500",repeatCount:"indefinite",to:"360 500 500",type:"rotate"})))}));function I(e,t){const{prefixCls:n}=f.useContext($),{className:a}=e;return f.createElement(S,(0,r.Z)({icon:null,color:"primary",variant:"text",shape:"circle"},e,{className:c()(a,`${n}-loading-button`),action:"onCancel",ref:t}),f.createElement(R,{className:`${n}-loading-icon`}))}var H=f.forwardRef(I),P=n(85673),D=function(e,t){return f.createElement(g.Z,(0,r.Z)({},e,{ref:t,icon:P.Z}))};var L=f.forwardRef(D);function X(e,t){return f.createElement(S,(0,r.Z)({icon:f.createElement(L,null),type:"primary",shape:"circle"},e,{action:"onSend",ref:t}))}var z=f.forwardRef(X),B=n(47422),j=function(e,t){return f.createElement(g.Z,(0,r.Z)({},e,{ref:t,icon:B.Z}))};var O=f.forwardRef(j),V=n(62497),W=function(e,t){return f.createElement(g.Z,(0,r.Z)({},e,{ref:t,icon:V.Z}))};var G=f.forwardRef(W);const A=140,_=250;function q({className:e}){return f.createElement("svg",{color:"currentColor",viewBox:"0 0 1000 1000",xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",className:e},f.createElement("title",null,"Speech Recording"),Array.from({length:4}).map(((e,t)=>{const n=286.66666666666663*t;return f.createElement("rect",{fill:"currentColor",rx:70,ry:70,height:_,width:A,x:n,y:375,key:t},f.createElement("animate",{attributeName:"height",values:"250; 500; 250",keyTimes:"0; 0.5; 1",dur:"0.8s",begin:.2*t+"s",repeatCount:"indefinite"}),f.createElement("animate",{attributeName:"y",values:"375; 250; 375",keyTimes:"0; 0.5; 1",dur:"0.8s",begin:.2*t+"s",repeatCount:"indefinite"}))})))}function F(e,t){const{speechRecording:n,onSpeechDisabled:a,prefixCls:o}=f.useContext($);let i=null;return i=n?f.createElement(q,{className:`${o}-recording-icon`}):a?f.createElement(O,null):f.createElement(G,null),f.createElement(S,(0,r.Z)({icon:i,color:"primary",variant:"text"},e,{action:"onSpeech",ref:t}))}var K=f.forwardRef(F),J=n(11568),Q=n(83262),U=n(43495);var Y=e=>{const{componentCls:t,calc:n}=e,r=`${t}-header`;return{[t]:{[r]:{borderBottomWidth:e.lineWidth,borderBottomStyle:"solid",borderBottomColor:e.colorBorder,"&-header":{background:e.colorFillAlter,fontSize:e.fontSize,lineHeight:e.lineHeight,paddingBlock:n(e.paddingSM).sub(e.lineWidthBold).equal(),paddingInlineStart:e.padding,paddingInlineEnd:e.paddingXS,display:"flex",[`${r}-title`]:{flex:"auto"}},"&-content":{padding:e.padding},"&-motion":{transition:["height","border"].map((t=>`${t} ${e.motionDurationSlow}`)).join(","),overflow:"hidden","&-enter-start, &-leave-active":{borderBottomColor:"transparent"},"&-hidden":{display:"none"}}}}}};const ee=e=>{const{componentCls:t,padding:n,paddingSM:r,paddingXS:a,lineWidth:o,lineWidthBold:i,calc:c}=e;return{[t]:{position:"relative",width:"100%",boxSizing:"border-box",boxShadow:`${e.boxShadowTertiary}`,transition:`background ${e.motionDurationSlow}`,borderRadius:{_skip_check_:!0,value:c(e.borderRadius).mul(2).equal()},borderColor:e.colorBorder,borderWidth:0,borderStyle:"solid","&:after":{content:'""',position:"absolute",inset:0,pointerEvents:"none",transition:`border-color ${e.motionDurationSlow}`,borderRadius:{_skip_check_:!0,value:"inherit"},borderStyle:"inherit",borderColor:"inherit",borderWidth:o},"&:focus-within":{boxShadow:`${e.boxShadowSecondary}`,borderColor:e.colorPrimary,"&:after":{borderWidth:i}},"&-disabled":{background:e.colorBgContainerDisabled},[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{display:"flex",gap:a,width:"100%",paddingBlock:r,paddingInlineStart:n,paddingInlineEnd:r,boxSizing:"border-box",alignItems:"flex-end"},[`${t}-prefix`]:{flex:"none"},[`${t}-input`]:{padding:0,borderRadius:0,flex:"auto",alignSelf:"center",minHeight:"auto"},[`${t}-actions-list`]:{flex:"none",display:"flex","&-presets":{gap:e.paddingXS}},[`${t}-actions-btn`]:{"&-disabled":{opacity:.45},"&-loading-button":{padding:0,border:0},"&-loading-icon":{height:e.controlHeight,width:e.controlHeight,verticalAlign:"top"},"&-recording-icon":{height:"1.2em",width:"1.2em",verticalAlign:"top"}}}}};var te=(0,U.I$)("Sender",(e=>{const{paddingXS:t,calc:n}=e,r=(0,Q.IX)(e,{SenderContentMaxWidth:`calc(100% - ${(0,J.bf)(n(t).add(32).equal())})`});return[ee(r),Y(r)]}),(()=>({})));let ne;function re(e,t){const n=(0,l.zX)(e),[r,a,o]=f.useMemo((()=>"object"==typeof t?[t.recording,t.onRecordingChange,"boolean"==typeof t.recording]:[void 0,void 0,!1]),[t]),[i,c]=f.useState(null);f.useEffect((()=>{if("undefined"!=typeof navigator&&"permissions"in navigator){let e=null;return navigator.permissions.query({name:"microphone"}).then((t=>{c(t.state),t.onchange=function(){c(this.state)},e=t})),()=>{e&&(e.onchange=null)}}}),[]);const s=ne&&"denied"!==i,d=f.useRef(null),[u,m]=(0,l.C8)(!1,{value:r}),p=f.useRef(!1),g=(0,l.zX)((e=>{e&&!u||(p.current=e,o?a?.(!u):((()=>{if(s&&!d.current){const e=new ne;e.onstart=()=>{m(!0)},e.onend=()=>{m(!1)},e.onresult=e=>{if(!p.current){const t=e.results?.[0]?.[0]?.transcript;n(t)}p.current=!1},d.current=e}})(),d.current&&(u?(d.current.stop(),a?.(!1)):(d.current.start(),a?.(!0)))))}));return[s,g,u]}ne||"undefined"==typeof window||(ne=window.SpeechRecognition||window.webkitSpeechRecognition);const ae=f.forwardRef(((e,t)=>{const{prefixCls:n,styles:i={},classNames:p={},className:g,rootClassName:b,style:h,defaultValue:v,value:y,readOnly:C,submitType:w="enter",onSubmit:E,loading:S,components:k,onCancel:N,onChange:Z,actions:M,onKeyPress:R,onKeyDown:I,disabled:P,allowSpeech:D,prefix:L,header:X,onPaste:B,onPasteFile:j,...O}=e,{direction:V,getPrefixCls:W}=(0,m.Z)(),G=W("sender",n),A=f.useRef(null),_=f.useRef(null);!function(e,t){(0,f.useImperativeHandle)(e,(()=>{const e=t(),{nativeElement:n}=e;return new Proxy(n,{get(t,n){return e[n]?e[n]:Reflect.get(t,n)}})}))}(t,(()=>({nativeElement:A.current,focus:_.current?.focus,blur:_.current?.blur})));const q=(0,u.Z)("sender"),F=`${G}-input`,[J,Q,U]=te(G),Y=c()(G,q.className,g,b,Q,U,{[`${G}-rtl`]:"rtl"===V,[`${G}-disabled`]:P}),ee=`${G}-actions-btn`,ne=`${G}-actions-list`,[ae,oe]=(0,l.C8)(v||"",{value:y}),ie=(e,t)=>{oe(e),Z&&Z(e,t)},[ce,le,se]=re((e=>{ie(`${ae} ${e}`)}),D),de=function(e,t,n){return(0,d.Z)(e,t)||n}(k,["input"],a.Z.TextArea),fe={...(0,s.Z)(O,{attr:!0,aria:!0,data:!0}),ref:_},ue=()=>{ae&&E&&!S&&E(ae)},me=f.useRef(!1);let pe=f.createElement(o.Z,{className:`${ne}-presets`},D&&f.createElement(K,null),S?f.createElement(H,null):f.createElement(z,null));return"function"==typeof M?pe=M(pe,{components:{SendButton:z,ClearButton:T,LoadingButton:H}}):M&&(pe=M),J(f.createElement("div",{ref:A,className:Y,style:{...q.style,...h}},X&&f.createElement(x.Provider,{value:{prefixCls:G}},X),f.createElement("div",{className:`${G}-content`,onMouseDown:e=>{e.target!==A.current?.querySelector(`.${F}`)&&e.preventDefault(),_.current?.focus()}},L&&f.createElement("div",{className:c()(`${G}-prefix`,q.classNames.prefix,p.prefix),style:{...q.styles.prefix,...i.prefix}},L),f.createElement(de,(0,r.Z)({},fe,{disabled:P,style:{...q.styles.input,...i.input},className:c()(F,q.classNames.input,p.input),autoSize:{maxRows:8},value:ae,onChange:e=>{ie(e.target.value,e),le(!0)},onPressEnter:e=>{const t="Enter"===e.key&&!me.current;switch(w){case"enter":t&&!e.shiftKey&&(e.preventDefault(),ue());break;case"shiftEnter":t&&e.shiftKey&&(e.preventDefault(),ue())}R&&R(e)},onCompositionStart:()=>{me.current=!0},onCompositionEnd:()=>{me.current=!1},onKeyDown:I,onPaste:e=>{const t=e.clipboardData?.files[0];t&&j&&(j(t),e.preventDefault()),B?.(e)},variant:"borderless",readOnly:C})),f.createElement("div",{className:c()(ne,q.classNames.actions,p.actions),style:{...q.styles.actions,...i.actions}},f.createElement($.Provider,{value:{prefixCls:ee,onSend:ue,onSendDisabled:!ae,onClear:()=>{ie("")},onClearDisabled:!ae,onCancel:N,onCancelDisabled:!S,onSpeech:()=>le(!1),onSpeechDisabled:!ce,speechRecording:se,disabled:P}},pe)))))})),oe=ae;oe.Header=function(e){const{title:t,onOpenChange:n,open:r,children:a,className:o,style:i,classNames:l={},styles:s={},closable:d,forceRender:u}=e,{prefixCls:m}=f.useContext(x),p=`${m}-header`;return f.createElement(y.ZP,{motionEnter:!0,motionLeave:!0,motionName:`${p}-motion`,leavedClassName:`${p}-motion-hidden`,onEnterStart:C,onEnterActive:w,onLeaveStart:w,onLeaveActive:C,visible:r,forceRender:u},(({className:e,style:u})=>f.createElement("div",{className:c()(p,e,o),style:{...u,...i}},(!1!==d||t)&&f.createElement("div",{className:c()(`${p}-header`,l.header),style:{...s.header}},f.createElement("div",{className:`${p}-title`},t),!1!==d&&f.createElement("div",{className:`${p}-close`},f.createElement(v.ZP,{type:"text",icon:f.createElement(h,null),size:"small",onClick:()=>{n?.(!r)}}))),a&&f.createElement("div",{className:c()(`${p}-content`,l.content),style:{...s.content}},a))))};var ie=oe},43495:function(e,t,n){n.d(t,{I$:function(){return p}});var r=n(83262),a=n(36158),o=n(11568),i=n(9361),c=n(29691),l=n(92372),s=n(67294);const d=(0,o.jG)(i.Z.defaultAlgorithm),f={screenXS:!0,screenXSMin:!0,screenXSMax:!0,screenSM:!0,screenSMMin:!0,screenSMMax:!0,screenMD:!0,screenMDMin:!0,screenMDMax:!0,screenLG:!0,screenLGMin:!0,screenLGMax:!0,screenXL:!0,screenXLMin:!0,screenXLMax:!0,screenXXL:!0,screenXXLMin:!0},u=(e,t,n)=>{const r=n.getDerivativeToken(e),{override:a,...o}=t;let i={...r,override:a};return i=(0,l.Z)(i),o&&Object.entries(o).forEach((([e,t])=>{const{theme:n,...r}=t;let a=r;n&&(a=u({...i,...r},{override:r},n)),i[e]=a})),i};function m(){const{token:e,hashed:t,theme:n=d,override:r,cssVar:a}=s.useContext(i.Z._internalContext),[l,m,p]=(0,o.fp)(n,[i.Z.defaultSeed,e],{salt:`1.0.5-${t||""}`,override:r,getComputedToken:u,cssVar:a&&{prefix:a.prefix,key:a.key,unitless:c.NJ,ignore:c.ID,preserve:f}});return[n,p,t?m:"",l,a]}const{genStyleHooks:p,genComponentStyleHook:g,genSubStyleComponent:b}=(0,r.rb)({usePrefix:()=>{const{getPrefixCls:e,iconPrefixCls:t}=(0,a.Z)();return{iconPrefixCls:t,rootPrefixCls:e()}},useToken:()=>{const[e,t,n,r,a]=m();return{theme:e,realToken:t,hashId:n,token:r,cssVar:a}},useCSP:()=>{const{csp:e}=(0,a.Z)();return e??{}},layer:{name:"antdx",dependencies:["antd"]}})},36158:function(e,t,n){var r=n(21532),a=n(67294);t.Z=function(){const{getPrefixCls:e,direction:t,csp:n,iconPrefixCls:o,theme:i}=a.useContext(r.ZP.ConfigContext);return{theme:i,getPrefixCls:e,direction:t,csp:n,iconPrefixCls:o}}},51398:function(e,t,n){n.d(t,{Z:function(){return ie}});var r=n(87462),a=n(97685),o=n(4942),i=n(91),c=n(67294),l=n(93967),s=n.n(l),d=n(15063),f=[{index:7,amount:15},{index:6,amount:25},{index:5,amount:30},{index:5,amount:45},{index:5,amount:65},{index:5,amount:85},{index:4,amount:90},{index:3,amount:95},{index:2,amount:97},{index:1,amount:98}];function u(e,t,n){var r;return(r=Math.round(e.h)>=60&&Math.round(e.h)<=240?n?Math.round(e.h)-2*t:Math.round(e.h)+2*t:n?Math.round(e.h)+2*t:Math.round(e.h)-2*t)<0?r+=360:r>=360&&(r-=360),r}function m(e,t,n){return 0===e.h&&0===e.s?e.s:((r=n?e.s-.16*t:4===t?e.s+.16:e.s+.05*t)>1&&(r=1),n&&5===t&&r>.1&&(r=.1),r<.06&&(r=.06),Math.round(100*r)/100);var r}function p(e,t,n){var r;return r=n?e.v+.05*t:e.v-.15*t,r=Math.max(0,Math.min(1,r)),Math.round(100*r)/100}var g=["#fff1f0","#ffccc7","#ffa39e","#ff7875","#ff4d4f","#f5222d","#cf1322","#a8071a","#820014","#5c0011"];g.primary=g[5];var b=["#fff2e8","#ffd8bf","#ffbb96","#ff9c6e","#ff7a45","#fa541c","#d4380d","#ad2102","#871400","#610b00"];b.primary=b[5];var h=["#fff7e6","#ffe7ba","#ffd591","#ffc069","#ffa940","#fa8c16","#d46b08","#ad4e00","#873800","#612500"];h.primary=h[5];var v=["#fffbe6","#fff1b8","#ffe58f","#ffd666","#ffc53d","#faad14","#d48806","#ad6800","#874d00","#613400"];v.primary=v[5];var y=["#feffe6","#ffffb8","#fffb8f","#fff566","#ffec3d","#fadb14","#d4b106","#ad8b00","#876800","#614700"];y.primary=y[5];var x=["#fcffe6","#f4ffb8","#eaff8f","#d3f261","#bae637","#a0d911","#7cb305","#5b8c00","#3f6600","#254000"];x.primary=x[5];var C=["#f6ffed","#d9f7be","#b7eb8f","#95de64","#73d13d","#52c41a","#389e0d","#237804","#135200","#092b00"];C.primary=C[5];var w=["#e6fffb","#b5f5ec","#87e8de","#5cdbd3","#36cfc9","#13c2c2","#08979c","#006d75","#00474f","#002329"];w.primary=w[5];var $=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];$.primary=$[5];var E=["#f0f5ff","#d6e4ff","#adc6ff","#85a5ff","#597ef7","#2f54eb","#1d39c4","#10239e","#061178","#030852"];E.primary=E[5];var S=["#f9f0ff","#efdbff","#d3adf7","#b37feb","#9254de","#722ed1","#531dab","#391085","#22075e","#120338"];S.primary=S[5];var k=["#fff0f6","#ffd6e7","#ffadd2","#ff85c0","#f759ab","#eb2f96","#c41d7f","#9e1068","#780650","#520339"];k.primary=k[5];var N=["#a6a6a6","#999999","#8c8c8c","#808080","#737373","#666666","#404040","#1a1a1a","#000000","#000000"];N.primary=N[5];var Z=["#2a1215","#431418","#58181c","#791a1f","#a61d24","#d32029","#e84749","#f37370","#f89f9a","#fac8c3"];Z.primary=Z[5];var M=["#2b1611","#441d12","#592716","#7c3118","#aa3e19","#d84a1b","#e87040","#f3956a","#f8b692","#fad4bc"];M.primary=M[5];var T=["#2b1d11","#442a11","#593815","#7c4a15","#aa6215","#d87a16","#e89a3c","#f3b765","#f8cf8d","#fae3b7"];T.primary=T[5];var R=["#2b2111","#443111","#594214","#7c5914","#aa7714","#d89614","#e8b339","#f3cc62","#f8df8b","#faedb5"];R.primary=R[5];var I=["#2b2611","#443b11","#595014","#7c6e14","#aa9514","#d8bd14","#e8d639","#f3ea62","#f8f48b","#fafab5"];I.primary=I[5];var H=["#1f2611","#2e3c10","#3e4f13","#536d13","#6f9412","#8bbb11","#a9d134","#c9e75d","#e4f88b","#f0fab5"];H.primary=H[5];var P=["#162312","#1d3712","#274916","#306317","#3c8618","#49aa19","#6abe39","#8fd460","#b2e58b","#d5f2bb"];P.primary=P[5];var D=["#112123","#113536","#144848","#146262","#138585","#13a8a8","#33bcb7","#58d1c9","#84e2d8","#b2f1e8"];D.primary=D[5];var L=["#111a2c","#112545","#15325b","#15417e","#1554ad","#1668dc","#3c89e8","#65a9f3","#8dc5f8","#b7dcfa"];L.primary=L[5];var X=["#131629","#161d40","#1c2755","#203175","#263ea0","#2b4acb","#5273e0","#7f9ef3","#a8c1f8","#d2e0fa"];X.primary=X[5];var z=["#1a1325","#24163a","#301c4d","#3e2069","#51258f","#642ab5","#854eca","#ab7ae0","#cda8f0","#ebd7fa"];z.primary=z[5];var B=["#291321","#40162f","#551c3b","#75204f","#a02669","#cb2b83","#e0529c","#f37fb7","#f8a8cc","#fad2e3"];B.primary=B[5];var j=["#151515","#1f1f1f","#2d2d2d","#393939","#494949","#5a5a5a","#6a6a6a","#7b7b7b","#888888","#969696"];j.primary=j[5];var O=(0,c.createContext)({}),V=n(1413),W=n(71002),G=n(44958),A=n(27571),_=n(80334);function q(e){return e.replace(/-(.)/g,(function(e,t){return t.toUpperCase()}))}function F(e){return"object"===(0,W.Z)(e)&&"string"==typeof e.name&&"string"==typeof e.theme&&("object"===(0,W.Z)(e.icon)||"function"==typeof e.icon)}function K(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.keys(e).reduce((function(t,n){var r=e[n];if("class"===n)t.className=r,delete t.class;else delete t[n],t[q(n)]=r;return t}),{})}function J(e,t,n){return n?c.createElement(e.tag,(0,V.Z)((0,V.Z)({key:t},K(e.attrs)),n),(e.children||[]).map((function(n,r){return J(n,"".concat(t,"-").concat(e.tag,"-").concat(r))}))):c.createElement(e.tag,(0,V.Z)({key:t},K(e.attrs)),(e.children||[]).map((function(n,r){return J(n,"".concat(t,"-").concat(e.tag,"-").concat(r))})))}function Q(e){return function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=[],r=new d.t(e),a=r.toHsv(),o=5;o>0;o-=1){var i=new d.t({h:u(a,o,!0),s:m(a,o,!0),v:p(a,o,!0)});n.push(i)}n.push(r);for(var c=1;c<=4;c+=1){var l=new d.t({h:u(a,c),s:m(a,c),v:p(a,c)});n.push(l)}return"dark"===t.theme?f.map((function(e){var r=e.index,a=e.amount;return new d.t(t.backgroundColor||"#141414").mix(n[r],a).toHexString()})):n.map((function(e){return e.toHexString()}))}(e)[0]}function U(e){return e?Array.isArray(e)?e:[e]:[]}var Y=["icon","className","onClick","style","primaryColor","secondaryColor"],ee={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};var te=function(e){var t,n,r,a,o,l,s,d,f=e.icon,u=e.className,m=e.onClick,p=e.style,g=e.primaryColor,b=e.secondaryColor,h=(0,i.Z)(e,Y),v=c.useRef(),y=ee;if(g&&(y={primaryColor:g,secondaryColor:b||Q(g)}),t=v,n=(0,c.useContext)(O),r=n.csp,a=n.prefixCls,o=n.layer,l="\n.anticon {\n  display: inline-flex;\n  align-items: center;\n  color: inherit;\n  font-style: normal;\n  line-height: 0;\n  text-align: center;\n  text-transform: none;\n  vertical-align: -0.125em;\n  text-rendering: optimizeLegibility;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.anticon > * {\n  line-height: 1;\n}\n\n.anticon svg {\n  display: inline-block;\n}\n\n.anticon::before {\n  display: none;\n}\n\n.anticon .anticon-icon {\n  display: block;\n}\n\n.anticon[tabindex] {\n  cursor: pointer;\n}\n\n.anticon-spin::before,\n.anticon-spin {\n  display: inline-block;\n  -webkit-animation: loadingCircle 1s infinite linear;\n  animation: loadingCircle 1s infinite linear;\n}\n\n@-webkit-keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n",a&&(l=l.replace(/anticon/g,a)),o&&(l="@layer ".concat(o," {\n").concat(l,"\n}")),(0,c.useEffect)((function(){var e=t.current,n=(0,A.A)(e);(0,G.hq)(l,"@ant-design-icons",{prepend:!o,csp:r,attachTo:n})}),[]),s=F(f),d="icon should be icon definiton, but got ".concat(f),(0,_.ZP)(s,"[@ant-design/icons] ".concat(d)),!F(f))return null;var x=f;return x&&"function"==typeof x.icon&&(x=(0,V.Z)((0,V.Z)({},x),{},{icon:x.icon(y.primaryColor,y.secondaryColor)})),J(x.icon,"svg-".concat(x.name),(0,V.Z)((0,V.Z)({className:u,onClick:m,style:p,"data-icon":x.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},h),{},{ref:v}))};te.displayName="IconReact",te.getTwoToneColors=function(){return(0,V.Z)({},ee)},te.setTwoToneColors=function(e){var t=e.primaryColor,n=e.secondaryColor;ee.primaryColor=t,ee.secondaryColor=n||Q(t),ee.calculated=!!n};var ne=te;function re(e){var t=U(e),n=(0,a.Z)(t,2),r=n[0],o=n[1];return ne.setTwoToneColors({primaryColor:r,secondaryColor:o})}var ae=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];re($.primary);var oe=c.forwardRef((function(e,t){var n=e.className,l=e.icon,d=e.spin,f=e.rotate,u=e.tabIndex,m=e.onClick,p=e.twoToneColor,g=(0,i.Z)(e,ae),b=c.useContext(O),h=b.prefixCls,v=void 0===h?"anticon":h,y=b.rootClassName,x=s()(y,v,(0,o.Z)((0,o.Z)({},"".concat(v,"-").concat(l.name),!!l.name),"".concat(v,"-spin"),!!d||"loading"===l.name),n),C=u;void 0===C&&m&&(C=-1);var w=f?{msTransform:"rotate(".concat(f,"deg)"),transform:"rotate(".concat(f,"deg)")}:void 0,$=U(p),E=(0,a.Z)($,2),S=E[0],k=E[1];return c.createElement("span",(0,r.Z)({role:"img","aria-label":l.name},g,{ref:t,tabIndex:C,onClick:m,className:x}),c.createElement(ne,{icon:l,primaryColor:S,secondaryColor:k,style:w}))}));oe.displayName="AntdIcon",oe.getTwoToneColor=function(){var e=ne.getTwoToneColors();return e.calculated?[e.primaryColor,e.secondaryColor]:e.primaryColor},oe.setTwoToneColor=re;var ie=oe},86250:function(e,t,n){n.d(t,{Z:function(){return $}});var r=n(67294),a=n(93967),o=n.n(a),i=n(98423),c=n(98065),l=n(53124),s=n(83559),d=n(83262);const f=["wrap","nowrap","wrap-reverse"],u=["flex-start","flex-end","start","end","center","space-between","space-around","space-evenly","stretch","normal","left","right"],m=["center","start","end","flex-start","flex-end","self-start","self-end","baseline","normal","stretch"];var p=function(e,t){return o()(Object.assign(Object.assign(Object.assign({},((e,t)=>{const n=!0===t.wrap?"wrap":t.wrap;return{[`${e}-wrap-${n}`]:n&&f.includes(n)}})(e,t)),((e,t)=>{const n={};return m.forEach((r=>{n[`${e}-align-${r}`]=t.align===r})),n[`${e}-align-stretch`]=!t.align&&!!t.vertical,n})(e,t)),((e,t)=>{const n={};return u.forEach((r=>{n[`${e}-justify-${r}`]=t.justify===r})),n})(e,t)))};const g=e=>{const{componentCls:t}=e;return{[t]:{display:"flex",margin:0,padding:0,"&-vertical":{flexDirection:"column"},"&-rtl":{direction:"rtl"},"&:empty":{display:"none"}}}},b=e=>{const{componentCls:t}=e;return{[t]:{"&-gap-small":{gap:e.flexGapSM},"&-gap-middle":{gap:e.flexGap},"&-gap-large":{gap:e.flexGapLG}}}},h=e=>{const{componentCls:t}=e,n={};return f.forEach((e=>{n[`${t}-wrap-${e}`]={flexWrap:e}})),n},v=e=>{const{componentCls:t}=e,n={};return m.forEach((e=>{n[`${t}-align-${e}`]={alignItems:e}})),n},y=e=>{const{componentCls:t}=e,n={};return u.forEach((e=>{n[`${t}-justify-${e}`]={justifyContent:e}})),n};var x=(0,s.I$)("Flex",(e=>{const{paddingXS:t,padding:n,paddingLG:r}=e,a=(0,d.IX)(e,{flexGapSM:t,flexGap:n,flexGapLG:r});return[g(a),b(a),h(a),v(a),y(a)]}),(()=>({})),{resetStyle:!1}),C=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n};const w=r.forwardRef(((e,t)=>{const{prefixCls:n,rootClassName:a,className:s,style:d,flex:f,gap:u,children:m,vertical:g=!1,component:b="div"}=e,h=C(e,["prefixCls","rootClassName","className","style","flex","gap","children","vertical","component"]),{flex:v,direction:y,getPrefixCls:w}=r.useContext(l.E_),$=w("flex",n),[E,S,k]=x($),N=null!=g?g:null==v?void 0:v.vertical,Z=o()(s,a,null==v?void 0:v.className,$,S,k,p($,e),{[`${$}-rtl`]:"rtl"===y,[`${$}-gap-${u}`]:(0,c.n)(u),[`${$}-vertical`]:N}),M=Object.assign(Object.assign({},null==v?void 0:v.style),d);return f&&(M.flex=f),u&&!(0,c.n)(u)&&(M.gap=u),E(r.createElement(b,Object.assign({ref:t,className:Z,style:M},(0,i.Z)(h,["justify","wrap","align"])),m))}));var $=w}}]);