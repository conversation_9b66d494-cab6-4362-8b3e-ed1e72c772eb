#!/usr/bin/env python3
"""
测试配置加载
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.utils.config import settings

print("=== MCP配置测试 ===")
print(f"MCP_WEBSEARCH_URL: {settings.MCP_WEBSEARCH_URL}")
print(f"MCP_WEBSEARCH_TRANSPORT: {settings.MCP_WEBSEARCH_TRANSPORT}")
print(f"MCP_ENABLED: {settings.MCP_ENABLED}")

print("\n=== 环境变量直接读取 ===")
import os
print(f"MCP_WEBSEARCH_URL (env): {os.getenv('MCP_WEBSEARCH_URL', 'NOT_SET')}")
print(f"MCP_WEBSEARCH_TRANSPORT (env): {os.getenv('MCP_WEBSEARCH_TRANSPORT', 'NOT_SET')}")
print(f"MCP_ENABLED (env): {os.getenv('MCP_ENABLED', 'NOT_SET')}")
