"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7019],{47019:function(e,t,n){n.d(t,{Z:function(){return Ge}});var r=n(65223),o=n(74902),l=n(67294),i=n(93967),a=n.n(i),s=n(29372),c=n(33603),u=n(35792);function d(e){const[t,n]=l.useState(e);return l.useEffect((()=>{const t=setTimeout((()=>{n(e)}),e.length?0:10);return()=>{clearTimeout(t)}}),[e]),t}var m=n(11568),f=n(14747),p=n(50438),g=n(33507),h=n(83262),b=n(83559);var v=e=>{const{componentCls:t}=e,n=`${t}-show-help-item`;return{[`${t}-show-help`]:{transition:`opacity ${e.motionDurationFast} ${e.motionEaseInOut}`,"&-appear, &-enter":{opacity:0,"&-active":{opacity:1}},"&-leave":{opacity:1,"&-active":{opacity:0}},[n]:{overflow:"hidden",transition:`height ${e.motionDurationFast} ${e.motionEaseInOut},\n                     opacity ${e.motionDurationFast} ${e.motionEaseInOut},\n                     transform ${e.motionDurationFast} ${e.motionEaseInOut} !important`,[`&${n}-appear, &${n}-enter`]:{transform:"translateY(-5px)",opacity:0,"&-active":{transform:"translateY(0)",opacity:1}},[`&${n}-leave-active`]:{transform:"translateY(-5px)"}}}}};const y=e=>({legend:{display:"block",width:"100%",marginBottom:e.marginLG,padding:0,color:e.colorTextDescription,fontSize:e.fontSizeLG,lineHeight:"inherit",border:0,borderBottom:`${(0,m.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},'input[type="search"]':{boxSizing:"border-box"},'input[type="radio"], input[type="checkbox"]':{lineHeight:"normal"},'input[type="file"]':{display:"block"},'input[type="range"]':{display:"block",width:"100%"},"select[multiple], select[size]":{height:"auto"},"input[type='file']:focus,\n  input[type='radio']:focus,\n  input[type='checkbox']:focus":{outline:0,boxShadow:`0 0 0 ${(0,m.bf)(e.controlOutlineWidth)} ${e.controlOutline}`},output:{display:"block",paddingTop:15,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight}}),$=(e,t)=>{const{formItemCls:n}=e;return{[n]:{[`${n}-label > label`]:{height:t},[`${n}-control-input`]:{minHeight:t}}}},x=e=>{const{componentCls:t}=e;return{[e.componentCls]:Object.assign(Object.assign(Object.assign({},(0,f.Wf)(e)),y(e)),{[`${t}-text`]:{display:"inline-block",paddingInlineEnd:e.paddingSM},"&-small":Object.assign({},$(e,e.controlHeightSM)),"&-large":Object.assign({},$(e,e.controlHeightLG))})}},w=e=>{const{formItemCls:t,iconCls:n,rootPrefixCls:r,antCls:o,labelRequiredMarkColor:l,labelColor:i,labelFontSize:a,labelHeight:s,labelColonMarginInlineStart:c,labelColonMarginInlineEnd:u,itemMarginBottom:d}=e;return{[t]:Object.assign(Object.assign({},(0,f.Wf)(e)),{marginBottom:d,verticalAlign:"top","&-with-help":{transition:"none"},[`&-hidden,\n        &-hidden${o}-row`]:{display:"none"},"&-has-warning":{[`${t}-split`]:{color:e.colorError}},"&-has-error":{[`${t}-split`]:{color:e.colorWarning}},[`${t}-label`]:{flexGrow:0,overflow:"hidden",whiteSpace:"nowrap",textAlign:"end",verticalAlign:"middle","&-left":{textAlign:"start"},"&-wrap":{overflow:"unset",lineHeight:e.lineHeight,whiteSpace:"unset","> label":{verticalAlign:"middle",textWrap:"balance"}},"> label":{position:"relative",display:"inline-flex",alignItems:"center",maxWidth:"100%",height:s,color:i,fontSize:a,[`> ${n}`]:{fontSize:e.fontSize,verticalAlign:"top"},[`&${t}-required`]:{"&::before":{display:"inline-block",marginInlineEnd:e.marginXXS,color:l,fontSize:e.fontSize,fontFamily:"SimSun, sans-serif",lineHeight:1,content:'"*"'},[`&${t}-required-mark-hidden, &${t}-required-mark-optional`]:{"&::before":{display:"none"}}},[`${t}-optional`]:{display:"inline-block",marginInlineStart:e.marginXXS,color:e.colorTextDescription,[`&${t}-required-mark-hidden`]:{display:"none"}},[`${t}-tooltip`]:{color:e.colorTextDescription,cursor:"help",writingMode:"horizontal-tb",marginInlineStart:e.marginXXS},"&::after":{content:'":"',position:"relative",marginBlock:0,marginInlineStart:c,marginInlineEnd:u},[`&${t}-no-colon::after`]:{content:'"\\a0"'}}},[`${t}-control`]:{"--ant-display":"flex",flexDirection:"column",flexGrow:1,[`&:first-child:not([class^="'${r}-col-'"]):not([class*="' ${r}-col-'"])`]:{width:"100%"},"&-input":{position:"relative",display:"flex",alignItems:"center",minHeight:e.controlHeight,"&-content":{flex:"auto",maxWidth:"100%"}}},[t]:{"&-additional":{display:"flex",flexDirection:"column"},"&-explain, &-extra":{clear:"both",color:e.colorTextDescription,fontSize:e.fontSize,lineHeight:e.lineHeight},"&-explain-connected":{width:"100%"},"&-extra":{minHeight:e.controlHeightSM,transition:`color ${e.motionDurationMid} ${e.motionEaseOut}`},"&-explain":{"&-error":{color:e.colorError},"&-warning":{color:e.colorWarning}}},[`&-with-help ${t}-explain`]:{height:"auto",opacity:1},[`${t}-feedback-icon`]:{fontSize:e.fontSize,textAlign:"center",visibility:"visible",animationName:p.kr,animationDuration:e.motionDurationMid,animationTimingFunction:e.motionEaseOutBack,pointerEvents:"none","&-success":{color:e.colorSuccess},"&-error":{color:e.colorError},"&-warning":{color:e.colorWarning},"&-validating":{color:e.colorPrimary}}})}},C=(e,t)=>{const{formItemCls:n}=e;return{[`${t}-horizontal`]:{[`${n}-label`]:{flexGrow:0},[`${n}-control`]:{flex:"1 1 0",minWidth:0},[`${n}-label[class$='-24'], ${n}-label[class*='-24 ']`]:{[`& + ${n}-control`]:{minWidth:"unset"}}}}},O=e=>{const{componentCls:t,formItemCls:n,inlineItemMarginBottom:r}=e;return{[`${t}-inline`]:{display:"flex",flexWrap:"wrap",[n]:{flex:"none",marginInlineEnd:e.margin,marginBottom:r,"&-row":{flexWrap:"nowrap"},[`> ${n}-label,\n        > ${n}-control`]:{display:"inline-block",verticalAlign:"top"},[`> ${n}-label`]:{flex:"none"},[`${t}-text`]:{display:"inline-block"},[`${n}-has-feedback`]:{display:"inline-block"}}}}},E=e=>({padding:e.verticalLabelPadding,margin:e.verticalLabelMargin,whiteSpace:"initial",textAlign:"start","> label":{margin:0,"&::after":{visibility:"hidden"}}}),k=e=>{const{componentCls:t,formItemCls:n,rootPrefixCls:r}=e;return{[`${n} ${n}-label`]:E(e),[`${t}:not(${t}-inline)`]:{[n]:{flexWrap:"wrap",[`${n}-label, ${n}-control`]:{[`&:not([class*=" ${r}-col-xs"])`]:{flex:"0 0 100%",maxWidth:"100%"}}}}}},S=e=>{const{componentCls:t,formItemCls:n,antCls:r}=e;return{[`${t}-vertical`]:{[`${n}:not(${n}-horizontal)`]:{[`${n}-row`]:{flexDirection:"column"},[`${n}-label > label`]:{height:"auto"},[`${n}-control`]:{width:"100%"},[`${n}-label,\n        ${r}-col-24${n}-label,\n        ${r}-col-xl-24${n}-label`]:E(e)}},[`@media (max-width: ${(0,m.bf)(e.screenXSMax)})`]:[k(e),{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-xs-24${n}-label`]:E(e)}}}],[`@media (max-width: ${(0,m.bf)(e.screenSMMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-sm-24${n}-label`]:E(e)}}},[`@media (max-width: ${(0,m.bf)(e.screenMDMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-md-24${n}-label`]:E(e)}}},[`@media (max-width: ${(0,m.bf)(e.screenLGMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-lg-24${n}-label`]:E(e)}}}}},I=e=>{const{formItemCls:t,antCls:n}=e;return{[`${t}-vertical`]:{[`${t}-row`]:{flexDirection:"column"},[`${t}-label > label`]:{height:"auto"},[`${t}-control`]:{width:"100%"}},[`${t}-vertical ${t}-label,\n      ${n}-col-24${t}-label,\n      ${n}-col-xl-24${t}-label`]:E(e),[`@media (max-width: ${(0,m.bf)(e.screenXSMax)})`]:[k(e),{[t]:{[`${n}-col-xs-24${t}-label`]:E(e)}}],[`@media (max-width: ${(0,m.bf)(e.screenSMMax)})`]:{[t]:{[`${n}-col-sm-24${t}-label`]:E(e)}},[`@media (max-width: ${(0,m.bf)(e.screenMDMax)})`]:{[t]:{[`${n}-col-md-24${t}-label`]:E(e)}},[`@media (max-width: ${(0,m.bf)(e.screenLGMax)})`]:{[t]:{[`${n}-col-lg-24${t}-label`]:E(e)}}}},j=(e,t)=>(0,h.IX)(e,{formItemCls:`${e.componentCls}-item`,rootPrefixCls:t});var M=(0,b.I$)("Form",((e,t)=>{let{rootPrefixCls:n}=t;const r=j(e,n);return[x(r),w(r),v(r),C(r,r.componentCls),C(r,r.formItemCls),O(r),S(r),I(r),(0,g.Z)(r),p.kr]}),(e=>({labelRequiredMarkColor:e.colorError,labelColor:e.colorTextHeading,labelFontSize:e.fontSize,labelHeight:e.controlHeight,labelColonMarginInlineStart:e.marginXXS/2,labelColonMarginInlineEnd:e.marginXS,itemMarginBottom:e.marginLG,verticalLabelPadding:`0 0 ${e.paddingXS}px`,verticalLabelMargin:0,inlineItemMarginBottom:0})),{order:-1e3});const F=[];function N(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return{key:"string"==typeof e?e:`${t}-${r}`,error:e,errorStatus:n}}var P=e=>{let{help:t,helpStatus:n,errors:i=F,warnings:m=F,className:f,fieldId:p,onVisibleChanged:g}=e;const{prefixCls:h}=l.useContext(r.Rk),b=`${h}-item-explain`,v=(0,u.Z)(h),[y,$,x]=M(h,v),w=l.useMemo((()=>(0,c.Z)(h)),[h]),C=d(i),O=d(m),E=l.useMemo((()=>null!=t?[N(t,"help",n)]:[].concat((0,o.Z)(C.map(((e,t)=>N(e,"error","error",t)))),(0,o.Z)(O.map(((e,t)=>N(e,"warning","warning",t)))))),[t,n,C,O]),k=l.useMemo((()=>{const e={};return E.forEach((t=>{let{key:n}=t;e[n]=(e[n]||0)+1})),E.map(((t,n)=>Object.assign(Object.assign({},t),{key:e[t.key]>1?`${t.key}-fallback-${n}`:t.key})))}),[E]),S={};return p&&(S.id=`${p}_help`),y(l.createElement(s.ZP,{motionDeadline:w.motionDeadline,motionName:`${h}-show-help`,visible:!!k.length,onVisibleChanged:g},(e=>{const{className:t,style:n}=e;return l.createElement("div",Object.assign({},S,{className:a()(b,t,x,v,f,$),style:n}),l.createElement(s.V4,Object.assign({keys:k},(0,c.Z)(h),{motionName:`${h}-show-help-item`,component:!1}),(e=>{const{key:t,error:n,errorStatus:r,className:o,style:i}=e;return l.createElement("div",{key:t,className:a()(o,{[`${b}-${r}`]:r}),style:i},n)})))})))},Z=n(88692),q=n(53124),R=n(98866),W=n(98675),H=n(97647),_=n(34203);const T=e=>"object"==typeof e&&null!=e&&1===e.nodeType,z=(e,t)=>(!t||"hidden"!==e)&&"visible"!==e&&"clip"!==e,D=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){const n=getComputedStyle(e,null);return z(n.overflowY,t)||z(n.overflowX,t)||(e=>{const t=(e=>{if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}})(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)})(e)}return!1},A=(e,t,n,r,o,l,i,a)=>l<e&&i>t||l>e&&i<t?0:l<=e&&a<=n||i>=t&&a>=n?l-e-r:i>t&&a<n||l<e&&a>n?i-t+o:0,L=e=>{const t=e.parentElement;return null==t?e.getRootNode().host||null:t},B=(e,t)=>{var n,r,o,l;if("undefined"==typeof document)return[];const{scrollMode:i,block:a,inline:s,boundary:c,skipOverflowHiddenElements:u}=t,d="function"==typeof c?c:e=>e!==c;if(!T(e))throw new TypeError("Invalid target");const m=document.scrollingElement||document.documentElement,f=[];let p=e;for(;T(p)&&d(p);){if(p=L(p),p===m){f.push(p);break}null!=p&&p===document.body&&D(p)&&!D(document.documentElement)||null!=p&&D(p,u)&&f.push(p)}const g=null!=(r=null==(n=window.visualViewport)?void 0:n.width)?r:innerWidth,h=null!=(l=null==(o=window.visualViewport)?void 0:o.height)?l:innerHeight,{scrollX:b,scrollY:v}=window,{height:y,width:$,top:x,right:w,bottom:C,left:O}=e.getBoundingClientRect(),{top:E,right:k,bottom:S,left:I}=(e=>{const t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e);let j="start"===a||"nearest"===a?x-E:"end"===a?C+S:x+y/2-E+S,M="center"===s?O+$/2-I+k:"end"===s?w+k:O-I;const F=[];for(let e=0;e<f.length;e++){const t=f[e],{height:n,width:r,top:o,right:l,bottom:c,left:u}=t.getBoundingClientRect();if("if-needed"===i&&x>=0&&O>=0&&C<=h&&w<=g&&(t===m&&!D(t)||x>=o&&C<=c&&O>=u&&w<=l))return F;const d=getComputedStyle(t),p=parseInt(d.borderLeftWidth,10),E=parseInt(d.borderTopWidth,10),k=parseInt(d.borderRightWidth,10),S=parseInt(d.borderBottomWidth,10);let I=0,N=0;const P="offsetWidth"in t?t.offsetWidth-t.clientWidth-p-k:0,Z="offsetHeight"in t?t.offsetHeight-t.clientHeight-E-S:0,q="offsetWidth"in t?0===t.offsetWidth?0:r/t.offsetWidth:0,R="offsetHeight"in t?0===t.offsetHeight?0:n/t.offsetHeight:0;if(m===t)I="start"===a?j:"end"===a?j-h:"nearest"===a?A(v,v+h,h,E,S,v+j,v+j+y,y):j-h/2,N="start"===s?M:"center"===s?M-g/2:"end"===s?M-g:A(b,b+g,g,p,k,b+M,b+M+$,$),I=Math.max(0,I+v),N=Math.max(0,N+b);else{I="start"===a?j-o-E:"end"===a?j-c+S+Z:"nearest"===a?A(o,c,n,E,S+Z,j,j+y,y):j-(o+n/2)+Z/2,N="start"===s?M-u-p:"center"===s?M-(u+r/2)+P/2:"end"===s?M-l+k+P:A(u,l,r,p,k+P,M,M+$,$);const{scrollLeft:e,scrollTop:i}=t;I=0===R?0:Math.max(0,Math.min(i+I/R,t.scrollHeight-n/R+Z)),N=0===q?0:Math.max(0,Math.min(e+N/q,t.scrollWidth-r/q+P)),j+=i-I,M+=e-N}F.push({el:t,top:I,left:N})}return F};function V(e,t){if(!e.isConnected||!(e=>{let t=e;for(;t&&t.parentNode;){if(t.parentNode===document)return!0;t=t.parentNode instanceof ShadowRoot?t.parentNode.host:t.parentNode}return!1})(e))return;const n=(e=>{const t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e);if((e=>"object"==typeof e&&"function"==typeof e.behavior)(t))return t.behavior(B(e,t));const r="boolean"==typeof t||null==t?void 0:t.behavior;for(const{el:o,top:l,left:i}of B(e,(e=>!1===e?{block:"end",inline:"nearest"}:(e=>e===Object(e)&&0!==Object.keys(e).length)(e)?e:{block:"start",inline:"nearest"})(t))){const e=l-n.top+n.bottom,t=i-n.left+n.right;o.scroll({top:e,left:t,behavior:r})}}const X=["parentNode"];function G(e){return void 0===e||!1===e?[]:Array.isArray(e)?e:[e]}function Y(e,t){if(!e.length)return;const n=e.join("_");if(t)return`${t}_${n}`;return X.includes(n)?`form_item_${n}`:n}function U(e,t,n,r,o,l){let i=r;return void 0!==l?i=l:n.validating?i="validating":e.length?i="error":t.length?i="warning":(n.touched||o&&n.validated)&&(i="success"),i}var K=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function Q(e){return G(e).join("_")}function J(e,t){const n=t.getFieldInstance(e),r=(0,_.bn)(n);if(r)return r;const o=Y(G(e),t.__INTERNAL__.name);return o?document.getElementById(o):void 0}function ee(e){const[t]=(0,Z.cI)(),n=l.useRef({}),r=l.useMemo((()=>null!=e?e:Object.assign(Object.assign({},t),{__INTERNAL__:{itemRef:e=>t=>{const r=Q(e);t?n.current[r]=t:delete n.current[r]}},scrollToField:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{focus:n}=t,o=K(t,["focus"]),l=J(e,r);l&&(V(l,Object.assign({scrollMode:"if-needed",block:"nearest"},o)),n&&r.focusField(e))},focusField:e=>{var t,n;const o=r.getFieldInstance(e);"function"==typeof(null==o?void 0:o.focus)?o.focus():null===(n=null===(t=J(e,r))||void 0===t?void 0:t.focus)||void 0===n||n.call(t)},getFieldInstance:e=>{const t=Q(e);return n.current[t]}})),[e,t]);return[r]}var te=n(37920),ne=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const re=(e,t)=>{const n=l.useContext(R.Z),{getPrefixCls:o,direction:i,requiredMark:s,colon:c,scrollToFirstError:d,className:m,style:f}=(0,q.dj)("form"),{prefixCls:p,className:g,rootClassName:h,size:b,disabled:v=n,form:y,colon:$,labelAlign:x,labelWrap:w,labelCol:C,wrapperCol:O,hideRequiredMark:E,layout:k="horizontal",scrollToFirstError:S,requiredMark:I,onFinishFailed:j,name:F,style:N,feedbackIcons:P,variant:_}=e,T=ne(e,["prefixCls","className","rootClassName","size","disabled","form","colon","labelAlign","labelWrap","labelCol","wrapperCol","hideRequiredMark","layout","scrollToFirstError","requiredMark","onFinishFailed","name","style","feedbackIcons","variant"]),z=(0,W.Z)(b),D=l.useContext(te.Z);const A=l.useMemo((()=>void 0!==I?I:!E&&(void 0===s||s)),[E,I,s]),L=null!=$?$:c,B=o("form",p),V=(0,u.Z)(B),[X,G,Y]=M(B,V),U=a()(B,`${B}-${k}`,{[`${B}-hide-required-mark`]:!1===A,[`${B}-rtl`]:"rtl"===i,[`${B}-${z}`]:z},Y,V,G,m,g,h),[K]=ee(y),{__INTERNAL__:Q}=K;Q.name=F;const J=l.useMemo((()=>({name:F,labelAlign:x,labelCol:C,labelWrap:w,wrapperCol:O,vertical:"vertical"===k,colon:L,requiredMark:A,itemRef:Q.itemRef,form:K,feedbackIcons:P})),[F,x,C,O,k,L,A,K,P]),re=l.useRef(null);l.useImperativeHandle(t,(()=>{var e;return Object.assign(Object.assign({},K),{nativeElement:null===(e=re.current)||void 0===e?void 0:e.nativeElement})}));const oe=(e,t)=>{if(e){let n={block:"nearest"};"object"==typeof e&&(n=Object.assign(Object.assign({},n),e)),K.scrollToField(t,n)}};return X(l.createElement(r.pg.Provider,{value:_},l.createElement(R.n,{disabled:v},l.createElement(H.Z.Provider,{value:z},l.createElement(r.RV,{validateMessages:D},l.createElement(r.q3.Provider,{value:J},l.createElement(Z.ZP,Object.assign({id:F},T,{name:F,onFinishFailed:e=>{if(null==j||j(e),e.errorFields.length){const t=e.errorFields[0].name;if(void 0!==S)return void oe(S,t);void 0!==d&&oe(d,t)}},form:K,ref:re,style:Object.assign(Object.assign({},f),N),className:U}))))))))};var oe=l.forwardRef(re),le=n(30470),ie=n(42550),ae=n(96159),se=n(27288),ce=n(50344);const ue=()=>{const{status:e,errors:t=[],warnings:n=[]}=l.useContext(r.aM);return{status:e,errors:t,warnings:n}};ue.Context=r.aM;var de=ue,me=n(75164);var fe=n(5110),pe=n(8410),ge=n(98423),he=n(17621),be=n(56790),ve=n(21584);const ye=e=>{const{formItemCls:t}=e;return{"@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none)":{[`${t}-control`]:{display:"flex"}}}};var $e=(0,b.bk)(["Form","item-item"],((e,t)=>{let{rootPrefixCls:n}=t;const r=j(e,n);return[ye(r)]})),xe=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};var we=e=>{const{prefixCls:t,status:n,labelCol:o,wrapperCol:i,children:s,errors:c,warnings:u,_internalItemRender:d,extra:m,help:f,fieldId:p,marginBottom:g,onErrorVisibleChanged:h,label:b}=e,v=`${t}-item`,y=l.useContext(r.q3),$=l.useMemo((()=>{let e=Object.assign({},i||y.wrapperCol||{});if(null===b&&!o&&!i&&y.labelCol){[void 0,"xs","sm","md","lg","xl","xxl"].forEach((t=>{const n=t?[t]:[],r=(0,be.U2)(y.labelCol,n),o="object"==typeof r?r:{},l=(0,be.U2)(e,n);"span"in o&&!("offset"in("object"==typeof l?l:{}))&&o.span<24&&(e=(0,be.t8)(e,[].concat(n,["offset"]),o.span))}))}return e}),[i,y]),x=a()(`${v}-control`,$.className),w=l.useMemo((()=>{const{labelCol:e,wrapperCol:t}=y;return xe(y,["labelCol","wrapperCol"])}),[y]),C=l.useRef(null),[O,E]=l.useState(0);(0,pe.Z)((()=>{m&&C.current?E(C.current.clientHeight):E(0)}),[m]);const k=l.createElement("div",{className:`${v}-control-input`},l.createElement("div",{className:`${v}-control-input-content`},s)),S=l.useMemo((()=>({prefixCls:t,status:n})),[t,n]),I=null!==g||c.length||u.length?l.createElement(r.Rk.Provider,{value:S},l.createElement(P,{fieldId:p,errors:c,warnings:u,help:f,helpStatus:n,className:`${v}-explain-connected`,onVisibleChanged:h})):null,j={};p&&(j.id=`${p}_extra`);const M=m?l.createElement("div",Object.assign({},j,{className:`${v}-extra`,ref:C}),m):null,F=I||M?l.createElement("div",{className:`${v}-additional`,style:g?{minHeight:g+O}:{}},I,M):null,N=d&&"pro_table_render"===d.mark&&d.render?d.render(e,{input:k,errorList:I,extra:M}):l.createElement(l.Fragment,null,k,F);return l.createElement(r.q3.Provider,{value:w},l.createElement(ve.Z,Object.assign({},$,{className:x}),N),l.createElement($e,{prefixCls:t}))},Ce=n(87462),Oe=n(36688),Ee=n(93771),ke=function(e,t){return l.createElement(Ee.Z,(0,Ce.Z)({},e,{ref:t,icon:Oe.Z}))};var Se=l.forwardRef(ke);var Ie=function(e){return null==e?null:"object"!=typeof e||(0,l.isValidElement)(e)?{title:e}:e},je=n(10110),Me=n(24457),Fe=n(83062),Ne=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};var Pe=e=>{let{prefixCls:t,label:n,htmlFor:o,labelCol:i,labelAlign:s,colon:c,required:u,requiredMark:d,tooltip:m,vertical:f}=e;var p;const[g]=(0,je.Z)("Form"),{labelAlign:h,labelCol:b,labelWrap:v,colon:y}=l.useContext(r.q3);if(!n)return null;const $=i||b||{},x=s||h,w=`${t}-item-label`,C=a()(w,"left"===x&&`${w}-left`,$.className,{[`${w}-wrap`]:!!v});let O=n;const E=!0===c||!1!==y&&!1!==c;E&&!f&&"string"==typeof n&&n.trim()&&(O=n.replace(/[:|：]\s*$/,""));const k=Ie(m);if(k){const{icon:e=l.createElement(Se,null)}=k,n=Ne(k,["icon"]),r=l.createElement(Fe.Z,Object.assign({},n),l.cloneElement(e,{className:`${t}-item-tooltip`,title:"",onClick:e=>{e.preventDefault()},tabIndex:null}));O=l.createElement(l.Fragment,null,O,r)}const S="optional"===d,I="function"==typeof d,j=!1===d;let M;I?O=d(O,{required:!!u}):S&&!u&&(O=l.createElement(l.Fragment,null,O,l.createElement("span",{className:`${t}-item-optional`,title:""},(null==g?void 0:g.optional)||(null===(p=Me.Z.Form)||void 0===p?void 0:p.optional)))),j?M="hidden":(S||I)&&(M="optional");const F=a()({[`${t}-item-required`]:u,[`${t}-item-required-mark-${M}`]:M,[`${t}-item-no-colon`]:!E});return l.createElement(ve.Z,Object.assign({},$,{className:C}),l.createElement("label",{htmlFor:o,className:F,title:"string"==typeof n?n:""},O))},Ze=n(19735),qe=n(17012),Re=n(29950),We=n(19267);const He={success:Ze.Z,warning:Re.Z,error:qe.Z,validating:We.Z};function _e(e){let{children:t,errors:n,warnings:o,hasFeedback:i,validateStatus:s,prefixCls:c,meta:u,noStyle:d}=e;const m=`${c}-item`,{feedbackIcons:f}=l.useContext(r.q3),p=U(n,o,u,null,!!i,s),{isFormItemInput:g,status:h,hasFeedback:b,feedbackIcon:v}=l.useContext(r.aM),y=l.useMemo((()=>{var e;let t;if(i){const r=!0!==i&&i.icons||f,s=p&&(null===(e=null==r?void 0:r({status:p,errors:n,warnings:o}))||void 0===e?void 0:e[p]),c=p&&He[p];t=!1!==s&&c?l.createElement("span",{className:a()(`${m}-feedback-icon`,`${m}-feedback-icon-${p}`)},s||l.createElement(c,null)):null}const r={status:p||"",errors:n,warnings:o,hasFeedback:!!i,feedbackIcon:t,isFormItemInput:!0};return d&&(r.status=(null!=p?p:h)||"",r.isFormItemInput=g,r.hasFeedback=!!(null!=i?i:b),r.feedbackIcon=void 0!==i?r.feedbackIcon:v),r}),[p,i,d,g,h]);return l.createElement(r.aM.Provider,{value:y},t)}var Te=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function ze(e){const{prefixCls:t,className:n,rootClassName:o,style:i,help:s,errors:c,warnings:u,validateStatus:m,meta:f,hasFeedback:p,hidden:g,children:h,fieldId:b,required:v,isRequired:y,onSubItemMetaChange:$,layout:x}=e,w=Te(e,["prefixCls","className","rootClassName","style","help","errors","warnings","validateStatus","meta","hasFeedback","hidden","children","fieldId","required","isRequired","onSubItemMetaChange","layout"]),C=`${t}-item`,{requiredMark:O,vertical:E}=l.useContext(r.q3),k=E||"vertical"===x,S=l.useRef(null),I=d(c),j=d(u),M=null!=s,F=!!(M||c.length||u.length),N=!!S.current&&(0,fe.Z)(S.current),[P,Z]=l.useState(null);(0,pe.Z)((()=>{if(F&&S.current){const e=getComputedStyle(S.current);Z(parseInt(e.marginBottom,10))}}),[F,N]);const q=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const t=e?I:f.errors,n=e?j:f.warnings;return U(t,n,f,"",!!p,m)}(),R=a()(C,n,o,{[`${C}-with-help`]:M||I.length||j.length,[`${C}-has-feedback`]:q&&p,[`${C}-has-success`]:"success"===q,[`${C}-has-warning`]:"warning"===q,[`${C}-has-error`]:"error"===q,[`${C}-is-validating`]:"validating"===q,[`${C}-hidden`]:g,[`${C}-${x}`]:x});return l.createElement("div",{className:R,style:i,ref:S},l.createElement(he.Z,Object.assign({className:`${C}-row`},(0,ge.Z)(w,["_internalItemRender","colon","dependencies","extra","fieldKey","getValueFromEvent","getValueProps","htmlFor","id","initialValue","isListField","label","labelAlign","labelCol","labelWrap","messageVariables","name","normalize","noStyle","preserve","requiredMark","rules","shouldUpdate","trigger","tooltip","validateFirst","validateTrigger","valuePropName","wrapperCol","validateDebounce"])),l.createElement(Pe,Object.assign({htmlFor:b},e,{requiredMark:O,required:null!=v?v:y,prefixCls:t,vertical:k})),l.createElement(we,Object.assign({},e,f,{errors:I,warnings:j,prefixCls:t,status:q,help:s,marginBottom:P,onErrorVisibleChanged:e=>{e||Z(null)}}),l.createElement(r.qI.Provider,{value:$},l.createElement(_e,{prefixCls:t,meta:f,errors:f.errors,warnings:f.warnings,hasFeedback:p,validateStatus:q},h)))),!!P&&l.createElement("div",{className:`${C}-margin-offset`,style:{marginBottom:-P}}))}const De=l.memo((e=>{let{children:t}=e;return t}),((e,t)=>function(e,t){const n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&n.every((n=>{const r=e[n],o=t[n];return r===o||"function"==typeof r||"function"==typeof o}))}(e.control,t.control)&&e.update===t.update&&e.childProps.length===t.childProps.length&&e.childProps.every(((e,n)=>e===t.childProps[n]))));const Ae=function(e){const{name:t,noStyle:n,className:i,dependencies:s,prefixCls:c,shouldUpdate:d,rules:m,children:f,required:p,label:g,messageVariables:h,trigger:b="onChange",validateTrigger:v,hidden:y,help:$,layout:x}=e,{getPrefixCls:w}=l.useContext(q.E_),{name:C}=l.useContext(r.q3),O=function(e){if("function"==typeof e)return e;const t=(0,ce.Z)(e);return t.length<=1?t[0]:t}(f),E="function"==typeof O,k=l.useContext(r.qI),{validateTrigger:S}=l.useContext(Z.zb),I=void 0!==v?v:S,j=!(null==t),F=w("form",c),N=(0,u.Z)(F),[P,R,W]=M(F,N);(0,se.ln)("Form.Item");const H=l.useContext(Z.ZM),_=l.useRef(null),[T,z]=function(e){const[t,n]=l.useState(e),r=l.useRef(null),o=l.useRef([]),i=l.useRef(!1);return l.useEffect((()=>(i.current=!1,()=>{i.current=!0,me.Z.cancel(r.current),r.current=null})),[]),[t,function(e){i.current||(null===r.current&&(o.current=[],r.current=(0,me.Z)((()=>{r.current=null,n((e=>{let t=e;return o.current.forEach((e=>{t=e(t)})),t}))}))),o.current.push(e))}]}({}),[D,A]=(0,le.Z)((()=>({errors:[],warnings:[],touched:!1,validating:!1,name:[],validated:!1}))),L=(e,t)=>{z((n=>{const r=Object.assign({},n),l=[].concat((0,o.Z)(e.name.slice(0,-1)),(0,o.Z)(t)).join("__SPLIT__");return e.destroy?delete r[l]:r[l]=e,r}))},[B,V]=l.useMemo((()=>{const e=(0,o.Z)(D.errors),t=(0,o.Z)(D.warnings);return Object.values(T).forEach((n=>{e.push.apply(e,(0,o.Z)(n.errors||[])),t.push.apply(t,(0,o.Z)(n.warnings||[]))})),[e,t]}),[T,D.errors,D.warnings]),X=function(){const{itemRef:e}=l.useContext(r.q3),t=l.useRef({});return function(n,r){const o=r&&"object"==typeof r&&(0,ie.C4)(r),l=n.join("_");return t.current.name===l&&t.current.originRef===o||(t.current.name=l,t.current.originRef=o,t.current.ref=(0,ie.sQ)(e(n),o)),t.current.ref}}();function U(t,r,o){return n&&!y?l.createElement(_e,{prefixCls:F,hasFeedback:e.hasFeedback,validateStatus:e.validateStatus,meta:D,errors:B,warnings:V,noStyle:!0},t):l.createElement(ze,Object.assign({key:"row"},e,{className:a()(i,W,N,R),prefixCls:F,fieldId:r,isRequired:o,errors:B,warnings:V,meta:D,onSubItemMetaChange:L,layout:x}),t)}if(!j&&!E&&!s)return P(U(O));let K={};return"string"==typeof g?K.label=g:t&&(K.label=String(t)),h&&(K=Object.assign(Object.assign({},K),h)),P(l.createElement(Z.gN,Object.assign({},e,{messageVariables:K,trigger:b,validateTrigger:I,onMetaChange:e=>{const t=null==H?void 0:H.getKey(e.name);if(A(e.destroy?{errors:[],warnings:[],touched:!1,validating:!1,name:[],validated:!1}:e,!0),n&&!1!==$&&k){let n=e.name;if(e.destroy)n=_.current||n;else if(void 0!==t){const[e,r]=t;n=[e].concat((0,o.Z)(r)),_.current=n}k(e,n)}}}),((n,r,i)=>{const a=G(t).length&&r?r.name:[],c=Y(a,C),u=void 0!==p?p:!!(null==m?void 0:m.some((e=>{if(e&&"object"==typeof e&&e.required&&!e.warningOnly)return!0;if("function"==typeof e){const t=e(i);return(null==t?void 0:t.required)&&!(null==t?void 0:t.warningOnly)}return!1}))),f=Object.assign({},n);let g=null;if(Array.isArray(O)&&j)g=O;else if(E&&(!d&&!s||j));else if(!s||E||j)if(l.isValidElement(O)){const t=Object.assign(Object.assign({},O.props),f);if(t.id||(t.id=c),$||B.length>0||V.length>0||e.extra){const n=[];($||B.length>0)&&n.push(`${c}_help`),e.extra&&n.push(`${c}_extra`),t["aria-describedby"]=n.join(" ")}B.length>0&&(t["aria-invalid"]="true"),u&&(t["aria-required"]="true"),(0,ie.Yr)(O)&&(t.ref=X(a,O));new Set([].concat((0,o.Z)(G(b)),(0,o.Z)(G(I)))).forEach((e=>{t[e]=function(){for(var t,n,r,o,l,i=arguments.length,a=new Array(i),s=0;s<i;s++)a[s]=arguments[s];null===(r=f[e])||void 0===r||(t=r).call.apply(t,[f].concat(a)),null===(l=(o=O.props)[e])||void 0===l||(n=l).call.apply(n,[o].concat(a))}}));const n=[t["aria-required"],t["aria-invalid"],t["aria-describedby"]];g=l.createElement(De,{control:f,update:O,childProps:n},(0,ae.Tm)(O,t))}else g=E&&(d||s)&&!j?O(i):O;else;return U(g,c,u)})))};Ae.useStatus=de;var Le=Ae,Be=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};var Ve=e=>{var{prefixCls:t,children:n}=e,o=Be(e,["prefixCls","children"]);const{getPrefixCls:i}=l.useContext(q.E_),a=i("form",t),s=l.useMemo((()=>({prefixCls:a,status:"error"})),[a]);return l.createElement(Z.aV,Object.assign({},o),((e,t,o)=>l.createElement(r.Rk.Provider,{value:s},n(e.map((e=>Object.assign(Object.assign({},e),{fieldKey:e.key}))),t,{errors:o.errors,warnings:o.warnings}))))};const Xe=oe;Xe.Item=Le,Xe.List=Ve,Xe.ErrorList=P,Xe.useForm=ee,Xe.useFormInstance=function(){const{form:e}=l.useContext(r.q3);return e},Xe.useWatch=Z.qo,Xe.Provider=r.RV,Xe.create=()=>{};var Ge=Xe}}]);