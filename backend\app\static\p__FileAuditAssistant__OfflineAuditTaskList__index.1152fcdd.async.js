"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3746],{47046:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"}},75573:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"}},82061:function(e,t,r){var n=r(1413),o=r(67294),a=r(47046),c=r(91146),s=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},i=o.forwardRef(s);t.Z=i},11475:function(e,t,r){r.d(t,{Z:function(){return i}});var n=r(1413),o=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"},c=r(91146),s=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a}))};var i=o.forwardRef(s)},97245:function(e,t,r){var n=r(1413),o=r(67294),a=r(75573),c=r(91146),s=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},i=o.forwardRef(s);t.Z=i},37446:function(e,t,r){r.d(t,{Z:function(){return i}});var n=r(1413),o=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"},c=r(91146),s=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a}))};var i=o.forwardRef(s)},51042:function(e,t,r){var n=r(1413),o=r(67294),a=r(42110),c=r(91146),s=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},i=o.forwardRef(s);t.Z=i},95753:function(e,t,r){r.r(t);var n=r(15009),o=r.n(n),a=r(99289),c=r.n(a),s=r(5574),i=r.n(s),l=r(67294),u=r(34041),d=r(71471),p=r(47019),f=r(2453),h=r(17788),g=r(74330),x=r(55102),m=r(83622),v=r(2487),y=r(32983),b=r(4393),k=r(85418),C=r(66309),Z=r(11475),j=r(40110),w=r(51042),S=r(55287),P=r(82061),T=r(37446),$=r(97245),E=r(96974),z=r(74573),I=r(97131),O=r(85893),N=u.default.Option,B=d.Z.Text,R=d.Z.Paragraph,L={pending_upload:{text:"待上传文件",color:"orange"},analyzing:{text:"分析中",color:"blue"},completed:{text:"已完成",color:"green"},failed:{text:"失败",color:"red"}},_={cardList:{margin:"-24px",padding:"24px"},card:{height:"100%",transition:"all 0.3s",borderRadius:"8px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},avatarContainer:{display:"flex",alignItems:"center",marginBottom:"16px"},avatarIcon:{display:"flex",justifyContent:"center",alignItems:"center",width:"40px",height:"40px",borderRadius:"8px",backgroundColor:"#e6f7ff",color:"#1890ff",fontSize:"20px",marginRight:"10px"},moreButton:{position:"absolute",top:"12px",right:"12px",fontSize:"18px",color:"#8c8c8c",cursor:"pointer",zIndex:10},cardStats:{display:"flex",justifyContent:"space-between",marginTop:"16px",fontSize:"13px",color:"#8c8c8c"},searchContainer:{padding:"16px 0",marginBottom:"16px",display:"flex",justifyContent:"space-between",alignItems:"center"},loadingContainer:{display:"flex",justifyContent:"center",alignItems:"center",height:"60vh"},emptyContainer:{margin:"40px 0"}};t.default=function(){var e=(0,E.s0)(),t=(0,l.useState)([]),r=i()(t,2),n=r[0],a=r[1],s=(0,l.useState)([]),d=i()(s,2),H=d[0],M=d[1],A=(0,l.useState)([]),F=i()(A,2),W=F[0],q=F[1],D=(0,l.useState)(!1),G=i()(D,2),V=G[0],X=G[1],U=p.Z.useForm(),J=i()(U,1)[0],K=(0,l.useState)(""),Q=i()(K,2),Y=Q[0],ee=Q[1],te=(0,l.useState)(!0),re=i()(te,2),ne=re[0],oe=re[1],ae=(0,l.useState)(!0),ce=i()(ae,2),se=ce[0],ie=ce[1],le=function(){var e=c()(o()().mark((function e(){var t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,ie(!0),e.next=4,(0,z.s6)();case 4:if(!(t=e.sent).success||!t.data){e.next=10;break}return e.next=8,q(t.data);case 8:e.next=12;break;case 10:f.ZP.error(t.error||"获取任务类型列表失败"),q([]);case 12:e.next=19;break;case 14:e.prev=14,e.t0=e.catch(0),console.error("获取任务类型列表失败:",e.t0),f.ZP.error("获取任务类型列表失败，请检查网络连接"),q([]);case 19:return e.prev=19,ie(!1),e.finish(19);case 22:case"end":return e.stop()}}),e,null,[[0,14,19,22]])})));return function(){return e.apply(this,arguments)}}(),ue=function(){var e=c()(o()().mark((function e(){var t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,oe(!0),e.next=4,(0,z.Ak)();case 4:(t=e.sent).success&&t.data?(a(t.data),M(t.data)):(f.ZP.error(t.error||"获取任务列表失败"),a([]),M([])),e.next=14;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("获取任务列表失败:",e.t0),f.ZP.error("获取任务列表失败，请检查网络连接"),a([]),M([]);case 14:return e.prev=14,oe(!1),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[0,8,14,17]])})));return function(){return e.apply(this,arguments)}}();(0,l.useEffect)((function(){Promise.all([ue(),le()])}),[]);var de=function(){var e=c()(o()().mark((function e(){var t,r;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,J.validateFields();case 3:return t=e.sent,e.next=6,(0,z.vr)({name:t.taskName,type:t.taskType});case 6:(r=e.sent).success&&r.data?(f.ZP.success("任务创建成功!"),J.resetFields(),X(!1),ue()):f.ZP.error(r.error||"创建任务失败"),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),console.log("表单验证失败:",e.t0);case 13:case"end":return e.stop()}}),e,null,[[0,10]])})));return function(){return e.apply(this,arguments)}}(),pe=function(){var e=c()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:h.Z.confirm({title:"确认删除",icon:(0,O.jsx)(Z.Z,{}),content:"确定要删除这个任务吗？相关文件和结果将一并删除。",okText:"确认",cancelText:"取消",onOk:function(){var e=c()(o()().mark((function e(){var r;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,z._5)(t);case 3:(r=e.sent).success?(f.ZP.success("任务删除成功!"),ue()):f.ZP.error(r.error||"删除任务失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("删除任务失败:",e.t0),f.ZP.error("删除任务失败，请检查网络连接");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),fe=function(t){e("/fileAuditAssistant/offlineAuditTask?task=".concat(t))};return ne||se?(0,O.jsx)(I._z,{children:(0,O.jsx)("div",{style:_.loadingContainer,children:(0,O.jsx)(g.Z,{size:"large",tip:"加载数据中..."})})}):(0,O.jsxs)(I._z,{children:[(0,O.jsxs)("div",{style:{marginBottom:"24px",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,O.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,O.jsx)(x.Z,{placeholder:"搜索任务名称或类型",prefix:(0,O.jsx)(j.Z,{style:{color:"#1890ff"}}),allowClear:!0,value:Y,onChange:function(e){return function(e){ee(e);var t=e.toLowerCase(),r=n.filter((function(e){var r,n=(null===(r=W.find((function(t){return t.code===e.type})))||void 0===r?void 0:r.name)||e.type;return e.name.toLowerCase().includes(t)||n.toLowerCase().includes(t)}));M(r)}(e.target.value)},style:{width:"300px"}}),Y&&(0,O.jsxs)("span",{style:{marginLeft:"8px",fontSize:"13px",color:"#888"},children:["找到 ",H.length," 个匹配的任务"]})]}),(0,O.jsx)(m.ZP,{type:"primary",icon:(0,O.jsx)(w.Z,{}),onClick:function(){0!==W.length?X(!0):se?f.ZP.warning("正在加载任务类型，请稍候..."):f.ZP.warning("当前没有可用的任务类型，请先在管理界面创建并激活任务类型！")},children:"新建任务"})]}),(0,O.jsx)("div",{style:_.cardList,children:(0,O.jsx)(v.Z,{rowKey:"id",loading:ne,grid:{gutter:24,xs:1,sm:2,md:2,lg:3,xl:4,xxl:4},dataSource:H,pagination:{pageSize:12,style:{textAlign:"center",marginTop:"16px"}},locale:{emptyText:(0,O.jsx)(y.Z,{description:(0,O.jsxs)("span",{children:[Y?"未找到匹配的任务":"暂无审核任务",(0,O.jsx)("br",{}),!Y&&(0,O.jsx)(B,{type:"secondary",children:'点击右上角"新建任务"按钮来创建'})]}),image:y.Z.PRESENTED_IMAGE_SIMPLE,style:_.emptyContainer})},renderItem:function(e){var t,r,n,o=[{key:"view",label:(0,O.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,O.jsx)(S.Z,{style:{marginRight:8}}),(0,O.jsx)("span",{children:"查看详情"})]}),onClick:function(t){t.domEvent.stopPropagation(),fe(e.id)}},{key:"delete",label:(0,O.jsxs)("div",{style:{display:"flex",alignItems:"center",color:"#ff4d4f"},children:[(0,O.jsx)(P.Z,{style:{marginRight:8,color:"#ff4d4f"}}),(0,O.jsx)("span",{children:"删除"})]}),onClick:function(t){t.domEvent.stopPropagation(),pe(e.id)}}];return(0,O.jsx)(v.Z.Item,{children:(0,O.jsxs)(b.Z,{hoverable:!0,style:_.card,onClick:function(){return fe(e.id)},bodyStyle:{paddingRight:"40px"},children:[(0,O.jsx)("div",{style:_.moreButton,onClick:function(e){e.stopPropagation()},children:(0,O.jsx)(k.Z,{menu:{items:o},trigger:["click"],placement:"bottomRight",getPopupContainer:function(e){return e.parentNode},overlayStyle:{minWidth:"120px",boxShadow:"0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)",borderRadius:"8px"},children:(0,O.jsx)(T.Z,{})})}),(0,O.jsxs)("div",{style:_.avatarContainer,children:[(0,O.jsx)("div",{style:_.avatarIcon,children:(0,O.jsx)($.Z,{})}),(0,O.jsx)("span",{style:{fontWeight:"bold",fontSize:"16px"},children:e.name})]}),(0,O.jsx)(b.Z.Meta,{title:null,description:(0,O.jsxs)(R,{ellipsis:{rows:2},children:[(null===(t=W.find((function(t){return t.code===e.type})))||void 0===t?void 0:t.name)||e.type,(0,O.jsx)(C.Z,{color:(null===(r=L[e.status])||void 0===r?void 0:r.color)||"default",style:{marginLeft:"8px"},children:(null===(n=L[e.status])||void 0===n?void 0:n.text)||e.status})]})}),(0,O.jsxs)("div",{style:_.cardStats,children:[(0,O.jsxs)("span",{children:["文件数量：",e.file_count||0]}),(0,O.jsxs)("span",{children:["创建时间：",new Date(e.created_at).toLocaleDateString()]})]})]})})}})}),(0,O.jsx)(h.Z,{title:"新建审核任务",open:V,onOk:de,onCancel:function(){J.resetFields(),X(!1)},okText:"创建",cancelText:"取消",destroyOnClose:!0,children:(0,O.jsxs)(p.Z,{form:J,layout:"vertical",name:"create_task_form",children:[(0,O.jsx)(p.Z.Item,{name:"taskName",label:"任务名称",rules:[{required:!0,message:"请输入任务名称!"}],children:(0,O.jsx)(x.Z,{placeholder:"例如：年度报告审核"})}),(0,O.jsx)(p.Z.Item,{name:"taskType",label:"审核规则类型",rules:[{required:!0,message:"请选择审核规则类型!"}],children:(0,O.jsx)(u.default,{placeholder:"选择对应的审核规则类型",children:W.map((function(e){return(0,O.jsxs)(N,{value:e.code,children:[e.name,e.description?" (".concat(e.description,")"):""]},e.id)}))})})]})})]})}},74573:function(e,t,r){r.d(t,{Ak:function(){return u},Sq:function(){return b},_5:function(){return v},s6:function(){return i},vr:function(){return h},xJ:function(){return x},yW:function(){return p}});var n=r(15009),o=r.n(n),a=r(99289),c=r.n(a),s=r(78158);function i(e){return l.apply(this,arguments)}function l(){return(l=c()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/auditTask/audit-task-types",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function u(e){return d.apply(this,arguments)}function d(){return(d=c()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/auditTask/audit-tasks",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function p(e){return f.apply(this,arguments)}function f(){return(f=c()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/auditTask/audit-tasks/".concat(t),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function h(e){return g.apply(this,arguments)}function g(){return(g=c()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/auditTask/audit-tasks",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function x(e,t){return m.apply(this,arguments)}function m(){return(m=c()(o()().mark((function e(t,r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/auditTask/audit-tasks/".concat(t),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function v(e){return y.apply(this,arguments)}function y(){return(y=c()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/auditTask/audit-tasks/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function b(e){return k.apply(this,arguments)}function k(){return(k=c()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/auditTask/audit-tasks/".concat(t,"/files"),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},66309:function(e,t,r){r.d(t,{Z:function(){return E}});var n=r(67294),o=r(93967),a=r.n(o),c=r(98423),s=r(98787),i=r(69760),l=r(96159),u=r(45353),d=r(53124),p=r(11568),f=r(15063),h=r(14747),g=r(83262),x=r(83559);const m=e=>{const{lineWidth:t,fontSizeIcon:r,calc:n}=e,o=e.fontSizeSM;return(0,g.IX)(e,{tagFontSize:o,tagLineHeight:(0,p.bf)(n(e.lineHeightSM).mul(o).equal()),tagIconSize:n(r).sub(n(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},v=e=>({defaultBg:new f.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var y=(0,x.I$)("Tag",(e=>(e=>{const{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:n,componentCls:o,calc:a}=e,c=a(n).sub(r).equal(),s=a(t).sub(r).equal();return{[o]:Object.assign(Object.assign({},(0,h.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:c,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:s,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:c}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(m(e))),v),b=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r};const k=n.forwardRef(((e,t)=>{const{prefixCls:r,style:o,className:c,checked:s,onChange:i,onClick:l}=e,u=b(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:f}=n.useContext(d.E_),h=p("tag",r),[g,x,m]=y(h),v=a()(h,`${h}-checkable`,{[`${h}-checkable-checked`]:s},null==f?void 0:f.className,c,x,m);return g(n.createElement("span",Object.assign({},u,{ref:t,style:Object.assign(Object.assign({},o),null==f?void 0:f.style),className:v,onClick:e=>{null==i||i(!s),null==l||l(e)}})))}));var C=k,Z=r(98719);var j=(0,x.bk)(["Tag","preset"],(e=>(e=>(0,Z.Z)(e,((t,r)=>{let{textColor:n,lightBorderColor:o,lightColor:a,darkColor:c}=r;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:n,background:a,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:c,borderColor:c},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(m(e))),v);const w=(e,t,r)=>{const n="string"!=typeof(o=r)?o:o.charAt(0).toUpperCase()+o.slice(1);var o;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var S=(0,x.bk)(["Tag","status"],(e=>{const t=m(e);return[w(t,"success","Success"),w(t,"processing","Info"),w(t,"error","Error"),w(t,"warning","Warning")]}),v),P=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r};const T=n.forwardRef(((e,t)=>{const{prefixCls:r,className:o,rootClassName:p,style:f,children:h,icon:g,color:x,onClose:m,bordered:v=!0,visible:b}=e,k=P(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:C,direction:Z,tag:w}=n.useContext(d.E_),[T,$]=n.useState(!0),E=(0,c.Z)(k,["closeIcon","closable"]);n.useEffect((()=>{void 0!==b&&$(b)}),[b]);const z=(0,s.o2)(x),I=(0,s.yT)(x),O=z||I,N=Object.assign(Object.assign({backgroundColor:x&&!O?x:void 0},null==w?void 0:w.style),f),B=C("tag",r),[R,L,_]=y(B),H=a()(B,null==w?void 0:w.className,{[`${B}-${x}`]:O,[`${B}-has-color`]:x&&!O,[`${B}-hidden`]:!T,[`${B}-rtl`]:"rtl"===Z,[`${B}-borderless`]:!v},o,p,L,_),M=e=>{e.stopPropagation(),null==m||m(e),e.defaultPrevented||$(!1)},[,A]=(0,i.Z)((0,i.w)(e),(0,i.w)(w),{closable:!1,closeIconRender:e=>{const t=n.createElement("span",{className:`${B}-close-icon`,onClick:M},e);return(0,l.wm)(e,t,(e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),M(t)},className:a()(null==e?void 0:e.className,`${B}-close-icon`)})))}}),F="function"==typeof k.onClick||h&&"a"===h.type,W=g||null,q=W?n.createElement(n.Fragment,null,W,h&&n.createElement("span",null,h)):h,D=n.createElement("span",Object.assign({},E,{ref:t,className:H,style:N}),q,A,z&&n.createElement(j,{key:"preset",prefixCls:B}),I&&n.createElement(S,{key:"status",prefixCls:B}));return R(F?n.createElement(u.Z,{component:"Tag"},D):D)})),$=T;$.CheckableTag=C;var E=$}}]);