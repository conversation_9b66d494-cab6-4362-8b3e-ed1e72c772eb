"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3752],{95993:function(e,t,n){n.r(t),n.d(t,{default:function(){return Ze}});var r=n(97857),o=n.n(r),s=n(15009),c=n.n(s),a=n(64599),i=n.n(a),l=n(19632),u=n.n(l),d=n(99289),p=n.n(d),f=n(5574),x=n.n(f),m=n(88310),v=n(17598),g=n(15525),h=n(19050),k=n(19669),y=n(42075),b=n(83062),Z=n(83622),j=n(2487),w=n(67294),_=n(24444),C=(0,_.kc)((function(e){var t=e.token;return{referenceList:{".ant-list-item:first-child":{paddingTop:"0"}},card:{listItemMetaTitle:{color:t.colorTextHeading},".ant-card-meta-title":{marginBottom:"4px","& > a":{display:"inline-block",maxWidth:"100%",color:t.colorTextHeading}},".ant-card-meta-description":{height:"44px",overflow:"hidden",lineHeight:"22px"},"&:hover":{".ant-card-meta-title > a":{color:t.colorPrimary}}},cardItemContent:{display:"flex",height:"20px",marginTop:"16px",marginBottom:"-4px",lineHeight:"20px","& > span":{flex:"1",color:t.colorTextSecondary,fontSize:"12px"}},avatarList:{flex:"0 1 auto"},cardList:{marginTop:"24px"},coverCardList:{".ant-list .ant-list-item-content-single":{maxWidth:"100%"}},toolbar:{boxShadow:"0 1px 4px rgba(0,0,0,0.1)"},listItem:{transition:"all 0.3s ease"}}})),S=n(85893),P=(0,w.forwardRef)((function(e,t){var n=e.messages,r=C().styles,o=(0,w.useState)([]),s=x()(o,2),c=s[0],a=s[1],i=(0,w.useState)(""),l=x()(i,2),u=l[0],d=l[1];(0,w.useEffect)((function(){n&&n.length>0?a(n):a([])}),[n]);var p=w.useState(!1),f=x()(p,2),_=(f[0],f[1]),P=w.useState("all"),D=x()(P,2),I=D[0],T=(D[1],(null==c?void 0:c.filter((function(e){var t="all"===I||e.type===I,n=!u||e.messageId===u;return t&&n})))||[]),F=w.useRef(null),E=(w.useRef(new Map),function(e){console.log("scrollToItem--",e);var t=document.getElementById("content-".concat(e));t&&t.scrollIntoView({behavior:"smooth",block:"center"})});return(0,w.useImperativeHandle)(t,(function(){return{scrollToItem:E,updateReferenceList:function(e){setReferenceList(e)},filterByMessageId:function(e){d(e)},clearFilter:function(){d("")},getFilterMessageId:function(){return u}}})),(0,S.jsxs)("div",{children:[(0,S.jsx)("div",{style:{position:"sticky",top:0,padding:"8px 12px 8px 12px",zIndex:1,borderBottom:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between",alignItems:"center"},children:(0,S.jsxs)(y.Z,{children:[(0,S.jsx)(b.Z,{title:"展开全部",mouseEnterDelay:2,children:(0,S.jsx)(Z.ZP,{type:"text",icon:(0,S.jsx)(m.Z,{}),onClick:function(){return _(!0)}})}),(0,S.jsx)(b.Z,{title:"收起全部",mouseEnterDelay:2,children:(0,S.jsx)(Z.ZP,{type:"text",icon:(0,S.jsx)(v.Z,{}),onClick:function(){return _(!1)}})}),(0,S.jsx)(b.Z,{title:"滚动到顶部",mouseEnterDelay:2,children:(0,S.jsx)(Z.ZP,{type:"text",icon:(0,S.jsx)(g.Z,{}),onClick:function(){F.current&&F.current.scrollTo({top:0,behavior:"smooth"})}})}),(0,S.jsx)(b.Z,{title:"滚动到底部",mouseEnterDelay:2,children:(0,S.jsx)(Z.ZP,{type:"text",icon:(0,S.jsx)(h.Z,{}),onClick:function(){F.current&&F.current.scrollTo({top:F.current.scrollHeight,behavior:"smooth"})}})}),u&&(0,S.jsx)(b.Z,{title:"清除筛选",mouseEnterDelay:2,children:(0,S.jsx)(Z.ZP,{type:"primary",icon:(0,S.jsx)(k.Z,{}),onClick:function(){return d("")}})})]})}),(0,S.jsx)("div",{ref:F,style:{height:"calc(100vh - 130px)",overflowY:"auto"},children:(0,S.jsx)(j.Z,{size:"large",className:r.referenceList,rowKey:"id",itemLayout:"vertical",dataSource:T})})]})}));P.displayName="WorkOrderReference";var D=P,I=n(93461),T=n(34114),F=n(78205),E=n(78919),R=n(4628),H=n(9502),M=n(76654),z=n(71471),N=n(2453),Y=n(17788),L=n(74330),B=n(86250),O=n(66309),A=n(55102),W=n(85265),J=n(10048),q=n(10981),G=n(78404),K=n(1832),X=n(14079),U=n(66513),$=n(93045),V=n(71255),Q=n(51042),ee=n(82061),te=n(47389),ne=n(87784),re=n(25820),oe=n(75750),se=n(12906),ce=n(85175),ae=n(43471),ie=n(27484),le=n.n(ie),ue=(0,_.kc)((function(e){var t=e.token;return{reference:{background:"".concat(t.colorBgLayout,"80"),minWidth:"400px",maxWidth:"720px",width:"30%"},layout:{width:"100%",minWidth:"800px",borderRadius:t.borderRadius,display:"flex",background:t.colorBgContainer,fontFamily:"AlibabaPuHuiTi, ".concat(t.fontFamily,", sans-serif"),".ant-prompts":{color:t.colorText},border:"3px solid",borderImage:"linear-gradient(45deg, ".concat(t.colorPrimary,", ").concat(t.colorSplit,") 1")},menu:{height:"100%",display:"flex",flexDirection:"column"},conversations:{padding:"0 12px",flex:1,overflowY:"auto"},chat:{height:"100%",width:"70%",maxWidth:"700px",margin:"0 auto",boxSizing:"border-box",display:"flex",flexDirection:"column",padding:t.paddingLG,gap:"16px"},messages:{flex:1},placeholder:{paddingTop:"32px"},sender:{boxShadow:t.boxShadow},logo:{display:"flex",height:"72px",alignItems:"center",justifyContent:"start",padding:"0 24px",boxSizing:"border-box",img:{width:"24px",height:"24px",display:"inline-block"},span:{display:"inline-block",margin:"0 8px",fontWeight:"bold",color:t.colorText,fontSize:"16px"}},addBtn:{background:"#1677ff0f",border:"1px solid #1677ff34",width:"calc(100% - 24px)",margin:"0 12px 24px 12px"}}})),de=n(93933),pe=n(13973);function fe(e){return e+"-"+Date.now()}var xe=function(e,t){return(0,S.jsxs)(y.Z,{align:"start",children:[e,(0,S.jsx)("span",{children:t})]})},me=[{key:"1",label:xe((0,S.jsx)(K.Z,{style:{color:"#FF4D4F"}}),"常见工单"),description:"快速处理常见工单问题",children:[{key:"1-1",description:"如何处理客户投诉工单？"},{key:"1-2",description:"账户异常工单处理流程？"},{key:"1-3",description:"系统故障工单如何处理？"}]},{key:"2",label:xe((0,S.jsx)(X.Z,{style:{color:"#52C41A"}}),"工单指南"),description:"工单处理标准流程",children:[{key:"2-1",icon:(0,S.jsx)(U.Z,{style:{color:"#722ED1"}}),description:"工单分类与优先级判断"},{key:"2-2",icon:(0,S.jsx)($.Z,{style:{color:"#1890FF"}}),description:"工单回复规范与模板"},{key:"2-3",icon:(0,S.jsx)(V.Z,{style:{color:"#13C2C2"}}),description:"工单升级与转派流程"}]},{key:"3",label:xe((0,S.jsx)(X.Z,{style:{color:"#FA8C16"}}),"业务知识"),description:"快速查找业务知识",children:[{key:"3-1",icon:(0,S.jsx)(U.Z,{style:{color:"#EB2F96"}}),description:"产品功能与使用说明"},{key:"3-2",icon:(0,S.jsx)($.Z,{style:{color:"#F5222D"}}),description:"常见问题解决方案"},{key:"3-3",icon:(0,S.jsx)(V.Z,{style:{color:"#A0D911"}}),description:"业务流程与规范说明"}]}],ve=[{key:"historyConversation",description:"历史对话",icon:(0,S.jsx)(X.Z,{style:{color:"#FF4D4F"}})},{key:"newConversation",description:"新建对话",icon:(0,S.jsx)(Q.Z,{style:{color:"#1890FF"}})},{key:"clearConversation",description:"清空对话",icon:(0,S.jsx)(ee.Z,{style:{color:"#1890FF"}})}],ge=(0,q.bG)(),he=(0,G.kH)(),ke=(0,J.Z)({html:!0,breaks:!0}),ye=function(e){return(0,S.jsx)(z.Z,{style:{marginBottom:0},children:(0,S.jsx)("div",{dangerouslySetInnerHTML:{__html:ke.render(e)}})})},be="workOrderAssistant",Ze=function(){var e,t=ue().styles,n=(0,w.useState)(window.innerHeight),r=x()(n,1)[0],s=w.useRef(),a=w.useState(!1),l=x()(a,2),d=l[0],f=l[1],m=w.useState(""),v=x()(m,2),g=v[0],h=v[1],k=w.useState([]),j=x()(k,2),_=j[0],C=j[1],P=w.useState(),z=x()(P,2),J=z[0],G=z[1],K=(0,w.useState)(!1),X=x()(K,2),U=X[0],$=X[1],V=(0,w.useState)(!1),ie=x()(V,2),xe=ie[0],ke=ie[1],Ze=(0,w.useState)(!1),je=x()(Ze,2),we=je[0],_e=je[1],Ce=(0,w.useState)(""),Se=x()(Ce,2),Pe=Se[0],De=Se[1],Ie=(0,w.useState)(""),Te=x()(Ie,2),Fe=Te[0],Ee=Te[1],Re=(0,w.useState)([]),He=x()(Re,2),Me=He[0],ze=He[1],Ne=(0,w.useRef)(null),Ye=(0,w.useState)(!1),Le=x()(Ye,2),Be=Le[0],Oe=Le[1],Ae=(0,w.useState)(""),We=x()(Ae,2),Je=We[0],qe=We[1],Ge=function(e){qe(e),Oe(!0)},Ke=function(e){var t=Me.find((function(t){return t.message_id===e}));t&&navigator.clipboard.writeText(t.content).then((function(){N.ZP.success("复制成功")})).catch((function(){N.ZP.error("复制失败")}))},Xe=(0,I.Z)({request:(e=p()(c()().mark((function e(t,n){var r,o,a,l,d,p,f,x,m,v,g,h,k,y,b,Z,j,w,_,C,S,P,D,I,T,F,E,R,H,M,z,Y,L,B,O,A,W;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.messages,o=t.message,a=n.onSuccess,l=n.onUpdate,d=n.onError,e.prev=2,!xe){e.next=6;break}return N.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 6:if(ke(!0),f=(0,q.bW)(),x=o?o.id:fe(s.current),o||a({content:"出现了异常:",role:"assistant",id:x,references:[],collected:!1}),m={conversation_id:s.current||"",message_id:x,meta_data:{},extra:{},role:o?o.role:"user",content:o?o.content:"",app_info:be,user_id:null==ge?void 0:ge.id,user_name:null==ge?void 0:ge.name,references:[],token_count:null,price:null,collected:!1,created_at:le()().format("YYYY-MM-DD HH:mm:ss")},ze((function(e){var t=[].concat(u()(e),[m]);return console.log("更新后的消息列表:",t),t})),s.current){e.next=15;break}throw N.ZP.error("No active conversation selected"),new Error("No active conversation selected");case 15:return console.log("activeKey===>",s.current),v={conversation_id:s.current,app_info:be,user_id:null==ge?void 0:ge.id,user_name:null==ge?void 0:ge.name,extra:{},messages:r},g={id:fe(s.current),role:"user",content:"",references:[],collected:!1},h=!1,k="",y=[],l(g),e.next=24,fetch("/api/app/chat/system_app_completions",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(f)},body:JSON.stringify(v)});case 24:if((b=e.sent).ok){e.next=27;break}throw new Error("HTTP 错误！状态码：".concat(b.status));case 27:if(Z=null===(p=b.body)||void 0===p?void 0:p.getReader()){e.next=30;break}throw new Error("当前浏览器不支持 ReadableStream。");case 30:j=new TextDecoder("utf-8"),w={conversation_id:s.current||"",message_id:g.id,meta_data:{},extra:{},role:"assistant",content:"",app_info:be,user_id:null==ge?void 0:ge.id,user_name:null==ge?void 0:ge.name,references:[],token_count:null,price:null,collected:!1,created_at:le()().format("YYYY-MM-DD HH:mm:ss")};case 32:if(h){e.next=100;break}return e.next=35,Z.read();case 35:_=e.sent,C=_.value,_.done&&(h=!0),k+=j.decode(C,{stream:!0}),S=k.split("\n\n"),k=S.pop()||"",P=i()(S),e.prev=43,P.s();case 45:if((D=P.n()).done){e.next=90;break}if(""!==(I=D.value).trim()){e.next=49;break}return e.abrupt("continue",88);case 49:T=I.split("\n"),F=null,E=null,R=i()(T);try{for(R.s();!(H=R.n()).done;)(M=H.value).startsWith("event: ")?F=M.substring(7).trim():M.startsWith("data: ")&&(E=M.substring(6))}catch(e){R.e(e)}finally{R.f()}if(!E){e.next=88;break}e.t0=F,e.next="answer"===e.t0?58:"moduleStatus"===e.t0?70:"appStreamResponse"===e.t0?72:"flowResponses"===e.t0?74:"end"===e.t0?76:"error"===e.t0?78:88;break;case 58:if("[DONE]"===E){e.next=69;break}e.prev=59,Y=JSON.parse(E),(L=(null===(z=Y.choices[0])||void 0===z||null===(z=z.delta)||void 0===z?void 0:z.content)||"")&&(g.content+=L,l(g)),e.next=69;break;case 65:return e.prev=65,e.t1=e.catch(59),console.error("Error parsing answer data:",e.t1),e.abrupt("return",a({content:"出现了异常:"+E,role:"assistant",id:fe(s.current),references:[],collected:!1}));case 69:return e.abrupt("break",88);case 70:try{B=JSON.parse(E),console.log("模块状态：",B)}catch(e){console.error("Error parsing moduleStatus data:",e)}return e.abrupt("break",88);case 72:try{O=JSON.parse(E),console.log("appStreamData===>",O),y=O,g.references=y}catch(e){console.error("Error parsing appStreamResponse data:",e)}return e.abrupt("break",88);case 74:try{console.log("flowResponsesData",E)}catch(e){console.error("Error parsing flowResponses data:",e)}return e.abrupt("break",88);case 76:return h=!0,e.abrupt("break",88);case 78:e.prev=78,A=JSON.parse(E),l(A),e.next=87;break;case 83:throw e.prev=83,e.t2=e.catch(78),console.error("Error event received:",e.t2),e.t2;case 87:return e.abrupt("break",88);case 88:e.next=45;break;case 90:e.next=95;break;case 92:e.prev=92,e.t3=e.catch(43),P.e(e.t3);case 95:return e.prev=95,P.f(),e.finish(95);case 98:e.next=32;break;case 100:if(a(g),!g.content||""===g.content.trim()){e.next=108;break}return w.content=g.content,w.references=y,e.next=106,(0,de.tn)(w);case 106:(W=e.sent).success?(w.message_id=W.data.message_id,console.log("创建消息成功，返回数据:",W.data),ze((function(e){var t=[].concat(u()(e),[W.data]);return console.log("更新后的消息列表:",t),t}))):N.ZP.error("消息上报失败");case 108:e.next=115;break;case 110:e.prev=110,e.t4=e.catch(2),console.log("error===>",e.t4),a({content:"出现了，系统正在处理其他对话。请稍后重试",role:"assistant",id:fe(s.current),references:[],collected:!1}),d(e.t4 instanceof Error?e.t4:new Error("Unknown error"));case 115:return e.prev=115,ke(!1),e.finish(115);case 118:case"end":return e.stop()}}),e,null,[[2,110,115,118],[43,92,95,98],[59,65],[78,83]])}))),function(t,n){return e.apply(this,arguments)})}),Ue=x()(Xe,1)[0],$e=(0,T.Z)({agent:Ue}),Ve=$e.onRequest,Qe=$e.messages,et=$e.setMessages,tt=function(e){G(e),console.log("activeKey 设置",e),s.current=e},nt=function(e){var t=e.filter((function(e){return e.pinned})).sort((function(e,t){return new Date(t.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return o()(o()({},e),{},{key:e.id||"",label:e.conversation_name||e.id,group:"置顶"})})),n=e.filter((function(e){return!e.pinned})).sort((function(e,t){return new Date(t.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return o()(o()({},e),{},{key:e.id||"",label:e.conversation_name||"",group:"对话"})}));C([].concat(u()(t),u()(n)))},rt=function(e){return 0===e.length?null:e.reduce((function(e,t){return new Date(t.active_at)>new Date(e.active_at)?t:e}))},ot=function(){var e=p()(c()().mark((function e(t){var n,r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,ke(!0),console.info("获取对话信息",t),n=le()().format("YYYY-MM-DD HH:mm:ss"),e.next=6,(0,de.$o)(t,{conversation_name:null,active_at:n,pinned_at:null,pinned:null});case 6:null!=(r=e.sent)&&r.messages?(console.info("设置对话信息",r.messages),o=r.messages.map((function(e){return{id:e.id||e.message_id,message:{id:e.id||e.message_id,content:e.content,role:e.role,references:e.references||[],collected:e.collected||!1},status:"assistant"===e.role?"success":"local",meta:e.meta||{avatar:"assistant"===e.role?(null==he?void 0:he.logo)||"/static/logo.png":(null==ge?void 0:ge.avatar)||"/avatar/default.jpeg"}}})),ze(r.messages),et(o),tt(t)):N.ZP.error("获取对话信息失败"),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),console.error("切换对话时出错：",e.t0);case 13:return e.prev=13,ke(!1),G(t),e.finish(13);case 17:case"end":return e.stop()}}),e,null,[[0,10,13,17]])})));return function(t){return e.apply(this,arguments)}}(),st=function(){var e=p()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(s.current){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,(0,de.Db)(s.current);case 4:e.sent.success?(ze([]),et([]),Ne.current&&Ne.current.updateReferenceList([])):N.ZP.error("清空对话失败");case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ct=function(){var e=p()(c()().mark((function e(){var t,n,r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!xe){e.next=3;break}return N.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(!(t=(0,q.bG)())){e.next=19;break}return e.prev=5,n=(new Date).toLocaleString(),r="对话-".concat(n),e.next=10,(0,de.Xw)({user_id:t.id,user_name:t.name,conversation_name:r,app_info:be});case 10:o=e.sent,nt([].concat(u()(_),[{key:o.id||"",id:o.id||"",label:o.conversation_name||"",conversation_name:o.conversation_name||"",active_at:o.active_at||"",pinned_at:o.pinned_at,pinned:o.pinned||!1,messages:[]}])),tt(o.id||""),st(),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(5),console.error("创建新对话时出错：",e.t0);case 19:case"end":return e.stop()}}),e,null,[[5,16]])})));return function(){return e.apply(this,arguments)}}(),at=function(){var e=p()(c()().mark((function e(t){var n,r,s,a,i;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=_.find((function(e){return e.key===t}))){e.next=3;break}return e.abrupt("return");case 3:return r=n.pinned,s=!r,e.prev=6,a=le()().format("YYYY-MM-DD HH:mm:ss"),e.next=10,(0,de.X1)(t,{conversation_name:null,active_at:null,pinned:s,pinned_at:a});case 10:i=_.map((function(e){return e.key===t?o()(o()({},e),{},{pinned:s}):e})),nt(i),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(6),console.error("更新置顶状态时出错：",e.t0);case 17:case"end":return e.stop()}}),e,null,[[6,14]])})));return function(t){return e.apply(this,arguments)}}(),it=function(){var e=p()(c()().mark((function e(t){var n;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,de.SJ)(t);case 3:n=_.filter((function(e){return e.key!==t})),nt(n),s.current===t&&n.length>0&&ot(n[0].key||""),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除对话时出错：",e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(t){return e.apply(this,arguments)}}(),lt=function(){var e=p()(c()().mark((function e(t,n){var r,s;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,_.find((function(e){return e.key===t}))){e.next=4;break}return e.abrupt("return");case 4:return r={conversation_name:n,active_at:null,pinned_at:null,pinned:null},e.next=7,(0,de.X1)(t,r);case 7:null!=(s=e.sent)&&s.success?C((function(e){return e.map((function(e){return e.key===t?o()(o()({},e),{},{label:n}):e}))})):N.ZP.error("更新对话标题失败"),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("更新对话标题时出错：",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t,n){return e.apply(this,arguments)}}(),ut=function(){var e=p()(c()().mark((function e(t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!xe){e.next=3;break}return N.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:return e.next=5,ot(t);case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();(0,w.useEffect)((function(){var e=function(){var e=p()(c()().mark((function e(){var t,n,r;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t=(0,q.bG)())){e.next=20;break}return e.prev=2,e.next=5,(0,de.Mw)({user_id:t.id,app_info:be});case 5:if(!(n=e.sent).success||!Array.isArray(n.data)){e.next=15;break}if(0!==n.data.length){e.next=12;break}return e.next=10,ct();case 10:e.next=15;break;case 12:r=rt(n.data),nt(n.data),ot(r?r.id:n.data[0].id||"");case 15:e.next=20;break;case 17:e.prev=17,e.t0=e.catch(2),console.error("初始化对话时出错：",e.t0);case 20:case"end":return e.stop()}}),e,null,[[2,17]])})));return function(){return e.apply(this,arguments)}}();e()}),[be]);var dt=function(){var e=p()(c()().mark((function e(t){var n,r,o,s;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("重新生成消息:",t),e.prev=1,console.info(Me),n=Me.findIndex((function(e){return e.message_id===t})),console.log("currentIndex===>",n),-1!==n){e.next=8;break}return N.ZP.error("未找到指定消息"),e.abrupt("return");case 8:return r=Me[n],o=Me.slice(n),console.log("将要删除的消息:",o),e.next=13,(0,de.qP)(o.map((function(e){return e.message_id})));case 13:e.sent.success||N.ZP.error("删除消息失败"),ze((function(e){return e.slice(0,n)})),et((function(e){return e.slice(0,n)})),"assistant"===r.role?(s=Me.slice(0,n).reverse().find((function(e){return"user"===e.role})))&&Ve({id:t,role:"user",content:s.content,references:[],collected:!1}):Ve({id:t,role:"user",content:r.content,references:[],collected:!1}),N.ZP.success("正在重新生成回复..."),e.next=25;break;case 21:e.prev=21,e.t0=e.catch(1),console.error("重新生成消息时出错：",e.t0),N.ZP.error("重新生成消息失败");case 25:case"end":return e.stop()}}),e,null,[[1,21]])})));return function(t){return e.apply(this,arguments)}}(),pt=function(){var e=p()(c()().mark((function e(t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:Y.Z.confirm({title:"确认删除",content:"确定要删除这条消息吗？删除后不可恢复。",okText:"确认",cancelText:"取消",onOk:function(){return p()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("delete messageId===>",t),e.next=4,(0,de.$Z)(t);case 4:e.sent.success?(ze((function(e){return e.filter((function(e){return e.message_id!==t}))})),console.log("delete currentConversationMessages===>",Me),et((function(e){return e.filter((function(e){return e.message.id!==t}))})),console.log("delete messages===>",Qe),N.ZP.success("消息及相关引用已删除")):N.ZP.error("删除消息失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除消息时出错：",e.t0),N.ZP.error("删除消息失败");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})))()}});case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ft=function(){var e=p()(c()().mark((function e(t,n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("收藏状态切换:",t,n),e.next=3,(0,de.bk)({message_id:t,collected:!n});case 3:e.sent.success?(N.ZP.success(n?"取消收藏成功":"收藏成功"),et((function(e){return e.map((function(e){return e.id===t?o()(o()({},e),{},{message:o()(o()({},e.message),{},{collected:!n})}):e}))})),ze((function(e){return e.map((function(e){return e.message_id===t?o()(o()({},e),{},{collected:!n}):e}))}))):N.ZP.error(n?"取消收藏失败":"收藏失败");case 5:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),xt=function(){var e=p()(c()().mark((function e(t){var n,r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!xe){e.next=3;break}return N.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(n=t.data,r=n.key,o=n.description,"historyConversation"!==r){e.next=8;break}$(!0),e.next=19;break;case 8:if("newConversation"!==r){e.next=13;break}return e.next=11,ct();case 11:e.next=19;break;case 13:if("clearConversation"!==r){e.next=18;break}return e.next=16,st();case 16:e.next=19;break;case 18:Ve({id:fe(s.current),role:"user",content:o,references:[],collected:!1});case 19:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),mt=(0,S.jsxs)(y.Z,{direction:"vertical",size:16,className:t.placeholder,children:[(0,S.jsx)(F.Z,{variant:"borderless",icon:(0,S.jsx)("img",{src:(null==he?void 0:he.logo)||"/static/logo.png",alt:"logo"}),title:"您好，我是智能客服助手",description:"我可以为您解答产品咨询、业务办理等相关问题，请问有什么可以帮您？"}),(0,S.jsx)(E.Z,{title:"以下是常见问题，您可以直接点击进行咨询",items:me,styles:{list:{width:"100%"},item:{backgroundImage:"linear-gradient(137deg, #f0f7ff 0%, #fff1f0 100%)",border:0,flex:1}},onItemClick:xt})]}),vt=Qe.length>0?Qe.map((function(e){var t=e.id,n=e.message,r=e.status;return{key:s.current+"_"+t,loadingRender:function(){return(0,S.jsxs)(y.Z,{children:[(0,S.jsx)(L.Z,{size:"small"}),"模型思考中..."]})},loading:"loading"===r&&n.content.length<1,content:n.content,shape:"local"===r?"corner":"round",variant:"local"===r?"filled":"borderless",rols:n.role,messageRender:ye,avatar:"local"===r?{src:(null==ge?void 0:ge.avatar)||"/avatar/default.jpeg"}:{src:(null==he?void 0:he.logo)||"/static/logo.png"},placement:"local"!==r?"start":"end",footer:"local"!==r?(0,S.jsxs)(B.Z,{children:[n.references.length>0&&(0,S.jsxs)(O.Z,{bordered:!1,color:"blue",onClick:function(){return e=n.id,console.log("filterMessageReference===>",e),void(Ne.current&&(Ne.current.getFilterMessageId()===e?Ne.current.clearFilter():Ne.current.filterByMessageId(e)));var e},children:["引用:",n.references.length]}),(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:(0,S.jsx)(ee.Z,{style:{color:"#ccc"}}),onClick:function(){return pt(n.id)}}),(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:n.collected?(0,S.jsx)(re.Z,{style:{color:"#FFD700"}}):(0,S.jsx)(oe.Z,{style:{color:"#ccc"}}),onClick:function(){return ft(n.id,n.collected)}}),(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:(0,S.jsx)(se.Z,{style:{color:"#ccc"}}),onClick:function(){return Ge(n.id)}}),(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:(0,S.jsx)(ce.Z,{style:{color:"#ccc"}}),onClick:function(){return Ke(n.id)}})]}):(0,S.jsxs)(B.Z,{children:[(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:(0,S.jsx)(ae.Z,{style:{color:"#ccc"}}),onClick:function(){return dt(n.id)}}),(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:(0,S.jsx)(ee.Z,{style:{color:"#ccc"}}),onClick:function(){return pt(n.id)}}),(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:n.collected?(0,S.jsx)(re.Z,{style:{color:"#FFD700"}}):(0,S.jsx)(oe.Z,{style:{color:"#ccc"}}),onClick:function(){return ft(n.id,n.collected)}}),(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:(0,S.jsx)(se.Z,{style:{color:"#ccc"}}),onClick:function(){return Ge(n.id)}}),(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:(0,S.jsx)(ce.Z,{style:{color:"#ccc"}}),onClick:function(){return Ke(n.id)}})]})}})):[{content:mt,variant:"borderless"}],gt=(0,S.jsx)(R.Z.Header,{title:"Attachments",open:d,onOpenChange:f,styles:{content:{padding:0}}}),ht=(0,S.jsxs)("div",{className:t.logo,children:[(0,S.jsx)("span",{children:"对话记录"}),(0,S.jsx)(b.Z,{title:"新对话",children:(0,S.jsx)(Z.ZP,{type:"text",icon:(0,S.jsx)(Q.Z,{}),onClick:ct,style:{fontSize:"16px"}})})]}),kt=(0,S.jsx)(Y.Z,{title:"修改对话标题",open:we,onOk:function(){Fe&&Pe.trim()&&(lt(Fe,Pe.trim()),_e(!1))},onCancel:function(){_e(!1),De(""),Ee("")},children:(0,S.jsx)(A.Z,{value:Pe,onChange:function(e){return De(e.target.value)},placeholder:"请输入新的对话标题"})}),yt=(0,S.jsx)(W.Z,{title:"历史对话",placement:"right",width:400,onClose:function(){return $(!1)},open:U,children:(0,S.jsxs)("div",{className:t.menu,children:[ht,(0,S.jsx)(H.Z,{items:_,activeKey:J,onActiveChange:ut,menu:function(e){return{items:[{label:"重命名",key:"edit",icon:(0,S.jsx)(te.Z,{})},{label:"置顶",key:"pin",icon:(0,S.jsx)(ne.Z,{})},{label:"删除",key:"delete",icon:(0,S.jsx)(ee.Z,{}),danger:!0}],onClick:function(t){switch(console.log("menuInfo","Click ".concat(e.key," - ").concat(t.key)),t.key){case"edit":Ee(e.key),De(e.label),_e(!0);break;case"pin":at(e.key);break;case"delete":if(xe)return void N.ZP.error("系统正在处理其他对话。请稍😊");it(e.key)}}}},groupable:!0})]})});return(0,w.useEffect)((function(){console.log("currentConversationMessages 更新了:",Me)}),[Me]),(0,S.jsxs)("div",{className:t.layout,style:{height:r-56},children:[(0,S.jsxs)("div",{className:t.chat,children:[(0,S.jsx)(M.Z.List,{items:vt,className:t.messages}),(0,S.jsx)(E.Z,{items:ve,onItemClick:xt}),(0,S.jsx)(R.Z,{value:g,header:gt,onSubmit:function(e){console.log("nextContent===>",e),e&&(Ve({id:fe(s.current),role:"user",content:e,references:[],collected:!1}),h(""))},onChange:h,loading:Ue.isRequesting(),className:t.sender})]}),(0,S.jsx)("div",{className:t.reference,children:(0,S.jsx)(D,{ref:Ne,messages:Me})}),kt,yt,(0,S.jsx)(pe.Z,{visible:Be,messageId:Je,conversationId:J,appInfo:be,onClose:function(){return Oe(!1)}})]})}}}]);